# BCI Virtual-Kubelet E2E测试架构设计

## 项目概述

BCI Virtual-Kubelet E2E测试框架是专门为百度云BCI Virtual-Kubelet设计的端到端测试解决方案。该框架基于现有的CCE E2E测试框架，针对Virtual-Kubelet的特殊性进行了定制化设计，确保BCI Virtual-Kubelet在各种场景下的功能正确性和稳定性。

## 设计目标

1. **全面覆盖**: 覆盖Virtual-Kubelet的所有核心功能，包括Pod生命周期管理、网络配置、存储挂载、监控日志等
2. **真实环境**: 在真实的CCE集群和BCI环境中进行测试，确保测试结果的可靠性
3. **自动化**: 支持完全自动化的测试执行、结果收集和报告生成
4. **可扩展**: 支持新测试用例的快速添加和现有用例的灵活配置
5. **CI/CD集成**: 与持续集成流水线深度集成，支持自动化测试和质量门禁

## 项目结构

```
bci-vk-e2e-test/
├── cmd/                         # 命令行工具
│   ├── root.go                  # 根命令定义
│   ├── exec.go                  # 测试执行命令
│   ├── show_case.go             # 显示测试用例命令
│   ├── report.go                # 测试报告生成命令
│   └── validate.go              # 配置验证命令
├── internal/                    # 内部实现
│   ├── cases/                   # 测试用例实现
│   │   ├── vk/                  # Virtual-Kubelet核心功能测试
│   │   │   ├── pod_lifecycle.go # Pod生命周期测试
│   │   │   ├── node_management.go # 节点管理测试
│   │   │   ├── resource_quota.go # 资源配额测试
│   │   │   └── provider_api.go  # Provider接口测试
│   │   ├── bci/                 # BCI服务相关测试
│   │   │   ├── pod_creation.go  # BCI Pod创建测试
│   │   │   ├── networking.go    # BCI网络功能测试
│   │   │   ├── storage.go       # BCI存储功能测试
│   │   │   └── monitoring.go    # BCI监控功能测试
│   ├── casegroup/               # 测试组配置
│   │   ├── vk-basic.yaml        # 基础功能测试组
│   │   ├── vk-advanced.yaml     # 高级功能测试组
│   │   ├── vk-performance.yaml  # 性能测试组
│   │   └── vk-reliability.yaml  # 可靠性测试组
│   ├── executor/                # 测试执行器
│   │   ├── vk_executor.go       # VK专用执行器
│   │   └── parallel_executor.go # 并行执行器
│   ├── models/                  # 数据模型
│   │   ├── vk_models.go         # VK相关数据模型
│   │   └── test_result.go       # 测试结果模型
│   └── utils/                   # 工具函数
│       ├── vk_utils.go          # VK工具函数
│       ├── bci_utils.go         # BCI工具函数
│       └── k8s_utils.go         # Kubernetes工具函数
├── pkg/                         # 公共包
│   ├── vkclient/                # Virtual-Kubelet客户端
│   │   ├── client.go            # VK客户端实现
│   │   └── types.go             # VK类型定义
│   ├── bciclient/               # BCI客户端封装
│   │   ├── client.go            # BCI客户端实现
│   │   └── mock.go              # BCI Mock客户端
│   ├── logger/                  # 日志组件
│   └── config/                  # 配置管理
├── config/                      # 配置文件
│   ├── test-config.yaml         # 测试配置模板
│   ├── cluster-config.yaml      # 集群配置模板
│   └── bci-config.yaml          # BCI配置模板
├── templates/                   # 资源模板
│   ├── pods/                    # Pod模板
│   ├── services/                # Service模板
│   └── configmaps/              # ConfigMap模板
├── scripts/                     # 脚本文件
│   ├── setup.sh                 # 环境设置脚本
│   ├── cleanup.sh               # 环境清理脚本
│   └── ci.sh                    # CI脚本
├── deploy/                      # 部署文件
│   ├── helm/                    # Helm Charts
│   └── k8s/                     # Kubernetes YAML
├── docs/                        # 文档
│   ├── user-guide.md            # 用户指南
│   ├── test-cases.md            # 测试用例说明
│   └── troubleshooting.md       # 故障排查指南
├── Makefile                     # 构建脚本
├── Dockerfile                   # 容器镜像构建
├── go.mod                       # Go模块定义
└── README.md                    # 项目说明
```

## 核心架构组件

### 1. 测试用例接口 (VKTestCase Interface)

```go
type VKTestCase interface {
    // 基础接口继承
    cases.Interface
    
    // VK特定方法
    SetupVirtualKubelet(ctx context.Context) error     // 设置Virtual-Kubelet环境
    CleanupVirtualKubelet(ctx context.Context) error   // 清理Virtual-Kubelet环境
    ValidateVKNode(ctx context.Context) error          // 验证VK节点状态
    GetVKMetrics(ctx context.Context) (*VKMetrics, error) // 获取VK指标
}
```

### 2. VK专用基础客户端 (VKBaseClient)

```go
type VKBaseClient struct {
    // 继承原有BaseClient
    *cases.BaseClient
    
    // VK特定客户端
    VKClient          vkclient.Interface      // Virtual-Kubelet客户端
    BCIClient         bciclient.Interface     // BCI客户端
    WebhookClient     webhook.Interface       // Webhook客户端
    
    // VK配置
    VKConfig          *VKConfig               // VK配置
    BCIConfig         *BCIConfig              // BCI配置
    
    // VK状态管理
    VKNodes           map[string]*VKNode      // VK节点状态
    BCIPods           map[string]*BCIPod      // BCI Pod状态
    
    // 测试环境
    TestNamespace     string                  // 测试命名空间
    VKNodeName        string                  // VK节点名称
}
```

### 3. VK测试执行器 (VKExecutor)

```go
type VKExecutor struct {
    // 继承基础执行器
    *executor.Executor
    
    // VK特定配置
    VKConfig          *VKConfig
    ParallelMode      bool                    // 是否并行执行
    VKHealthCheck     bool                    // 是否进行VK健康检查
    
    // VK环境管理
    vkSetupDone       bool                    // VK环境是否已设置
    vkNodes           []*VKNode               // 管理的VK节点
    
    // 性能监控
    metricsCollector  *MetricsCollector       // 指标收集器
    performanceData   *PerformanceData        // 性能数据
}
```

## 测试用例分类

### 1. Virtual-Kubelet核心功能测试 (vk/)

#### 1.1 Pod生命周期测试
- **VKPodCreation**: VK Pod创建功能测试
- **VKPodUpdate**: VK Pod更新功能测试
- **VKPodDeletion**: VK Pod删除功能测试
- **VKPodStatus**: VK Pod状态同步测试
- **VKPodRestart**: VK Pod重启功能测试

#### 1.2 节点管理测试
- **VKNodeRegistration**: VK节点注册测试
- **VKNodeCapacity**: VK节点容量管理测试
- **VKNodeConditions**: VK节点状态条件测试
- **VKNodeLabels**: VK节点标签管理测试
- **VKNodeTaints**: VK节点污点管理测试

#### 1.3 资源配额测试
- **VKResourceQuota**: VK资源配额限制测试
- **VKResourceAllocation**: VK资源分配测试
- **VKResourceScaling**: VK资源扩缩容测试

#### 1.4 Provider接口测试
- **VKProviderAPI**: Provider接口完整性测试
- **VKContainerLogs**: 容器日志获取测试
- **VKContainerExec**: 容器命令执行测试
- **VKPortForward**: 端口转发功能测试
- **VKMetricsCollection**: 指标收集功能测试

### 2. BCI服务相关测试 (bci/)

#### 2.1 BCI Pod创建测试
- **BCIPodCreation**: BCI Pod创建流程测试
- **BCIPodConfiguration**: BCI Pod配置转换测试
- **BCIPodScheduling**: BCI Pod调度测试
- **BCIPodImagePull**: BCI Pod镜像拉取测试

#### 2.2 BCI网络功能测试
- **BCINetworking**: BCI网络配置测试
- **BCISubnetSelection**: BCI子网选择测试
- **BCISecurityGroup**: BCI安全组配置测试
- **BCIEIP**: BCI弹性IP配置测试
- **BCILoadBalancer**: BCI负载均衡器测试

#### 2.3 BCI存储功能测试
- **BCIVolumeMount**: BCI存储卷挂载测试
- **BCIPV**: BCI持久化存储测试
- **BCIConfigMap**: BCI ConfigMap挂载测试
- **BCISecret**: BCI Secret挂载测试

#### 2.4 BCI监控功能测试
- **BCIMetrics**: BCI指标收集测试
- **BCILogging**: BCI日志收集测试
- **BCIEvents**: BCI事件处理测试


## 数据模型设计

### 1. VK配置模型

```go
type VKConfig struct {
    // 基础配置
    Region              string                 `json:"region"`
    ClusterID           string                 `json:"clusterID"`
    NodeName            string                 `json:"nodeName"`

    // 资源配置
    CPUCapacity         string                 `json:"cpuCapacity"`
    MemoryCapacity      string                 `json:"memoryCapacity"`
    PodsCapacity        string                 `json:"podsCapacity"`
    IPsCapacity         string                 `json:"ipsCapacity"`
    ENIsCapacity        string                 `json:"enisCapacity"`

    // BCI配置
    BCISubnets          []BCISubnet            `json:"bciSubnets"`
    SecurityGroupID     string                 `json:"securityGroupID"`

    // 功能开关
    EnablePodCache      bool                   `json:"enablePodCache"`
    EnablePodRescue     bool                   `json:"enablePodRescue"`
    EnableWebhook       bool                   `json:"enableWebhook"`
    AutoInjectKubeProxy bool                   `json:"autoInjectKubeProxy"`

    // 高级配置
    WebhookServerPort   int                    `json:"webhookServerPort"`
    PodSyncWorkers      int                    `json:"podSyncWorkers"`
    MaxRequestsInFlight int                    `json:"maxRequestsInFlight"`
}

type BCISubnet struct {
    Zone     string `json:"zone"`
    SubnetID string `json:"subnetID"`
}
```

### 2. 测试结果模型

```go
type VKTestResult struct {
    // 基础信息
    TestID          string                 `json:"testId"`
    CaseName        string                 `json:"caseName"`
    StartTime       time.Time              `json:"startTime"`
    EndTime         time.Time              `json:"endTime"`
    Duration        time.Duration          `json:"duration"`
    Success         bool                   `json:"success"`
    ErrorMessage    string                 `json:"errorMessage,omitempty"`

    // VK特定信息
    VKNodeName      string                 `json:"vkNodeName"`
    BCIPodsCreated  int                    `json:"bciPodsCreated"`
    ResourcesUsed   VKResourceUsage        `json:"resourcesUsed"`

    // 性能数据
    PerformanceData *PerformanceMetrics    `json:"performanceData,omitempty"`

    // 详细日志
    Logs            []string               `json:"logs,omitempty"`
    Events          []VKEvent              `json:"events,omitempty"`
}

type VKResourceUsage struct {
    CPUUsed    string `json:"cpuUsed"`
    MemoryUsed string `json:"memoryUsed"`
    PodsUsed   int    `json:"podsUsed"`
    IPsUsed    int    `json:"ipsUsed"`
}

type PerformanceMetrics struct {
    PodStartupLatency    time.Duration `json:"podStartupLatency"`
    APIResponseLatency   time.Duration `json:"apiResponseLatency"`
    ThroughputPods       float64       `json:"throughputPods"`
    ResourceUtilization  float64       `json:"resourceUtilization"`
}
```

### 3. VK节点模型

```go
type VKNode struct {
    Name            string                 `json:"name"`
    Status          VKNodeStatus           `json:"status"`
    Capacity        v1.ResourceList        `json:"capacity"`
    Allocatable     v1.ResourceList        `json:"allocatable"`
    Conditions      []v1.NodeCondition     `json:"conditions"`
    CreatedAt       time.Time              `json:"createdAt"`
    LastHeartbeat   time.Time              `json:"lastHeartbeat"`

    // VK特定信息
    ProviderVersion string                 `json:"providerVersion"`
    BCIEndpoint     string                 `json:"bciEndpoint"`
    PodCacheEnabled bool                   `json:"podCacheEnabled"`
    WebhookEnabled  bool                   `json:"webhookEnabled"`
}

type VKNodeStatus string

const (
    VKNodeStatusReady    VKNodeStatus = "Ready"
    VKNodeStatusNotReady VKNodeStatus = "NotReady"
    VKNodeStatusUnknown  VKNodeStatus = "Unknown"
)
```

## 工作流程设计

### 1. 测试环境准备流程

```
1. 验证测试配置
   ├── 检查CCE集群连接性
   ├── 验证BCI服务可用性
   ├── 检查必要的权限和配额
   └── 验证网络和存储配置

2. 部署Virtual-Kubelet
   ├── 创建测试命名空间
   ├── 部署VK Helm Chart
   ├── 等待VK节点就绪
   └── 验证VK功能正常

3. 初始化测试环境
   ├── 创建测试资源模板
   ├── 设置监控和日志收集
   ├── 初始化性能基线
   └── 准备测试数据
```

### 2. 测试执行流程

```
1. 预检查阶段 (PreCheck)
   ├── VK节点状态检查
   ├── BCI服务状态检查
   ├── 网络连通性检查
   └── 资源配额检查

2. 主测试阶段 (MainTest)
   ├── 按测试组顺序执行
   ├── 支持并行执行模式
   ├── 实时监控测试进度
   ├── 收集性能和日志数据
   └── 处理测试依赖关系

3. 后检查阶段 (PostCheck)
   ├── 资源清理验证
   ├── 数据一致性检查
   ├── 性能数据分析
   └── 生成测试报告
```

### 3. 测试用例执行流程

```
单个测试用例执行流程:
1. 初始化 (Init)
   ├── 解析测试配置
   ├── 创建VK客户端连接
   ├── 设置测试环境
   └── 准备测试数据

2. 执行检查 (Check)
   ├── 创建测试资源
   ├── 执行测试逻辑
   ├── 验证预期结果
   ├── 收集性能数据
   └── 记录测试事件

3. 环境清理 (Clean)
   ├── 删除测试资源
   ├── 清理BCI Pod
   ├── 重置VK状态
   └── 释放占用资源

4. 结果记录
   ├── 更新测试结果
   ├── 保存性能数据
   ├── 上传日志文件
   └── 发送通知
```

## 配置管理

### 1. 测试配置文件结构

```yaml
# config/test-config.yaml
testConfig:
  # 基础配置
  batchID: "vk-e2e-test-20240101-001"
  region: "bj"
  environment: "test"

  # 集群配置
  cluster:
    clusterID: "cce-xxx"
    kubeConfig: "/path/to/kubeconfig"
    createCluster: false
    deleteCluster: false

  # Virtual-Kubelet配置
  virtualKubelet:
    image: "registry.baidubce.com/cce-plugin-pro/bci-virtual-kubelet:df9d9cd-20250825"
    nodeCount: 1
    nodeNamePrefix: "vk-e2e-test"
    cpuCapacity: "1000"
    memoryCapacity: "4Ti"
    podsCapacity: "1000"
    enablePodCache: true
    enableWebhook: true

  # BCI配置
  bci:
    subnets:
      - zone: "zoneA"
        subnetID: "sbn-xxx"
      - zone: "zoneB"
        subnetID: "sbn-yyy"
    securityGroupID: "g-xxx"

  # 测试执行配置
  execution:
    parallelMode: true
    maxConcurrency: 10
    timeout: "30m"
    retryCount: 3

  # 监控配置
  monitoring:
    enableMetrics: true
    enableTracing: true
    metricsInterval: "30s"

  # 报告配置
  reporting:
    outputFormat: ["json", "html", "junit"]
    uploadResults: true
    notifyOnFailure: true
```

### 2. 测试组配置示例

```yaml
# internal/casegroup/vk-basic.yaml
CreateCluster: false
ClusterID: "cce-xxx"
DeleteCluster: false
VirtualKubeletRequired: true

PreCheckList:
  - name: VKNodeRegistration
    desc: "验证VK节点注册功能"
  - name: VKProviderAPI
    desc: "验证VK Provider接口"

CheckList:
  - name: VKPodCreation
    desc: "测试VK Pod创建功能"
    config:
      podCount: 5
      imageRegistry: "registry.baidubce.com"

  - name: VKPodLifecycle
    desc: "测试VK Pod完整生命周期"
    config:
      testScenarios: ["create", "update", "delete"]

  - name: BCINetworking
    desc: "测试BCI网络功能"
    config:
      testTypes: ["subnet", "securityGroup", "eip"]

  - name: BCIVolumeMount
    desc: "测试BCI存储卷挂载"
    config:
      volumeTypes: ["configMap", "secret", "pv"]

PostCheckList:
  - name: VKResourceCleanup
    desc: "验证VK资源清理"
  - name: VKMetricsValidation
    desc: "验证VK指标数据"
```

## 命令行工具设计

### 1. 基本命令

```bash
# 构建项目
make build

# 查看所有可用的VK测试用例
./bci-vk-e2e-test show-case --category vk

# 执行VK基础功能测试
./bci-vk-e2e-test exec -g internal/casegroup/vk-basic.yaml

# 执行特定VK测试用例
./bci-vk-e2e-test exec -g internal/casegroup/vk-basic.yaml --cases VKPodCreation,VKPodLifecycle

# 执行性能测试
./bci-vk-e2e-test exec -g internal/casegroup/vk-performance.yaml --parallel

# 验证VK配置
./bci-vk-e2e-test validate --config config/test-config.yaml

# 生成VK测试报告
./bci-vk-e2e-test report --batch-id vk-e2e-test-20240101-001 --format html

# 查询VK测试结果
./bci-vk-e2e-test query --job vk-basic-test --metrics

# 清理VK测试环境
./bci-vk-e2e-test cleanup --cluster-id cce-xxx --force
```

### 2. 高级命令

```bash
# 部署VK测试环境
./bci-vk-e2e-test setup --config config/test-config.yaml

# 监控VK测试执行
./bci-vk-e2e-test monitor --batch-id vk-e2e-test-20240101-001

# 导出VK测试数据
./bci-vk-e2e-test export --batch-id vk-e2e-test-20240101-001 --format csv

# 比较VK测试结果
./bci-vk-e2e-test compare --baseline baseline-batch-id --current current-batch-id

# 生成VK性能报告
./bci-vk-e2e-test perf-report --batch-id vk-e2e-test-20240101-001
```

## 技术特色和创新

### 1. VK专用测试能力

- **虚拟节点生命周期管理**: 完整的VK节点创建、配置、监控和清理流程
- **BCI集成测试**: 深度集成BCI服务，测试真实的Pod调度和运行场景
- **Provider接口验证**: 全面验证Virtual-Kubelet Provider接口的实现正确性
- **Webhook功能测试**: 专门测试VK Webhook的变更和验证逻辑

### 2. 性能和可靠性测试

- **大规模扩缩容测试**: 支持数千Pod的扩缩容性能测试
- **并发操作测试**: 验证VK在高并发场景下的稳定性
- **故障注入测试**: 模拟各种故障场景，验证VK的容错能力
- **长期稳定性测试**: 支持长时间运行的稳定性验证

### 3. 智能化测试管理

- **自适应测试调度**: 根据集群资源状况智能调度测试用例
- **动态配置热更新**: 支持测试过程中的配置动态调整
- **智能故障诊断**: 自动分析测试失败原因并提供修复建议
- **测试结果预测**: 基于历史数据预测测试结果趋势

### 4. 企业级特性

- **多租户支持**: 支持多个团队同时使用测试框架
- **权限管理**: 细粒度的权限控制和审计日志
- **集成CI/CD**: 与百度内部CI/CD系统深度集成
- **监控告警**: 完善的监控指标和告警机制

这个BCI Virtual-Kubelet E2E测试架构设计提供了一个完整、专业的测试解决方案，既继承了现有CCE E2E测试框架的优秀特性，又针对Virtual-Kubelet的特殊需求进行了深度定制，确保能够全面、准确地验证BCI Virtual-Kubelet的功能和性能。
