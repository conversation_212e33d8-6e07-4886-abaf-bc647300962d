/* Copyright 2018 Baidu Inc. All Rights Reserved. */
/* annotations.go - Define the annotation and associated functions
/*
modification history
--------------------
2019/07/06, by <PERSON><PERSON><PERSON>, create
*/
/*
DESCRIPTION
This file contains the annotations used by cce-ingress-controller
*/

package common

import (
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/appblb"
)

const (
	enableDirectAccessEnv = "ENABLE_DIRECT_ACCESS"
)

// Handler by cce-ingress-controller
const (
	// AnnotationIngressClass Ingress Class
	AnnotationIngressClass = "kubernetes.io/ingress.class"

	// AnnotationIngressHandler only handler kubernetes.io/ingress.class=cce
	AnnotationIngressHandler = "cce"

	// AnnotationIngressKeepIaaSResource 设置该参数 = true, 不删除 IaaS 资源
	AnnotationIngressKeepIaaSResource = "kubernetes.io/cce.ingress.keep-iaas-resource"

	// AnnotationIngressNetworkMode & IngressNetworkModeDirectAccess Indicate Ingress is pod direct network mode
	AnnotationIngressNetworkMode   = "kubernetes.io/cce.ingress.network-mode"
	IngressNetworkModeDirectAccess = "direct-access"

	// AnnotationNoUseBLBClusterL4 不使用 BLB L4 专属集群，BLB 将会建在 L4 共享集群上
	AnnotationNoUseBLBClusterL4 = "kubernetes.io/no-use-blb-cluster-l4"

	// AnnotationNoUseBLBClusterL7 不使用 BLB L7 专属集群，BLB 将会建在 L7 共享集群上
	AnnotationNoUseBLBClusterL7 = "kubernetes.io/no-use-blb-cluster-l7"

	// AnnotationBLBClusterIDL4 用户指定的BLB 专属L4集群 ID
	AnnotationBLBClusterIDL4 = "kubernetes.io/blb-cluster-l4-id"

	// AnnotationBLBClusterIDL7 用户指定的BLB 专属L7集群 ID
	AnnotationBLBClusterIDL7 = "kubernetes.io/blb-cluster-l7-id"
)

// AppBLB annotation
const (
	// AnnotationBLBName to set BLB Name
	AnnotationBLBName = "kubernetes.io/cce.ingress.blb-name"

	// AnnotationVPCSubnetID to set VPC SubnetID
	AnnotationVPCSubnetID = "kubernetes.io/cce.ingress.vpc-subnet-id"

	// AnnotationBLBShortID Ingress's associated app blbShortID
	AnnotationBLBShortID = "kubernetes.io/cce.ingress.blb-id"

	// AnnotationBLBShortIDAllocateVip is the annotation which indicates BLB with a VIP (for baidu internal user)
	AnnotationBLBShortIDAllocateVip = "kubernetes.io/cce-load-balancer-allocate-vip"
)

// 监听器相关 Annotation
const (
	// AnnotationHTTPRedirect If Ingress support HTTP Redirect
	AnnotationHTTPRedirect = "kubernetes.io/cce.ingress.http-redirect"

	// AnnotationTimeoutInSeconds to set timeout in seconds
	AnnotationTimeoutInSeconds = "kubernetes.io/cce.ingress.timeout-in-seconds"

	// AnnotationHTTPS If Ingress support HTTPS
	AnnotationHTTPS = "kubernetes.io/cce.ingress.https"

	// AnnotationBLBCertID Ingress HTTPS's cert ID
	AnnotationBLBCertID = "kubernetes.io/cce.ingress.blb-cert-id"

	// DefaultBLBScheduler 监听器默认采用 RoundRobin 调度算法
	DefaultBLBScheduler = appblb.SchedulerTypeRoundRobin

	// AnnotationBLBHTTPScheduler 设置 HTTP 监听器的调度算法 仅创建时有效
	AnnotationBLBHTTPScheduler = "kubernetes.io/cce.ingress.blb.listener.http.scheduler"

	// AnnotationBLBHTTPSScheduler 设置 HTTPS 监听器的调度算法 仅创建时有效
	AnnotationBLBHTTPSScheduler = "kubernetes.io/cce.ingress.blb.listener.https.scheduler"

	// DefaultBLBXff 默认打开 XFF
	DefaultBLBXff = true

	// AnnotationBLBHTTPHeaderXFF 设置 HTTP XFF Header 仅创建时有效
	AnnotationBLBHTTPHeaderXFF = "kubernetes.io/cce.ingress.blb.listener.http.enable-x-forwarded-for-header"

	// AnnotationBLBHTTPSHeaderXFF 设置 HTTPS 监听器的调度算法 仅创建时有效
	AnnotationBLBHTTPSHeaderXFF = "kubernetes.io/cce.ingress.blb.listener.https.enable-x-forwarded-for-header"

	// 以下内容用于设置 HTTP 监听器会话保持机制

	// 会话保持机制的默认选项
	Default_ListenerKeepSessionEnable     = false
	Default_ListenerKeepSessionType       = appblb.KeepSessionTypeInsert
	Default_ListenerKeepSessionTimeout    = 3600
	Default_ListenerKeepSessionCookieName = "KeepSessionCookie"

	AnnotationBLBHTTP_KeepSessionEnable     = "kubernetes.io/cce.ingress.blb.listener.http.keep-session.enable"
	AnnotationBLBHTTP_KeepSessionType       = "kubernetes.io/cce.ingress.blb.listener.http.keep-session.type"
	AnnotationBLBHTTP_KeepSessionTimeout    = "kubernetes.io/cce.ingress.blb.listener.http.keep-session.timeout"
	AnnotationBLBHTTP_KeepSessionCookieName = "kubernetes.io/cce.ingress.blb.listener.http.keep-session.cookie-name"

	// 以下内容用于设置 HTTPS 监听器会话保持机制

	AnnotationBLBHTTPS_KeepSessionEnable     = "kubernetes.io/cce.ingress.blb.listener.https.keep-session.enable"
	AnnotationBLBHTTPS_KeepSessionType       = "kubernetes.io/cce.ingress.blb.listener.https.keep-session.type"
	AnnotationBLBHTTPS_KeepSessionTimeout    = "kubernetes.io/cce.ingress.blb.listener.https.keep-session.timeout"
	AnnotationBLBHTTPS_KeepSessionCookieName = "kubernetes.io/cce.ingress.blb.listener.https.keep-session.cookie-name"
)

// EIP annotation
const (
	// AnnotationEIP Ingress's associated EIP
	AnnotationEIP = "kubernetes.io/cce.ingress.eip"

	// AnnotationInternal If Ingress only internal
	AnnotationInternal = "kubernetes.io/cce.ingress.internal"

	// AnnotationEIPBandWidth to set EIP BandWidth
	AnnotationEIPBandWidth = "kubernetes.io/cce.ingress.eip-bandwidth-in-mbps"

	// AnnotationEIPBillingMethod is the annotation of ElasticIPBillingMethod
	AnnotationEIPBillingMethod = "kubernetes.io/cce.ingress.eip-billing-method"

	// AnnotationEIPRouteType is the annotation of ElasticIPReservationLength
	AnnotationEIPRouteType = "kubernetes.io/cce.ingress.eip-route-type"
)

// IPv6 annotation
const (
	// AnnotationEnableIPv6 If Ingress LB should have IPv6 Address
	AnnotationEnableIPv6 = "kubernetes.io/cce.ingress.enable-ipv6"
)

// Policy annotation
const (
	// AnnotationHTTPRules for http rules, format:
	AnnotationHTTPRules = "kubernetes.io/cce.ingress.http-rules"

	// AnnotationHTTPSRules for https rules
	AnnotationHTTPSRules = "kubernetes.io/cce.ingress.https-rules"

	// AnnotationPoliciesRearrange 是否允许对该 Ingress 关联的 BLB 转发规则在满足条件的情况下进行重排序
	AnnotationPoliciesRearrange = "kubernetes.io/cce.ingress.enable-rearrange"

	// AnnotationPoliciesRearrangePriorityBound 指令重排序的优先级限值 在所有已有规则都低于该优先级时才会进行重排序
	AnnotationPoliciesRearrangePriorityBound = "kubernetes.io/cce.ingress.rearrange-priority-bound"

	AnnotationPoliciesMixHTTPSAndHTTP = "kubernetes.io/cce.ingress.mix-http-https-rules"

	defaultRearrangePolicyPriorityBound = 100
)

// BackendServer annotation
const (
	// AnnotationMaxBackendCount the max count of backend
	AnnotationMaxBackendCount = "kubernetes.io/cce.ingress.max-backend-count"
)

// Control Annotation
const (
	// AnnotationIgnoreByCCE if value is true, cce controller will not reconcile this ingress resource
	AnnotationIgnoreByCCE = "kubernetes.io/cce.ingress.ignore-by-cce"
)

// EnvDisableEIPByDefault 环境变量 在用户没有添加 InternalAnnotation 的情况下 是否启用EIP
const (
	EnvDisableEIPByDefault = "DISABlE_EIP_BY_DEFAULT"
)
