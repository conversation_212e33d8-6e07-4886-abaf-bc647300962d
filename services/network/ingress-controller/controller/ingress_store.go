package controller

import (
	"context"
	"time"

	"k8s.io/api/extensions/v1beta1"
	networkingv1 "k8s.io/api/networking/v1"
	metaV1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/client-go/informers"
	v1beta1extensionsinformer "k8s.io/client-go/informers/extensions/v1beta1"
	v1 "k8s.io/client-go/informers/networking/v1"
	"k8s.io/client-go/kubernetes"
	ingresslisters "k8s.io/client-go/listers/extensions/v1beta1"
	ingressnetworkingv1listers "k8s.io/client-go/listers/networking/v1"
	"k8s.io/client-go/tools/cache"
	apisextensionsv1beta1 "k8s.io/kubernetes/pkg/apis/extensions/v1beta1"
	"k8s.io/kubernetes/pkg/apis/networking"
	networkingv1api "k8s.io/kubernetes/pkg/apis/networking/v1"

	log "icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/utils"
)

type IngressGroupType string

const IngressGroupTypeNetworkingV1 IngressGroupType = "NetworkingV1"
const IngressGroupTypeExtensionsV1beta1 IngressGroupType = "ExtensionsV1beta1"

type IngressStoreOperator interface {
	GetIngressByLister(namespace string, name string) (*v1beta1.Ingress, error)
	ListIngressByLister(namespace string) ([]*v1beta1.Ingress, error)
	AddEventHandlerWithResyncPeriod(handler cache.ResourceEventHandler, resyncPeriod time.Duration)
	IngressInformerSynced() cache.InformerSynced
	UpdateIngressStatus(ctx context.Context, ingress *v1beta1.Ingress) error
	ListAllIngressByLister() ([]*v1beta1.Ingress, error)
}

type IngressStore struct {
	groupType IngressGroupType

	kubeClient kubernetes.Interface

	extensionsV1Informer    v1beta1extensionsinformer.IngressInformer
	extensionsV1beta1Lister ingresslisters.IngressLister

	networkingV1Informer v1.IngressInformer
	networkingV1Lister   ingressnetworkingv1listers.IngressLister

	ingressListerSynced cache.InformerSynced
}

func NewIngressStore(k8sClient kubernetes.Interface, informers informers.SharedInformerFactory) (*IngressStore, error) {
	isSupportNetworkingV1, err := utils.IsCCESupportNetworkingV1(k8sClient)
	if err != nil {
		return nil, err
	}

	if isSupportNetworkingV1 {
		return &IngressStore{
			groupType:            IngressGroupTypeNetworkingV1,
			kubeClient:           k8sClient,
			networkingV1Informer: informers.Networking().V1().Ingresses(),
			networkingV1Lister:   informers.Networking().V1().Ingresses().Lister(),
			ingressListerSynced:  informers.Networking().V1().Ingresses().Informer().HasSynced,
		}, nil
	}
	return &IngressStore{
		groupType:               IngressGroupTypeExtensionsV1beta1,
		kubeClient:              k8sClient,
		extensionsV1Informer:    informers.Extensions().V1beta1().Ingresses(),
		extensionsV1beta1Lister: informers.Extensions().V1beta1().Ingresses().Lister(),
		ingressListerSynced:     informers.Extensions().V1beta1().Ingresses().Informer().HasSynced,
	}, nil
}

func (store *IngressStore) UpdateIngressStatus(ctx context.Context, ingress *v1beta1.Ingress) error {
	if store.groupType == IngressGroupTypeExtensionsV1beta1 {
		_, err := store.kubeClient.ExtensionsV1beta1().Ingresses(ingress.Namespace).UpdateStatus(ctx, ingress, metaV1.UpdateOptions{})
		return err
	}
	networkingv1Ingress, err := ConvertExtensionsV1beta1ToNetworkingV1(ingress)
	if err != nil {
		return err
	}
	_, err = store.kubeClient.NetworkingV1().Ingresses(ingress.Namespace).UpdateStatus(ctx, networkingv1Ingress, metaV1.UpdateOptions{})
	return err
}

func (store *IngressStore) AddEventHandlerWithResyncPeriod(handler cache.ResourceEventHandler, resyncPeriod time.Duration) {
	if store.groupType == IngressGroupTypeExtensionsV1beta1 {
		store.extensionsV1Informer.Informer().AddEventHandlerWithResyncPeriod(handler, resyncPeriod)
	} else {
		addIngressCompatiable := func(obj any) {
			curIngress, ok := obj.(*networkingv1.Ingress)
			if !ok {
				log.Errorf(nil, "addIngressCompatiable: Object %+v is not networkingv1 ingress", obj)
				return
			}
			extensionsv1beta1Ingress, err := ConvertNetworkingV1IngressToExtensionsV1beta1(curIngress)
			if err != nil {
				log.Errorf(nil, "ConvertNetworkingV1IngressToExtensionsV1beta1 fail: %+v", err)
				return
			}

			handler.OnAdd(extensionsv1beta1Ingress)
		}

		updateIngressCompatiable := func(old, cur any) {
			oldIngress, ok := old.(*networkingv1.Ingress)
			if !ok {
				return
			}
			oldExtensionsv1beta1Ingress, err := ConvertNetworkingV1IngressToExtensionsV1beta1(oldIngress)
			if err != nil {
				log.Errorf(nil, "ConvertNetworkingV1IngressToExtensionsV1beta1 fail: %+v", err)
				return
			}

			newIngress, ok := cur.(*networkingv1.Ingress)
			if !ok {
				return
			}
			newExtensionsv1beta1Ingress, err := ConvertNetworkingV1IngressToExtensionsV1beta1(newIngress)
			if err != nil {
				log.Errorf(nil, "ConvertNetworkingV1IngressToExtensionsV1beta1 fail: %+v", err)
				return
			}

			handler.OnUpdate(oldExtensionsv1beta1Ingress, newExtensionsv1beta1Ingress)
		}

		newHandler := cache.ResourceEventHandlerFuncs{
			AddFunc:    addIngressCompatiable,
			UpdateFunc: updateIngressCompatiable,
		}

		store.networkingV1Informer.Informer().AddEventHandlerWithResyncPeriod(newHandler, resyncPeriod)
	}
}

func (store *IngressStore) IngressInformerSynced() cache.InformerSynced {
	return store.ingressListerSynced
}

func (store *IngressStore) GetIngressByLister(namespace string, name string) (*v1beta1.Ingress, error) {
	if store.groupType == IngressGroupTypeExtensionsV1beta1 {
		return store.extensionsV1beta1Lister.Ingresses(namespace).Get(name)
	}

	networkingV1Ingress, err := store.networkingV1Lister.Ingresses(namespace).Get(name)
	if err != nil {
		return nil, err
	}
	return ConvertNetworkingV1IngressToExtensionsV1beta1(networkingV1Ingress)
}

func (store *IngressStore) ListAllIngressByLister() ([]*v1beta1.Ingress, error) {
	if store.groupType == IngressGroupTypeExtensionsV1beta1 {
		return store.extensionsV1beta1Lister.List(labels.Everything())
	}

	networkingV1Ingresses, err := store.networkingV1Lister.List(labels.Everything())
	if err != nil {
		return nil, err
	}
	result := make([]*v1beta1.Ingress, 0)
	for _, networkingV1Ingress := range networkingV1Ingresses {
		extensionsv1Ingress, err := ConvertNetworkingV1IngressToExtensionsV1beta1(networkingV1Ingress)
		if err != nil {
			return nil, err
		}
		result = append(result, extensionsv1Ingress)
	}
	return result, nil
}

func (store *IngressStore) ListIngressByLister(namespace string) ([]*v1beta1.Ingress, error) {
	if store.groupType == IngressGroupTypeExtensionsV1beta1 {
		return store.extensionsV1beta1Lister.Ingresses(namespace).List(labels.Everything())
	}

	networkingV1Ingresses, err := store.networkingV1Lister.Ingresses(namespace).List(labels.Everything())
	if err != nil {
		return nil, err
	}
	result := make([]*v1beta1.Ingress, 0)
	for _, networkingV1Ingress := range networkingV1Ingresses {
		extensionsv1Ingress, err := ConvertNetworkingV1IngressToExtensionsV1beta1(networkingV1Ingress)
		if err != nil {
			return nil, err
		}
		result = append(result, extensionsv1Ingress)
	}
	return result, nil
}

func ConvertExtensionsV1beta1ToNetworkingV1(ingress *v1beta1.Ingress) (*networkingv1.Ingress, error) {
	networkingIngress := &networking.Ingress{}
	if err := apisextensionsv1beta1.Convert_v1beta1_Ingress_To_networking_Ingress(ingress, networkingIngress, nil); err != nil {
		return nil, err
	}

	networkingV1Ingress := &networkingv1.Ingress{}
	if err := networkingv1api.Convert_networking_Ingress_To_v1_Ingress(networkingIngress, networkingV1Ingress, nil); err != nil {
		return nil, err
	}

	return networkingV1Ingress, nil
}

func ConvertNetworkingV1IngressToExtensionsV1beta1(ingress *networkingv1.Ingress) (*v1beta1.Ingress, error) {
	networkingIngress := &networking.Ingress{}
	if err := networkingv1api.Convert_v1_Ingress_To_networking_Ingress(ingress, networkingIngress, nil); err != nil {
		return nil, err
	}

	extensionsV1beta1Ingress := &v1beta1.Ingress{}
	if err := apisextensionsv1beta1.Convert_networking_Ingress_To_v1beta1_Ingress(networkingIngress, extensionsV1beta1Ingress, nil); err != nil {
		return nil, err
	}

	return extensionsV1beta1Ingress, nil
}
