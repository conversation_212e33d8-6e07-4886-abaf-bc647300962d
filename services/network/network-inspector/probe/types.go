// Copyright 2019 Baidu Inc. All rights reserved.
// Use of this source code is governed by a xxx
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2019/08/06 21:46:25, by <EMAIL>, create
*/

package probe

import (
	"context"
	"database/sql/driver"
)

// Interface defines functions to communicates with another pods
type Interface interface {
	// Probe another endpoint
	Probe(ctx context.Context, ip string, port int32) (any, error)

	// Save link & result into persistent storage
	Save(ctx context.Context, link *Link, result any) error
}

// Link defines the Source and Destination
type Link struct {
	// BatchID to identify the batch of this probe
	BatchID string `json:"batchID"`

	SourceType   SourceType `json:"sourceType"`
	SourcePodIP  string     `json:"sourcePodIP"`
	SourceNodeIP string     `json:"sourceNodeIP"`

	DestinationType DestinationType `json:"destinationType"`
	DestinationIP   string          `json:"destinationIP"`
	DestinationPort int32           `json:"destinationPort"`
}

// SourceType pod or node
type SourceType string

func (u *SourceType) Scan(value any) error { *u = SourceType(value.([]byte)); return nil }

func (u SourceType) Value() (driver.Value, error) { return string(u), nil }

const (
	// SourceTypeVPCPod = source is vpc pod
	SourceTypeVPCPod SourceType = "vpc_pod"

	// SourceTypeENIInVPCPod = source is eni_in_vpc pod
	SourceTypeENIInVPCPod SourceType = "eni_in_vpc_pod"

	// SourceTypeENIPod = source is eni pod
	SourceTypeENIPod SourceType = "eni_pod"

	// SourceTypeNode = source is hostnetwork pod
	SourceTypeNode SourceType = "node"

	// SourceTypeUnknown = source is unknown
	SourceTypeUnknown SourceType = "unknown"
)

// DestinationType destination type
type DestinationType string

func (u *DestinationType) Scan(value any) error {
	*u = DestinationType(value.([]byte))
	return nil
}

func (u DestinationType) Value() (driver.Value, error) { return string(u), nil }

const (
	// DestinationTypeVPCPod = desination is vpc pod
	DestinationTypeVPCPod DestinationType = "vpc_pod"

	// DestinationTypeENIInVPCPod = desination is eni_in_vpc pod
	DestinationTypeENIInVPCPod DestinationType = "eni_in_vpc_pod"

	// DestinationTypeENIPod = desination is eni pod
	DestinationTypeENIPod DestinationType = "eni_pod"

	// DestinationTypeNode = destination is host-network pod
	DestinationTypeNode DestinationType = "node"

	// DestinationTypeClusterIPService = destination is ClusterIP service
	DestinationTypeClusterIPService DestinationType = "clusterip_service"

	// DestinationTypeNodePortService = destination is NodePort service
	DestinationTypeNodePortService DestinationType = "nodeport_service"

	// DestinationTypeLBService = destination is LoadBalancer service
	DestinationTypeLBService DestinationType = "lb_service"

	// DestinationTypeServiceName = destination is service name
	DestinationTypeServiceName DestinationType = "service_name"

	// DestinationTypeExternalURL = destination is external url
	DestinationTypeExternalURL DestinationType = "external_url"
)
