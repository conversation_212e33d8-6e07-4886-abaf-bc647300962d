// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2020/07/18 12:58:00, by ch<PERSON><PERSON><PERSON>@baidu.com, create
*/
/*
result 用于从数据库读取数据, 并写入 ConfigMap 方便查询回归结果
*/

package result

import (
	"icode.baidu.com/baidu/cce-test/e2e-test/services/network/network-inspector/probe"
)

// Result - 检查聚合结果
type Result struct {
	SourceType      probe.SourceType      `json:"source_type" gorm:"column:source_type"`
	DestinationType probe.DestinationType `json:"destination_type" gorm:"column:destination_type"`
	IsSuccess       bool                  `json:"is_success" gorm:"column:is_success"`
	Count           int                   `json:"count" gorm:"column:count"`
}
