// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2020/07/18 12:58:00, by <EMAIL>, create
*/
/*
result 用于从数据库读取数据, 并写入 ConfigMap 方便查询回归结果
*/

package result

const (
	// DefaultNamespace - 存储结果 configmap 所在 namespace
	DefaultNamespace = "cce-network"

	// DefaultConfigMapName - 存储结果 configmap 名
	DefaultConfigMapName = "cce-inspect-result"

	// ResultKey - Configmap 中存储结果 Key
	ResultKey = "result"
)

// Config - 定义 client 依赖配置
type Config struct {
	// inspector 存储结果数据库链接
	MySQLEndpoint string

	// 集群中存储结果 ConfigMap
	KubeConfig string
}
