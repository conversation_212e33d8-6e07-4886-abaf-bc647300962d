/**
 * @Author: pansiyuan02
 * @Description:
 * @File:  appblb_listener
 * @Version: 1.0.0
 * @Date: 2020/9/15 4:19 下午
 */
package appblb

import (
	"context"
	"fmt"
	"strings"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/appblb"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	"icode.baidu.com/baidu/cce-test/e2e-test/services/network/service-controller/pkg/cloud"
)

const (
	TCPProtocol = "TCP"
	UDPProtocol = "UDP"
)

type PortListener struct {
	Protocol string
	Port     int
}

func (suit *appblbSuit) ReconcileListener(ctx context.Context, listeners []PortListener, appBLBID string) error {
	log := logger.WithValues("method", "appblbSuit.ReconcileListener").
		WithValues("appBLBID", appBLBID)

	log.Infof(ctx, "begin reconcile blb listener")
	expected := make(map[string]PortListener)
	for _, listener := range listeners {
		expected[listenerKeyFunc(listener)] = listener
	}
	log.Infof(ctx, "expected listeners: %v", expected)

	allListeners, err := suit.getAllListeners(ctx, appBLBID)
	if err != nil {
		return err
	}
	var listenersToDelete []PortListener
	for _, l := range allListeners {
		if _, found := expected[listenerKeyFunc(l)]; !found {
			listenersToDelete = append(listenersToDelete, l)
		} else {
			delete(expected, listenerKeyFunc(l))
		}
	}

	log.Infof(ctx, "listeners to be deleted: %v", listenersToDelete)
	if len(listenersToDelete) > 0 {
		err = suit.deleteListeners(ctx, appBLBID, listenersToDelete)
		if err != nil {
			log.Errorf(ctx, "failed to delete listener %v for service, err: %v", listenersToDelete, err)
			return fmt.Errorf("failed to delete listener %v for service, err: %v", listenersToDelete, err)
		}
	}

	log.Infof(ctx, "listeners to be added: %v", expected)
	for _, portListener := range expected {
		err = suit.createListener(ctx, appBLBID, portListener)
		if err != nil {
			log.Errorf(ctx, "failed to create listener %v for service, err: %v", portListener, err)
			return fmt.Errorf("failed to create listener %v for service, err: %v", portListener, err)
		}
	}

	return nil
}

func (suit *appblbSuit) getAllListeners(ctx context.Context, appBLBID string) ([]PortListener, error) {
	log := logger.WithValues("method", "appblbSuit.getAllListeners").
		WithValues("appBLBID", appBLBID)
	result := make([]PortListener, 0)
	signOption, err := cloud.GetSignFromSecret(ctx, suit.cloud, suit.k8sClient)
	if err != nil {
		log.Errorf(ctx, "failed to create sign option from volume, err: %v", err)
		return nil, err
	}
	tcpListenerResp, err := suit.cloud.ClientSet.AppBLBClient.DescribeAppTCPListener(ctx, appBLBID, 0, signOption)
	if err != nil {
		log.Errorf(ctx, "failed to list tcp listener, err: %v", err)
		return nil, err
	}
	for _, lis := range tcpListenerResp.ListenerList {
		result = append(result, PortListener{
			Protocol: TCPProtocol,
			Port:     lis.ListenerPort,
		})
	}
	signOption, err = cloud.GetSignFromSecret(ctx, suit.cloud, suit.k8sClient)
	if err != nil {
		log.Errorf(ctx, "failed to create sign option from volume, err: %v", err)
		return nil, err
	}
	udpListenerResp, err := suit.cloud.ClientSet.AppBLBClient.DescribeAppUDPListener(ctx, appBLBID, 0, signOption)
	if err != nil {
		log.Errorf(ctx, "failed to list udp listener, err: %v", err)
		return nil, err
	}
	for _, lis := range udpListenerResp.ListenerList {
		result = append(result, PortListener{
			Protocol: UDPProtocol,
			Port:     lis.ListenerPort,
		})
	}
	return result, nil
}

func (suit *appblbSuit) deleteListeners(ctx context.Context, appBLBID string, listeners []PortListener) error {
	log := logger.WithValues("method", "appblbSuit.deleteListeners").
		WithValues("appBLBID", appBLBID)
	args := &appblb.DeleteAppListenersArgs{
		PortList: make([]int, 0),
	}
	for _, pl := range listeners {
		args.PortList = append(args.PortList, pl.Port)
	}
	signOption, err := cloud.GetSignFromSecret(ctx, suit.cloud, suit.k8sClient)
	if err != nil {
		log.Errorf(ctx, "failed to create sign option from volume, err: %v", err)
		return err
	}
	err = suit.cloud.ClientSet.AppBLBClient.DeleteAppListeners(ctx, appBLBID, args, signOption)
	if err != nil {
		log.Errorf(ctx, "failed to delete listener, err: %v", err)
		return err
	}
	return nil
}

func (suit *appblbSuit) createListener(ctx context.Context, appBLBID string, listener PortListener) error {
	log := logger.WithValues("method", "appblbSuit.createListener").
		WithValues("appBLBID", appBLBID)

	signOption, err := cloud.GetSignFromSecret(ctx, suit.cloud, suit.k8sClient)
	if err != nil {
		log.Errorf(ctx, "failed to create sign option from volume, err: %v", err)
		return err
	}

	switch listener.Protocol {
	case TCPProtocol:
		args := &appblb.CreateAppTCPListenerArgs{
			ListenerPort: listener.Port,
			Scheduler:    appblb.SchedulerTypeRoundRobin,
		}
		return suit.cloud.ClientSet.AppBLBClient.CreateAppTCPListener(ctx, appBLBID, args, signOption)
	case UDPProtocol:
		args := &appblb.CreateAppUDPListenerArgs{
			ListenerPort: listener.Port,
			Scheduler:    appblb.SchedulerTypeRoundRobin,
		}
		return suit.cloud.ClientSet.AppBLBClient.CreateAppUDPListener(ctx, appBLBID, args, signOption)
	default:
		return fmt.Errorf("unsupported Protocol: %s", listener.Protocol)
	}
}

func listenerKeyFunc(port PortListener) string {
	return fmt.Sprintf("%s-%v", strings.ToUpper(port.Protocol), port.Port)
}
