/**
 * @Author: pansiyuan02
 * @Description:
 * @File:  appblb_eip
 * @Version: 1.0.0
 * @Date: 2020/9/16 4:48 下午
 */
package appblb

import (
	"context"
	"errors"
	"fmt"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/appblb"
	eipsdk "icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/eip"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	"icode.baidu.com/baidu/cce-test/e2e-test/services/network/service-controller/pkg/cloud"
)

const (
	// DefaultBandWidthInMBPS EIP default Band width In Mbps
	DefaultBandWidthInMBPS = 100

	// EIPBillingPaymentTiming default eip billing payment timing
	EIPBillingPaymentTiming = "Postpaid"

	// EIPBillingMethod default eip billing method
	EIPBillingMethod = "ByTraffic"
)

func (suit *appblbSuit) GetEIPByIPOrName(ctx context.Context, ip, name string) (*eipsdk.EIP, error) {
	log := logger.WithValues("method", "appblbSuit.GetEIPByIPOrName").
		WithValues("ip", ip).
		WithValues("name", name)

	if ip == "" && name == "" {
		return nil, errors.New("eip ip and name are not set")
	}

	log.Infof(ctx, "list eips from cloud")
	signOpt, err := cloud.GetSignFromSecret(ctx, suit.cloud, suit.k8sClient)
	if err != nil {
		return nil, err
	}
	eips, err := suit.cloud.ClientSet.EIPClient.GetEIPs(ctx, &eipsdk.GetEIPsArgs{EIP: ip}, signOpt)
	if err != nil {
		log.Errorf(ctx, "failed to list eips from cloud, err: %v", err)
		return nil, err
	}

	if ip != "" {
		return getOneEIPOrFailed(eips)
	}

	// ip == "" && name != ""
	var nameMatchedEIPs []*eipsdk.EIP
	for _, eip := range eips {
		if eip.Name == name {
			nameMatchedEIPs = append(nameMatchedEIPs, eip)
		}
	}
	return getOneEIPOrFailed(nameMatchedEIPs)
}

func (suit *appblbSuit) CreateEIP(ctx context.Context, name string, eipBandwidth int) (*eipsdk.EIP, error) {
	log := logger.WithValues("method", "appblbSuit.CreateEIP").
		WithValues("name", name)

	createEipArgs := &eipsdk.CreateEIPArgs{
		Billing: &eipsdk.Billing{
			PaymentTiming: EIPBillingPaymentTiming,
			BillingMethod: EIPBillingMethod,
		},
		Name:            name,
		BandwidthInMbps: eipBandwidth,
	}

	log.Infof(ctx, "create eip for service, args is %v", createEipArgs)
	signOption, err := cloud.GetSignFromSecret(ctx, suit.cloud, suit.k8sClient)
	if err != nil {
		log.Errorf(ctx, "failed to create sign option from volume, err: %v", err)
		return nil, err
	}
	eip, err := suit.cloud.ClientSet.EIPClient.CreateEIP(ctx, createEipArgs, signOption)
	if err != nil {
		log.Errorf(ctx, "failed to create eip, err: %v", err)
		return nil, err
	}

	return &eipsdk.EIP{
		Name:            name,
		EIP:             eip,
		BandwidthInMbps: eipBandwidth,
		PaymentTiming:   EIPBillingPaymentTiming,
		BillingMethod:   EIPBillingMethod,
	}, nil
}

func (suit *appblbSuit) DeleteEIP(ctx context.Context, eip *eipsdk.EIP) error {
	log := logger.WithValues("method", "appblbSuit.DeleteEIP").
		WithValues("eip", eip.EIP)

	signOption, err := cloud.GetSignFromSecret(ctx, suit.cloud, suit.k8sClient)
	if err != nil {
		log.Errorf(ctx, "failed to create sign option from volume, err: %v", err)
		return err
	}

	if err = suit.cloud.ClientSet.EIPClient.DeleteEIP(ctx, eip.EIP, signOption); err != nil {
		log.Errorf(ctx, "failed to delete eip, err: %v", err)
		return err
	}

	return nil
}

func (suit *appblbSuit) SetEIPBandWidthIfNotMatched(ctx context.Context, eip *eipsdk.EIP, bandWidth int) error {
	log := logger.WithValues("method", "appblbSuit.SetEIPBandWidthIfNotMatched").
		WithValues("eip", eip.EIP)

	if eip.BandwidthInMbps != bandWidth {
		signOption, err := cloud.GetSignFromSecret(ctx, suit.cloud, suit.k8sClient)
		if err != nil {
			log.Errorf(ctx, "failed to create sign option from volume, err: %v", err)
			return err
		}
		err = suit.cloud.ClientSet.EIPClient.ResizeEIP(ctx, eip.EIP, &eipsdk.ResizeEIPArgs{BandwidthInMbps: bandWidth}, signOption)
		if err != nil {
			log.WithValues("bandWidth", bandWidth).Errorf(ctx, "failed to resize bandwidth of eip, err: %v", err)
			return err
		}
	}
	eip.BandwidthInMbps = bandWidth
	return nil
}

func (suit *appblbSuit) BindEIPToAPPBLB(ctx context.Context, eip *eipsdk.EIP, appBLB *appblb.AppLoadBalancer) error {
	log := logger.WithValues("method", "appblbSuit.BindEIPToAPPBLB").
		WithValues("eip", eip.EIP).
		WithValues("appBLB", appBLB.BLBID)

	log.Infof(ctx, "eip status: %s", eip.Status)
	if eip.Status == eipsdk.EIPBinded {
		if eip.InstanceType == eipsdk.BLB && eip.InstanceID == appBLB.BLBID {
			log.Infof(ctx, "eip had been bind with appBLB, skip binding")
			return nil
		}

		msg := fmt.Sprintf("eip had been bind with other instance, type=%s id=%s", eip.InstanceType, eip.InstanceID)
		log.Errorf(ctx, msg)
		return fmt.Errorf(msg)
	}

	args := &eipsdk.BindEIPArgs{
		InstanceID:   appBLB.BLBID,
		InstanceType: eipsdk.BLB,
	}

	log.Infof(ctx, "binding EIP with args: %v", args)
	signOption, err := cloud.GetSignFromSecret(ctx, suit.cloud, suit.k8sClient)
	if err != nil {
		log.Errorf(ctx, "failed to create sign option from volume, err: %v", err)
		return err
	}
	if err := suit.cloud.ClientSet.EIPClient.BindEIP(ctx, eip.EIP, args, signOption); err != nil {
		log.Errorf(ctx, "failed to bind eip to appBLB, err: %v", err)
		return err
	}
	return nil
}

func (suit *appblbSuit) UnbindEIPFromAPPBLB(ctx context.Context, eip *eipsdk.EIP, appBLB *appblb.AppLoadBalancer) error {
	log := logger.WithValues("method", "appblbSuit.UnbindEIPFromAPPBLB").
		WithValues("eip", eip.EIP).
		WithValues("appBLB", appBLB.BLBID)

	log.Infof(ctx, "eip status: %s", eip.Status)
	if eip.Status == eipsdk.EIPAvailable {
		log.Infof(ctx, "eip is already unbind")
		return nil
	}

	if eip.InstanceType != eipsdk.BLB || eip.InstanceID != appBLB.BLBID {
		log.Errorf(ctx, "can't unbind eip which bind with other instance, type: %s, id: %s", eip.InstanceType, eip.InstanceID)
		return fmt.Errorf("can't unbind eip which bind with other instance, type: %s, id: %s", eip.InstanceType, eip.InstanceID)
	}

	log.Infof(ctx, "unbind eip from appBLB")
	signOption, err := cloud.GetSignFromSecret(ctx, suit.cloud, suit.k8sClient)
	if err != nil {
		log.Errorf(ctx, "failed to create sign option from volume, err: %v", err)
		return err
	}
	if err = suit.cloud.ClientSet.EIPClient.UnbindEIP(ctx, eip.EIP, signOption); err != nil {
		log.Errorf(ctx, "failed to unbind eip, err: %v", err)
		return err
	}
	return nil
}

func getOneEIPOrFailed(eips []*eipsdk.EIP) (*eipsdk.EIP, error) {
	switch len(eips) {
	case 0:
		return nil, cloud.ErrNotExist
	case 1:
		return eips[0], nil
	default:
		return nil, errors.New("multiplie eip found")
	}
}
