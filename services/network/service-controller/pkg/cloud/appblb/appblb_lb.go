/**
 * @Author: pansiyuan02
 * @Description:
 * @File:  appblb_lb
 * @Version: 1.0.0
 * @Date: 2020/9/15 4:19 下午
 */
package appblb

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/appblb"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	cloud "icode.baidu.com/baidu/cce-test/e2e-test/services/network/service-controller/pkg/cloud"
)

const (
	MaxBLBNameLength = 65
)

func (suit *appblbSuit) GetAPPBLBByIDOrName(ctx context.Context, id, name string) (*appblb.AppLoadBalancer, error) {
	log := logger.WithValues("method", "appblbSuit.GetAPPBLBByIDOrName").
		WithValues("id", id).
		WithValues("name", name)

	log.Infof(ctx, "try to get appBLB")

	if id == "" && name == "" {
		return nil, errors.New("appblb id and name are not set")
	}

	signOpt, err := cloud.GetSignFromSecret(ctx, suit.cloud, suit.k8sClient)
	if err != nil {
		return nil, err
	}

	if id != "" {
		appBLB, err := suit.cloud.ClientSet.AppBLBClient.DescribeAppLoadBalancerByID(ctx, id, signOpt)
		if err != nil {
			return nil, err
		}
		return appBLB, nil
	}

	// id == "" && name != ""
	appBLBs, err := suit.cloud.ClientSet.AppBLBClient.DescribeAppLoadBalancersByName(ctx, name, signOpt)
	if err != nil {
		return nil, err
	}
	if len(appBLBs) == 0 {
		return nil, cloud.ErrNotExist
	}
	if len(appBLBs) > 1 {
		return nil, errors.New("multiplie appBLB found")
	}

	return appBLBs[0], nil
}

// CreateAPPBLB - CreateAPPBLB create appblb, return appblb and error
func (suit *appblbSuit) CreateAPPBLB(ctx context.Context, name, vpcSubnetID string) (*appblb.AppLoadBalancer, error) {
	log := logger.WithValues("method", "appblbSuit.CreateAPPBLB").
		WithValues("name", name)
	log.Infof(ctx, "going to create appblb")

	if err := verifyBLBName(name); err != nil {
		return nil, err
	}

	signOpt, err := cloud.GetSignFromSecret(ctx, suit.cloud, suit.k8sClient)
	if err != nil {
		log.Errorf(ctx, "failed to create sign option from volume, err: %v", err)
		return nil, err
	}

	clusterResp, err := suit.cloud.ClientSet.CCEV2Client.GetCluster(ctx, suit.cloud.CloudConfig.ClusterID, signOpt)
	if err != nil {
		log.WithValues("clusterID", suit.cloud.CloudConfig.ClusterID).Errorf(ctx, "failed to get cluster info, err: %v", err)
		return nil, err
	}

	vpcID := clusterResp.Cluster.Spec.VPCID
	if vpcSubnetID == "" {
		vpcSubnetID = clusterResp.Cluster.Spec.ContainerNetworkConfig.LBServiceVPCSubnetID
	}

	// Create App BLB
	createAPPBLBArgs := &appblb.CreateAppLoadBalancerArgs{
		Name:        name,
		Desc:        fmt.Sprintf("Handler by CCE %v", name),
		VPCID:       vpcID,
		SubNetID:    vpcSubnetID,
		AllowDelete: true,
	}

	argsStr, _ := json.Marshal(createAPPBLBArgs)
	log.Infof(ctx, "create BLB with args: %s", string(argsStr))

	signOpt, err = cloud.GetSignFromSecret(ctx, suit.cloud, suit.k8sClient)
	if err != nil {
		log.Errorf(ctx, "failed to create sign option from volume, err: %v", err)
		return nil, err
	}
	createAPPBLBResp, err := suit.cloud.ClientSet.AppBLBClient.CreateAppLoadBalancer(ctx, createAPPBLBArgs, signOpt)
	if err != nil || createAPPBLBResp == nil {
		log.Errorf(ctx, "failed to create appblb, err: %s", err)
		return nil, err
	}

	log.WithValues("appBLBID", createAPPBLBResp.BLBID).Infof(ctx, "appblb created")
	return &appblb.AppLoadBalancer{
		BLBID:   createAPPBLBResp.BLBID,
		Name:    createAPPBLBResp.Name,
		Desc:    createAPPBLBResp.Desc,
		Address: createAPPBLBResp.Address,
	}, nil
}

func (suit *appblbSuit) DeleteAPPBLB(ctx context.Context, id string) error {
	log := logger.WithValues("method", "appblbSuit.DeleteAPPBLB").
		WithValues("id", id)

	signOpt, err := cloud.GetSignFromSecret(ctx, suit.cloud, suit.k8sClient)
	if err != nil {
		log.Errorf(ctx, "failed to create sign option from volume, err: %v", err)
		return err
	}

	if err := suit.cloud.ClientSet.AppBLBClient.DeleteAppLoadBalancer(ctx, id, signOpt); err != nil {
		log.Errorf(ctx, "failed to delete appBLB, err: %v", err)
		return err
	}
	return nil
}

func verifyBLBName(name string) error {
	if name == "" || len(name) > MaxBLBNameLength {
		return fmt.Errorf("invalid APPBLB name: %s", name)
	}
	return nil
}
