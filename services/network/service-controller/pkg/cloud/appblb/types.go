/**
 * @Author: pansiyuan02
 * @Description:
 * @File:  types
 * @Version: 1.0.0
 * @Date: 2020/9/22 3:39 下午
 */
package appblb

import (
	"context"

	"sigs.k8s.io/controller-runtime/pkg/client"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/appblb"
	eipsdk "icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/eip"
	"icode.baidu.com/baidu/cce-test/e2e-test/services/network/service-controller/pkg/cloud"
)

//go:generate mockgen -destination=./mock/mock_appblb.go -package=mock icode.baidu.com/baidu/cce-test/e2e-test/services/network/service-controller/pkg/cloud/appblb Interface

type Interface interface {
	GetAPPBLBByIDOrName(ctx context.Context, id, name string) (*appblb.AppLoadBalancer, error)
	CreateAPPBLB(ctx context.Context, name, vpcSubnetID string) (*appblb.AppLoadBalancer, error)
	DeleteAPPBLB(ctx context.Context, id string) error
	ReconcileListener(ctx context.Context, listeners []PortListener, appBLBID string) error
	ReconcileBackends(ctx context.Context, expectedVGroups []*VGroup, appBLBID string, backends *APPBLBBackend) error

	GetEIPByIPOrName(ctx context.Context, ip, name string) (*eipsdk.EIP, error)
	CreateEIP(ctx context.Context, name string, bandWidth int) (*eipsdk.EIP, error)
	DeleteEIP(ctx context.Context, eip *eipsdk.EIP) error
	SetEIPBandWidthIfNotMatched(ctx context.Context, eip *eipsdk.EIP, bandWidth int) error
	BindEIPToAPPBLB(ctx context.Context, eip *eipsdk.EIP, appBLB *appblb.AppLoadBalancer) error
	UnbindEIPFromAPPBLB(ctx context.Context, eip *eipsdk.EIP, appBLB *appblb.AppLoadBalancer) error
}

type appblbSuit struct {
	cloud     *cloud.Cloud
	k8sClient client.Client
}

func NewAPPBLBSuit(cloud *cloud.Cloud, k8sClient client.Client) Interface {
	return &appblbSuit{
		cloud:     cloud,
		k8sClient: k8sClient,
	}
}
