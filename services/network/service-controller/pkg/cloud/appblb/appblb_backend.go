/**
 * @Author: pansiyuan02
 * @Description:
 * @File:  appblb_backend
 * @Version: 1.0.0
 * @Date: 2020/9/15 4:19 下午
 */
package appblb

import (
	"context"
	"fmt"
	"strings"

	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/util/intstr"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/appblb"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	"icode.baidu.com/baidu/cce-test/e2e-test/services/network/service-controller/pkg/cloud"
)

const (
	DefaultUDPHealthCheckString = "HealthCheck"
)

// APPBLBBackend
// Currently, APPBLBBackend accept two kind of backend
// normal nodes of type []*coreV1.Node, and endpoints of type *coreV1.Endpoints
type APPBLBBackend struct {
	// LocalMode externalTraffic=Local
	LocalMode bool

	// BackendTypeIPGroup
	// whether it is an IP group backend
	BackendTypeIPGroup bool

	// Nodes
	// contains all the candidate nodes consider of LoadBalance Backends.
	// Cloud implementation has the right to make any filter on it.
	Nodes []*corev1.Node

	// Endpoints
	// It is the direct pod location information which cloud implementation
	// may needed for some kind of filtering. eg. direct ENI attach.
	Endpoints *corev1.Endpoints
}

type VGroup struct {
	Port                 intstr.IntOrString
	NodePort             intstr.IntOrString
	TargetPort           intstr.IntOrString
	Protocol             string
	Name                 string
	UDPHealthCheckString string

	BLBID     string
	IPGroupID string
}

func (suit *appblbSuit) ReconcileBackends(ctx context.Context, expectedVGroups []*VGroup, appBLBID string, backends *APPBLBBackend) error {
	log := logger.WithValues("method", "appblbSuit.ReconcileBackends").
		WithValues("appBLBID", appBLBID)

	vGroupsFromCloud, err := suit.getVGroupsFromCloud(ctx, appBLBID)
	if err != nil {
		log.Errorf(ctx, "failed to get vGroups from cloud, err: %v", err)
		return err
	}

	toBeAddeds, toBeDeleteds := gapBetweenExpectedAndActualVGroup(expectedVGroups, vGroupsFromCloud)
	// 创建和配置新的ipGroup一定要放在删除旧的ipGroup的前面，估计是blb那边有什么限制，如果先删再配置新的，删除的时候会报错。
	for _, vg := range toBeAddeds {
		if err := suit.createIPGroup(ctx, vg); err != nil {
			log.WithValues("IPGroupName", vg.Name).Errorf(ctx, "failed to create IPGroup, err: %v", err)
			return err
		}
	}

	for _, vg := range expectedVGroups {
		if err := suit.reconcileIPGroup(ctx, vg, backends); err != nil {
			log.WithValues("IPGroupName", vg.Name).Errorf(ctx, "failed to reconcile IPGroup, err: %v", err)
			return err
		}
	}

	for _, vg := range toBeDeleteds {
		if err := suit.deleteIPGroup(ctx, vg); err != nil {
			log.WithValues("IPGroupID", vg.IPGroupID).Errorf(ctx, "failed to delete IPGroup, err: %v", err)
			return err
		}
	}

	return nil
}

func (suit *appblbSuit) getVGroupsFromCloud(ctx context.Context, appBLBID string) ([]*VGroup, error) {
	log := logger.WithValues("method", "appblbSuit.getVGroupFromCloud").
		WithValues("appBLBID", appBLBID)
	signOption, err := cloud.GetSignFromSecret(ctx, suit.cloud, suit.k8sClient)
	if err != nil {
		log.Errorf(ctx, "failed to create sign option from volume, err: %v", err)
		return nil, err
	}
	resp, err := suit.cloud.ClientSet.AppBLBClient.ListIPGroups(ctx, appBLBID, nil, signOption)
	if err != nil {
		log.Errorf(ctx, "failed to list ipGroup, err: %v", err)
		return nil, err
	}
	vgs := make([]*VGroup, 0)
	for _, ipg := range resp.AppIPGroupList {
		vgs = append(vgs, &VGroup{
			Name:      ipg.Name,
			IPGroupID: ipg.ID,
			BLBID:     appBLBID,
		})
	}
	return vgs, nil
}

func (suit *appblbSuit) reconcileIPGroup(ctx context.Context, vg *VGroup, backends *APPBLBBackend) error {
	log := logger.WithValues("method", "appblbSuit.reconcileIPGroup").
		WithValues("IPGroupName", vg.Name).
		WithValues("IPGroupID", vg.IPGroupID)

	signOption, err := cloud.GetSignFromSecret(ctx, suit.cloud, suit.k8sClient)
	if err != nil {
		log.Errorf(ctx, "failed to create sign option from volume, err: %v", err)
		return err
	}
	resp, err := suit.cloud.ClientSet.AppBLBClient.ListIPGroups(ctx, vg.BLBID, &appblb.ListIPGroupArgs{Name: vg.Name, ExactlyMatch: true}, signOption)
	if err != nil {
		log.WithValues("appBLBID", vg.BLBID).Errorf(ctx, "failed to list IPGroups, err: %v", err)
		return err
	}

	if len(resp.AppIPGroupList) > 1 {
		return fmt.Errorf("multi ipgroup named %s found: %v", vg.Name, resp.AppIPGroupList)
	}
	if len(resp.AppIPGroupList) == 0 {
		return fmt.Errorf("ipgroup named %s not found", vg.Name)
	}

	if vg.IPGroupID == "" {
		vg.IPGroupID = resp.AppIPGroupList[0].ID
	}

	if err := suit.reconcileIPGroupBackendPolicy(ctx, vg, resp.AppIPGroupList[0]); err != nil {
		log.Errorf(ctx, "failed to reconcile IPGroup backend policy, err: %v", err)
		return err
	}

	if err := suit.reconcileBLBAPPPolicy(ctx, vg); err != nil {
		log.Errorf(ctx, "failed to reconcile blb app policy, err: %v", err)
		return err
	}

	if err := suit.reconcileIPGroupBackendMembers(ctx, vg, backends); err != nil {
		log.Errorf(ctx, "failed to reconcile IPGroup backend members, err: %v", err)
		return err
	}

	return nil
}

func (suit *appblbSuit) reconcileIPGroupBackendPolicy(ctx context.Context, vg *VGroup, ipGroup *appblb.AppIPGroup) error {
	log := logger.WithValues("method", "appblbSuit.reconcileIPGroupBackendPolicy").
		WithValues("appBLBID", vg.BLBID).
		WithValues("IPGroupName", ipGroup.Name).
		WithValues("IPGroupID", ipGroup.ID)

	if len(ipGroup.BackendPolicyList) == 0 {
		signOption, err := cloud.GetSignFromSecret(ctx, suit.cloud, suit.k8sClient)
		if err != nil {
			log.Errorf(ctx, "failed to create sign option from volume, err: %v", err)
			return err
		}
		args := &appblb.CreateIPGroupBackendPolicyArgs{
			IPGroupID:   ipGroup.ID,
			Type:        appblb.BackendPolicyType(vg.Protocol),
			HealthCheck: appblb.BackendPolicyHealthCheck(vg.Protocol),
		}
		if args.Type == appblb.BackendPolicyTypeUDP {
			args.UDPHealthCheckString = vg.UDPHealthCheckString
		}
		err = suit.cloud.ClientSet.AppBLBClient.CreateIPGroupBackendPolicy(ctx, vg.BLBID, args, signOption)
		if err != nil {
			log.WithValues("args", args).Errorf(ctx, "ensure backendPolicy for ipgroup failed, err: %v", err)
			return err
		}
	}
	return nil
}

func (suit *appblbSuit) reconcileBLBAPPPolicy(ctx context.Context, vg *VGroup) error {
	log := logger.WithValues("method", "appblbSuit.reconcileBLBAPPPolicy").
		WithValues("appBLBID", vg.BLBID).
		WithValues("IPGroupName", vg.Name).
		WithValues("IPGroupID", vg.IPGroupID)
	signOption, err := cloud.GetSignFromSecret(ctx, suit.cloud, suit.k8sClient)
	if err != nil {
		log.Errorf(ctx, "failed to create sign option from volume, err: %v", err)
		return err
	}
	resp, err := suit.cloud.ClientSet.AppBLBClient.DescribeAppPolicys(ctx, vg.BLBID, &appblb.DescribeAppPolicysArgs{Port: vg.Port.IntValue()}, signOption)
	if err != nil {
		log.Errorf(ctx, "failed to describe blb app policy, err: %v", err)
		return err
	}
	if len(resp.PolicyList) == 0 {
		pol := &appblb.AppPolicy{
			AppIPGroupID: vg.IPGroupID,
			PortType:     appblb.ServerGroupPortType(strings.ToUpper(string(vg.Protocol))),
			FrontendPort: int(vg.Port.IntValue()),
			BackendPort:  vg.TargetPort.IntValue(),
			Priority:     1,
			RuleList: []*appblb.AppRule{
				{
					Key:   appblb.AppRuleKeyTypeAll,
					Value: "*"},
			},
		}
		signOption, err := cloud.GetSignFromSecret(ctx, suit.cloud, suit.k8sClient)
		if err != nil {
			log.Errorf(ctx, "failed to create sign option from volume, err: %v", err)
			return err
		}
		argvs := &appblb.CreateAppPolicyArgs{
			ListenerPort:  vg.Port.IntValue(),
			AppPolicyList: []*appblb.AppPolicy{pol},
		}
		if err := suit.cloud.ClientSet.AppBLBClient.CreateAppPolicys(ctx, vg.BLBID, argvs, signOption); err != nil {
			log.WithValues("argvs", argvs).Errorf(ctx, "failed to create blb app policy, err: %v", err)
			return err
		}
	}
	return nil
}

func (suit *appblbSuit) reconcileIPGroupBackendMembers(ctx context.Context, vg *VGroup, backends *APPBLBBackend) error {
	log := logger.WithValues("method", "appblbSuit.reconcileIPGroupBackendMembers").
		WithValues("appBLBID", vg.BLBID).
		WithValues("IPGroupName", vg.Name).
		WithValues("IPGroupID", vg.IPGroupID)

	expected := suit.expectedIPGroupBackendMembers(vg, backends)
	actual, err := suit.getIPGroupBackendMembersFromCloud(ctx, vg)
	if err != nil {
		log.Errorf(ctx, "failed to get IPGroup backend members from cloud, err: %v", err)
		return err
	}

	toBeAdded, toBeDeleted := gapBetweenExpectedAndActualAPPIPGroupMember(expected, actual)
	log.Infof(ctx, "IPGroup member to be added are %v, member to be deleted are %v", toBeAdded, toBeDeleted)

	if len(toBeDeleted) > 0 {
		ids := make([]string, 0)
		for _, m := range toBeDeleted {
			ids = append(ids, m.MemberID)
		}
		signOption, err := cloud.GetSignFromSecret(ctx, suit.cloud, suit.k8sClient)
		if err != nil {
			log.Errorf(ctx, "failed to create sign option from volume, err: %v", err)
			return err
		}
		err = suit.cloud.ClientSet.AppBLBClient.DeleteIPGroupMember(ctx, vg.BLBID, &appblb.DeleteIPGroupMemberArgs{IPGroupID: vg.IPGroupID, MemberIDList: ids}, signOption)
		if err != nil {
			log.Errorf(ctx, "failed to delete appBLB IPGroup backend members, err: %v", err)
			return err
		}
	}

	if len(toBeAdded) > 0 {
		signOption, err := cloud.GetSignFromSecret(ctx, suit.cloud, suit.k8sClient)
		if err != nil {
			log.Errorf(ctx, "failed to create sign option from volume, err: %v", err)
			return err
		}
		err = suit.cloud.ClientSet.AppBLBClient.CreateIPGroupMember(ctx, vg.BLBID, &appblb.CreateIPGroupMemberArgs{IPGroupID: vg.IPGroupID, MemberList: toBeAdded}, signOption)
		if err != nil {
			log.Errorf(ctx, "failed to add appBLB IPGroup backend members, err: %v", err)
			return err
		}
	}

	return nil
}

func (suit *appblbSuit) expectedIPGroupBackendMembers(vg *VGroup, backends *APPBLBBackend) []*appblb.AppIPGroupMember {
	members := make([]*appblb.AppIPGroupMember, 0)

	// eni or kubernetes service
	if backends.BackendTypeIPGroup {
		if backends.Endpoints == nil {
			return members
		}
		for _, sub := range backends.Endpoints.Subsets {
			for _, addr := range sub.Addresses {
				mem := &appblb.AppIPGroupMember{
					IP:     addr.IP,
					Port:   vg.TargetPort,
					Weight: 100,
				}
				members = append(members, mem)
			}
		}
		return members
	}

	// add node Port
	for _, node := range backends.Nodes {
		if node.Labels["type"] == "virtual-kubelet" {
			continue
		}
		mem := &appblb.AppIPGroupMember{
			// TODO 尽管目前node.Status.Addresses[0]的地址确实是节点IP地址，但这样的方法是不妥的
			// 因为理论上讲Addresses[0]可能是Hostname类型，目前CCM将其赋值为IP地址，但未来或许会将其改成节点真正的HostName
			// 这里应该遍历Addresses找出真正IPv4地址后再赋值
			IP:     node.Status.Addresses[0].Address,
			Port:   vg.NodePort,
			Weight: 100,
		}
		members = append(members, mem)
	}

	// add bci
	if backends.Endpoints != nil {
		for _, sub := range backends.Endpoints.Subsets {
			for _, addr := range sub.Addresses {
				if addr.NodeName == nil {
					continue
				}
				for _, n := range backends.Nodes {
					if n.Name == *addr.NodeName && n.Labels["type"] == "virtual-kubelet" {
						mem := &appblb.AppIPGroupMember{
							IP:     addr.IP,
							Port:   vg.TargetPort,
							Weight: 100,
						}
						members = append(members, mem)
						break
					}
				}
			}
		}
	}
	return members
}

func (suit *appblbSuit) getIPGroupBackendMembersFromCloud(ctx context.Context, vg *VGroup) ([]*appblb.AppIPGroupMember, error) {
	log := logger.WithValues("method", "appblbSuit.getIPGroupBackendMembersFromCloud").
		WithValues("appBLBID", vg.BLBID).
		WithValues("IPGroupName", vg.Name).
		WithValues("IPGroupID", vg.IPGroupID)

	signOption, err := cloud.GetSignFromSecret(ctx, suit.cloud, suit.k8sClient)
	if err != nil {
		log.Errorf(ctx, "failed to create sign option from volume, err: %v", err)
		return nil, err
	}
	listIPGroupMembersResponse, err := suit.cloud.ClientSet.AppBLBClient.ListIPGroupMember(ctx, vg.BLBID, &appblb.ListIPGroupMemberArgs{IPGroupID: vg.IPGroupID}, signOption)
	if err != nil {
		log.Errorf(ctx, "failed to list appBLB IPGroup backend members, err: %v", err)
		return nil, err
	}

	return listIPGroupMembersResponse.MemberList, nil
}

func (suit *appblbSuit) createIPGroup(ctx context.Context, vg *VGroup) error {
	signOption, err := cloud.GetSignFromSecret(ctx, suit.cloud, suit.k8sClient)
	if err != nil {
		return err
	}
	resp, err := suit.cloud.ClientSet.AppBLBClient.CreateIPGroup(ctx, vg.BLBID, &appblb.CreateIPGroupArgs{Name: vg.Name}, signOption)
	if err != nil {
		return err
	}
	vg.IPGroupID = resp.ID
	return nil
}

func (suit *appblbSuit) deleteIPGroup(ctx context.Context, vg *VGroup) error {
	log := logger.WithValues("method", "appblbSuit.deleteIPGroup").
		WithValues("appBLBID", vg.BLBID).
		WithValues("IPGroupName", vg.Name).
		WithValues("IPGroupID", vg.IPGroupID)

	log.Infof(ctx, "start to delete ipGroup")
	signOption, err := cloud.GetSignFromSecret(ctx, suit.cloud, suit.k8sClient)
	if err != nil {
		return err
	}
	err = suit.cloud.ClientSet.AppBLBClient.DeleteIPGroup(ctx, vg.BLBID, &appblb.DeleteIPGroupArgs{IPGroupID: vg.IPGroupID}, signOption)
	if err != nil {
		return err
	}
	return nil
}

func gapBetweenExpectedAndActualVGroup(expected, actual []*VGroup) ([]*VGroup, []*VGroup) {
	expectedMap, actualMap := make(map[string]struct{}), make(map[string]struct{})
	for _, vg := range expected {
		expectedMap[vg.Name] = struct{}{}
	}
	for _, vg := range actual {
		actualMap[vg.Name] = struct{}{}
	}

	var toBeAdds, toBeDeleted []*VGroup
	for _, vg := range expected {
		if _, found := actualMap[vg.Name]; !found {
			toBeAdds = append(toBeAdds, vg)
		}
	}
	for _, vg := range actual {
		if _, found := expectedMap[vg.Name]; !found {
			toBeDeleted = append(toBeDeleted, vg)
		}
	}

	return toBeAdds, toBeDeleted
}

func gapBetweenExpectedAndActualAPPIPGroupMember(expected, actual []*appblb.AppIPGroupMember) ([]*appblb.AppIPGroupMember, []*appblb.AppIPGroupMember) {
	toBeDeleted := make([]*appblb.AppIPGroupMember, 0)
	toBeAdded := make([]*appblb.AppIPGroupMember, 0)

	actualMap := make(map[string]*appblb.AppIPGroupMember)
	expectedMap := make(map[string]*appblb.AppIPGroupMember)
	for _, mem := range actual {
		actualMap[fmt.Sprintf("%v-%v", mem.IP, mem.Port)] = mem
	}
	for _, mem := range expected {
		expectedMap[fmt.Sprintf("%v-%v", mem.IP, mem.Port)] = mem
	}

	for key, val := range actualMap {
		if _, exist := expectedMap[key]; !exist {
			toBeDeleted = append(toBeDeleted, val)
		}
	}

	for key, val := range expectedMap {
		if _, exist := actualMap[key]; !exist {
			toBeAdded = append(toBeAdded, val)
		}
	}
	return toBeAdded, toBeDeleted
}
