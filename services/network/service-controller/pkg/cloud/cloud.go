package cloud

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"os"
	"time"

	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/types"
	"sigs.k8s.io/controller-runtime/pkg/client"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/appblb"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/ccegateway"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/ccev2"
	eipsdk "icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/eip"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/vpc"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
)

const (
	// CCEUserAgent is prefix of http header UserAgent
	CCEUserAgent = "cce-k8s"

	// 开启 BCE SDK Debug 模式, e.g. SET_DEBUG: true 表示开启
	setDebug = "SET_DEBUG"

	// Endpoint 环境变量命名
	blbEndpointEnv         = "BLB_ENDPOINT"
	eipEndpointEnv         = "EIP_ENDPOINT"
	vpcEndpointEnv         = "VPC_ENDPOINT"
	cceEndpointEnv         = "CCE_ENDPOINT"
	cceV2EndpointEnv       = "CCEV2_ENDPOINT"
	internalBLBEndpointEnv = "INT_BLB_ENDPOINT"

	CCEGatewaySecretVolume = "/var/run/secrets/cce/cce-plugin-token"
)

var (
	ErrNotExist = errors.New("not exist")
)

// Cloud the implemention of BCE Interface
type Cloud struct {
	CloudConfig *CloudConfig
	ClientSet   *ClientSet
}

// CloudConfig read from /etc/kubernetes/cloud.config
type CloudConfig struct {
	Region          string `json:"Region"`
	ClusterID       string `json:"ClusterID"`
	ClusterName     string `json:"ClusterName"`
	AccessKeyID     string `json:"AccessKeyID"`
	SecretAccessKey string `json:"SecretAccessKey"`
	VpcID           string `json:"VpcID"`
	SubnetID        string `json:"SubnetID"`
	Endpoint        string `json:"Endpoint"`
	CCEV2Endpoint   string `json:"CCEV2Endpoint"`
	Debug           bool   `json:"Debug"`
}

// ClientSet contains all the bce product client
type ClientSet struct {
	AppBLBClient appblb.Interface
	EIPClient    eipsdk.Interface
	VPCClient    vpc.Interface
	CCEV2Client  ccev2.Interface

	Helper ccegateway.Helper
}

// NewCloud return client implements bce.AppLoadBalancer interface
func NewCloud(cloudconfig string) (*Cloud, error) {
	ctx := context.Background()
	log := logger.WithValues("method", "NewCloud")

	if cloudconfig == "" {
		return nil, errors.New("no --cloudconfig was specified")
	}

	config := &CloudConfig{}

	// Read cloud config file
	data, err := os.ReadFile(cloudconfig)
	if err != nil {
		log.Errorf(ctx, "Read cloudconfig file %s failed: %s", cloudconfig, err)
		return nil, err
	}

	// Load cloud config
	if err := json.Unmarshal(data, config); err != nil {
		log.Errorf(ctx, "Load cloudconfig file %s failed: %s", cloudconfig, err)
		return nil, err
	}

	if config.Region == "" || config.ClusterID == "" || config.CCEV2Endpoint == "" {
		str, _ := json.Marshal(config)
		msg := fmt.Sprintf("CloudConfig miss params: %s", string(str))

		return nil, fmt.Errorf(msg)
	}

	clientSet, err := newClientSet(config)
	if err != nil {
		log.Errorf(ctx, "Create AppBLB clientset failed: %s", err)
		return nil, err
	}

	return &Cloud{
		CloudConfig: config,
		ClientSet:   clientSet,
	}, nil
}

func newClientSet(config *CloudConfig) (*ClientSet, error) {
	ctx := context.Background()
	log := logger.WithValues("method", "newClientSet")

	if config == nil {
		return nil, errors.New("newClientSet failed: config is nil")
	}

	clientset := &ClientSet{}

	// using cce-gateway
	helper := ccegateway.NewHelper(config.Region, config.ClusterID)
	proxyHost, proxyPort := helper.GetHostAndPort()
	clientset.Helper = helper

	// AppBLBClient
	blbEndpoint, isExist := os.LookupEnv(blbEndpointEnv)
	if !isExist {
		log.Infof(ctx, "Env BLB_ENDPOINT not set, using bce-go-sdk config")
		blbEndpoint = appblb.Endpoint[config.Region]
	}

	log.Infof(ctx, "BLB endpoint: %v", blbEndpoint)

	clientset.AppBLBClient = appblb.NewClient(&bce.Config{
		Credentials: bce.NewCredentials(config.AccessKeyID, config.SecretAccessKey),
		Checksum:    true,
		Timeout:     30 * time.Second,
		Region:      config.Region,
		ProxyHost:   proxyHost,
		ProxyPort:   proxyPort,
		// e.g. Endpoint: "blb.api-sandbox.baidu.com/v1",
		Endpoint: fmt.Sprintf("%s/v1", blbEndpoint),
	})

	// EIPClient
	eipEndpoint, isExist := os.LookupEnv(eipEndpointEnv)
	if !isExist {
		log.Infof(ctx, "Env EIP_ENDPOINT not set, using bce-go-sdk config")
		eipEndpoint = eipsdk.Endpoint[config.Region]
	}

	log.Infof(ctx, "EIP endpoint: %v", eipEndpoint)

	clientset.EIPClient = eipsdk.NewClient(&bce.Config{
		Credentials: bce.NewCredentials(config.AccessKeyID, config.SecretAccessKey),
		Checksum:    true,
		Timeout:     30 * time.Second,
		Region:      config.Region,
		ProxyHost:   proxyHost,
		ProxyPort:   proxyPort,
		// e.g. Endpoint: "eip.api-sandbox.baidu.com",
		Endpoint: eipEndpoint,
	})

	// CCEV2Client request CCEV2 API
	cceV2Endpoint, isExist := os.LookupEnv(cceV2EndpointEnv)
	if !isExist {
		log.Infof(ctx, "Env CCEV2_ENDPOINT not set, using bcesdk config")
		cceV2Endpoint = config.CCEV2Endpoint
	}

	log.Infof(ctx, "CCE V2 endpoint: %v", cceV2Endpoint)

	clientset.CCEV2Client = ccev2.NewClient(
		&bce.Config{
			Credentials: bce.NewCredentials(config.AccessKeyID, config.SecretAccessKey),
			Checksum:    true,
			Timeout:     30 * time.Second,
			Region:      config.Region,
			Endpoint:    cceV2Endpoint,
			ProxyHost:   proxyHost,
			ProxyPort:   proxyPort,
		},
	)

	// VPCClient
	vpcEndpoint, isExist := os.LookupEnv(vpcEndpointEnv)
	if !isExist {
		log.Infof(ctx, "Env VPC_ENDPOINT not set, using bce-go-sdk config")
		vpcEndpoint = vpc.Endpoints[config.Region]
	}

	log.Infof(ctx, "VPC endpoint: %v", vpcEndpoint)

	clientset.VPCClient = vpc.NewClient(&bce.Config{
		Credentials: bce.NewCredentials(config.AccessKeyID, config.SecretAccessKey),
		Checksum:    true,
		Timeout:     30 * time.Second,
		Region:      config.Region,
		ProxyHost:   proxyHost,
		ProxyPort:   proxyPort,
		// e.g. Endpoint: "bcc.bce-api.baidu.com",
		Endpoint: vpcEndpoint,
	})

	// Set Debug
	debug, isExist := os.LookupEnv(setDebug)
	if !isExist {
		log.Infof(ctx, "Env SET_DEBUG not set")
	}

	if debug == "true" {
		clientset.AppBLBClient.SetDebug(true)
		clientset.EIPClient.SetDebug(true)
		clientset.CCEV2Client.SetDebug(true)
		clientset.VPCClient.SetDebug(true)
	}

	return clientset, nil
}

func fetchSecretFunc(client client.Client) func(namespace, name string) (*corev1.Secret, error) {
	return func(namespace, name string) (*corev1.Secret, error) {
		var secret corev1.Secret
		if err := client.Get(context.Background(), types.NamespacedName{
			Namespace: namespace,
			Name:      name,
		}, &secret); err != nil {
			return nil, err
		}
		return &secret, nil
	}
}

func GetSignFromSecret(ctx context.Context, cloud *Cloud, k8sClient client.Client) (*bce.SignOption, error) {
	return cloud.ClientSet.Helper.NewSignOptionFromSecret(ctx, fetchSecretFunc(k8sClient))
}
