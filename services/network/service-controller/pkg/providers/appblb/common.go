/**
 * @Author: pansiyuan02
 * @Description:
 * @File:  common
 * @Version: 1.0.0
 * @Date: 2020/9/22 5:42 下午
 */
package appblb

import (
	"errors"
	"fmt"
	"os"
	"strconv"
	"strings"

	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/util/intstr"

	"icode.baidu.com/baidu/cce-test/e2e-test/services/network/service-controller/pkg/cloud/appblb"
)

const (
	AnnotationAPPBLBID                   = "service.kubernetes.io/cce-appblb-id"
	AnnotationAPPBLBName                 = "service.kubernetes.io/cce-appblb-name"
	AnnotationAPPBLBVPCSubnetID          = "service.kubernetes.io/cce-appblb-vpc-subnet-id"
	AnnotationBackendType                = "service.kubernetes.io/cce-appblb-backend-type"
	AnnotationBLBReserve                 = "service.kubernetes.io/cce-load-balancer-reserve-lb"
	AnnotationEIPBandWidth               = "service.kubernetes.io/cce-elastic-ip-bandwidth-in-mbps"
	AnnotationAssignedEIP                = "service.kubernetes.io/cce-assigned-eip"
	AnnotationAPPBLBUDPHealthCheckString = "service.kubernetes.io/cce-appblb-udp-health-check-string"
	AnnotationInternalVPCLB              = "service.kubernetes.io/cce-load-balancer-internal-vpc"
	BackendTypeENI                       = "eni"

	AnnotationCompatibleAPPBLBID          = "service.beta.kubernetes.io/cce-load-balancer-id"
	AnnotationCompatibleAPPBLBName        = "service.beta.kubernetes.io/cce-load-balancer-lb-name"
	AnnotationCompatibleAPPBLBVPCSubnetID = "service.beta.kubernetes.io/cce-load-balancer-subnet-id"
	AnnotationCompatibleBackendType       = "service.beta.kubernetes.io/cce-load-balancer-backend-type"
	AnnotationCompatibleBLBReserve        = "service.beta.kubernetes.io/cce-load-balancer-reserve-lb"
	AnnotationCompatibleEIPBandWidth      = "service.beta.kubernetes.io/cce-elastic-ip-bandwidth-in-mbps"
	AnnotationCompatibleInternalVPCLB     = "service.beta.kubernetes.io/cce-load-balancer-internal-vpc"
)

func reserveAPPBLB(service *corev1.Service) bool {
	if reserve, found := service.Annotations[AnnotationBLBReserve]; found && reserve == "true" {
		return true
	}
	// compatible code
	if reserve, found := service.Annotations[AnnotationCompatibleBLBReserve]; found && reserve == "true" {
		return true
	}
	return false
}

func isBackendTypeIPGroup(service *corev1.Service) bool {
	// ENI 和 serverless 集群内的endpoints都是用IPGroup作为blb的后端
	if backendType, found := service.Annotations[AnnotationBackendType]; found && backendType != "" {
		return backendType == BackendTypeENI
	}
	// compatible code
	if backendType, found := service.Annotations[AnnotationCompatibleBackendType]; found && backendType != "" {
		return backendType == BackendTypeENI
	}

	// set for serverless cluster
	if os.Getenv("SERVICE_FORCE_BACKEND_TYPE_IP_GROUP") == "true" {
		return true
	}
	return false
}

func ensureRemoveServiceStatusLBIP(service *corev1.Service, ip string) {
	if ip != "" {
		var ingresses []corev1.LoadBalancerIngress
		for _, ingress := range service.Status.LoadBalancer.Ingress {
			if ingress.IP == ip {
				continue
			}
			ingresses = append(ingresses, ingress)
		}
		service.Status.LoadBalancer.Ingress = ingresses
	}
}

func reserveEIP(service *corev1.Service) bool {
	return service.Spec.LoadBalancerIP != ""
}

func getAPPBLBName(clusterID string, service *corev1.Service) string {
	if name, found := service.Annotations[AnnotationAPPBLBName]; found && name != "" {
		return name
	}
	// compatible code
	if name, found := service.Annotations[AnnotationCompatibleAPPBLBName]; found && name != "" {
		return name
	}
	return defaultAPPBLBName(clusterID, service)
}

func defaultAPPBLBName(clusterID string, service *corev1.Service) string {
	return fmt.Sprintf("CCE/svc/%s/%s/%s", clusterID, service.Namespace, service.Name)
}

func getAPPBLBID(service *corev1.Service) string {
	if id, found := service.Annotations[AnnotationAPPBLBID]; found {
		return id
	}
	// compatible code
	if id, found := service.Annotations[AnnotationCompatibleAPPBLBID]; found {
		return id
	}
	return ""
}

func getEIPName(clusterID string, service *corev1.Service) string {
	return getAPPBLBName(clusterID, service)
}

func getEIPBandWidthFromService(service *corev1.Service) (int, error) {
	value, found := service.Annotations[AnnotationEIPBandWidth]
	// compatible code
	if !found || value == "" {
		value, found = service.Annotations[AnnotationCompatibleEIPBandWidth]
	}
	if found {
		eipBandWidthInMBPS, err := strconv.Atoi(value)
		if err != nil {
			return 0, err
		}

		if eipBandWidthInMBPS < 1 || eipBandWidthInMBPS > 1000 {
			return 0, errors.New("eip band width should between [1, 1000]")
		}

		return eipBandWidthInMBPS, nil
	}
	return appblb.DefaultBandWidthInMBPS, nil
}

func getExpectedVGroup(service *corev1.Service, blbID string) []*appblb.VGroup {
	vgs := make([]*appblb.VGroup, 0)
	for _, port := range service.Spec.Ports {
		vg := &appblb.VGroup{
			Port:       intstr.IntOrString{IntVal: port.Port, Type: intstr.Int},
			NodePort:   intstr.IntOrString{IntVal: port.NodePort, Type: intstr.Int},
			TargetPort: port.TargetPort,
			Protocol:   strings.ToUpper(string(port.Protocol)),
			Name:       fmt.Sprintf("%v-%v", port.Protocol, port.Port),
			BLBID:      blbID,
		}
		if vg.Protocol == appblb.UDPProtocol {
			vg.UDPHealthCheckString = appblb.DefaultUDPHealthCheckString
			if specifiedHealdCheckString := getSpecifiedAPPBLBUDPHealthCheckString(service); specifiedHealdCheckString != "" {
				vg.UDPHealthCheckString = specifiedHealdCheckString
			}
		}
		vgs = append(vgs, vg)
	}
	return vgs
}

func getSpecifiedBLBVPCSubnetID(service *corev1.Service) string {
	if id, found := service.Annotations[AnnotationAPPBLBVPCSubnetID]; found && id != "" {
		return id
	}
	// compatible code
	if id, found := service.Annotations[AnnotationCompatibleAPPBLBVPCSubnetID]; found && id != "" {
		return id
	}
	return ""
}

func shouldAssignExternalIP(service *corev1.Service) bool {
	if internal, found := service.Annotations[AnnotationInternalVPCLB]; found && internal == "true" {
		return false
	}
	// compatible code
	if internal, found := service.Annotations[AnnotationCompatibleInternalVPCLB]; found && internal == "true" {
		return false
	}
	return true
}

func getSpecifiedAPPBLBUDPHealthCheckString(service *corev1.Service) string {
	if s, found := service.Annotations[AnnotationAPPBLBUDPHealthCheckString]; found {
		return s
	}
	return ""
}
