package common

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"os"
	"regexp"
	"strconv"

	v1 "k8s.io/api/core/v1"

	log "icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
)

// AppBLB annotation
const (
	// AnnotationBLBName to set BLB Name
	AnnotationBLBName = "service.beta.kubernetes.io/cce-load-balancer-lb-name"

	// AnnotationVPCID to set VPC ID
	AnnotationVPCID = "service.beta.kubernetes.io/cce-load-balancer-vpc-id"

	// AnnotationVPCSubnetID to set VPC SubnetID
	AnnotationVPCSubnetID = "service.beta.kubernetes.io/cce-load-balancer-subnet-id"

	// AnnotationBLBShortID service's associated app blbShortID
	AnnotationBLBShortID = "service.beta.kubernetes.io/cce-load-balancer-id"

	// AnnotationBLBReserve
	AnnotationBLBReserve = "service.beta.kubernetes.io/cce-load-balancer-reserve-lb"

	// AnnotationBLBType 标识 BLB 的类型 取值分为 "blb" "appblb" "ipv6" 三种
	AnnotationBLBType = "service.beta.kubernetes.io/cce-blb-type"

	// AnnotationNoUseBLBClusterL4 不使用 BLB L4 专属集群，BLB 将会建在共享集群上
	AnnotationNoUseBLBClusterL4 = "service.beta.kubernetes.io/no-use-blb-cluster-l4"

	// AnnotationBLBClusterIDL4 用户指定的BLB 专属L4集群 ID
	AnnotationBLBClusterIDL4 = "service.beta.kubernetes.io/blb-cluster-l4-id"

	// ServiceAnnotationLoadBalancerAllocateVip is the annotation which indicates BLB with a VIP (for baidu internal user)
	ServiceAnnotationLoadBalancerAllocateVip = "service.beta.kubernetes.io/cce-load-balancer-allocate-vip"

	// ServiceAnnotationElasticIPBillingMethod is the annotation of ElasticIPBillingMethod
	ServiceAnnotationElasticIPBillingMethod = "service.beta.kubernetes.io/cce-elastic-ip-billing-method"
)

// CCEBLBType BLB的类型 分为 blb:普通型实例 appblb:应用型实例 ipv6:IPv6实例
type CCEBLBType string

const (
	CCEBLBTypeNormal CCEBLBType = "blb"
	CCEBLBTypeApp    CCEBLBType = "appblb"
)

// EIP annotation
const (
	// AnnotationInternal If service only internal
	AnnotationInternal = "service.beta.kubernetes.io/cce-load-balancer-internal-vpc"

	// AnnotationExternal If service only external
	AnnotationExternal = "service.beta.kubernetes.io/cce-load-balancer-external"

	// AnnotationEIPBandWidth to set EIP BandWidth
	AnnotationEIPBandWidth = "service.beta.kubernetes.io/cce-elastic-ip-bandwidth-in-mbps"

	// ServiceAnnotationElasticIPPaymentTiming is the annotation of ElasticIPPaymentTiming
	ServiceAnnotationElasticIPPaymentTiming = "service.beta.kubernetes.io/cce-elastic-ip-payment-timing"

	// ServiceAnnotationElasticIPReservationLength is the annotation of ElasticIPReservationLength
	ServiceAnnotationElasticIPReservationLength = "service.beta.kubernetes.io/cce-elastic-ip-reservation-length"

	// AnnotationEIPRouteType is the annotation of ElasticIPReservationLength
	AnnotationEIPRouteType = "service.beta.kubernetes.io/cce-elastic-ip-route-type"
)

// BackendServer annotation
const (
	// AnnotationMaxBackendCount the max count of backend
	AnnotationMaxBackendCount = "service.beta.kubernetes.io/cce-load-balancer-rs-max-num"

	// AnnotationBackendType the type of backend
	AnnotationBackendType = "service.beta.kubernetes.io/cce-load-balancer-backend-type"
	BACKEND_TYPE_ENI      = "eni"
	BACKEND_TYPE_NodeIP   = "node-ip"

	// AnnotationDynamicServerWeight the max count of backend
	AnnotationDynamicServerWeight = "service.beta.kubernetes.io/cce-dynamic-backend-server-weight"
)

// Control Annotation
const (
	// AnnotationIgnoreByCCE if value is true, cce controller will not reconcile this service resource
	AnnotationIgnoreByCCE = "service.beta.kubernetes.io/ignore-by-cce"
)

// Listener Annotation
// 注意: 协议不是可以任意指定的 如果要使用 TCP-SSL/HTTP/HTTPS 协议，原始 Port 必须是 TCP 协议
const (
	// AnnotationCustomizedListener 允许用户自定义某个端口号的协议及其配置
	AnnotationCustomizedListener = "service.beta.kubernetes.io/cce.listener.customized-listener"
)

// EnvDisableEIPByDefault 环境变量 在用户没有添加 InternalAnnotation 的情况下 是否启用EIP
const (
	EnvDisableEIPByDefault = "DISABlE_EIP_BY_DEFAULT"
)

func GetCustomizedListener(annotations map[string]string) (map[string]CustomizedListener, error) {
	if annotations == nil {
		return map[string]CustomizedListener{}, nil
	}
	if annotations[AnnotationCustomizedListener] == "" {
		return map[string]CustomizedListener{}, nil
	}

	customizedListenerBytes := []byte(annotations[AnnotationCustomizedListener])

	result := map[string]CustomizedListener{}

	if err := json.Unmarshal(customizedListenerBytes, &result); err != nil {
		return nil, err
	}
	return result, nil
}

func CheckCustomizedListener(port string, customizedListener *CustomizedListener) error {
	if customizedListener == nil {
		return nil
	}
	if _, err := ParseIntStringWithDefaultValue(customizedListener.Timeout); err != nil {
		return fmt.Errorf("customizedListener port %s timeout has illegal value %s", port, customizedListener.Timeout)
	}
	if _, err := ParseIntStringWithDefaultValue(customizedListener.RedirectPort); err != nil {
		return fmt.Errorf("customizedListener port %s refirectPort has illegal value %s", port, customizedListener.RedirectPort)
	}
	if _, err := ParseIntStringWithDefaultValue(customizedListener.KeepSessionTimeout); err != nil {
		return fmt.Errorf("customizedListener port %s keepSessionTimeout has illegal value %s", port, customizedListener.KeepSessionTimeout)
	}
	if _, err := ParseBoolStringWithDefaultValue(customizedListener.DualAuth); err != nil {
		return fmt.Errorf("customizedListener port %s dualAuth has illegal value %s", port, customizedListener.DualAuth)
	}
	if _, err := ParseBoolStringWithDefaultValue(customizedListener.DisableXFFHeader); err != nil {
		return fmt.Errorf("customizedListener port %s disableXFFHeader has illegal value %s", port, customizedListener.DisableXFFHeader)
	}
	if _, err := ParseBoolStringWithDefaultValue(customizedListener.EnableXForwardedProtoHeader); err != nil {
		return fmt.Errorf("customizedListener port %s enableXForwardedProtoHeader has illegal value %s", port, customizedListener.EnableXForwardedProtoHeader)
	}
	if _, err := ParseBoolStringWithDefaultValue(customizedListener.EnableKeepSession); err != nil {
		return fmt.Errorf("customizedListener port %s knableKeepSession has illegal value %s", port, customizedListener.EnableKeepSession)
	}

	return nil
}

// ParseIntStringWithDefaultValue 解析字符串为 int 如果为空串则给予默认值 0
func ParseIntStringWithDefaultValue(value string) (int, error) {
	if value == "" {
		return 0, nil
	}
	return strconv.Atoi(value)
}

// ParseBoolStringWithDefaultValue 解析字符串为 bool 如果为空串则给予默认值 false
func ParseBoolStringWithDefaultValue(value string) (bool, error) {
	if value == "" {
		return false, nil
	}
	return strconv.ParseBool(value)
}

func IsIgnoreByCCE(annotations map[string]string) bool {
	if annotations == nil {
		return false
	}
	if annotations[AnnotationIgnoreByCCE] == "true" {
		return true
	}
	return false
}

func IsBackendTypeENI(ctx context.Context, service *v1.Service) bool {
	if service.Annotations == nil {
		return false
	}
	if service.Annotations[AnnotationBackendType] != "" {
		return service.Annotations[AnnotationBackendType] == BACKEND_TYPE_ENI
	}
	if os.Getenv("SERVICE_FORCE_BACKEND_TYPE_ENI") == "true" {
		return true
	}
	return false
}

func IsBackendTypeNodeIP(service v1.Service) bool {
	if service.Annotations == nil {
		return false
	}
	if service.Annotations[AnnotationBackendType] != "" {
		return service.Annotations[AnnotationBackendType] == BACKEND_TYPE_NodeIP
	}
	if os.Getenv("SERVICE_FORCE_BACKEND_TYPE_NODEIP") == "true" {
		return true
	}
	return false
}

func ShouldKeepLoadBalancer(ctx context.Context, service *v1.Service) bool {
	if service == nil {
		log.Errorf(ctx, "ShouldKeepLoadBalancer failed: service is nil")
		return false
	}
	anno, keepLB := service.Annotations[AnnotationBLBReserve]
	return keepLB && anno == "true"
}

// GetVPCSubnetIDFromAnnotation - Get VPC SubnetID from annotation: kubernetes.io/cce.service.vpc-subnet-id
func GetVPCSubnetIDFromAnnotation(ctx context.Context, service *v1.Service) string {
	if service == nil {
		log.Errorf(ctx, "GetVPCSubnetIDFromAnnotation failed: service is nil")
		return ""
	}

	subnetID, ok := service.ObjectMeta.Annotations[AnnotationVPCSubnetID]
	if ok {
		reg := regexp.MustCompile(`sbn-.+`)
		if !reg.MatchString(subnetID) {
			log.Errorf(ctx, fmt.Sprintf("getVPCSubnetID failed: got=%s", subnetID))
			return ""
		}

		return subnetID
	}

	return ""
}

// IsAppBLBIDSettedInAnnotation - Is BLBID setted: kubernetes.io/cce.service.blb-id
func IsAppBLBIDSettedInAnnotation(ctx context.Context, service *v1.Service) (string, bool) {
	if service == nil {
		log.Errorf(ctx, "IsAppBLBIDSettedInAnnotation failed: service is nil")
		return "", false
	}

	blbID, ok := service.ObjectMeta.Annotations[AnnotationBLBShortID]
	if ok {
		reg := regexp.MustCompile(`lb-.+`)
		if !reg.MatchString(blbID) {
			log.Errorf(ctx, fmt.Sprintf("isAppBLBIDSetted failed: %s format not match", blbID))
			return "", false
		}

		return blbID, true
	}

	return "", false
}

// IsAppBLBInternalInAnnotation If AppBLB need EIP
func IsAppBLBInternalInAnnotation(ctx context.Context, service *v1.Service) bool {
	if service == nil {
		log.Errorf(ctx, "IsAppBLBInternalInAnnotation failed: service is nil")
		return false
	}

	annotations := service.ObjectMeta.Annotations

	if annotations != nil {
		value, ok := annotations[AnnotationInternal]
		if ok {
			return value == "true"
		}
	}

	disableEIPByDefault, isExist := os.LookupEnv(EnvDisableEIPByDefault)
	if isExist && disableEIPByDefault == "true" {
		return true
	}

	return false
}

func IsAppBLBExternalInAnnotation(ctx context.Context, service *v1.Service) bool {
	if service == nil {
		log.Errorf(ctx, "IsAppBLBExternalInAnnotation failed: service is nil")
		return false
	}

	if value, found := service.Annotations[AnnotationExternal]; found {
		return value == "true"
	}
	return false
}

// GetEIPBandWidthFromAnnotation If set EIP BandWidthInMBPS
func GetEIPBandWidthFromAnnotation(ctx context.Context, service *v1.Service) (int, bool) {
	if service == nil {
		log.Errorf(ctx, "GetEIPBandWidthFromAnnotation failed: service is nil")
		return 0, false
	}

	annotations := service.ObjectMeta.Annotations

	value, ok := annotations[AnnotationEIPBandWidth]
	if ok {
		eipBandWidthInMBPS, err := strconv.Atoi(value)
		if err != nil {
			log.Errorf(ctx, fmt.Sprintf("%s set, but not number: %v", AnnotationEIPBandWidth, err))
			return 0, false
		}

		if eipBandWidthInMBPS < 1 || eipBandWidthInMBPS > 1000 {
			log.Errorf(ctx, fmt.Sprintf("%s set, but between 1-1000: %d", AnnotationEIPBandWidth, eipBandWidthInMBPS))
			return 0, false
		}

		return eipBandWidthInMBPS, true
	}

	return 0, false
}

// GetMaxBackendCountFromAnnotation get max backend count
func GetMaxBackendCountFromAnnotation(ctx context.Context, service *v1.Service) (int, error) {
	if service == nil {
		log.Errorf(ctx, "GetMaxBackendCountFromAnnotation failed: service is nil")
		return 0, errors.New("service is nil")
	}
	if service.ObjectMeta.Annotations == nil {
		return 0, nil
	}

	// Get max backend count From service.ObjectMeta.Annotations[AnnotationEIP]
	maxBackendCount, ok := service.ObjectMeta.Annotations[AnnotationMaxBackendCount]
	if ok {
		c, err := strconv.Atoi(maxBackendCount)
		if err != nil {
			log.Errorf(ctx, fmt.Sprintf("GetMaxBackendCountFromAnnotation failed: %v is not number", maxBackendCount))
			return 0, fmt.Errorf("invalid number: value of %s must be a number", AnnotationMaxBackendCount)
		}

		if c <= 0 {
			log.Errorf(ctx, fmt.Sprintf("GetMaxBackendCountFromAnnotation failed: %v <= 0", maxBackendCount))
			return 0, fmt.Errorf("invalid number: value of %s must be a positive number", AnnotationMaxBackendCount)
		}

		return c, nil
	}

	return 0, nil
}

// GetBLBL4ClusterID - Get BLB Cluster ID from Service Annotation
// PARAMS:
//   - service: The LoadBalancer Service
//
// RETURNS:
//
//	bool: is user decided to select a BLB Cluster
//	string: BLB L4 cluster ID
func GetBLBL4ClusterID(service v1.Service) (bool, string) {
	if service.Annotations == nil {
		return false, ""
	}
	blbClusterID, ok := service.Annotations[AnnotationBLBClusterIDL4]
	return ok, blbClusterID
}

// IsNoUseBLBClusterL4 - Is service not want to use l4 blb cluster
// PARAMS:
//   - service: The LoadBalancer Service
//
// RETURNS:
//
//	bool: is user not want to use l4 blb cluster
func IsNoUseBLBClusterL4(service v1.Service) bool {
	if service.Annotations == nil {
		return false
	}
	result, ok := service.Annotations[AnnotationNoUseBLBClusterL4]
	if ok && result == "true" {
		return true
	}
	return false
}

// GetUserSetBLBNameFromService 获取 Service 关于记录 BLB 名称的 Annotation
func GetUserSetBLBNameFromService(service v1.Service) string {
	if service.Annotations == nil {
		return ""
	}
	blbName := service.Annotations[AnnotationBLBName]
	return blbName
}

// GetBLBTypeFromService 获取 Service 关于记录 BLB 类型的 Annotation
func GetBLBTypeFromService(service v1.Service) string {
	if service.Annotations == nil {
		return ""
	}
	blbType := service.Annotations[AnnotationBLBType]
	return blbType
}

// GetIsBLBAllocateVIP 获取 Service 关于记录用户是否需要为 BLB 分配 VIP 的 Annotation
func GetIsBLBAllocateVIP(service v1.Service) bool {
	if service.Annotations == nil {
		return false
	}
	result, ok := service.Annotations[ServiceAnnotationLoadBalancerAllocateVip]
	if ok && result == "true" {
		return true
	}
	return false
}

func GetEIPBillingMethodFromAnnotation(service v1.Service) string {
	if service.Annotations == nil {
		return ""
	}
	result, ok := service.Annotations[ServiceAnnotationElasticIPBillingMethod]
	if !ok {
		return ""
	}
	return result
}

// GetEIPBillingMethod 获取 Service 关于记录 EIP 计费方式
func GetEIPBillingMethod(service v1.Service, defaultBillingMethod string) string {
	billingMethod := GetEIPBillingMethodFromAnnotation(service)
	if billingMethod == "" {
		return defaultBillingMethod
	}
	return billingMethod
}

// GetVPCIDFromAnnotation 获取 Service 指定的 VPC ID
func GetVPCIDFromAnnotation(service v1.Service) string {
	if service.Annotations == nil {
		return ""
	}
	result := service.Annotations[AnnotationVPCID]
	return result
}

// GetPaymentTimingFromAnnotation 获取 Service 的付费类型
func GetPaymentTimingFromAnnotation(service v1.Service) string {
	if service.Annotations == nil {
		return ""
	}
	result := service.Annotations[ServiceAnnotationElasticIPPaymentTiming]
	return result
}

var allowedReservationLength = []int{1, 2, 3, 4, 5, 6, 7, 8, 9, 12, 24, 36}

// GetReservationLengthFromAnnotation 获取 Service 的付费类型
func GetReservationLengthFromAnnotation(service v1.Service) (int, error) {
	if service.Annotations == nil {
		return 0, nil
	}
	result, ok := service.Annotations[ServiceAnnotationElasticIPReservationLength]
	if !ok || len(result) == 0 {
		return 0, nil
	}

	length, err := strconv.Atoi(result)
	if err != nil {
		return 0, err
	}
	for _, allowed := range allowedReservationLength {
		if length == allowed {
			return length, nil
		}
	}
	return 0, fmt.Errorf("prepaid EIP reservationLength should in %v", allowedReservationLength)
}

// GetIsAppBLBDynamicServerWeight If AppBLB need EIP
func GetIsAppBLBDynamicServerWeight(service v1.Service) bool {
	if service.Annotations == nil {
		return false
	}
	result, ok := service.Annotations[AnnotationDynamicServerWeight]
	if ok && result == "true" {
		return true
	}
	return false
}

// GetIsEIPRouteType Decide EIP Route Type
func GetIsEIPRouteType(service v1.Service) string {
	if service.Annotations == nil {
		return ""
	}

	result := service.Annotations[AnnotationEIPRouteType]
	return result
}
