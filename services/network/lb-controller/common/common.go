package common

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"

	v1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/util/strategicpatch"
)

type CustomizedListener struct {
	// 要扩展的协议类型
	Protocol string `json:"protocol"`

	// TCP/UDP/HTTP/HTTPS/TCP-SSL
	// 请求调度方法
	Scheduler string `json:"scheduler,omitempty"`

	// TCP/UDP/HTTP/HTTPS
	// 超时时间 TCP会话超时/UDP会话超时/HTTP\HTTPS服务端超时
	Timeout string `json:"timeout,omitempty"`

	// TCP-SSL/HTTPS
	// 服务端证书 ID
	ServerCertIDs string `json:"serverCertIDs,omitempty"` // 逗号分隔的字符串

	// 加密协议相关配置 参考文档 https://cloud.baidu.com/doc/BLB/s/Pjwvxnxdm#encryption
	EncryptionType string `json:"encryptionType,omitempty"` // 加密选项。支持：tls_cipher_policy_default/tls_cipher_policy_1_1/tls_cipher_policy_1_2/tls_cipher_policy_1_2_secure/userDefind

	EncryptionProtocols string `json:"encryptionProtocols,omitempty"` // 当encryptionType值为userDefind时协议类型列表，是以"tlsv10"、"tlsv11"、"tlsv12"三种协议组合形成的字符串列表 逗号分隔的字符串
	AppliedCiphers      string `json:"appliedCiphers,omitempty"`      // 不同加密套件用冒号":"隔开

	// 是否开启双向认证 以及开启双向认证后的客户端端证书 ID
	DualAuth string `json:"dualAuth,omitempty"`

	ClientCertIDs string `json:"clientCertIDs,omitempty"` // 逗号分隔的字符串

	// HTTP
	// HTTPS 重定向端口 - 重定向到哪个端口
	RedirectPort string `json:"redirectPort,omitempty"`

	// HTTP/HTTPS
	// 是否关闭 XForwardedFor 头
	DisableXFFHeader string `json:"disableXForwardedForHeader,omitempty"`

	// 是否关开启 XForwardedProto 头
	EnableXForwardedProtoHeader string `json:"enableXForwardedProtoHeader,omitempty"`

	// HTTP/HTTPS
	// 会话保持相关
	EnableKeepSession string `json:"enableKeepSession,omitempty"`

	KeepSessionType       string `json:"keepSessionType,omitempty"`
	KeepSessionTimeout    string `json:"keepSessionTimeout,omitempty"`
	KeepSessionCookieName string `json:"keepSessionCookieName,omitempty"`
}

var (
	ShouldHandleServiceAnnotations = map[string]string{}
)

// ParseShouldHandleServiceAnnotations 解析annotations字符串到map
// xxx/yyy=zzz,a=b => {"xxx/yyy": "zzz", "a": "b"}
func ParseShouldHandleServiceAnnotations(annotations string) error {
	kvs := strings.Split(annotations, ",")
	for _, kv := range kvs {
		pair := strings.Split(kv, "=")
		if len(pair) != 2 {
			return fmt.Errorf("invalid annotations: %s", annotations)
		}
		ShouldHandleServiceAnnotations[pair[0]] = pair[1]
	}
	return nil
}

// IsLBControllerResponsibleFor 该 Service 是否是 cce-lb-controller 的负责范围
func IsLBControllerResponsibleFor(service *v1.Service) bool {
	if !isLoadBalancer(service) {
		return false
	}

	if len(ShouldHandleServiceAnnotations) == 0 {
		return true
	}

	for k, v := range ShouldHandleServiceAnnotations {
		if value, found := service.Annotations[k]; found && v == value {
			return true
		}
	}

	return false
}

// IsLoadBalancerWithNodeBackend 判断该 Service 是否是普通LB Service 即后端直接挂载的是Node
func IsLoadBalancerWithNodeBackend(service *v1.Service) bool {
	return isLoadBalancer(service) && !IsBackendTypeENI(context.TODO(), service)
}

// IsLoadBalancerWithPodBackend 判断该 Service 是否是 Service 直连 Pod 模式 LB Service 即后端直接挂载的是Pod
func IsLoadBalancerWithPodBackend(service *v1.Service) bool {
	return isLoadBalancer(service) && IsBackendTypeENI(context.TODO(), service)
}

// isLoadBalancer 判断 Service 是否是 LB 类型
func isLoadBalancer(service *v1.Service) bool {
	return service.Spec.Type == v1.ServiceTypeLoadBalancer
}

// GetServicePatchBytes 对比新旧 Service 获取 Patch Bytes 用于 Service Patch 接口
func GetServicePatchBytes(oldSvc, newSvc *v1.Service) ([]byte, error) {
	oldData, err := json.Marshal(oldSvc)
	if err != nil {
		return nil, fmt.Errorf("failed to mashal old data for svc %s/%s: %v", oldSvc.Namespace, oldSvc.Name, err)
	}

	newData, err := json.Marshal(newSvc)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal new data for svc %s/%s: %v", newSvc.Namespace, newSvc.Name, err)
	}

	patchBytes, err := strategicpatch.CreateTwoWayMergePatch(oldData, newData, v1.Service{})
	if err != nil {
		return nil, fmt.Errorf("failed to create two way merge batch for svc %s/%s: %v", oldSvc.Namespace, oldSvc.Name, err)
	}
	return patchBytes, nil
}
