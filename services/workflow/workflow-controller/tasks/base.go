// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2021/12/15 08:51:00, by <EMAIL>, create
*/
/*
BastTask 完成公共 Clients/Configs 等初始化
*/

package tasks

import (
	"context"
	"errors"
	"fmt"
	"strings"

	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/fields"
	"k8s.io/client-go/kubernetes"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	ccetypes "icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/utils"
)

// ErrNodesNotReady - NodesNotReady error
type ErrNodesNotReady []string

func (e *ErrNodesNotReady) Error() string {
	if e == nil {
		return ""
	}
	return "nodes NotReady: " + utils.ToJSON(e)
}

// ErrPodsUnHealthy - PodsUnHealthy error
type ErrPodsUnHealthy []string

func (e *ErrPodsUnHealthy) Error() string {
	if e == nil {
		return ""
	}
	return "pods unhealthy: " + utils.ToJSON(e)
}

// IsErrPodsUnHealthy - 判断错误类型是否为 PodsUnHealthy
func IsErrPodsUnHealthy(err error) bool {
	if err == nil {
		return false
	}

	var errPodsUnHealthy *ErrPodsUnHealthy
	return errors.As(err, &errPodsUnHealthy)
}

// CheckNodeConditions - 检查 Node.Status.Conditions 中 KubeletReady, e.g.
// conditions:
//   - lastHeartbeatTime: "2022-02-08T06:15:22Z"
//     lastTransitionTime: "2022-02-08T05:54:16Z"
//     message: kubelet is posting ready status
//     reason: KubeletReady
//     status: "True"
//     type: Ready
func CheckNodeConditions(ctx context.Context, node *corev1.Node) (bool, error) {
	if node == nil {
		return false, errors.New("node is nil")
	}

	for _, condition := range node.Status.Conditions {
		if condition.Type == corev1.NodeReady {
			if condition.Status == corev1.ConditionTrue {
				return true, nil
			}
			return false, nil
		}
	}

	// TODO: 默认使用 false
	return false, nil
}

// CheckPodReady - 检查 Pod 是否都 Ready
func CheckPodReady(ctx context.Context, pod *corev1.Pod) (bool, error) {
	podName := fmt.Sprintf("%s/%s", pod.GetNamespace(), pod.GetName())

	// 检查 Pod.Status.Phase
	if pod.Status.Phase != corev1.PodRunning &&
		pod.Status.Phase != corev1.PodSucceeded {
		logger.Errorf(ctx, "Pod %s Phase=%s, not ready", podName, pod.Status.Phase)
		return false, nil
	}

	// 检查 Pod.Status.ContainerStatuses.Ready
	for _, container := range pod.Status.ContainerStatuses {
		// Job 类型 Pod: Pod.Phase = Succeeded && Container.Ready = false
		if !container.Ready && pod.Status.Phase != corev1.PodSucceeded {
			logger.Errorf(ctx, "Pod %s Container not ready", podName)
			return false, nil
		}
	}

	return true, nil
}

// CheckPodRunning - 检查 Pod 是否都 Running
func CheckPodRunning(ctx context.Context, pod *corev1.Pod) error {
	podName := fmt.Sprintf("%s/%s", pod.GetNamespace(), pod.GetName())

	// 检查 Pod.Status.Phase
	if pod.Status.Phase != corev1.PodRunning {
		return fmt.Errorf("Pod %s Phase=%s, not running", podName, pod.Status.Phase)
	}

	// 检查 Pod.Status.ContainerStatuses.Ready
	for _, container := range pod.Status.ContainerStatuses {
		if !container.Ready {
			return fmt.Errorf("Pod %s Container %s not running", podName, container.Name)
		}
	}

	return nil
}

// CheckPodsReady - 检查 Pod 是否都 Ready
func CheckPodsReady(ctx context.Context, podList *corev1.PodList) (bool, []corev1.Pod, error) {
	ready := true
	failedPodList := []corev1.Pod{}

	if podList == nil {
		return ready, failedPodList, errors.New("podList is nil")
	}

	// 检查 Pod 状态
	for _, pod := range podList.Items {
		podName := fmt.Sprintf("%s/%s", pod.GetNamespace(), pod.GetName())

		// 过滤 Terminating Pod
		if !pod.DeletionTimestamp.IsZero() {
			continue
		}

		// 检查 Pod.Status.Phase
		if pod.Status.Phase != corev1.PodRunning &&
			pod.Status.Phase != corev1.PodSucceeded {
			ready = false
			logger.Infof(ctx, "Pod %s Phase=%s", podName, pod.Status.Phase)
			failedPodList = append(failedPodList, pod)
			continue
		}

		// cce-eni-ipam 有选主逻辑，不判断 container 是否ready，全局检查中做处理
		if strings.Contains(pod.GetName(), "cce-eni-ipam") {
			continue
		}

		// 检查 Pod.Status.ContainerStatuses.Ready
		for _, container := range pod.Status.ContainerStatuses {
			// Job 类型 Pod: Pod.Phase = Succeeded && Container.Ready = false
			if !container.Ready && pod.Status.Phase != corev1.PodSucceeded {
				ready = false
				failedPodList = append(failedPodList, pod)
				continue
			}
		}
	}

	return ready, failedPodList, nil
}

// KubeletNeedUpgrade -
func KubeletNeedUpgrade(ctx context.Context, client kubernetes.Interface,
	k8sNodeName string, k8sVersion ccetypes.K8SVersion) (bool, error) {
	// 获取 K8S Node
	node, err := client.CoreV1().Nodes().Get(ctx, k8sNodeName, metav1.GetOptions{})
	if err != nil {
		logger.Errorf(ctx, "GetNode %s failed: %s", k8sNodeName, err)
		return false, err
	}

	// 检查 K8S Node Kubelet 组件版本
	toK8SVersion := fmt.Sprintf("v%s", k8sVersion)
	if node.Status.NodeInfo.KubeletVersion == string(toK8SVersion) {
		logger.Infof(ctx, "Node %s KubeletVersion == ToK8SVersion %s, skip upgrade",
			k8sNodeName, toK8SVersion)
		return false, nil
	}

	// TODO: 检查 DisableScheduler 状态的处理

	return true, nil
}

func CheckK8SComponentReadiness(ctx context.Context, prober *ClusterComponentProber, k8sVersion ccetypes.K8SVersion) error {
	if prober == nil {
		return errors.New("prober is nil")
	}
	before1_20, err := k8sVersion.IsBefore(ccetypes.K8S_1_20_8)
	if err != nil {
		logger.Errorf(ctx, "invalid k8s version, err: %v", err)
		return fmt.Errorf("invalid k8s version, err: %w", err)
	}
	if before1_20 {
		return CheckK8SComponentStatus(ctx, prober.K8SClient)
	}
	if err := prober.Prober.Probe(ctx); err != nil {
		return err
	}

	return nil
}

// CheckK8SComponentStatus - kubectl get cs
func CheckK8SComponentStatus(ctx context.Context, client kubernetes.Interface) error {
	if client == nil {
		return errors.New("client is nil")
	}

	// kubectl get cs
	componentStatusList, err := client.CoreV1().ComponentStatuses().List(ctx, metav1.ListOptions{})
	if err != nil {
		logger.Errorf(ctx, "List ComponentStatuses failed: %s", err)
		return err
	}

	if componentStatusList == nil {
		return errors.New("componentStatusList is nil")
	}

	var unhealthyComponents []string
	for _, cs := range componentStatusList.Items {
		for _, c := range cs.Conditions {
			if c.Type != corev1.ComponentHealthy || c.Status != corev1.ConditionTrue {
				logger.Errorf(ctx, "K8S component not ready: %s", utils.ToJSON(cs))
				unhealthyComponents = append(unhealthyComponents, cs.GetName())
			}
		}
	}

	if len(unhealthyComponents) > 0 {
		return fmt.Errorf("Unhealthy components found: %v", strings.Join(unhealthyComponents, ","))
	}
	return nil
}

// GetPodsOnNode - 获取 Node 上 PodList
func GetPodsOnNode(ctx context.Context, client kubernetes.Interface, k8sNodeName string) (*corev1.PodList, error) {
	if k8sNodeName == "" {
		return nil, errors.New("k8sNodeName is empty")
	}

	fieldSelector, err := fields.ParseSelector(fmt.Sprintf("spec.nodeName=%s", k8sNodeName))
	if err != nil {
		logger.Errorf(ctx, "fields.ParseSelector failed: %s", err)
		return nil, err
	}

	podList, err := client.CoreV1().Pods(metav1.NamespaceAll).List(ctx, metav1.ListOptions{
		FieldSelector:   fieldSelector.String(),
		ResourceVersion: "0",
	})
	if err != nil {
		logger.Errorf(ctx, "GetPods on %s failed: %s", k8sNodeName, err)
		return nil, err
	}

	return podList, err
}

// CheckMasterPodImageVersion - 检查 Master 组件镜像版本是否正确
func CheckMasterPodImageVersion(ctx context.Context, podList *corev1.PodList, k8sVersion ccetypes.K8SVersion) error {
	if podList == nil {
		return errors.New("podList is empty")
	}

	if k8sVersion == "" {
		return errors.New("k8sVersion is empty")
	}

	// 必须包含 kube-apiserver, kube-scheduler, kube-controller-manager
	masterCount := 0
	for _, pod := range podList.Items {
		podName := pod.GetName()

		if !IsMasterPod(ctx, podName) {
			continue
		}
		masterCount = masterCount + 1

		// Master Pod 仅一个 Container
		container := pod.Spec.Containers[0]
		if !strings.Contains(container.Image, string(k8sVersion)) {
			logger.Errorf(ctx, "MasterPod %s image does not contain %s", podName, k8sVersion)
			return fmt.Errorf("MasterPod %s image does not contain %s", podName, k8sVersion)
		}
	}

	if masterCount != 3 {
		logger.Errorf(ctx, "master pod count != 3")
		return errors.New("master pod count != 3")
	}

	return nil
}

// IsMasterPod - Pod 是否为 MasterPod
func IsMasterPod(ctx context.Context, podName string) bool {
	return strings.Contains(podName, "kube-apiserver") ||
		strings.Contains(podName, "kube-controller-manager") ||
		strings.Contains(podName, "kube-scheduler")
}

func IsMasterContainer(ctx context.Context, name string) bool {
	return strings.Contains(name, "apiserver") ||
		strings.Contains(name, "controller-manager") ||
		strings.Contains(name, "scheduler")
}
