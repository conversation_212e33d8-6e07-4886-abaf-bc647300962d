// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2021/11/30 10:09:00, by <EMAIL>, create
*/
/*
定义 Task Interface, 各种不同类型的 Task 均在此初始化
*/

package tasks

import (
	"context"
	"fmt"
	"io"
	"net/http"
	"time"

	"k8s.io/apimachinery/pkg/types"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/rest"
	"k8s.io/client-go/tools/clientcmd"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/clientset"
	ccev1 "icode.baidu.com/baidu/cce-test/e2e-test/pkg/crd/api/v1"
	dptypes "icode.baidu.com/baidu/cce-test/e2e-test/pkg/deployer/types"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/plugin/clients"
	ccetypes "icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
	"icode.baidu.com/baidu/cce-test/e2e-test/services/cluster/cluster-controller/provider"
)

//go:generate mockgen -destination=./mock/mock_client.go -package=mock icode.baidu.com/baidu/cce-test/e2e-test/services/workflow/workflow-controller/tasks Interface

// Interface - Task 实现该 Interface
//
// 为方便同一种类型的 Task 复用及避免漏写, 定义 BaseTask 用于实现同一种类型 Task 可复用的方法
//
// 通常 BaseTask 实现如下 3 个方法:
//
// 1. UpdateMetaBeforeExecute(ctx context.Context, task ccetypes.WorkflowTask) error
// 2. CheckStatus(ctx context.Context, task ccetypes.WorkflowTask) error
// 3. UpdateMetaAfterExecute(ctx context.Context, task ccetypes.WorkflowTask) error
// 4. Cluster(ctx context.Context) *ccev1.Cluster
//
// 一般 Task 继承 BaseTask 各自实现如下方法:
//
// 1. TaskType(ctx context.Context) ccetypes.WorkflowTaskType
// 2. TaskConfig(ctx context.Context) interface{}
// 3. Execute(ctx context.Context, task ccetypes.WorkflowTask) (TaskExecuteResult, error)
type Interface interface {
	// Name 主要用于前端展示, 区别于 TaskType, 可使用中文
	Name(ctx context.Context) string

	// 返回 TaskType
	TaskType(ctx context.Context) ccetypes.WorkflowTaskType

	// 返回 TaskConfig
	TaskConfig(ctx context.Context) any

	// 检查是否需要变更
	NeedUpgrade(ctx context.Context, task ccetypes.WorkflowTask) (bool, error)

	// 变更前检查变更对象状态
	CheckStatusBeforeExecute(ctx context.Context, task ccetypes.WorkflowTask) error

	// 变更前元数据变更
	// 注意 deployer.Cluster 及 deployer.Instance 的元数据变更需要在该方法中实现
	UpdateMetaBeforeExecute(ctx context.Context, task ccetypes.WorkflowTask) error

	// 执行变更操作
	Execute(ctx context.Context, task ccetypes.WorkflowTask) (TaskExecuteResult, error)

	// 变更后元数据变更
	UpdateMetaAfterExecute(ctx context.Context, task ccetypes.WorkflowTask) error

	// 变更后检查变更对象状态
	CheckStatusAfterExecute(ctx context.Context, task ccetypes.WorkflowTask) error

	CheckStatusAfterExecuteInterval(ctx context.Context) time.Duration // 轮询间隔
	CheckStatusAfterExecuteTimeout(ctx context.Context) time.Duration  // 超时时长

	// 清理升级残留
	Clean(ctx context.Context, task ccetypes.WorkflowTask) error

	// 返回操作对象
	Targets(ctx context.Context) ([]any, error)
}

// TaskBaseConfig - Task 通用 Config
type TaskBaseConfig struct {
	ClusterID      string
	AccountID      string
	WorkflowID     string
	Cluster        *ccev1.Cluster
	DeployerConfig *dptypes.Config
	Clients        *clientset.ClientSet

	// 大量展示会用到, 提前建立映射
	CCEInstanceIDToVPCIP map[string]string

	// 集群操作前失败 Pod OwnerReference 映射, 便于后置检查失败后跳过
	// ownerReferences:
	// - apiVersion: apps/v1
	//   blockOwnerDeletion: true
	//   controller: true
	//   kind: StatefulSet
	//   name: advanced-tidb-pd
	//   uid: 5d30f932-fcf6-475e-ab56-3737f907762d
	FailedPodOwnerReferencesSet map[types.UID]any

	// PluginConfig helm 二进制配置
	PluginConfig *clients.Config

	ProviderConfig *provider.Config
}

// TaskBaseWithoutClusterConfig -
type TaskBaseWithoutClusterConfig struct {
	Clients *clientset.ClientSet
}

// TaskExecuteResult - Task 运行结果数据结构实现该方法
type TaskExecuteResult interface {
	Result()
	Append(ctx context.Context, result TaskExecuteResult) TaskExecuteResult
}

type ClusterComponentProber struct {
	K8SClient kubernetes.Interface
	Prober    *ReadinessProber
}

type ReadinessProber struct {
	url    string
	getter *http.Client
}

func NewReadinessProber(ctx context.Context, kubeconfig string) (*ReadinessProber, error) {
	restConfig, err := clientcmd.RESTConfigFromKubeConfig([]byte(kubeconfig))
	if err != nil {
		return nil, err
	}
	tp, err := rest.TransportFor(restConfig)
	if err != nil {
		return nil, err
	}

	httpClient := &http.Client{
		Transport: tp,
		Timeout:   restConfig.Timeout,
	}
	return &ReadinessProber{
		url:    restConfig.Host + "/readyz?verbose",
		getter: httpClient,
	}, nil
}

func (p *ReadinessProber) Probe(ctx context.Context) error {
	resp, err := p.getter.Get(p.url)
	if err != nil {
		logger.Errorf(ctx, "get readyz err: %v", err)
		return err
	}
	defer resp.Body.Close()

	data, err := io.ReadAll(resp.Body)
	if err != nil {
		logger.Errorf(ctx, "read data err: %v", err)
		return err
	}

	if resp.StatusCode != http.StatusOK {
		err := fmt.Errorf("cluster health check failed: \n%v", string(data))
		return err
	}
	return nil
}
