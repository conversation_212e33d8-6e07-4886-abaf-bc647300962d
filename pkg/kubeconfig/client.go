package kubeconfig

import (
	"bytes"
	"context"
	"encoding/base64"
	"errors"
	"text/template"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/utils"
)

const (
	oidcKubeConfig = `apiVersion: v1
clusters:
- cluster:
    certificate-authority-data: {{.ClusterCA}}
    server: https://{{.ServerIP}}
  name: kubernetes
contexts:
- context:
    cluster: kubernetes
    user: {{.UserID}}
  name: {{.UserID}}@kubernetes
current-context: {{.UserID}}@kubernetes
kind: Config
preferences: {}
users:
- name: {{.UserID}}
  user:
    auth-provider:
      config:
        client-id: {{.ClientID}}
        client-secret: {{.ClientSecret}}
        id-token: {{.IDToken}}
        idp-issuer-url: {{.IDPIssuerURL}}
        refresh-token: {{.RefreshToken}}
      name: oidc`
)

var oidcKubeConfigTpl = template.Must(template.New("oidcKubeConfigTpl").Parse(oidcKubeConfig))

type client struct{}

func NewClient(ctx context.Context) Interface {
	return &client{}
}

type OIDCKubeconfigVars struct {
	UserID       string
	ClientID     string // ClusterID
	ClusterCA    string // 读取 t_cce_ca_cert 中的 ca_pem 字段
	ServerIP     string // API-Server 地址(带端口)
	IDPIssuerURL string // CCE OIDC Issuer
	ClientSecret string
	RefreshToken string
	IDToken      string
}

func (c *client) GenKubeconfig(ctx context.Context, data any) (string, error) {
	if data == nil {
		return "", errors.New("data is nil")
	}

	// TODO: 通过 data 判断是 oidc 还是 x509
	vars, ok := data.(*OIDCKubeconfigVars)
	if !ok {
		return "", errors.New("data is not *OIDCKubeconfigVars")
	}

	clusterCA := []byte(vars.ClusterCA)
	vars.ClusterCA = base64.StdEncoding.EncodeToString(clusterCA)

	var kubeconfig bytes.Buffer
	if err := oidcKubeConfigTpl.Execute(&kubeconfig, data); err != nil {
		logger.Errorf(ctx, "fail to render kubeconfig with vars=%s: %v", utils.ToJSON(vars), err)
		return "", err
	}

	return kubeconfig.String(), nil
}
