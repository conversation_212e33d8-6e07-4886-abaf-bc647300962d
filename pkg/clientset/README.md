# 说明

统一 CCE 三方依赖 Client, 其他模块调用只需要初始化 ClientSet 即可.

```go
// ClientSet - 封装 Clients 和 Interface.
type ClientSet struct {
	*Clients
	Factory
}

// Clients - CCE 依赖全局三方 Client 集合
type Clients struct {
	// 数据库
	Model models.Interface

	// MetaCluster
	MetaClient meta.Interface

	// BCE SDK
	STSClient  sts.Interface
	VPCClient  vpc.Interface
    OIDCClient cceoidc.Interface
    ......

	// 公共配置
	Config *Config
}

// Factory - CCE 依赖三方 Client 集合
type Factory interface {
	NewK8SClient(ctx context.Context, kubeconfig string) (kubernetes.Interface, error)
	NewSSHClient(ctx context.Context, ip, password string) (exec.Interface, error)
    NewCertClient(ctx context.Context, mode ccetypes.AuthenticateMode, oidcEndpoing string) (cert.Interface, error)
    ......
}
```