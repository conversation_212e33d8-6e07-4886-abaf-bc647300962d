// Copyright 2020 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2020/08/27 10:53:00, by <EMAIL>, create
*/
/*
DESCRIPTION
CCE 依赖三方 Client, 主要包括:
1. 数据库;
2. BCE SDK;
3. MetaCluster K8S Client;

ps: 用户 k8s client 并非全局, 不能包含在该 package 中.
*/

package clientset

import (
	"context"
	"sync"

	"k8s.io/client-go/kubernetes"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/alert"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bcc"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bus"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/iam"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/logicbcc"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/logicimage"
	ccev1 "icode.baidu.com/baidu/cce-test/e2e-test/pkg/crd/api/v1"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/deployer/exec"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/deployer/types"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/kubeconfig"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/plugin"
	ccetypes "icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
)

// 单例模式
var clientSetFactory *factory
var clientSetFactoryMutex sync.Mutex

//go:generate mockgen -destination=./mock/mock_factory.go -package=mock icode.baidu.com/baidu/cce-test/e2e-test/pkg/clientset Factory

// Factory - CCE 依赖三方 Client 集合
type Factory interface {
	NewK8SClient(ctx context.Context, kubeconfig string) (kubernetes.Interface, error)
	NewSSHClient(ctx context.Context, ip, password string) (exec.Interface, error)
	NewKubeConfigClient(ctx context.Context) kubeconfig.Interface
	NewPluginClient(ctx context.Context, cluster *ccev1.Cluster, kubeconfig string) (plugin.Interface, error)
	NewLogicBCCClient(ctx context.Context, config *Config) (logicbcc.Interface, error)
	NewLogicImageClient(ctx context.Context, config *Config) (logicimage.Interface, error)
	NewAlertClient(ctx context.Context) alert.Alerter
	NewIAMClient(ctx context.Context, clientConfig *bce.Config) (iam.Interface, error)
	NewBusClient(ctx context.Context, clientConfig *bce.Config) (bus.Interface, error)

	NewBCCClient(ctx context.Context, config *Config) (bcc.Interface, error)
	NewExecClient(ctx context.Context, instance *types.Instance, config *types.Config) (exec.Interface, ccetypes.InstanceDeployType, error)
}

// factory - 实现 clientset.Factory
type factory struct {
}
