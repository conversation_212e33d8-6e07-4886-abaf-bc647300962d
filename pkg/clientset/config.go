// Copyright 2020 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2020/08/27 10:53:00, by <EMAIL>, create
*/
/*
DESCRIPTION
初始化 clientset.Clients 依赖配置
*/

package clientset

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"os"
	"sync"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/resmanager"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	ccetypes "icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
)

// 单例模式
var clientSetConfig *Config
var configMutex sync.Mutex

// Config - 定义初始化 Clients 依赖配置
type Config struct {
	Region string `json:"Region"`

	// Endpoints

	STSEndpoint                string                                `json:"STSEndpoint"`
	VPCEndpoint                string                                `json:"VPCEndpoint"`
	IAMEndpoint                string                                `json:"IAMEndpoint"`
	PriceEndpoint              string                                `json:"PriceEndpoint"`
	InternalBLBEndpoint        string                                `json:"InternalBLBEndpoint"`
	BCCLogicEndpoint           string                                `json:"BCCLogicEndpoint"`
	BCCLogicalEndpoint         string                                `json:"BCCLogicalEndpoint"`
	UserSettingEndpoint        string                                `json:"UserSettingEndpoint"`
	QuotaCenterEndpoint        string                                `json:"QuotaCenterEndpoint"`
	QualifyEndpoint            string                                `json:"QualifyEndpoint"`
	FinanceEndpoint            string                                `json:"FinanceEndpoint"`
	CPromEndpoint              string                                `json:"CPromEndpoint"`
	ImageLogicEndpoint         string                                `json:"ImageLogicEndpoint"`
	MySQLEndpoint              string                                `json:"MySQLEndpoint"`
	OIDCEndpoint               string                                `json:"OIDCEndpoint"`
	NeutronEndpoint            string                                `json:"NeutronEndpoint"`
	BLBOpenAPIEndpoint         string                                `json:"BLBOpenAPIEndpoint"`
	AppBLBOpenAPIEndpoint      string                                `json:"AppBLBOpenAPIEndpoint"`
	BLSEndpoint                string                                `json:"BLSEndpoint"`
	BLSInternalEndpoint        string                                `json:"BLSInternalEndpoint"`
	CCEOIDCIssuer              string                                `json:"CCEOIDCIssuer"`
	ResourceManagerEndpoint    string                                `json:"ResourceManagerEndpoint"`
	ResourceManagerKafkaConfig resmanager.ResourceManagerKafkaConfig `json:"ResourceManagerKafkaConfig"`
	MetaKubeConfig             string                                `json:"MetaKubeConfig,omitempty"` // 默认不传, 使用 ServiceAccount

	ServiceName string `json:"ServiceName"`

	ServicePassword string `json:"ServicePassword"`
	ServiceRoleName string `json:"ServiceRoleName"`

	ECCREndpoint string `json:"ECCREndpoint"`

	// BCE SDK 超时时间
	BCESDKTimeout int `json:"BCESDKTimeout"`

	CCEV1Endpoint string `json:"CCEV1Endpoint"` // cce-service endpoint
}

// ConfigType - 初始化配置方式
type ConfigType string

const (
	// ConfigTypeRegion - 使用该 Region 默认配置, 简化初始化 Config 流程
	ConfigTypeRegion ConfigType = "region"

	// ConfigTypePath - 使用文件初始化 Config 配置
	ConfigTypePath ConfigType = "path"
)

// NewConfig - 读取配置并返回
//
// PARAMS:
//   - ctx: The context to trace request
//   - path: 配置文件路径, 如果 path 为空, 使用默认配置
//
// RETURNS:
//
//	error: nil if succeed, error if fail
func NewConfig(ctx context.Context, t ConfigType, value string) (*Config, error) {
	configMutex.Lock()
	defer configMutex.Unlock()

	if clientSetConfig != nil {
		return clientSetConfig, nil
	}

	// 按地域读取配置
	if t == ConfigTypeRegion {
		logger.Infof(ctx, "ConfigType=%s region=%s", t, value)

		region := ccetypes.Region(value)

		// 检查 region 是否符合要求
		if _, ok := ccetypes.SupportedRegion[region]; !ok {
			logger.Errorf(ctx, "Region %s not supported", region)
			return nil, fmt.Errorf("Region %s not supported", region)
		}

		config, err := getConfigByRegion(ctx, region)
		if err != nil {
			logger.Errorf(ctx, "getConfigByRegion failed: %s", err)
			return nil, err
		}

		// 检查配置
		if err := checkConfig(ctx, config); err != nil {
			logger.Errorf(ctx, "casesConfig failed: %v", err)
			return nil, err
		}

		clientSetConfig = config

		return clientSetConfig, nil
	}

	// 按配置文件读取配置
	logger.Infof(ctx, "ConfigType=%s path=%s", t, value)
	path := value

	// 获取配置文件
	exist, err := pathExists(path)
	if err != nil {
		logger.Errorf(ctx, "read config file %v failed: %v", path, err)
		return nil, err
	}

	if !exist {
		msg := fmt.Sprintf("config file %v not exists", path)
		logger.Errorf(ctx, msg)

		return nil, fmt.Errorf(msg)
	}

	// 读取配置文件
	content, err := os.ReadFile(path) // just pass the file name
	if err != nil {
		logger.Errorf(ctx, "ReadFile %s failed: %s", path, err)
		return nil, err
	}

	// 序列化
	var config Config
	if err := json.Unmarshal(content, &config); err != nil {
		logger.Errorf(ctx, "Unmarshal failed: %s", err)
		return nil, err
	}

	// 检查配置
	if err := checkConfig(ctx, &config); err != nil {
		logger.Errorf(ctx, "casesConfig failed: %v", err)
		return nil, err
	}

	config.BCESDKTimeout = 15

	clientSetConfig = &config

	return clientSetConfig, nil
}

// pathExists - 检查配置文件是否存在
func pathExists(path string) (bool, error) {
	if path == "" {
		return false, errors.New("config path is nil")
	}

	_, err := os.Stat(path)
	if err == nil {
		return true, nil
	}

	if os.IsNotExist(err) {
		return false, nil
	}

	return false, err
}

// checkConfig - 检查配置
func checkConfig(ctx context.Context, config *Config) error {
	if config == nil {
		return errors.New("config is nil")
	}

	if config.Region == "" {
		return errors.New("Region is nil")
	}

	if config.STSEndpoint == "" {
		return errors.New("STSEndpoint is nil")
	}

	if config.VPCEndpoint == "" {
		return errors.New("VPCEndpoint is nil")
	}

	if config.OIDCEndpoint == "" {
		return errors.New("OIDCEndpoint is nil")
	}

	if config.IAMEndpoint == "" {
		return errors.New("IAMEndpoint is nil")
	}

	if config.InternalBLBEndpoint == "" {
		return errors.New("InternalBLBEndpoint is nil")
	}

	if config.CCEOIDCIssuer == "" {
		return errors.New("CCEOIDCIssuer is nil")
	}

	if config.MySQLEndpoint == "" {
		return errors.New("MySQLEndpoint is nil")
	}

	if config.ServiceName == "" {
		return errors.New("ServiceName is nil")
	}

	if config.ServicePassword == "" {
		return errors.New("ServicePassword is nil")
	}

	if config.ServiceRoleName == "" {
		return errors.New("ServiceRoleName is nil")
	}

	if config.BLBOpenAPIEndpoint == "" {
		return errors.New("BLBOpenAPIEndpoint is empty")
	}

	if config.AppBLBOpenAPIEndpoint == "" {
		return errors.New("AppBLBOpenAPIEndpoint is empty")
	}

	return nil
}

// getConfigByRegion - 通过地域获取 Config 配置
func getConfigByRegion(ctx context.Context, region ccetypes.Region) (*Config, error) {
	switch region {
	case ccetypes.RegionSandbox:
		return sandboxConfig, nil
	case ccetypes.RegionGZTest:
		return gztestConfig, nil
	case ccetypes.RegionBJ:
		return bjConfig, nil
	case ccetypes.RegionGZ:
		return gznsConfig, nil
	case ccetypes.RegionSU:
		return szthConfig, nil
	case ccetypes.RegionBD:
		return bdblConfig, nil
	case ccetypes.RegionFWH:
		return whggConfig, nil
	case ccetypes.RegionHKG:
		return hkgConfig, nil
	case ccetypes.RegionCNHZPro:
		return cnhzproConfig, nil
	case ccetypes.RegionCNJD:
		return cnjdConfig, nil
	}

	return nil, fmt.Errorf("unsupported region: %s", region)
}
