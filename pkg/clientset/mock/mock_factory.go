// Code generated by MockGen. DO NOT EDIT.
// Source: icode.baidu.com/baidu/cce-test/e2e-test/pkg/clientset (interfaces: Factory)

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"

	models "icode.baidu.com/baidu/cce-test/e2e-test/internal/models"
	alert "icode.baidu.com/baidu/cce-test/e2e-test/pkg/alert"
	bcc "icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bcc"
	bce "icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
	bus "icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bus"
	iam "icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/iam"
	logicbcc "icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/logicbcc"
	logicimage "icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/logicimage"
	clientset "icode.baidu.com/baidu/cce-test/e2e-test/pkg/clientset"
	v1 "icode.baidu.com/baidu/cce-test/e2e-test/pkg/crd/api/v1"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/deployer/exec"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/deployer/types"
	kubeconfig "icode.baidu.com/baidu/cce-test/e2e-test/pkg/kubeconfig"
	plugin "icode.baidu.com/baidu/cce-test/e2e-test/pkg/plugin"
	ccetypes "icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
	kubernetes "k8s.io/client-go/kubernetes"
)

// MockFactory is a mock of Factory interface.
type MockFactory struct {
	ctrl     *gomock.Controller
	recorder *MockFactoryMockRecorder
}

// MockFactoryMockRecorder is the mock recorder for MockFactory.
type MockFactoryMockRecorder struct {
	mock *MockFactory
}

// NewMockFactory creates a new mock instance.
func NewMockFactory(ctrl *gomock.Controller) *MockFactory {
	mock := &MockFactory{ctrl: ctrl}
	mock.recorder = &MockFactoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockFactory) EXPECT() *MockFactoryMockRecorder {
	return m.recorder
}

// NewAlertClient mocks base method.
func (m *MockFactory) NewAlertClient(arg0 context.Context) alert.Alerter {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NewAlertClient", arg0)
	ret0, _ := ret[0].(alert.Alerter)
	return ret0
}

// NewAlertClient indicates an expected call of NewAlertClient.
func (mr *MockFactoryMockRecorder) NewAlertClient(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NewAlertClient", reflect.TypeOf((*MockFactory)(nil).NewAlertClient), arg0)
}

// NewBCCClient mocks base method.
func (m *MockFactory) NewBCCClient(arg0 context.Context, arg1 *clientset.Config) (bcc.Interface, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NewBCCClient", arg0, arg1)
	ret0, _ := ret[0].(bcc.Interface)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// NewBCCClient indicates an expected call of NewBCCClient.
func (mr *MockFactoryMockRecorder) NewBCCClient(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NewBCCClient", reflect.TypeOf((*MockFactory)(nil).NewBCCClient), arg0, arg1)
}

// NewBusClient mocks base method.
func (m *MockFactory) NewBusClient(arg0 context.Context, arg1 *bce.Config) (bus.Interface, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NewBusClient", arg0, arg1)
	ret0, _ := ret[0].(bus.Interface)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// NewBusClient indicates an expected call of NewBusClient.
func (mr *MockFactoryMockRecorder) NewBusClient(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NewBusClient", reflect.TypeOf((*MockFactory)(nil).NewBusClient), arg0, arg1)
}

// NewExecClient mocks base method.
func (m *MockFactory) NewExecClient(ctx context.Context, instance *types.Instance, config *types.Config) (exec.Interface, ccetypes.InstanceDeployType, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NewExecClient", ctx, instance, config)
	ret0, _ := ret[0].(exec.Interface)
	ret1, _ := ret[1].(ccetypes.InstanceDeployType)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// NewExecClient indicates an expected call of NewExecClient.
func (mr *MockFactoryMockRecorder) NewExecClient(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NewExecClient", reflect.TypeOf((*MockFactory)(nil).NewExecClient), arg0, arg1, arg2)
}

// NewIAMClient mocks base method.
func (m *MockFactory) NewIAMClient(arg0 context.Context, arg1 *bce.Config) (iam.Interface, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NewIAMClient", arg0, arg1)
	ret0, _ := ret[0].(iam.Interface)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// NewIAMClient indicates an expected call of NewIAMClient.
func (mr *MockFactoryMockRecorder) NewIAMClient(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NewIAMClient", reflect.TypeOf((*MockFactory)(nil).NewIAMClient), arg0, arg1)
}

// NewK8SClient mocks base method.
func (m *MockFactory) NewK8SClient(arg0 context.Context, arg1 string) (kubernetes.Interface, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NewK8SClient", arg0, arg1)
	ret0, _ := ret[0].(kubernetes.Interface)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// NewK8SClient indicates an expected call of NewK8SClient.
func (mr *MockFactoryMockRecorder) NewK8SClient(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NewK8SClient", reflect.TypeOf((*MockFactory)(nil).NewK8SClient), arg0, arg1)
}

// NewK8SClientWithClusterID mocks base method.
func (m *MockFactory) NewK8SClientWithClusterID(arg0 context.Context, arg1 string, arg2 models.Interface) (kubernetes.Interface, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NewK8SClientWithClusterID", arg0, arg1, arg2)
	ret0, _ := ret[0].(kubernetes.Interface)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// NewK8SClientWithClusterID indicates an expected call of NewK8SClientWithClusterID.
func (mr *MockFactoryMockRecorder) NewK8SClientWithClusterID(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NewK8SClientWithClusterID", reflect.TypeOf((*MockFactory)(nil).NewK8SClientWithClusterID), arg0, arg1, arg2)
}

// NewKubeConfigClient mocks base method.
func (m *MockFactory) NewKubeConfigClient(arg0 context.Context) kubeconfig.Interface {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NewKubeConfigClient", arg0)
	ret0, _ := ret[0].(kubeconfig.Interface)
	return ret0
}

// NewKubeConfigClient indicates an expected call of NewKubeConfigClient.
func (mr *MockFactoryMockRecorder) NewKubeConfigClient(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NewKubeConfigClient", reflect.TypeOf((*MockFactory)(nil).NewKubeConfigClient), arg0)
}

// NewLogicBCCClient mocks base method.
func (m *MockFactory) NewLogicBCCClient(arg0 context.Context, arg1 *clientset.Config) (logicbcc.Interface, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NewLogicBCCClient", arg0, arg1)
	ret0, _ := ret[0].(logicbcc.Interface)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// NewLogicBCCClient indicates an expected call of NewLogicBCCClient.
func (mr *MockFactoryMockRecorder) NewLogicBCCClient(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NewLogicBCCClient", reflect.TypeOf((*MockFactory)(nil).NewLogicBCCClient), arg0, arg1)
}

// NewLogicImageClient mocks base method.
func (m *MockFactory) NewLogicImageClient(arg0 context.Context, arg1 *clientset.Config) (logicimage.Interface, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NewLogicImageClient", arg0, arg1)
	ret0, _ := ret[0].(logicimage.Interface)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// NewLogicImageClient indicates an expected call of NewLogicImageClient.
func (mr *MockFactoryMockRecorder) NewLogicImageClient(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NewLogicImageClient", reflect.TypeOf((*MockFactory)(nil).NewLogicImageClient), arg0, arg1)
}

// NewPluginClient mocks base method.
func (m *MockFactory) NewPluginClient(arg0 context.Context, arg1 *v1.Cluster, arg2 string) (plugin.Interface, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NewPluginClient", arg0, arg1, arg2)
	ret0, _ := ret[0].(plugin.Interface)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// NewPluginClient indicates an expected call of NewPluginClient.
func (mr *MockFactoryMockRecorder) NewPluginClient(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NewPluginClient", reflect.TypeOf((*MockFactory)(nil).NewPluginClient), arg0, arg1, arg2)
}

// NewSSHClient mocks base method.
func (m *MockFactory) NewSSHClient(arg0 context.Context, arg1, arg2 string) (exec.Interface, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NewSSHClient", arg0, arg1, arg2)
	ret0, _ := ret[0].(exec.Interface)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// NewSSHClient indicates an expected call of NewSSHClient.
func (mr *MockFactoryMockRecorder) NewSSHClient(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NewSSHClient", reflect.TypeOf((*MockFactory)(nil).NewSSHClient), arg0, arg1, arg2)
}
