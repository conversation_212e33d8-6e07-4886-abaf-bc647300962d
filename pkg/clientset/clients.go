// Copyright 2020 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2020/08/27 10:53:00, by <EMAIL>, create
*/
/*
DESCRIPTION
CCE 依赖三方 Client, 主要包括:
1. 数据库;
2. BCE SDK;
3. MetaCluster K8S Client;

ps: 用户 k8s client 并非全局, 不能包含在该 package 中.
*/

package clientset

import (
	"context"
	"errors"
	"sync"
	"time"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/appblb"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/blb"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/cceoidc"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/cprom"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/eccr"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/finance"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/internalblb"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/internalbls"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/logicalbcc"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/logicbcc"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/qualify"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/quota"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/resmanager"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/retrypolicy"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/usersetting"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/vpc"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/k8s/meta"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/sts"
)

// 单例模式
var clients *Clients
var clientsMutex sync.Mutex

// Clients - CCE 依赖全局三方 Client 集合
type Clients struct {
	// MetaCluster
	MetaClient meta.Interface

	// BCE SDK
	STSClient sts.Interface

	VPCClient             vpc.Interface
	OIDCClient            cceoidc.Interface
	InternalBLBClient     internalblb.Interface
	LogicBCCClient        logicbcc.Interface
	LogicalBCCClient      logicalbcc.Interface
	UserClient            usersetting.Interface
	QuotaCenterClient     quota.Interface
	QualifyClient         qualify.Interface
	FinanceClient         finance.Interface
	OpenBLBClient         blb.Interface
	OpenAppBLBClient      appblb.Interface
	CPromClient           cprom.Interface
	BLSClient             internalbls.Interface
	ECCRClient            eccr.Interface
	ResourceManagerClient resmanager.Interface
}

// NewClients - 初始化 Clients
func NewClients(ctx context.Context, config *Config) (*Clients, error) {
	clientsMutex.Lock()
	defer clientsMutex.Unlock()

	if clients != nil {
		return clients, nil
	}

	if config == nil {
		return nil, errors.New("config is nil")
	}

	// 初始化 metaclient
	metaclient, err := meta.NewClient(ctx, config.MetaKubeConfig)
	if err != nil {
		logger.Errorf(ctx, "meta.NewClient failed: %v", err)
		return nil, err
	}

	// 初始化 vpcClient
	vpcClient := vpc.NewClient(bceConfig(ctx, config.VPCEndpoint, config.BCESDKTimeout, config.Region))
	vpcClient.SetDebug(true)

	// 初始化 stsClient
	stsClient := sts.NewClient(ctx,
		bceConfig(ctx, config.STSEndpoint, config.BCESDKTimeout, config.Region),
		bceConfig(ctx, config.IAMEndpoint, config.BCESDKTimeout, config.Region),
		config.ServiceRoleName,
		config.ServiceName,
		config.ServicePassword,
	)

	// 初始化 oidcClient
	oidcClient := cceoidc.NewClient(bceConfig(ctx, config.OIDCEndpoint, 5, config.Region))

	// 初始化 internalblbClient
	internalBLBClient := internalblb.NewClient(bceConfig(ctx, config.InternalBLBEndpoint, 5, config.Region))

	logicBCCClient := logicbcc.NewClient(bceConfig(ctx, config.BCCLogicEndpoint, 60, config.Region))
	logicBCCClient.SetDebug(true)

	logicalBCCClient := logicalbcc.NewClient(ctx, bceConfig(ctx, config.BCCLogicalEndpoint, 30, config.Region))
	logicalBCCClient.SetDebug(true)

	userClient := usersetting.NewClient(bceConfig(ctx, config.UserSettingEndpoint, config.BCESDKTimeout, config.Region))
	userClient.SetDebug(true)

	quotaCenterClient := quota.NewClient(ctx, bceConfig(ctx, config.QuotaCenterEndpoint, config.BCESDKTimeout, config.Region))
	quotaCenterClient.SetDebug(true)

	qualifyClient := qualify.NewClient(bceConfig(ctx, config.QualifyEndpoint, config.BCESDKTimeout, config.Region))
	qualifyClient.SetDebug(true)

	financeClient := finance.NewClient(bceConfig(ctx, config.FinanceEndpoint, config.BCESDKTimeout, config.Region))
	financeClient.SetDebug(true)

	cpromClient := cprom.NewClient(bceConfig(ctx, config.CPromEndpoint, config.BCESDKTimeout, config.Region))
	cpromClient.SetDebug(true)

	eccrClient := eccr.NewClient(bceConfig(ctx, config.ECCREndpoint, config.BCESDKTimeout, config.Region).
		WithApiVersion(ctx, "v1"))
	eccrClient.SetDebug(true)

	blsClient := internalbls.NewClient(
		bceConfig(ctx, config.BLSInternalEndpoint, config.BCESDKTimeout, config.Region),
		bceConfig(ctx, config.BLSEndpoint, config.BCESDKTimeout, config.Region),
	)
	blsClient.SetDebug(true)
	clients = &Clients{
		MetaClient:        metaclient,
		STSClient:         stsClient,
		VPCClient:         vpcClient,
		OIDCClient:        oidcClient,
		InternalBLBClient: internalBLBClient,
		LogicBCCClient:    logicBCCClient,
		LogicalBCCClient:  logicalBCCClient,
		UserClient:        userClient,
		QuotaCenterClient: quotaCenterClient,
		QualifyClient:     qualifyClient,
		FinanceClient:     financeClient,
		CPromClient:       cpromClient,
		ECCRClient:        eccrClient,
		BLSClient:         blsClient,
	}
	resmanagerClient, err := resmanager.NewClient(ctx, bceConfig(ctx, config.ResourceManagerEndpoint, config.BCESDKTimeout, config.Region), &config.ResourceManagerKafkaConfig)
	if err != nil {
		logger.Errorf(ctx, "new resource manager client err: %v", err)
		return nil, err
	}
	resmanagerClient.SetDebug(true)
	clients.ResourceManagerClient = resmanagerClient

	if config.BLBOpenAPIEndpoint != "" {
		clients.OpenBLBClient = blb.NewClient(bceConfig(ctx, config.BLBOpenAPIEndpoint, config.BCESDKTimeout, config.Region))
	} else {
		logger.Warnf(ctx, "BLBOpenAPIEndpoint is empty, skip creating OpenBLBClient")
	}

	if config.AppBLBOpenAPIEndpoint != "" {
		clients.OpenAppBLBClient = appblb.NewClient(bceConfig(ctx, config.AppBLBOpenAPIEndpoint, config.BCESDKTimeout, config.Region))
	} else {
		logger.Warnf(ctx, "AppBLBOpenAPIEndpoint is empty, skip creating OpenAppBLBClient")
	}

	return clients, nil
}

// bceConfig 返回 *bce.Config 配置
func bceConfig(ctx context.Context, endpoint string, timeout int, region string) *bce.Config {
	if endpoint == "" {
		logger.Errorf(ctx, "Generate bce.Config failed: endpoint is empty")
	}

	return &bce.Config{
		Checksum:    true,
		Endpoint:    endpoint,
		Timeout:     time.Duration(timeout) * time.Second,
		Region:      region,
		RetryPolicy: retrypolicy.NewIntervalRetryPolicy(ctx, 0, 0), // 不做重试
	}
}
