// Copyright 2020 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2020/08/27 10:53:00, by <EMAIL>, create
*/
/*
DESCRIPTION
各地域配置
*/

package clientset

// TODO: 如果从默认配置读取, 需要使用 initContainer 等方式
var (
	sandboxConfig = &Config{
		Region:              "sandbox",
		STSEndpoint:         "sts.bj.internal-qasandbox.baidu-int.com:8586/v1",
		VPCEndpoint:         "bcc.bj.qasandbox.baidu-int.com",
		OIDCEndpoint:        "************:8848",
		IAMEndpoint:         "iam.bj.internal-qasandbox.baidu-int.com/v3",
		InternalBLBEndpoint: "blb.internal-qasandbox.baidu-int.com/meta-server/index.php",
		BCCLogicEndpoint:    "logic-bcc.internal-qasandbox.baidu-int.com",
		ImageLogicEndpoint:  "logic-bcc.internal-qasandbox.baidu-int.com",

		MySQLEndpoint: "bcetest:bcetest@tcp(************:6666)/cce_service?charset=utf8&parseTime=True&loc=Local",

		MetaKubeConfig: "", // 使用 ServiceAccount

		ServiceName:     "cce",
		ServicePassword: "nHrpnEDTIOonHC1QTOLGDOSjKbqfwKKy",
		ServiceRoleName: "BceServiceRole_SERVICE_CCE",

		CCEOIDCIssuer: "https://************:8793/oidc",

		BLBOpenAPIEndpoint:    "blb.bj.qasandbox.baidu-int.com",
		AppBLBOpenAPIEndpoint: "blb.bj.qasandbox.baidu-int.com/v1",
	}

	gztestConfig = &Config{
		Region:              "gztest",
		STSEndpoint:         "sts.gz.iam.sdns.baidu.com:8586/v1",
		VPCEndpoint:         "bcc.gz.baidubce.com",
		OIDCEndpoint:        "10.164.32.142:8848",
		IAMEndpoint:         "iam.gz.bce-internal.baidu.com/v3",
		InternalBLBEndpoint: "blb.gz.bce-internal.baidu.com",
		BCCLogicEndpoint:    "bcclogic.gz.bce-internal.baidu.com",
		ImageLogicEndpoint:  "bcclogic.gz.bce-internal.baidu.com",

		MySQLEndpoint: "cce_service_w:6fS_5tvWNA@tcp(10.169.25.222:6001)/bce_gzns_sandbox_cce_service?charset=utf8&parseTime=true",

		MetaKubeConfig: "", // 使用 ServiceAccount

		ServiceName:     "cce",
		ServicePassword: "c8QBqhOKxmGu0Sl2p13guMil3U2grbYt",
		ServiceRoleName: "BceServiceRole_SERVICE_CCE",

		CCEOIDCIssuer: "https://cce.gz.baidubce.com/gztest/oidc",

		BLBOpenAPIEndpoint:    "blb.gz.baidubce.com",
		AppBLBOpenAPIEndpoint: "blb.gz.baidubce.com/v1",
	}

	bjConfig = &Config{
		Region:              "bj",
		STSEndpoint:         "sts.bj.iam.sdns.baidu.com:8586/v1",
		VPCEndpoint:         "bcc.bj.baidubce.com",
		OIDCEndpoint:        "10.180.114.235:8848",
		IAMEndpoint:         "iam.bj.bce-internal.baidu.com/v3",
		InternalBLBEndpoint: "blb.bj.bce-internal.baidu.com",
		BCCLogicEndpoint:    "bcclogic.bce-internal.baidu.com",
		ImageLogicEndpoint:  "bcclogic.bce-internal.baidu.com",

		MySQLEndpoint: "cce_service_w:8bqvaLui3Pp3_nFt@tcp(10.11.87.54:6202)/cce_service?charset=utf8&parseTime=true",

		MetaKubeConfig: "", // 使用 ServiceAccount

		ServiceName:     "cce",
		ServicePassword: "c8QBqhOKxmGu0Sl2p13guMil3U2grbYt",
		ServiceRoleName: "BceServiceRole_SERVICE_CCE",

		CCEOIDCIssuer: "https://cce.bj.baidubce.com/oidc",

		BLBOpenAPIEndpoint:    "blb.bj.baidubce.com",
		AppBLBOpenAPIEndpoint: "blb.bj.baidubce.com/v1",
	}

	gznsConfig = &Config{
		Region:              "gz",
		STSEndpoint:         "sts.gz.iam.sdns.baidu.com:8586/v1",
		VPCEndpoint:         "bcc.gz.baidubce.com",
		OIDCEndpoint:        "10.169.25.216:8848",
		IAMEndpoint:         "iam.gz.bce-internal.baidu.com/v3",
		InternalBLBEndpoint: "blb.gz.bce-internal.baidu.com",
		BCCLogicEndpoint:    "bcclogic.gz.bce-internal.baidu.com",
		ImageLogicEndpoint:  "bcclogic.gz.bce-internal.baidu.com",

		MySQLEndpoint: "gz_cce_service_w:UJ5XrDr_rLcfRMau@tcp(10.169.25.222:6001)/gz_cce_service?charset=utf8&parseTime=true",

		MetaKubeConfig: "", // 使用 ServiceAccount

		ServiceName:     "cce",
		ServicePassword: "c8QBqhOKxmGu0Sl2p13guMil3U2grbYt",
		ServiceRoleName: "BceServiceRole_SERVICE_CCE",

		CCEOIDCIssuer: "https://cce.gz.baidubce.com/oidc",

		BLBOpenAPIEndpoint:    "blb.gz.baidubce.com",
		AppBLBOpenAPIEndpoint: "blb.gz.baidubce.com/v1",
	}

	szthConfig = &Config{
		Region:              "su",
		STSEndpoint:         "sts.su.iam.sdns.baidu.com:8586/v1",
		VPCEndpoint:         "bcc.su.baidubce.com",
		OIDCEndpoint:        "10.191.105.201:8848",
		IAMEndpoint:         "iam.su.bce-internal.baidu.com/v3",
		InternalBLBEndpoint: "blb.su.bce-internal.baidu.com",
		BCCLogicEndpoint:    "bcclogic.su.bce-internal.baidu.com",
		ImageLogicEndpoint:  "bcclogic.su.bce-internal.baidu.com",

		MySQLEndpoint: "sz_cce_service_w:Kxt6L_FGxmerRuPD@tcp(10.191.104.18:6300)/sz_cce_service?charset=utf8&parseTime=true",

		MetaKubeConfig: "", // 使用 ServiceAccount

		ServiceName:     "cce",
		ServicePassword: "c8QBqhOKxmGu0Sl2p13guMil3U2grbYt",
		ServiceRoleName: "BceServiceRole_SERVICE_CCE",

		CCEOIDCIssuer: "https://cce.su.baidubce.com/oidc",

		BLBOpenAPIEndpoint:    "blb.su.baidubce.com",
		AppBLBOpenAPIEndpoint: "blb.su.baidubce.com/v1",
	}

	bdblConfig = &Config{
		Region:              "bd",
		STSEndpoint:         "sts.bdbl.bce.baidu-int.com:8586/v1",
		VPCEndpoint:         "bcc.bd.baidubce.com",
		OIDCEndpoint:        "10.70.1.204:8848",
		IAMEndpoint:         "iam.bdbl.bce.baidu-int.com/v3",
		InternalBLBEndpoint: "blb.bdbl.bce.baidu-int.com",
		BCCLogicEndpoint:    "bcclogic.bdbl.bce.baidu-int.com",
		ImageLogicEndpoint:  "bcclogic.bdbl.bce.baidu-int.com",

		MySQLEndpoint: "cce_service_w:XjJPujGjSDb@tcp(10.70.0.178:5009)/bdbl_cce_service?charset=utf8&parseTime=true",

		MetaKubeConfig: "", // 使用 ServiceAccount

		ServiceName:     "cce",
		ServicePassword: "c8QBqhOKxmGu0Sl2p13guMil3U2grbYt",
		ServiceRoleName: "BceServiceRole_SERVICE_CCE",

		CCEOIDCIssuer: "https://cce.bd.baidubce.com/oidc",

		BLBOpenAPIEndpoint:    "blb.bd.baidubce.com",
		AppBLBOpenAPIEndpoint: "blb.bd.baidubce.com/v1",
	}

	whggConfig = &Config{
		Region:              "fwh",
		STSEndpoint:         "sts.fwh.bce.baidu-int.com:8586/v1",
		VPCEndpoint:         "bcc.fwh.baidubce.com",
		OIDCEndpoint:        "10.70.16.96:8848",
		IAMEndpoint:         "iam.fwh.bce.baidu-int.com/v3",
		InternalBLBEndpoint: "blb.fwh.bce.baidu-int.com",
		BCCLogicEndpoint:    "bcclogic.fwh.bce.baidu-int.com",
		ImageLogicEndpoint:  "bcclogic.fwh.bce.baidu-int.com",

		MySQLEndpoint: "cce_service_w:1bU_YVAgx2CCJrmDtP7BaqaEp@tcp(10.70.16.33:5201)/whgg_cce_service?charset=utf8&parseTime=true",

		MetaKubeConfig: "", // 使用 ServiceAccount

		ServiceName:     "cce",
		ServicePassword: "c8QBqhOKxmGu0Sl2p13guMil3U2grbYt",
		ServiceRoleName: "BceServiceRole_SERVICE_CCE",

		CCEOIDCIssuer: "https://cce.fwh.baidubce.com/oidc",

		BLBOpenAPIEndpoint:    "blb.fwh.baidubce.com",
		AppBLBOpenAPIEndpoint: "blb.fwh.baidubce.com/v1",
	}

	hkgConfig = &Config{
		Region:              "hkg",
		STSEndpoint:         "sts.hkg.bce.baidu-int.com:8586/v1",
		VPCEndpoint:         "bcc.hkg.baidubce.com",
		OIDCEndpoint:        "10.70.8.94:8848",
		IAMEndpoint:         "iam.hkg.bce.baidu-int.com/v3",
		InternalBLBEndpoint: "blb-hkg-bce-internal.baidu-int.com",
		BCCLogicEndpoint:    "bcclogic.hkg.bce.baidu-int.com",
		ImageLogicEndpoint:  "bcclogic.hkg.bce.baidu-int.com",

		MySQLEndpoint: "cce_service_w:mAiQdc9rbwEWtAJbRwiqxKU@tcp(10.250.128.101:5208)/hkg_cce_service?charset=utf8&parseTime=true",

		MetaKubeConfig: "", // 使用 ServiceAccount

		ServiceName:     "cce",
		ServicePassword: "c8QBqhOKxmGu0Sl2p13guMil3U2grbYt",
		ServiceRoleName: "BceServiceRole_SERVICE_CCE",

		CCEOIDCIssuer: "https://cce.hkg.baidubce.com/oidc",

		BLBOpenAPIEndpoint:    "blb.hkg.baidubce.com",
		AppBLBOpenAPIEndpoint: "blb.hkg.baidubce.com/v1",
	}

	cnhzproConfig = &Config{
		Region:              "cnhzpro",
		STSEndpoint:         "sts.agilecloud.com:8586/v1",
		VPCEndpoint:         "bcc.cnhzpro.agilecloud.com:8680",
		OIDCEndpoint:        "10.180.114.235:8848",
		IAMEndpoint:         "iam.agilecloud.com:35357/v3",
		InternalBLBEndpoint: "blb-meta.cnhzpro.agilecloud.com:8885",
		BCCLogicEndpoint:    "bcclogic.internal.cnhzpro.agilecloud.com:8680",
		ImageLogicEndpoint:  "bcclogic.internal.cnhzpro.agilecloud.com:8680",

		MySQLEndpoint: "cce_service_w:mhxzkhl@2018@tcp(xdb-paas.cnhzpro.agilecloud.com:6203)/cce_service?charset=utf8&parseTime=true",

		MetaKubeConfig: "", // 不能使用 ServiceAccount

		ServiceName:     "cce",
		ServicePassword: "DokkcjVbq1baUkNg80OhaFE3Q1i6hAa1",
		ServiceRoleName: "BceServiceRole_SERVICE_CCE",

		CCEOIDCIssuer: "https://cce.agilecloud.baidubce.com/oidc",

		BLBOpenAPIEndpoint:    "blb.bj.qasandbox.baidu-int.com",
		AppBLBOpenAPIEndpoint: "blb.bj.qasandbox.baidu-int.com/v1",
	}

	cnjdConfig = &Config{
		Region:              "cnjd",
		STSEndpoint:         "sts.anicecity.org:8586/v1",
		VPCEndpoint:         "bcc.cnjd.anicecity.org:8680",
		OIDCEndpoint:        "10.180.114.235:8848",
		IAMEndpoint:         "iam.anicecity.org:35357/v3",
		InternalBLBEndpoint: "blb-meta.cnjd.anicecity.org:8885",
		BCCLogicEndpoint:    "bcclogic.internal.cnjd.anicecity.org:8680",
		ImageLogicEndpoint:  "bcclogic.internal.cnjd.anicecity.org:8680",

		MySQLEndpoint: "cce_service_w:mhxzkhl@2018@tcp(xdb-iaas.cnjd.anicecity.org:6203)/cce_service?charset=utf8&parseTime=true",

		MetaKubeConfig: "", // 不能使用 ServiceAccount

		ServiceName:     "cce",
		ServicePassword: "Nubbngpdv8eP9Mw29mgHbWSwZeBldAa8",
		ServiceRoleName: "BceServiceRole_SERVICE_CCE",

		CCEOIDCIssuer: "https://cce.agilecloud.baidubce.com/oidc",

		BLBOpenAPIEndpoint:    "blb.bj.qasandbox.baidu-int.com",
		AppBLBOpenAPIEndpoint: "blb.bj.qasandbox.baidu-int.com/v1",
	}
)
