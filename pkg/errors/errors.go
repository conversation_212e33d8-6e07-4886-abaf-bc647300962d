//go:generate go run gen_error_types.go

package errors

import (
	"context"
	"errors"
	"fmt"

	"github.com/google/go-cmp/cmp"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/errorcode"
)

// NOTE: Add more common errors here.
var CommonErrors = []string{
	"NotExist",
	"AlreadyExists",
}

// Error is an error type with errorcode shortcut method.
type Error interface {
	error

	// WithCode embeds errorcode into the Error.
	WithCode(*errorcode.ErrorCode) error
}

type ErrType interface {
	New(ctx context.Context, format string, a ...any) Error
	Wrap(ctx context.Context, err error, msg ...string) Error
	Wrapf(ctx context.Context, err error, format string, a ...any) Error
	As(err error) (Error, bool)
	Is(err error) bool
	Unwrap() error
	Type() any
}

// ErrTypeName represents an argument implementation for building ErrType.
type ErrTypeName string

const (
	Unknown ErrTypeName = "UNKNOWN"
)

// Following is a solution for custom error type.
// - Support error warp.
// - Support errorcode shortcut.
//
// Example:
// Say we have two custom error types: error type A and error type B.
//
// ```
// definition：

// var (
//     AError = NewErrType(ErrTypeName("A"))
//     BError = NewErrType(ErrTypeName("B"))
// )

// usage：

// err := AError.New(ctx, "some A error")
// AError.Is(err) // true
// BError.Is(err) // false
//
// err = BError.Wrap(ctx, err)
// AError.Is(err) // true
// BError.Is(err) // true
// ```

// errType represents an error type as well as its factory.
// It implements both ErrType and Error.
// The error generated by factory is also an errType object underlying, with its method sets
// limited by the corresponding interface.
type errType struct {
	typ any
	ctx context.Context

	// Both reason and err are empty in abstract ErrType.
	// Either reason or err is present in a concrete error, but never both.
	// reason is for newly built error, and err is for the case basing on existing error(wrap).
	reason string

	err error

	// msg is set along with err if necessary, indicate the extra context of err
	// optional
	msg string
}

// NewErrType builds a new error type and returns its factory.
func NewErrType(typ any) ErrType {
	return &errType{
		typ: typ,
	}
}

// New builds a new error instance of e (error factory).
func (e *errType) New(ctx context.Context, format string, a ...any) Error {
	return &errType{
		typ:    e.typ,
		ctx:    ctx,
		reason: fmt.Sprintf(format, a...),
	}
}

// Wrap builds a new error object of type e from an existing error.
func (e *errType) Wrap(ctx context.Context, err error, msg ...string) Error {
	var m string
	if len(msg) > 0 {
		m = msg[0]
	}
	return &errType{
		typ: e.typ,
		ctx: ctx,
		err: err,
		msg: m,
	}
}

// Wrapf is like Wrap with format specifier support.
func (e *errType) Wrapf(ctx context.Context, err error, format string, a ...any) Error {
	return &errType{
		typ: e.typ,
		ctx: ctx,
		err: err,
		msg: fmt.Sprintf(format, a...),
	}
}

// WithCode implements Error interface.
func (e *errType) WithCode(code *errorcode.ErrorCode) error {
	return errorcode.NewError(code, e)
}

// Type returns the immediate error type of e (no Unwarp).
func (e *errType) Type() any {
	return e.typ
}

// Error implements error interface.
func (e *errType) Error() string {
	if e.err != nil {
		return e.err.Error()
	}
	return e.reason
}

// Unwrap implements errors.Wrapper interface.
func (e *errType) Unwrap() error {
	if e.err != nil {
		return e.err
	}
	return nil
}

// Is determins whether a error of the errType of e exists in err's chain.
func (e *errType) Is(err error) bool {
	_, ok := e.As(err)
	return ok
}

// As finds the first error in err's chain that matches type of e, and if so returns the error.
// If no match error can be found in err's chain, return nil and false.
func (e *errType) As(err error) (Error, bool) {
	var t *errType
	if errors.As(err, &t) {
		if cmp.Equal(t.typ, e.typ) {
			return t, true
		}
		if t.err == nil {
			return nil, false
		}
		// recursively find if a matched type equaling e can be found
		return e.As(t.err)
	}
	return nil, false
}
