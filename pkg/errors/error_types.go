// Copyright 2023 The baidubce Cloud Container Engine (CCE) AUTHORS. All rights reserved.

// Code generated by gen_error_types; DO NOT EDIT.

package errors

import (
	"context"
	"errors"
	"fmt"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/errorcode"
)

// ErrNotExist represents NotExist error.
type ErrNotExist struct {
	// ctx preserves the context when the error is built.
	ctx context.Context

	// of reason and err only one field is valid, depending on if the object is built by New* or To*
	reason string

	err error
}

// NewNotExist builds a new NotExist error.
func NewNotExist(ctx context.Context, format string, a ...any) Error {
	return &ErrNotExist{
		ctx:    ctx,
		reason: fmt.Sprintf(format, a...),
	}
}

// ToNotExist wraps err with NotExist error.
func ToNotExist(ctx context.Context, err error) Error {
	return &ErrNotExist{
		ctx: ctx,
		err: err,
	}
}

// IsNotExist assert if err is of NotExist error type.
func IsNotExist(err error) bool {
	var e *ErrNotExist
	return errors.As(err, &e)
}

// Error implements error interface.
func (e *ErrNotExist) Error() string {
	if e.err != nil {
		return e.err.Error()
	}
	return e.reason
}

// Unwrap implements errors.Wrapper interface.
func (e *ErrNotExist) Unwrap() error {
	return e.err
}

// WithCode implements Error interface.
func (e *ErrNotExist) WithCode(code *errorcode.ErrorCode) error {
	return errorcode.NewError(code, e)
}

// ErrAlreadyExists represents AlreadyExists error.
type ErrAlreadyExists struct {
	// ctx preserves the context when the error is built.
	ctx context.Context

	// of reason and err only one field is valid, depending on if the object is built by New* or To*
	reason string

	err error
}

// NewAlreadyExists builds a new AlreadyExists error.
func NewAlreadyExists(ctx context.Context, format string, a ...any) Error {
	return &ErrAlreadyExists{
		ctx:    ctx,
		reason: fmt.Sprintf(format, a...),
	}
}

// ToAlreadyExists wraps err with AlreadyExists error.
func ToAlreadyExists(ctx context.Context, err error) Error {
	return &ErrAlreadyExists{
		ctx: ctx,
		err: err,
	}
}

// IsAlreadyExists assert if err is of AlreadyExists error type.
func IsAlreadyExists(err error) bool {
	var e *ErrAlreadyExists
	return errors.As(err, &e)
}

// Error implements error interface.
func (e *ErrAlreadyExists) Error() string {
	if e.err != nil {
		return e.err.Error()
	}
	return e.reason
}

// Unwrap implements errors.Wrapper interface.
func (e *ErrAlreadyExists) Unwrap() error {
	return e.err
}

// WithCode implements Error interface.
func (e *ErrAlreadyExists) WithCode(code *errorcode.ErrorCode) error {
	return errorcode.NewError(code, e)
}
