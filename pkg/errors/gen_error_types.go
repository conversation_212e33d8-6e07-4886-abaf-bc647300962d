// Copyright 2020 The baidubce Cloud Container Engine (CCE) AUTHORS. All rights reserved.

//go:build ignore
// +build ignore

// gen_error_types generates all error types and related methods based on slice Errors.

// It is meant to be used by the baidubce Cloud Container Engine (CCE) authors in conjunction
// with the go generate tool before commiting code.

package main

import (
	"bytes"
	"errors"
	"flag"
	"go/format"
	"log"
	"os"
	"text/template"
	"time"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/errors"
)

var verbose = flag.Bool("v", false, "Print verbose log messages")

type templateData struct {
	filename string
	Year     int
	Errors   []string
	Tmpl     *template.Template
}

func (t *templateData) dump() error {
	if len(t.Errors) == 0 {
		logf("no error codes found, skip generating %s.", t.filename)
		return nil
	}

	if len(t.filename) == 0 {
		return errors.New("must specify dst filename")
	}

	var buf bytes.Buffer
	if err := t.Tmpl.Execute(&buf, t); err != nil {
		return err
	}

	clean, err := format.Source(buf.Bytes())
	if err != nil {
		return err
	}

	logf("Writing %v...", t.filename)
	return os.WriteFile(t.filename, clean, 0644)
}

func logf(fmt string, args ...any) {
	if *verbose {
		log.Printf(fmt, args...)
	}
}

func main() {
	flag.Parse()

	sourceData := &templateData{
		filename: "error_types.go",
		Year:     time.Now().Year(),
		Errors:   errors.CommonErrors,
		Tmpl:     template.Must(template.New("source").Parse(source)),
	}

	if err := sourceData.dump(); err != nil {
		log.Fatal(err)
	}

	testSourceData := &templateData{
		filename: "error_types_test.go",
		Year:     time.Now().Year(),
		Errors:   errors.CommonErrors,
		Tmpl:     template.Must(template.New("testSource").Parse(testSource)),
	}
	if err := testSourceData.dump(); err != nil {
		log.Fatal(err)
	}

	logf("Done.")
}

const (
	source = `// Copyright {{.Year}} The baidubce Cloud Container Engine (CCE) AUTHORS. All rights reserved.

// Code generated by gen_error_types; DO NOT EDIT.

package errors

import (
	"context"
	"errors"
	"fmt"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/errorcode"
)

{{range $index, $name := .Errors}}
// Err{{$name}} represents {{$name}} error.
type Err{{$name}} struct {
	// ctx preserves the context when the error is built.
	ctx context.Context
	// of reason and err only one field is valid, depending on if the object is built by New* or To*
	reason string
	err    error
}

// New{{$name}} builds a new {{$name}} error.
func New{{$name}}(ctx context.Context, format string, a ...interface{}) Error {
	return &Err{{$name}}{
		ctx:    ctx,
		reason: fmt.Sprintf(format, a...),
	}
}

// To{{$name}} wraps err with {{$name}} error.
func To{{$name}}(ctx context.Context, err error) Error {
	return &Err{{$name}}{
		ctx: ctx,
		err: err,
	}
}

// Is{{$name}} assert if err is of {{$name}} error type.
func Is{{$name}}(err error) bool {
	var e *Err{{$name}}
	return errors.As(err, &e)
}

// Error implements error interface.
func (e *Err{{$name}}) Error() string {
	if e.err != nil {
		return e.err.Error()
	}
	return e.reason
}

// Unwrap implements errors.Wrapper interface.
func (e *Err{{$name}}) Unwrap() error {
	return e.err
}

// WithCode implements Error interface.
func (e *Err{{$name}}) WithCode(code *errorcode.ErrorCode) error {
	return errorcode.NewError(code, e)
}
{{end}}
`
	testSource = `// Copyright {{.Year}} The baidubce Cloud Container Engine (CCE) AUTHORS. All rights reserved.

// Code generated by gen_error_types; DO NOT EDIT.

package errors

import (
	"context"
	"fmt"
	"testing"

	"gotest.tools/assert"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/errorcode"
)

{{range $index, $name := .Errors}}
func TestErr{{$name}}(t *testing.T) {
	ctx := context.TODO()
	var err error

	// wrapped by other error
	err = New{{$name}}(ctx, "Err{{$name}}")
	assert.Assert(t, Is{{$name}}(err))
	assert.Error(t, err, "Err{{$name}}")
	for i := 0; i < 3; i++ {
		err := fmt.Errorf("failed: %w", err)
		assert.Assert(t, Is{{$name}}(err))
		assert.ErrorContains(t, err, "Err{{$name}}")
	}

	// wrap other error
	err = To{{$name}}(ctx, newTest(ctx, "test error"))
	assert.Assert(t, Is{{$name}}(err))
	assert.Assert(t, isTest(err))
	assert.Error(t, err, "test error")
	for i := 0; i < 3; i++ {
		err = fmt.Errorf("failed: %w", err)
		assert.Assert(t, Is{{$name}}(err))
		assert.Assert(t, isTest(err))
		assert.ErrorContains(t, err, "test error")
	}

	// with errorcode
	err = To{{$name}}(ctx, newTest(ctx, "test error")).WithCode(errorcode.NewMalformedJSON("invalid"))
	assert.Assert(t, Is{{$name}}(err))
	assert.Assert(t, isTest(err))
	assert.Error(t, err, "test error")
	assert.DeepEqual(t, errorcode.GetCode(err), errorcode.NewMalformedJSON("invalid"))
	for i := 0; i < 3; i++ {
		err = fmt.Errorf("failed: %w", err)
		assert.Assert(t, Is{{$name}}(err))
		assert.Assert(t, isTest(err))
		assert.ErrorContains(t, err, "test error")
		assert.DeepEqual(t, errorcode.GetCode(err), errorcode.NewMalformedJSON("invalid"))
	}
}
{{end}}
`
)
