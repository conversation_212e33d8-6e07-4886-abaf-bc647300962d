/*

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package v1

import (
	"fmt"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	ccetypes "icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
)

const (
	// InstanceGroupFinalizer allows <PERSON><PERSON><PERSON><PERSON> to clean up CCE Instance resources associated with InstanceGroup before
	InstanceGroupFinalizer = "finalizer.k8s-instance-group.cce.baidubce.com"

	// InstanceGroupAPIVersion InstanceGroup CRD API version
	InstanceGroupAPIVersion = "cce.baidubce.com/v1"

	// InstanceGroupKind InstanceGroup CRD kind
	InstanceGroupKind = "InstanceGroup"

	// InstanceGroupResource InstanceGroup CRD resource
	InstanceGroupResource = "instancegroups"
)

// //// +kubebuilder:subresource:status
// subresource status 参考: https://kubernetes.io/docs/tasks/access-kubernetes-api/custom-resources/custom-resource-definitions/

// +kubebuilder:object:root=true
// +kubebuilder:printcolumn:name="GROUP_NAME",type=string,JSONPath=`.spec.instanceGroupName`
// +kubebuilder:printcolumn:name="Cluster_ID",type=string,JSONPath=`.spec.clusterID`
// +kubebuilder:printcolumn:name="Cluster_Role",type=string,JSONPath=`.spec.clusterRole`
// +kubebuilder:printcolumn:name="Replicas",type=integer,JSONPath=`.spec.replicas`
// +kubebuilder:printcolumn:name="Actual",type=integer,JSONPath=`.status.actualReplicas`
// +kubebuilder:printcolumn:name="Ready",type=integer,JSONPath=`.status.readyReplicas`
// +kubebuilder:printcolumn:name="Scaling",type=integer,JSONPath=`.status.scalingReplicas`
// +kubebuilder:printcolumn:name="Deleting",type=integer,JSONPath=`.status.deletingReplicas`
// +kubebuilder:printcolumn:name="Failed",type=integer,JSONPath=`.status.failedReplicas`
// +kubebuilder:printcolumn:name="Handler",type=string,JSONPath=`.spec.handler`
// +kubebuilder:printcolumn:name="RetryCount",type=integer,JSONPath=`.status.retryCount`
// +kubebuilder:printcolumn:name="Create_Time",type=string,JSONPath=`.metadata.creationTimestamp`
// +kubebuilder:printcolumn:name="Delete_Time",type=string,JSONPath=`.metadata.deletionTimestamp`
// +kubebuilder:printcolumn:name="ACCOUNT_ID",type=string,JSONPath=`.spec.accountID`,priority=1

// InstanceGroup is the Schema for the instancegroups API
type InstanceGroup struct {
	metav1.TypeMeta   `json:",inline"`
	metav1.ObjectMeta `json:"metadata,omitempty"`

	Spec   ccetypes.InstanceGroupSpec   `json:"spec,omitempty"`
	Status ccetypes.InstanceGroupStatus `json:"status,omitempty"`
}

// +kubebuilder:object:root=true

// InstanceGroupList contains a list of InstanceGroup
type InstanceGroupList struct {
	metav1.TypeMeta `json:",inline"`
	metav1.ListMeta `json:"metadata,omitempty"`
	Items           []InstanceGroup `json:"items"`
}

func init() {
	SchemeBuilder.Register(&InstanceGroup{}, &InstanceGroupList{})
}

func (ig *InstanceGroup) NamespacedName() string {
	return fmt.Sprintf("%s/%s", ig.Namespace, ig.Name)
}
