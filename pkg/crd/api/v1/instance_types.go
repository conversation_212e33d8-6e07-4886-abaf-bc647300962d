/*

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package v1

import (
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	ccetypes "icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
)

const (
	// InstanceFinalizer allows Reconcile to clean up CCE IaaS resources associated with Cluster before
	// TODO: 不同阶段使用不同 Finalizer
	InstanceFinalizer = "finalizer.k8s-instance.cce.baidubce.com"

	// InstanceAPIVersion instance CRD API version
	InstanceAPIVersion = "cce.baidubce.com/v1"

	// InstanceKind instance CRD kind
	InstanceKind = "Instance"

	// InstanceHandlerV1ToV2 - 处理 Instance V1 迁移至 V2
	InstanceHandlerV1ToV2 = "handler-v1-to-v2"
)

// //// +kubebuilder:subresource:status
// subresource status 参考: https://kubernetes.io/docs/tasks/access-kubernetes-api/custom-resources/custom-resource-definitions/

// +kubebuilder:object:root=true
// +kubebuilder:printcolumn:name="Cluster_ID",type=string,JSONPath=`.spec.clusterID`
// +kubebuilder:printcolumn:name="Instance_ID",type=string,JSONPath=`.status.machine.instanceID`
// +kubebuilder:printcolumn:name="Floating_IP",type=string,JSONPath=`.status.machine.floatingIP`
// +kubebuilder:printcolumn:name="VPC_IP",type=string,JSONPath=`.status.machine.vpcIP`
// +kubebuilder:printcolumn:name="PASSWORD",type=string,JSONPath=`.spec.adminPassword`
// +kubebuilder:printcolumn:name="Cluster_Role",type=string,JSONPath=`.spec.clusterRole`
// +kubebuilder:printcolumn:name="Instance_Group_ID",type=string,JSONPath=`.spec.instanceGroupID`
// +kubebuilder:printcolumn:name="Instance_Phase",type=string,JSONPath=`.status.instancePhase`
// +kubebuilder:printcolumn:name="Retry_Count",type=integer,JSONPath=`.status.retryCount`
// +kubebuilder:printcolumn:name="handler",type=string,JSONPath=`.spec.handler`
// +kubebuilder:printcolumn:name="createtime",type=string,JSONPath=`.metadata.creationTimestamp`
// +kubebuilder:printcolumn:name="ACCOUNT_ID",type=string,JSONPath=`.spec.accountID`,priority=1

// Instance is the Schema for the instances API
type Instance struct {
	metav1.TypeMeta   `json:",inline"`
	metav1.ObjectMeta `json:"metadata,omitempty"`

	Spec   ccetypes.InstanceSpec   `json:"spec,omitempty"`
	Status ccetypes.InstanceStatus `json:"status,omitempty"`
}

// +kubebuilder:object:root=true

// InstanceList contains a list of Instance
type InstanceList struct {
	metav1.TypeMeta `json:",inline"`
	metav1.ListMeta `json:"metadata"`
	Items           []Instance `json:"items"`
}

func init() {
	SchemeBuilder.Register(&Instance{}, &InstanceList{})
}
