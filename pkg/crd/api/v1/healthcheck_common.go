/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Description:
 * @File:  healthcheck_types
 * @Version: 1.0.0
 * @Date: 2020-10-26 16:15
 */

package v1

import (
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

// ready检查：参数
type NotReadySpec struct {
	// 阈值类型：sum/rate
	ThresholdType ThresholdType `json:"thresholdType,omitempty"`

	// not ready 的阈值，数字表示
	NotReadyThresholdSum int `json:"notReadyThresholdSum,omitempty"`

	// not ready 的阈值，比例表示，百分制
	NotReadyThresholdRate int `json:"notReadyThresholdRate,omitempty"`
}

type ThresholdType string

const (
	ThresholdSum  ThresholdType = "sum"
	ThresholdRate ThresholdType = "rate"
)

// 进程检查：参数
type ProcessSpec struct {
	// 进程名
	ProcessName string `json:"processName,omitempty"`

	// 进程路径
	ProcessPath string `json:"processPath,omitempty"`

	// 进程数目
	ProcessNum int `json:"processNum,omitempty"`

	// 进程 url, 可能用于 curl 探测
	ProcessUrl string `json:"processUrl,omitempty"`
}

// 磁盘检查：参数
type DiskSpec struct {
	// 路径
	Path string `json:"path,omitempty"`

	// 阈值类型：sum/rate
	ThresholdType ThresholdType `json:"thresholdType,omitempty"`

	// 磁盘大小：数字表示,单位 Gi
	Threshold int `json:"threshold,omitempty"`

	// 磁盘大小：使用率
	ThresholdRate int `json:"thresholdRate,omitempty"`
}

// 节点组检查：声明格式
type InstanceGroupCheckSpec struct {
}

// ClusterCondition contains condition information for a cluster.
type ClusterCondition struct {
	Type               string                 `json:"type" protobuf:"bytes,1,opt,name=type,casttype=ClusterConditionType"`
	Status             corev1.ConditionStatus `json:"status" protobuf:"bytes,2,opt,name=status,casttype=ConditionStatus"`
	LastHeartbeatTime  metav1.Time            `json:"lastHeartbeatTime,omitempty" protobuf:"bytes,3,opt,name=lastHeartbeatTime"`
	LastTransitionTime metav1.Time            `json:"lastTransitionTime,omitempty" protobuf:"bytes,4,opt,name=lastTransitionTime"`
	Reason             string                 `json:"reason,omitempty" protobuf:"bytes,5,opt,name=reason"`
	Message            string                 `json:"message,omitempty" protobuf:"bytes,6,opt,name=message"`
}

type ClusterConditionType string

const (
	Scheduler         = "scheduler"
	ControllerManager = "controller-manager"
	ETCD              = "etcd"
	Apiserver         = "apiserver"
)
