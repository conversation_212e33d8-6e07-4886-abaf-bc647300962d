/**
 * @Author: pansiyuan02
 * @Description:
 * @File:  instancegroup_operation_types
 * @Version: 1.0.0
 * @Date: 2021/6/11 11:19 上午
 */
package v1

import (
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	ccetypes "icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
)

// +kubebuilder:object:root=true
// InstanceGroupReplicasOperation is the Schema for the InstanceGroupReplicasOperation API
type InstanceGroupReplicasOperation struct {
	metav1.TypeMeta `json:",inline"`

	Spec ccetypes.InstanceGroupReplicasOperationSpec `json:"spec,omitempty"`
}

func init() {
	SchemeBuilder.Register(&InstanceGroupReplicasOperation{})
}
