// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2019/01/09 14:48:00, by <EMAIL>, create
*/
/*
DESCRIPTION
Cluster 相关 Annotations
*/

package v1

// 公共 Annotation, Cluster 和 Instance 都可以设置

const (
	// AnnotationSkipDeleteK8SResource - true = 释放集群时不删除 K8S Resource
	// 避免删除集群时, 集群处于异常状态, 无法连接 K8S Master
	AnnotationSkipDeleteK8SResource = "kubernetes.io/cce.skip-delete-k8s-resource"

	// 避免删除集群时, MasterBLB泄漏, 阻塞集群删除
	AnnotationSkipDeletemasterBLB = "kubernetes.io/cce.skip-delete-master-BLB"

	// 避免删除集群时, MasterEIP泄漏, 阻塞集群删除
	AnnotationSkipDeletemasterEIP = "kubernetes.io/cce.skip-delete-master-EIP"

	AnnotationReconcileMaxRetryCount = "kubernetes.io/cce.reconcile-max-retry-count"
)

// Cluster 公共 Annotation
const (
	// AnnotationSkipDeleteETCDCluster 设置该annotation会跳过外部ETCD Cluster资源回收
	// 避免在未部署公共ETCD集群的地域无法删除预期使用公共ETCD资源的集群
	AnnotationSkipDeleteETCDCluster = "kubernetes.io/cce.cluster.skip-delete-etcd-cluster"

	// AnnotationSkipValidateCRD
	// 跳过 admission webhook 中对 Cluster/Instance CRD 的校验
	AnnotationSkipValidateCRD = "kubernetes.io/cce.cluster.skip-validate-crd"

	// AnnotationManagedClusterMinNodeNum
	// 托管 Master 集群最小 Node 数
	AnnotationManagedClusterMinNodeNum = "kubernetes.io/cce.cluster.managed-cluster-min-node-num"

	// AnnotationSkipInstallDocker
	// 跳过 docker 部署, 集群维度
	// 针对客户已部署docker、且 docker-endpoint 为默认值: Default: `unix:///var/run/docker.sock`
	AnnotationSkipInstallDocker = "kubernetes.io/cce.cluster.skip-install-docker"

	// AnnotationSkipInstallNFS
	// 跳过 nfs 安装, 集群维度
	AnnotationSkipInstallNFS = "kubernetes.io/cce.cluster.skip-install-nfs"

	// AnnotationSkipPrometheusScrape
	// 跳过 Prometheus 抓取
	AnnotationSkipPrometheusScrape = "kubernetes.io/cce.cluster.skip-prometheus-scrape"

	// AnnotationBLSToken bls token
	AnnotationBLSToken = "kubernetes.io/bls.token"

	// AnnotationEnableControlPlaneLog 是否开启控制面日志采集
	AnnotationEnableControlPlaneLog = "kubernetes.io/enable-control-plane-log"

	// AnnotationTelegrafWithHttps telegraf 开启 https 访问
	AnnotationTelegrafWithHttps = "kubernetes.io/cce.cluster.telegraf-with-https"

	// AnnotationUsedOldMonitorComponents 标记集群是否使用过老的容器监控
	AnnotationUsedOldMonitorComponents = "kubernetes.io/cce.cluster.used-old-monitor-components"

	// AnnotationSkipSyncEndpoints 跳过同步 endpoint
	AnnotationSkipSyncEndpoints = "kubernetes.io/cce.cluster.skip-sync-endpoints"

	// AnnotationCPromAgentID cprom agentID
	AnnotationCPromAgentID = "kubernetes.io/cprom.agent.id"

	// AnnotationCPromInstanceID cprom instanceID
	AnnotationCPromInstanceID = "kubernetes.io/cprom.instance.id"

	// AnnotationTelegrafScopeCluster 所有节点都开启 telegraf
	AnnotationTelegrafScopeCluster = "kubernetes.io/cce.cluster.telegraf-scope-cluster"

	// AnnotationKubeStateMetricsWithSharding 用于标记 kube-state-metrics 是否开启 sharding
	AnnotationKubeStateMetricsWithSharding = "kubernetes.io/cce.cluster.kube-state-metrics-with-sharding"

	// AnnotationNotDeleteInstanceByGCController
	// Annotation: Cluster 的 Instance 不被 gc-controller 处理
	AnnotationNotDeleteInstanceByGCController = "kubernetes.io/cce.cluster.not-delete-instance-by-gc-controller"

	// AnnotationAllowMultiWorkflow - 允许集群中存在多个 Workflow
	AnnotationAllowMultiWorkflow = "kubernetes.io/cce.cluster.allow-multi-workflow"

	// AnnotationDisableKubeProxyMasq
	// kube-proxy masquerade: false, 关闭 conntrack 配置
	AnnotationDisableKubeProxyMasq = "kubernetes.io/cce.cluster.disable-kubeproxy-masq"

	// AnnotationRedeployControlPlane
	// 在已经存在对应部署对象的情况下，如果存在该annotation且为true,则会重复部署
	AnnotationRedeployControlPlane = "kubernetes.io/cce.cluster.redeploy-control-plane"

	// ca 镜像版本
	AnnotationCAWithImageTag = "kubernetes.io/cce.cluster.ca-with-image-tag"

	// 跳过冲突检查
	AnnotationSkipInstanceConfictCheck = "kubernetes.io/cce.cluster.skip-instance-confict-check"
)

// Instance 公共 Annotation
const (
	// AnnotationDrainK8SNode: Drain K8S Node
	AnnotationDrainK8SNode = "kubernetes.io/cce.instance.drain-k8s-node"

	// AnnotationNotHandlerByCCE: Not Handler by CCE
	AnnotationNotHandlerByCCE = "kubernetes.io/cce.instance.not-handler-by-cce"

	// InstanceAnnotationReconcileDeployWithoutCheckStatus true = reconcileInfrastructrue 不考虑 DeploySuccessStatus 状态
	InstanceAnnotationReconcileDeployWithoutCheckStatus = "kubernetes.io/cce.instance.reconcile-deploy-without-check-status"

	// 跳过移出实例时, 删除 /etc/systemd/system/kubelet.service 配置
	AnnotationSkipDeleteKubeletConfig = "kubernetes.io/cce.instance.skip-delete-kubelet-config"

	// 指定装机方式，value 可填 oos 和 ssh
	AnnotationInstanceDeployType = "kubernetes.io/cce.instance.install-method"

	// 创建失败, 则进行实例回滚
	AnnotationRollbackWhenCreatedFailed = "kubernetes.io/cce.instance.rollback-when-created-failed"

	// AnnotationCheckManagedMasterOrderBeforeDelete 在managed集群删除阶段，删除master机器前，检查机器的订单状态
	AnnotationCheckManagedMasterOrderBeforeDelete = "kubernetes.io/cce.check-managed-master-order-before-delete"

	AnnotationUpdateInstanceSpec = "kubernetes.io/cce.instance.update-spec"

	AnnotationSkipWaitingCrossVPCENI = "kubernetes.io/cce.instance.skip-wait-cveni"

	// 重新创建订单ID
	AnnotationUpdateOrderID = "kubernetes.io/cce.instance.update-orderid"

	// 扩容控制面组件总开关
	AnnotationScaleControlPlane = "kubernetes.io/cce.cluster.scale-up-control-plane"

	// 扩容etcd
	AnnotationScaleETCD = "kubernetes.io/cce.cluster.scale-up-etcd"

	// 扩容 apiserver
	AnnotationScaleApiserver = "kubernetes.io/cce.cluster.scale-up-apiserver"

	// 扩容 controller manager
	AnnotationScaleControllerManager = "kubernetes.io/cce.cluster.scale-up-controller-manager"

	// 扩容 kube scheduler
	AnnotationScaleScheduler = "kubernetes.io/cce.cluster.scale-up-scheduler"

	// 使用临时生成的配置，非必需，指定该annotation，值：true。 v1 升 v2 的集群禁止使用临时配置
	AnnotationScaleNewConf = "kubernetes.io/cce.cluster.scale-ctl-new-conf"

	// 使用老节点的配置。除非明确了是用临时生成的配置，否则必须要设置该annotation，值：某一个存量master的vpcIP
	AnnotationScaleSourceHost = "kubernetes.io/cce.cluster.scale-ctl-source-host"

	// AnnotationScaleSourceHost 配置中的机器密码。除非明确指定OOS部署，否则必填。值：存量master对应的root密码
	AnnotationScaleSourcePassWD = "kubernetes.io/cce.cluster.scale-ctl-source-passwd"

	// ssh port。非必需，默认22，如果使用ssh部署，并且要指定别的端口，可以使用该annotation设置。
	AnnotationSSHPort = "kubernetes.io/cce.cluster.scale-ctl-sshport"
)

// Workflow 公共 Annotation
const (
	// AnnotationSkipGenerateFailedPods: 跳过生成 FailedPods 列表
	AnnotationSkipGenerateFailedPods = "kubernetes.io/cce.workflow.skip-generate-failed-pods"
)

const (
	// AnnotationTrue true
	AnnotationTrue = "true"

	// AnnotationFalse false
	AnnotationFalse = "false"
)
