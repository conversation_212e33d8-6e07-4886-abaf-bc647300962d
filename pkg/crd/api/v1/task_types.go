/**
 * @Author: pansiyuan02
 * @Description:
 * @File:  task_types
 * @Version: 1.0.0
 * @Date: 2021/6/9 5:46 下午
 */
package v1

import (
	"fmt"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	ccetypes "icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
)

const (
	// TaskResource Task CRD resource
	TaskResource = "tasks"
)

// +kubebuilder:object:root=true
// +kubebuilder:subresource:status
// +kubebuilder:printcolumn:name="NAME",type=string,JSONPath=`.metadata.name`
// +kubebuilder:printcolumn:name="TASK_TYPE",type=string,JSONPath=`.spec.taskType`
// +kubebuilder:printcolumn:name="TARGET_REF",type=string,JSONPath=`.spec.targetRef`
// +kubebuilder:printcolumn:name="HANDLER",type=string,JSONPath=`.spec.handler`
// +kubebuilder:printcolumn:name="PHASE",type=string,JSONPath=`.status.phase`
// +kubebuilder:printcolumn:name="ERR_MESSAGE",type=string,JSONPath=`.status.errMessage`
// +kubebuilder:printcolumn:name="CREATE_TIME",type=string,JSONPath=`.spec.createdTime`
// +kubebuilder:printcolumn:name="FINISH_TIME",type=string,JSONPath=`.status.finishTime`
// +kubebuilder:printcolumn:name="DELETE_TIME",type=string,JSONPath=`.metadata.deletionTimestamp`
// +kubebuilder:printcolumn:name="ACCOUNT_ID",type=string,JSONPath=`.spec.accountID`,priority=1

// Task is the Schema for the tasks API
type Task struct {
	metav1.TypeMeta   `json:",inline"`
	metav1.ObjectMeta `json:"metadata,omitempty"`

	Spec   ccetypes.TaskSpec   `json:"spec,omitempty"`
	Status ccetypes.TaskStatus `json:"status,omitempty"`
}

// +kubebuilder:object:root=true

// TaskList contains a list of Task
type TaskList struct {
	metav1.TypeMeta `json:",inline"`
	metav1.ListMeta `json:"metadata"`
	Items           []Task `json:"items"`
}

func init() {
	SchemeBuilder.Register(&Task{}, &TaskList{})
}

func (in *Task) NamespacedName() string {
	return fmt.Sprintf("%s/%s", in.Namespace, in.Name)
}
