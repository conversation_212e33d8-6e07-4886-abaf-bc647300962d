/*

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package v1

import (
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

const (
	InstanceHealthCheckAPIVersion = "cce.baidubce.com/v1"
	InstanceHealthCheckKind       = "InstanceHealthCheck"
	InstanceHealthCheckResource   = "instancehealthchecks"
	ClusterHealthCheckKind        = "ClusterHealthCheck"
	ClusterHealthCheckResource    = "clusterhealthchecks"
)

// InstanceHealthCheckSpec defines the desired state of InstanceHealthCheck
type InstanceHealthCheckSpec struct {
	// Handler 标识该 Instance 由哪个 Controller 处理
	Handler string `json:"handler,omitempty"`

	// 所属集群 ID
	ClusterID string `json:"clusterID,omitempty"`

	// instance ID
	CCEInstanceID string `json:"cceInstanceID,omitempty"`
}

// InstanceHealthCheckStatus defines the observed state of InstanceHealthCheck
type InstanceHealthCheckStatus struct {
	// node ready 状态
	Ready corev1.ConditionStatus `json:"ready,omitempty"`

	// 详细检查结果
	Conditions []corev1.NodeCondition `json:"instanceCondition,omitempty"`
}

// +kubebuilder:object:root=true
// +kubebuilder:resource:shortName=ihc

// InstanceHealthCheck is the Schema for the instancehealthchecks API
type InstanceHealthCheck struct {
	metav1.TypeMeta   `json:",inline"`
	metav1.ObjectMeta `json:"metadata,omitempty"`

	Spec   InstanceHealthCheckSpec   `json:"spec,omitempty"`
	Status InstanceHealthCheckStatus `json:"status,omitempty"`
}

// +kubebuilder:object:root=true

// InstanceHealthCheckList contains a list of InstanceHealthCheck
type InstanceHealthCheckList struct {
	metav1.TypeMeta `json:",inline"`
	metav1.ListMeta `json:"metadata,omitempty"`
	Items           []InstanceHealthCheck `json:"items"`
}

func init() {
	SchemeBuilder.Register(&InstanceHealthCheck{}, &InstanceHealthCheckList{})
}

var NativeInstanceConditions = map[corev1.NodeConditionType]bool{
	corev1.NodeReady:              true,
	corev1.NodeMemoryPressure:     true,
	corev1.NodeDiskPressure:       true,
	corev1.NodePIDPressure:        true,
	corev1.NodeNetworkUnavailable: true,
}

type NodeConditionType string

const (
	NodeReadonlyFilesystem     NodeConditionType = "ReadonlyFilesystem"
	NodeKernelDeadlock         NodeConditionType = "KernelDeadlock"
	NodeDockerUnhealthy        NodeConditionType = "DockerUnhealthy"
	NodeFrequentDockerRestart  NodeConditionType = "FrequentDockerRestart"
	NodeFrequentKubeletRestart NodeConditionType = "FrequentKubeletRestart"
	NodeCorruptDockerOverlay2  NodeConditionType = "CorruptDockerOverlay2"
)
