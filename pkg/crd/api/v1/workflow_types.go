// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2021/12/07 11:12:00, by <EMAIL>, create
*/
/*
Workflow 定义为一系列串行或并行 Task 的组合, 用于实现 CCE 各种操作及检查自动化:

1. 集群升级;
2. 集群创建前置检查;
2. 集群健康状态检查;
3. ......
*/

package v1

import (
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	ccetypes "icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
)

const (
	// WorkflowFinalizer allows Reconcile to clean up CCE Workflow
	WorkflowFinalizer = "finalizer.k8s-workflow.cce.baidubce.com"

	// WorkflowAPIVersion cluster CRD API version
	WorkflowAPIVersion = "cce.baidubce.com/v1"

	// WorkflowKind workflow CRD kind
	WorkflowKind = "Workflow"
)

// +kubebuilder:object:root=true
// +kubebuilder:printcolumn:name="WorkflowID",type=string,JSONPath=`.spec.workflowID`
// +kubebuilder:printcolumn:name="ClusterID",type=string,JSONPath=`.spec.clusterID`
// +kubebuilder:printcolumn:name="Workflow_Type",type=string,JSONPath=`.spec.workflowType`
// +kubebuilder:printcolumn:name="AccountID",type=string,JSONPath=`.spec.accountID`
// +kubebuilder:printcolumn:name="Workflow_Phase",type=string,JSONPath=`.status.phase`
// +kubebuilder:printcolumn:name="Retry_Count",type=integer,JSONPath=`.status.retryCount`
// +kubebuilder:printcolumn:name="handler",type=string,JSONPath=`.spec.handler`
// +kubebuilder:printcolumn:name="createtime",type=string,JSONPath=`.metadata.creationTimestamp`

// Workflow is the Schema for the clusters API
type Workflow struct {
	metav1.TypeMeta   `json:",inline"`
	metav1.ObjectMeta `json:"metadata,omitempty"`

	Spec   ccetypes.WorkflowSpec   `json:"spec,omitempty"`
	Status ccetypes.WorkflowStatus `json:"status,omitempty"`
}

// +kubebuilder:object:root=true

// WorkflowList contains a list of Workflow
type WorkflowList struct {
	metav1.TypeMeta `json:",inline"`
	metav1.ListMeta `json:"metadata,omitempty"`
	Items           []Workflow `json:"items"`
}

func init() {
	SchemeBuilder.Register(&Workflow{}, &WorkflowList{})
}
