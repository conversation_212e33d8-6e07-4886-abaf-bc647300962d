/*

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package v1

import (
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	ccetypes "icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
)

const (
	// ClusterFinalizer allows Reconcile to clean up CCE IaaS resources associated with <PERSON>luster before
	// removing it from the apiserver.
	// TODO: 不同阶段使用不同 Finalizer
	ClusterFinalizer = "finalizer.k8s-cluster.cce.baidubce.com"

	// ClusterAPIVersion cluster CRD API version
	ClusterAPIVersion = "cce.baidubce.com/v1"

	// ClusterKind cluster CRD kind
	ClusterKind = "Cluster"

	// ClusterHandlerV1ToV2: v1 to v2
	ClusterHandlerV1ToV2 = "handler-v1-to-v2"
)

// ///// +kubebuilder:subresource:status
// subresource status 参考: https://kubernetes.io/docs/tasks/access-kubernetes-api/custom-resources/custom-resource-definitions/

// +kubebuilder:object:root=true
// +kubebuilder:printcolumn:name="ClusterID",type=string,JSONPath=`.spec.clusterID`
// +kubebuilder:printcolumn:name="ClusterName",type=string,JSONPath=`.spec.clusterName`
// +kubebuilder:printcolumn:name="MasterType",type=string,JSONPath=`.spec.masterConfig.masterType`
// +kubebuilder:printcolumn:name="AccountID",type=string,JSONPath=`.spec.accountID`
// +kubebuilder:printcolumn:name="K8SVersion",type=string,JSONPath=`.spec.k8sVersion`
// +kubebuilder:printcolumn:name="Infrastructure_Ready",type=boolean,JSONPath=`.status.infrastructureReady`
// +kubebuilder:printcolumn:name="APIServer_Success",type=boolean,JSONPath=`.status.apiServerAccessSuccess`
// +kubebuilder:printcolumn:name="K8SPlugin_Success",type=boolean,JSONPath=`.status.k8sPluginDeploySuccess`
// +kubebuilder:printcolumn:name="Cluster_Phase",type=string,JSONPath=`.status.clusterPhase`
// +kubebuilder:printcolumn:name="Retry_Count",type=integer,JSONPath=`.status.retryCount`
// +kubebuilder:printcolumn:name="handler",type=string,JSONPath=`.spec.handler`
// +kubebuilder:printcolumn:name="createtime",type=string,JSONPath=`.metadata.creationTimestamp`

// Cluster is the Schema for the clusters API
type Cluster struct {
	metav1.TypeMeta   `json:",inline"`
	metav1.ObjectMeta `json:"metadata,omitempty"`

	Spec   ccetypes.ClusterSpec   `json:"spec,omitempty"`
	Status ccetypes.ClusterStatus `json:"status,omitempty"`
}

// +kubebuilder:object:root=true

// ClusterList contains a list of Cluster
type ClusterList struct {
	metav1.TypeMeta `json:",inline"`
	metav1.ListMeta `json:"metadata,omitempty"`
	Items           []Cluster `json:"items"`
}

func init() {
	SchemeBuilder.Register(&Cluster{}, &ClusterList{})
}
