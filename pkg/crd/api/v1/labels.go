// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2021/07/05 13:01:00, by <EMAIL>, create
*/
/*
DESCRIPTION
Cluster 相关 Labels
*/

package v1

// Cluster 公共 Label
const (
	// LabelClusterSkipDeleteENI
	LabelClusterSkipDeleteENI = "kubernetes.io/cce.cluster.skip-list-eni"

	// LabelClusterNodesCanBeUpgraded
	LabelClusterNodesCanBeUpgraded = "kubernetes.io/cce.cluster.nodes-can-be-upgraded"

	// LabelClusterSkipCheckDeleteENI
	LabelClusterSkipCheckDeleteENI = "kubernetes.io/cce.cluster.skip-check-delete-eni"

	LabelClusterResourceGroupID = "kubernetes.io/cce.cluster.resource-group-id"
)

// Instance 公共 Label
const (
	// LabelInstanceHadBeenDrained: Instance associated K8S Node has been preempted
	LabelInstanceHadBeenPreempted = "kubernetes.io/cce.instance.instance-has-been-preempted"

	// LabelInstanceHadBeenDrained: Instance associated K8S Node has been drained
	LabelInstanceHadBeenDrained = "kubernetes.io/cce.instance.instance-has-been-drained"

	// LabelInstanceLeaked: 实例泄漏 CCE Instance 存在, 对应 IaaS 机器不存在
	LabelInstanceLeaked = "kubernetes.io/cce.instance.instance-leaked"

	// LabelInstanceUseBCCOpenAPI: 创建 BCC 使用 OpenAPI
	LabelInstanceUseBCCOpenAPI = "kubernetes.io/cce.instance.create-instance-use-openapi"

	// LabelInstanceSkipDeleteENI
	LabelInstanceSkipDeleteENI = "kubernetes.io/cce.instance.skip-delete-eni"

	// LabelInstanceSkipCheckDeleteENI
	LabelInstanceSkipCheckDeleteENI = "kubernetes.io/cce.instance.skip-check-delete-eni"

	// LabelInstanceJoinByCCEAdm
	LabelInstanceJoinByCCEAdm = "cce.baidubce.com/cce.instance.instance-join-by-cceadm"

	// LabelEnableImageAccelerate
	// 节点安装加速, 节点维度
	LabelEnableImageAccelerate = "cce.baidubce.com/cce.cluster.enable-image-accelerate"
)

// Workflow 公共 Label
const (
	// LabelWorkflowClusterID: Workflow 隶属 Cluster
	LabelWorkflowClusterID = "cluster-id"

	// LabelWorkflowAccountID: Workflow 隶属 Account
	LabelWorkflowAccountID = "account-id"

	// LabelWorkflowAllowMultipleWithinClusterLabelKey - 允许多个同类型 Workflow
	LabelWorkflowAllowMultipleWithinClusterLabelKey = "allow-multiple-within-cluster"
)
