/*

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package v1

import (
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime/schema"
)

// EDIT THIS FILE!  THIS IS SCAFFOLDING FOR YOU TO OWN!
// NOTE: json tags are required.  Any new fields you add must have json tags for the fields to be serialized.

// ClusterHealthCheckSpec defines the desired state of ClusterHealthCheck
type ClusterHealthCheckSpec struct {
	// Handler 标识该 Instance 由哪个 Controller 处理
	Handler string `json:"handler,omitempty"`

	// 所属集群 ID
	ClusterID string `json:"clusterID,omitempty"`

	// AccountID
	AccountID string `json:"accountID,omitempty"`

	// UserName
	UserName string `json:"userName,omitempty"`

	// Company
	Company string `json:"company,omitempty"`
}

// ClusterHealthCheckStatus defines the observed state of ClusterHealthCheck
type ClusterHealthCheckStatus struct {
	// 整体 ready 状态
	Ready corev1.ConditionStatus `json:"ready,omitempty"`

	// 详细检查结果
	Conditions []ClusterCondition `json:"clusterCondition,omitempty"`

	// 插件状态
	PluginCondition PluginCondition `json:"pluginCondition,omitempty"`

	// 第一次异常出现的时间, Ready = true 后会被刷新
	ErrorCount int `json:"count,omitempty"`

	FirstErrorTime *metav1.Time `json:"firstErrorTime,omitempty"`
}

// +kubebuilder:object:root=true
// +kubebuilder:resource:shortName=chc

// ClusterHealthCheck is the Schema for the clusterhealthchecks API
type ClusterHealthCheck struct {
	metav1.TypeMeta   `json:",inline"`
	metav1.ObjectMeta `json:"metadata,omitempty"`

	Spec   ClusterHealthCheckSpec   `json:"spec,omitempty"`
	Status ClusterHealthCheckStatus `json:"status,omitempty"`
}

// +kubebuilder:object:root=true

// ClusterHealthCheckList contains a list of ClusterHealthCheck
type ClusterHealthCheckList struct {
	metav1.TypeMeta `json:",inline"`
	metav1.ListMeta `json:"metadata,omitempty"`
	Items           []ClusterHealthCheck `json:"items"`
}

// +kubebuilder:object:root=true

// PluginCondition - 集群插件状态
type PluginCondition struct {
	*APIServerExternalAuditor `json:"apiServerExternalAuditor,omitempty"` // 是否开启审计日志推送

	PluginImages []PluginImage `json:"pluginImages,omitempty"` // CCE K8S Plugin 镜像

	// 废弃
	*CCEIngressController `json:"cceIngressController,omitempty"` // CCE Ingress Controller插件的状态信息

	*CCELBController `json:"cceLBController,omitempty"` // CCE LB Controller插件的状态信息
}

// +kubebuilder:object:root=true

// PluginImage -
type PluginImage struct {
	ImageID  string `json:"imageID,omitempty"`
	Replicas int    `json:"replicas,omitempty"`
}

// CCEIngressController - 插件
type CCEIngressController struct {
	WorkLoad WorkLoad `json:"workLoadCount,omitempty"` // 组件工作负载状态统计
}

// CCELBController - 插件
type CCELBController struct {
	WorkLoad WorkLoad `json:"workLoadCount,omitempty"` // 组件工作负载状态统计
}

// WorkLoad - CCE 组件通用结构
type WorkLoad struct {
	Image       string `json:"image,omitempty"`       // 最新镜像地址
	TargetCount int    `json:"targetCount,omitempty"` // 预期数量
	ReadyCount  int    `json:"readyCount,omitempty"`  // 当前在就绪状态的数量

	Reason  WorkLoadReason `json:"reason,omitempty"`  // Brief words for condition
	Message string         `json:"message,omitempty"` // Detail message to describe the condition
}

// WorkLoadReason -
type WorkLoadReason string

const (
	// NotFoundWorkLoadReason - NotFound
	NotFoundWorkLoadReason WorkLoadReason = "NotFound"

	// NotReadyWorkLoadReason - NotReady
	NotReadyWorkLoadReason WorkLoadReason = "NotReady"

	// InvalidWorkLoadReason - Invalid
	InvalidWorkLoadReason WorkLoadReason = "Invalid"

	// NormalWorkLoadReason - Normal
	NormalWorkLoadReason WorkLoadReason = "Normal"
)

// APIServerExternalAuditor - APIServer 开启审计配置
type APIServerExternalAuditor struct {
	EnableExternalAuditor *bool  `json:"enableExternalAuditor,omitempty"`
	BCTEndpoint           string `json:"bctEndpoint,omitempty"`
}

// GetObjectKind -
// TODO: 该方法无实际用处, 为了使用 controller-gen 自动生成 DeepCopy 函数
// controller-gen object 需要实现 runtime.Object interface
func (*PluginCondition) GetObjectKind() schema.ObjectKind {
	return schema.EmptyObjectKind
}

// GetObjectKind -
// TODO: 该方法无实际用处, 为了使用 controller-gen 自动生成 DeepCopy 函数
// controller-gen object 需要实现 runtime.Object interface
func (*APIServerExternalAuditor) GetObjectKind() schema.ObjectKind {
	return schema.EmptyObjectKind
}

// GetObjectKind -
// TODO: 该方法无实际用处, 为了使用 controller-gen 自动生成 DeepCopy 函数
// controller-gen object 需要实现 runtime.Object interface
func (*PluginImage) GetObjectKind() schema.ObjectKind {
	return schema.EmptyObjectKind
}

func init() {
	SchemeBuilder.Register(&ClusterHealthCheck{}, &ClusterHealthCheckList{})
}
