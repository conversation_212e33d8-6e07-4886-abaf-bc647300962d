// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2020/06/16 16:51:00, by <EMAIL>, create
*/
/*
cmp.Interface 实现
*/

package cmp

import (
	"context"
	"errors"
	"fmt"
	"math"
	"strconv"
	"strings"
	"time"

	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	cloudproviderapi "k8s.io/cloud-provider/api"

	ccesdk "icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/ccev2"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	ccetypes "icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/utils"
)

// ephemeralStorage - kubelet数据目录所在空间大小
// 比较 ephemeralStorage, 分两种情况:
// 1. 未挂载 CDS: node.status.capacity.ephemeral-storage = cce.spec.instanceResource.RootDiskSize;
// 1. 挂载 CDS: node.status.capacity.ephemeral-storage = cce.spec.instanceResource.CDSConfig;
// 2021-11-08 更新：
// kubelet 数据目录默认为 /var/lib/kubelet, 不再默认选择第一块磁盘
//
// PARAMS:
//   - ctx: context to trace request
//   - cce: *ccesdk.Instance
//   - node: *corev1.Node
//
// RETURNS:
// - *Diff: 如果一致, 则返回 nil
// - error: nil if succeed, error if fail
func ephemeralStorage(ctx context.Context, cce *ccesdk.Instance, node *corev1.Node) (*Diff, error) {
	if cce == nil {
		return nil, errors.New("cceInstance is nil")
	}

	if node == nil {
		return nil, errors.New("k8sNode is nil")
	}

	// CCE Instance DiskSize
	var cdsSize, rootSize int64

	if len(cce.Spec.InstanceResource.CDSList) != 0 {
		/*
			// 部署高级配置还没落库，暂时获取不到 kubelet 数据目录
			// 根据 kubelet 数据目录获取对应磁盘大小
			for _, cds := range cce.Spec.InstanceResource.CDSList {
				if strings.HasPrefix(cce.Spec.DeployCustomConfig.KubeletRootDir, cds.Path) {
					want = int64(cds.CDSSize)
					break
				}
			}
		*/

		// 暂时先用第一个 path 不为空的磁盘
		for _, cds := range cce.Spec.InstanceResource.CDSList {
			if cds.Path != "" {
				cdsSize = int64(cds.CDSSize)
				break
			}
		}
	}

	rootSize = int64(cce.Spec.InstanceResource.RootDiskSize)

	logger.Infof(ctx, "cds disk size: %d, root disk size: %d", cdsSize, rootSize)

	// K8S Node DiskSize
	var got int64

	got, ok := node.Status.Capacity.StorageEphemeral().AsInt64()
	if !ok {
		return nil, errors.New("StorageEphemeral().AsInt64() failed")
	}

	logger.Infof(ctx, "Got disk size: %d", got)

	// 比较 cdsSize, rootSize 和 got
	// 其中一个匹配即可
	// 考虑可能出现 400GB 和 398GB, 或单位误差 , 相等 = 二者差值绝对值小于 10 GB
	// 对于cds盘较大的情况，误差同样会被放大，因此选择误差百分比进行比较更科学,取值为10%
	if math.Abs(float64(cdsSize-got/1000/1000/1000))*100/float64(cdsSize) < 10 || math.Abs(float64(rootSize-got/1000/1000/1000))*100/float64(rootSize) < 10 {
		return nil, nil
	}

	return &Diff{
		Attribute: "EphemeralStorage",
		Want:      fmt.Sprintf("cds: %d or root: %d", cdsSize, rootSize),
		Got:       fmt.Sprintf("%d", got/1000/1000/1000),
	}, nil
}

// maxPods - 比较 maxPods
//
// PARAMS:
//   - ctx: context to trace request
//   - cluster: *ccesdk.Cluster
//   - node: *corev1.Node
//
// RETURNS:
// - *Diff: 如果一致, 则返回 nil
// - error: nil if succeed, error if fail
func maxPods(ctx context.Context, cluster *ccesdk.Cluster, node *corev1.Node) (*Diff, error) {
	if cluster == nil {
		return nil, errors.New("cluster is nil")
	}

	if node == nil {
		return nil, errors.New("k8sNode is nil")
	}

	// Want
	var want int64

	want = int64(cluster.Spec.ContainerNetworkConfig.MaxPodsPerNode)

	logger.Infof(ctx, "Want max pods: %d", want)

	got, ok := node.Status.Capacity.Pods().AsInt64()
	if !ok {
		return nil, errors.New("Pods().AsInt64() failed")
	}

	logger.Infof(ctx, "Got max pods: %d", got)

	// 比较 want 和 got
	if want != got {
		return &Diff{
			Attribute: "MaxPods",
			Want:      fmt.Sprintf("%d", want),
			Got:       fmt.Sprintf("%d", got),
		}, nil
	}

	return nil, nil
}

// systemInfo - 对比 System Info
// 包括：操作系统、docker版本、kubelet 版本、kubeproxy版本
// PARAMS:
//   - ctx: context to trace request
//   - cluster: *ccesdk.Cluster
//   - node: *corev1.Node
//
// RETURNS:
// - *Diff: 如果一致, 则返回 nil
// - error: nil if succeed, error if fail
func systemInfo(ctx context.Context, cluster *ccesdk.Cluster, instance *ccesdk.Instance, node *corev1.Node) ([]*Diff, error) {
	if cluster == nil {
		return nil, errors.New("cluster is nil")
	}

	if node == nil {
		return nil, errors.New("k8sNode is nil")
	}

	diffList := make([]*Diff, 0)

	// OSType 判断，如 linux/windows
	wantOSType := string(instance.Spec.InstanceOS.OSType)
	gotOSType := string(node.Status.NodeInfo.OperatingSystem)
	if wantOSType != gotOSType {
		diff := &Diff{
			Attribute: "OSType",
			Want:      wantOSType,
			Got:       gotOSType,
		}
		diffList = append(diffList, diff)
	}

	// TODO: CentOS6U3 会出现 unknown, 待修复

	// OSName 判断，如 CentOS
	// instance的声明中是CentOS字段，但 node describe 存的一般是CentOS Linux 7 (Core)，因此先用字符串包含来判断
	// wantOSName := string(instance.Spec.InstanceOS.OSName)
	// gotOSName := string(node.Status.NodeInfo.OSImage)
	// if !strings.Contains(gotOSName, wantOSName)
	// 	diff := &Diff{
	// 		Attribute: "OSName",
	// 		Want:      wantOSName,
	// 		Got:       gotOSName,
	// 	}
	// 	diffList = append(diffList, diff)
	// }

	// os version 版本： 因为 describe node 时无法得到centos 7.3中的 7.3发行版信息，7.3 的校验先不做

	// runtime 判断：如 docker 18.09.02 和 Container Runtime Version:  docker://18.9.2
	wantRuntimeType := string(instance.Spec.RuntimeType)
	wantRuntimeVersionTuple := strings.Split(string(instance.Spec.RuntimeVersion), ".")

	gotRuntimeInfoTuple := strings.Split(string(node.Status.NodeInfo.ContainerRuntimeVersion), "://")
	gotRuntimeType := gotRuntimeInfoTuple[0]
	gotRuntimeVersionTuple := strings.Split(string(gotRuntimeInfoTuple[1]), ".")

	// 先判断 runtime 类型，如 docker
	if wantRuntimeType != gotRuntimeType {
		diff := &Diff{
			Attribute: "RuntimeType",
			Want:      wantRuntimeType,
			Got:       gotRuntimeType,
		}
		diffList = append(diffList, diff)
	}

	// 再判断版本
	if len(wantRuntimeVersionTuple) != len(gotRuntimeVersionTuple) {
		diff := &Diff{
			Attribute: "RuntimeVersion",
			Want:      utils.ToJSON(wantRuntimeVersionTuple),
			Got:       utils.ToJSON(gotRuntimeVersionTuple),
		}
		diffList = append(diffList, diff)
	} else {
		for index, got := range gotRuntimeVersionTuple {
			// 09 和 9转为数字是一样
			wantNum, err := strconv.Atoi(wantRuntimeVersionTuple[index])
			if err != nil {
				logger.Errorf(ctx, err.Error())
				return nil, fmt.Errorf(err.Error())
			}

			gotNum, err := strconv.Atoi(got)

			if err != nil {
				logger.Errorf(ctx, err.Error())
				return nil, fmt.Errorf(err.Error())
			}

			if wantNum != gotNum {
				diff := &Diff{
					Attribute: "RuntimeVersion",
					Want:      utils.ToJSON(wantRuntimeVersionTuple),
					Got:       utils.ToJSON(gotRuntimeVersionTuple),
				}
				diffList = append(diffList, diff)
				break
			}
		}
	}

	// kubelet 版本判断, 1.16.3 -- v1.16.3
	wantKubeletVersion := string(cluster.Spec.K8SVersion)
	gotKubeletVersion := string(node.Status.NodeInfo.KubeletVersion)
	if fmt.Sprintf("v%s", wantKubeletVersion) != gotKubeletVersion {
		diff := &Diff{
			Attribute: "KubeletVersion",
			Want:      wantKubeletVersion,
			Got:       gotKubeletVersion,
		}
		diffList = append(diffList, diff)
	}

	// kube-proxy 版本判断, 1.16.3 -- v1.16.3
	wantKubeProxyVersion := string(cluster.Spec.K8SVersion)
	gotKubeProxyVersion := string(node.Status.NodeInfo.KubeProxyVersion)
	if fmt.Sprintf("v%s", wantKubeProxyVersion) != gotKubeProxyVersion {
		diff := &Diff{
			Attribute: "KubeProxyVersion",
			Want:      wantKubeProxyVersion,
			Got:       gotKubeProxyVersion,
		}
		diffList = append(diffList, diff)
	}

	return diffList, nil
}

// capacityInfo - 对比 capacityInfo
// 包括：cpu、内存、最大 pod数目
// PARAMS:
//   - ctx: context to trace request
//   - cluster: *ccesdk.Cluster
//   - node: *corev1.Node
//
// RETURNS:
// - *Diff: 如果一致, 则返回 nil
// - error: nil if succeed, error if fail
func capacityInfo(ctx context.Context, cluster *ccesdk.Cluster, instance *ccesdk.Instance, node *corev1.Node) ([]*Diff, error) {
	if cluster == nil {
		return nil, errors.New("cluster is nil")
	}

	if node == nil {
		return nil, errors.New("k8sNode is nil")
	}

	diffList := make([]*Diff, 0)

	// CPU 判断(BBC类型的实例，可能instance详情接口获取不到cpu，直接原因是前端没传，根本原因就是BBC那边的逻辑，bbc他们是看flavorID字段)
	wantCPU := instance.Spec.InstanceResource.CPU
	gotCPU, _ := node.Status.Capacity.Cpu().AsInt64()

	if instance.Spec.MachineType != ccetypes.MachineTypeBBC {
		if int64(wantCPU) != gotCPU {
			diff := &Diff{
				Attribute: "CPU",
				Want:      fmt.Sprintf("%d", wantCPU),
				Got:       fmt.Sprintf("%d", gotCPU),
			}
			diffList = append(diffList, diff)
		}
	} else {
		logger.Warnf(ctx, "Instance MachineType=%s, .Spec.InstanceResource.CPU=%d, skip cmp capacityInfo of cpu",
			instance.Spec.MachineType, wantCPU)
	}

	// 内存 判断 (BBC类型的实例，可能instance详情接口获取不到mem)
	wantMEM := int64(instance.Spec.InstanceResource.MEM)
	gotMEM, _ := node.Status.Capacity.Memory().AsInt64()

	if instance.Spec.MachineType != ccetypes.MachineTypeBBC {
		// 内存的误差不超过 1G就算通过
		if math.Abs(float64(wantMEM*1024*1024*1024-gotMEM)) >= 1024*1024*1024 {
			diff := &Diff{
				Attribute: "MEM",
				Want:      fmt.Sprintf("%d", wantMEM),
				Got:       fmt.Sprintf("%d", gotMEM),
			}
			diffList = append(diffList, diff)
		}
	} else {
		logger.Warnf(ctx, "Instance MachineType=%s, .Spec.InstanceResource.MEM=%d, skip cmp capacityInfo of mem",
			instance.Spec.MachineType, wantMEM)
	}

	return diffList, nil
}

// allocatableInfo - 对比 allocatableInfo
// 包括：预留后的可分配额，如cpu、内存、最大 pod数目
// PARAMS:
//   - ctx: context to trace request
//   - cluster: *ccesdk.Cluster
//   - node: *corev1.Node
//
// RETURNS:
// - *Diff: 如果一致, 则返回 nil
// - error: nil if succeed, error if fail
func allocatableInfo(ctx context.Context, cluster *ccesdk.Cluster, instance *ccesdk.Instance, node *corev1.Node) ([]*Diff, error) {
	if cluster == nil {
		return nil, errors.New("cluster is nil")
	}

	if node == nil {
		return nil, errors.New("k8sNode is nil")
	}

	// todo: instance 引用的是 cce v2 中的结构体，还不包含 资源预留 等自定义配置项

	diffList := make([]*Diff, 0)

	return diffList, nil
}

// vpcRoute - 检查 VPC Route 是否创建
// conditions:
//   - lastHeartbeatTime: "2020-07-21T02:24:29Z"
//     lastTransitionTime: "2020-07-21T02:24:29Z"
//     message: RouteController created a route
//     reason: RouteCreated
//     status: "False"
//     type: NetworkUnavailable
//
// PARAMS:
//   - ctx: context to trace request
//   - cluster: *ccesdk.Cluster
//   - cceInstanceID: string
//
// RETURNS:
// - *Diff: 如果一致, 则返回 nil
// - error: nil if succeed, error if fail
func (c *client) vpcRoute(ctx context.Context, cluster *ccesdk.Cluster, nodeName string) ([]*Diff, error) {
	if cluster == nil {
		return nil, errors.New("cluster is nil")
	}

	if nodeName == "" {
		return nil, errors.New("nodeName is nil")
	}

	if cluster.Spec.ContainerNetworkConfig.Mode != ccetypes.ContainerNetworkModeKubenet {
		return nil, nil
	}

	// 查询路由创建
	k8sClient, err := c.newK8SClient(ctx, cluster.Spec.ClusterID)
	if err != nil {
		logger.Errorf(ctx, "newK8SClient failed: %s", err)
		return nil, err
	}

	var node *corev1.Node
	for i := 0; i < 15; i++ {
		node, err = k8sClient.CoreV1().Nodes().Get(ctx, nodeName, metav1.GetOptions{})
		if err != nil {
			logger.Errorf(ctx, "Get K8S Node %s failed: %s", nodeName, err)
			return nil, err
		}

		// 检查路由创建
		for _, c := range node.Status.Conditions {
			if c.Type == corev1.NodeNetworkUnavailable {
				if c.Status == corev1.ConditionFalse && c.Reason == "RouteCreated" {
					logger.Infof(ctx, "NodeNetworkUnavailable = false")
					return nil, nil
				}

				logger.Errorf(ctx, "NodeNetworkUnavailable not ready, retry: %s", utils.ToJSON(node.Status.Conditions))
				break
			}
		}

		time.Sleep(10 * time.Second)
	}

	return []*Diff{
		{
			Attribute: "NodeNetworkUnavailable",
			Want:      "False",
			Got:       "True",
		},
	}, nil
}

// DynamicConfig - 比较 kubelet 动态配置是否生效
//
// spec:
//
//	configSource:
//	  configMap:
//	    kubeletConfigKey: kubelet
//	    name: node-config-*************
//	    namespace: kube-system
//	podCIDR: ********/26
//	providerID: cce://i-rE0wzNd3
func DynamicConfig(ctx context.Context, node *corev1.Node, vpcip string) ([]*Diff, error) {
	if node == nil {
		return nil, errors.New("node is nil")
	}

	diffs := []*Diff{}

	// 动态配置是否生效
	configSource := node.Spec.ConfigSource
	if configSource == nil || configSource.ConfigMap == nil {
		logger.Infof(ctx, "node.Spec.ConfigSource is nil")

		return []*Diff{
			{
				Attribute: "KubeletDynamicConfig",
				Want:      "exists",
				Got:       "nil",
			},
		}, nil
	}

	// 动态配置 configmap namespace
	if configSource.ConfigMap.Namespace != "kube-system" {
		diffs = append(diffs, &Diff{
			Attribute: "KubeletDynamicConfig.Namespace",
			Want:      "kube-system",
			Got:       configSource.ConfigMap.Namespace,
		})
	}

	// 动态配置 configmap name
	if configSource.ConfigMap.Name != fmt.Sprintf("node-config-%s", vpcip) {
		diffs = append(diffs, &Diff{
			Attribute: "KubeletDynamicConfig.Name",
			Want:      fmt.Sprintf("node-config-%s", vpcip),
			Got:       configSource.ConfigMap.Name,
		})
	}

	return diffs, nil
}

// 判断 label 是否存在
func NodeLabel(ctx context.Context, node *corev1.Node, labelKey, labelValue string) ([]*Diff, error) {
	if node == nil {
		return nil, errors.New("node is nil")
	}

	diffs := []*Diff{}

	labels := node.ObjectMeta.Labels
	logger.Infof(ctx, "node.ObjectMeta.Labels: %v", labels)

	if value, ok := labels[labelKey]; ok {
		diffs = append(diffs, &Diff{
			Attribute: "Node.Label",
			Want:      labelValue,
			Got:       value,
		})
	} else {
		logger.Infof(ctx, "label: %s not exist", labelKey)
		return nil, errors.New("label not exist")
	}

	return diffs, nil
}

// providerID - 对比 ProviderID
// Node.Spec.ProviderID 应该被正确设置包含 InstanceID
// PARAMS:
//   - ctx: context to trace request
//   - cluster: *ccesdk.Cluster
//   - instance: *ccesdk.Instance
//   - node: *corev1.Node
//
// RETURNS:
// - *Diff: 如果一致, 则返回 nil
// - error: nil if succeed, error if fail
func providerID(ctx context.Context, cluster *ccesdk.Cluster, instance *ccesdk.Instance, node *corev1.Node) ([]*Diff, error) {
	if cluster == nil {
		return nil, errors.New("cluster is nil")
	}
	if instance == nil {
		return nil, errors.New("instance is nil")
	}
	if node == nil {
		return nil, errors.New("k8sNode is nil")
	}

	diffList := make([]*Diff, 0)
	gotProviderID := node.Spec.ProviderID
	wantProviderID := utils.ProviderID(ccetypes.ProviderName, instance.Status.Machine.InstanceID)

	if gotProviderID != wantProviderID {
		diff := &Diff{
			Attribute: "ProviderID",
			Want:      wantProviderID,
			Got:       gotProviderID,
		}
		diffList = append(diffList, diff)
	}

	return diffList, nil
}

// taint - 检查Taint
// node.cloudprovider.kubernetes.io/uninitialized 不应该存在于节点中
// PARAMS:
//   - ctx: context to trace request
//   - cluster: *ccesdk.Cluster
//   - instance: *ccesdk.Instance
//   - node: *corev1.Node
//
// RETURNS:
// - *Diff: 如果一致, 则返回 nil
// - error: nil if succeed, error if fail
func taint(ctx context.Context, cluster *ccesdk.Cluster, instance *ccesdk.Instance, node *corev1.Node) ([]*Diff, error) {
	if cluster == nil {
		return nil, errors.New("cluster is nil")
	}
	if instance == nil {
		return nil, errors.New("instance is nil")
	}
	if node == nil {
		return nil, errors.New("k8sNode is nil")
	}

	diffList := make([]*Diff, 0)

	gotCloudTaint := false
	for _, taint := range node.Spec.Taints {
		if taint.Key == cloudproviderapi.TaintExternalCloudProvider {
			gotCloudTaint = true
		}
	}

	if gotCloudTaint {
		diff := &Diff{
			Attribute: "HasCloudTaint",
			Want:      strconv.FormatBool(false),
			Got:       strconv.FormatBool(gotCloudTaint),
		}
		diffList = append(diffList, diff)
	}

	return diffList, nil
}

// label - 检查 Label
// 包括 InstanceType Zone Region GPU 等是否存在
// PARAMS:
//   - ctx: context to trace request
//   - cluster: *ccesdk.Cluster
//   - instance: *ccesdk.Instance
//   - node: *corev1.Node
//
// RETURNS:
// - *Diff: 如果一致, 则返回 nil
// - error: nil if succeed, error if fail
func labels(ctx context.Context, cluster *ccesdk.Cluster, instance *ccesdk.Instance, node *corev1.Node) ([]*Diff, error) {
	if cluster == nil {
		return nil, errors.New("cluster is nil")
	}
	if instance == nil {
		return nil, errors.New("instance is nil")
	}
	if node == nil {
		return nil, errors.New("k8sNode is nil")
	}

	diffList := make([]*Diff, 0)

	if len(node.ObjectMeta.Labels) == 0 {
		diff := &Diff{
			Attribute: "HasLabels",
			Want:      strconv.FormatBool(true),
			Got:       strconv.FormatBool(false),
		}
		diffList = append(diffList, diff)
		return diffList, nil
	}

	gotInstanceType, ok := node.ObjectMeta.Labels[corev1.LabelInstanceType]
	if !ok || gotInstanceType == "" {
		diff := &Diff{
			Attribute: "LabelInstanceType",
			Want:      "Not Empty",
			Got:       gotInstanceType,
		}
		diffList = append(diffList, diff)
	}

	gotRegion, ok := node.ObjectMeta.Labels[corev1.LabelZoneRegion]
	if !ok || gotRegion == "" {
		diff := &Diff{
			Attribute: "LabelZoneRegion",
			Want:      "Not Empty",
			Got:       gotRegion,
		}
		diffList = append(diffList, diff)
	}

	if gotRegion != "edge" {
		gotDomain, ok := node.ObjectMeta.Labels[corev1.LabelZoneFailureDomain]
		if !ok || gotDomain == "" {
			diff := &Diff{
				Attribute: "LabelZoneFailureDomain",
				Want:      "Not Empty",
				Got:       gotDomain,
			}
			diffList = append(diffList, diff)
		}
	}

	// gotGPU, ok := node.ObjectMeta.Labels[controller.InstanceGPULabel]
	// if !ok || gotGPU == "" {
	//	diff := &Diff{
	//		Attribute: "InstanceGPULabel",
	//		Want:      "Not Empty",
	//		Got:       gotGPU,
	//	}
	//	diffList = append(diffList, diff)
	// }

	return diffList, nil
}
