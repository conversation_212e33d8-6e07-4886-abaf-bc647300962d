// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2020/06/16 16:51:00, by <EMAIL>, create
*/
/*
cmp.Interface 实现
*/

package cmp

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/tools/clientcmd"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/models"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bcc"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bec"
	ccesdk "icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/ccev2"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/retrypolicy"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	ccetypes "icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/utils"
)

const (
	kubeletDefaultMaxPods = 110
	defaultLabelKey       = "post-key"
	defaultLabelValue     = "post-value"
)

type client struct {
	config *Config

	bccClient bcc.Interface
	becClient bec.Interface
	cceClient ccesdk.Interface
}

// Config - 初始化 client 配置
type Config struct {
	Region          string `json:"Region"`
	AccessKey       string `json:"AccessKey"`
	AccessKeySecret string `json:"AccessKeySecret"`
	BCCEndpoint     string `json:"BCCEndpoint"`
	BECEndpoint     string `json:"BECEndpoint"`
	CCEV2Endpoint   string `json:"CCEV2Endpoint"`

	KubeConfigType models.KubeConfigType `json:"KubeConfigType"`
}

// NewClient - 初始化 Client
func NewClient(ctx context.Context, config *Config) (Interface, error) {
	if config == nil {
		return nil, errors.New("config is nil")
	}

	// 初始化 bcc client
	bccClient := bcc.NewClient(bceConfig(config.BCCEndpoint, config))

	// 初始化 bec client
	var becClient bec.Interface
	if config.BECEndpoint != "" {
		becClient = bec.NewClient(bceConfig(config.BECEndpoint, config))
	}

	// 初始化 cce client
	cceClient := ccesdk.NewClient(bceConfig(config.CCEV2Endpoint, config))

	return &client{
		config:    config,
		bccClient: bccClient,
		becClient: becClient,
		cceClient: cceClient,
	}, nil
}

func bceConfig(endpoint string, config *Config) *bce.Config {
	return &bce.Config{
		Credentials: &bce.Credentials{
			AccessKeyID:     config.AccessKey,
			SecretAccessKey: config.AccessKeySecret,
		},
		Endpoint:    endpoint,
		Checksum:    true,
		Region:      config.Region,
		RetryPolicy: retrypolicy.NewIntervalRetryPolicy(context.TODO(), 0, 0),
	}
}

// InstanceCompareWithBCC - CCE Instance 和 BCC Instance 对比
//
// PARAMS:
//   - ctx: context to trace request
//   - cceInstanceID: CCE InstanceID
//
// RETURNS:
// - error: nil if succeed, error if fail
func (c *client) InstanceCompareWithBCC(ctx context.Context, clusterID string, cceInstance *ccesdk.Instance, option *bce.SignOption) ([]*Diff, error) {
	// 获取 BCC Instance
	bccInstanceID := cceInstance.Status.Machine.InstanceID
	bccInstance, err := c.bccClient.DescribeInstance(ctx, bccInstanceID, option)
	if err != nil {
		logger.Errorf(ctx, "DescribeInstance %s failed: %s", bccInstanceID, err)
		return nil, err
	}

	logger.Infof(ctx, "GetBCCInstance %s success: %s", bccInstanceID, utils.ToJSON(bccInstance))

	// 比较 CCEInstance 和 BCCInstance
	result, err := c.cmpInstance(ctx, cceInstance, bccInstance)
	if err != nil {
		logger.Errorf(ctx, "cmpInstance failed: %s", err)
		return nil, err
	}
	return result, nil
}

// InstanceCompareWithBEC - CCE Instance 和 BEC Instance 对比
//
// PARAMS:
//   - ctx: context to trace request
//   - cceInstanceID: CCE InstanceID
//
// RETURNS:
// - error: nil if succeed, error if fail
func (c *client) InstanceCompareWithBEC(ctx context.Context, clusterID string, cceInstance *ccesdk.Instance, option *bce.SignOption) ([]*Diff, error) {
	// 获取 BEC Instance
	becInstanceID := cceInstance.Status.Machine.InstanceID
	becInstance, err := c.becClient.GetVMInstance(ctx, becInstanceID, option)
	if err != nil {
		logger.Errorf(ctx, "BEC client GetVMInstance %s failed: %s", becInstanceID, err)
		return nil, err
	}

	logger.Infof(ctx, "GetBECInstance %s success: %s", becInstanceID, utils.ToJSON(becInstance))

	// 比较 CCEInstance 和 BECInstance
	result, err := c.cmpBECInstance(ctx, cceInstance, becInstance)
	if err != nil {
		logger.Errorf(ctx, "cmpBECInstance failed: %s", err)
		return nil, err
	}
	return result, nil
}

// InstanceCompareWithIaaS - CCE Instance 和 BCC/BEC Instance 对比
//
// PARAMS:
//   - ctx: context to trace request
//   - cceInstanceID: CCE InstanceID
//
// RETURNS:
// - error: nil if succeed, error if fail
func (c *client) InstanceCompareWithIaaS(ctx context.Context, clusterID, cceInstanceID string, option *bce.SignOption) ([]*Diff, error) {
	if cceInstanceID == "" {
		return nil, errors.New("cceInstanceID is empty")
	}

	// 获取 CCE Instance
	cceResp, err := c.cceClient.GetInstance(ctx, clusterID, cceInstanceID, option)
	if err != nil {
		logger.Errorf(ctx, "GetInstance %s failed: %s", cceInstanceID, err)
		return nil, err
	}

	if cceResp == nil {
		return nil, errors.New("GetInstance return nil")
	}

	cceInstance := cceResp.Instance
	if cceInstance.Status == nil {
		return nil, errors.New("cceInstance.Status is nil")
	}

	logger.Infof(ctx, "GetCCEInstance %s success: %s", cceInstanceID, utils.ToJSON(cceInstance))

	// 获取 MachineType
	machineType := cceInstance.Spec.MachineType
	switch machineType {
	case ccetypes.MachineTypeEBC:
		return c.InstanceCompareWithBCC(ctx, clusterID, cceInstance, option)
	case ccetypes.MachineTypeBCC:
		return c.InstanceCompareWithBCC(ctx, clusterID, cceInstance, option)
	case ccetypes.MachineTypeBEC:
		return c.InstanceCompareWithBEC(ctx, clusterID, cceInstance, option)
	default:
		return nil, fmt.Errorf("not support machineType to cmp instance: %s", machineType)
	}
}

// InstanceCompareWithK8S - CCE Instance 和 K8S Node 对比
//
// PARAMS:
//   - ctx: context to trace request
//   - cceInstanceID: CCE InstanceID
//
// RETURNS:
// - error: nil if succeed, error if fail
func (c *client) InstanceCompareWithK8S(ctx context.Context, clusterID, cceInstanceID string, option *bce.SignOption) ([]*Diff, error) {
	if clusterID == "" {
		return nil, errors.New("clusterID is empty")
	}

	if cceInstanceID == "" {
		return nil, errors.New("cceInstanceID is empty")
	}

	// 获取 CCE Instance 对应 Cluster
	clusterResp, err := c.cceClient.GetCluster(ctx, clusterID, option)
	if err != nil {
		logger.Errorf(ctx, "GetCluster %s failed: %s", clusterID, err)
		return nil, err
	}

	cluster := clusterResp.Cluster

	// 获取 CCE Instance
	cceResp, err := c.cceClient.GetInstance(ctx, clusterID, cceInstanceID, option)
	if err != nil {
		logger.Errorf(ctx, "GetInstance %s failed: %s", cceInstanceID, err)
		return nil, err
	}

	if cceResp == nil {
		return nil, errors.New("GetInstance return nil")
	}

	cceInstance := cceResp.Instance
	if cceInstance.Status == nil {
		return nil, errors.New("cceInstance.Status is nil")
	}

	logger.Infof(ctx, "GetCCEInstance %s success: %s", cceInstanceID, utils.ToJSON(cceInstance))

	// TODO: 如果支持 Hostname 这里要改
	// 获取 K8S Node
	nodeName := utils.GetNodeName(ctx, cceInstance.Status.Machine.VPCIP, cceInstance.Status.Machine.Hostname, cceInstance.Spec.InstanceName, cluster.Spec.K8SCustomConfig.EnableHostname)

	k8sClient, err := c.newK8SClient(ctx, clusterID)
	if err != nil {
		logger.Errorf(ctx, "newK8SClient failed: %s", err)
		return nil, err
	}

	var node *corev1.Node
	for i := 0; i < 12; i++ {
		node, err = k8sClient.CoreV1().Nodes().Get(ctx, nodeName, metav1.GetOptions{})
		if err != nil {
			logger.Errorf(ctx, "Get K8S Node %s failed: %s", nodeName, err)
			return nil, err
		}

		got, ok := node.Status.Capacity.Pods().AsInt64()
		if !ok {
			return nil, errors.New("Pods().AsInt64() failed")
		}

		if got != kubeletDefaultMaxPods {
			logger.Infof(ctx, "Got max pods: %d, kubelet already restarted to use config", got)
			break
		}

		logger.Infof(ctx, "Got max pods: %d may be kubelet default max pod, waiting kubelet restart", got)
		time.Sleep(10 * time.Second)
	}

	// 比较 CCE Instance 和 K8S Node
	result, err := c.cmpK8SNode(ctx, cluster, cceInstance, node)
	if err != nil {
		logger.Errorf(ctx, "cmpK8SNode failed: %s", err)
		return nil, err
	}

	return result, nil
}

func (c *client) newK8SClient(ctx context.Context, clusterID string) (kubernetes.Interface, error) {
	// 获取 KubeConfig
	resp, err := c.cceClient.GetAdminKubeConfig(ctx, clusterID, c.config.KubeConfigType, nil)
	if err != nil {
		logger.Errorf(ctx, "GetAdminKubeConfig failed: %s", err)
		return nil, err
	}

	// 初始化 kubernetes.Interface
	config, err := clientcmd.RESTConfigFromKubeConfig([]byte(resp.KubeConfig))
	if err != nil {
		logger.Errorf(ctx, "RESTConfigFromKubeConfig failed: %s", err)
		return nil, err
	}

	config.Timeout = 5 * time.Second
	config.ContentType = "application/vnd.kubernetes.protobuf"

	client, err := kubernetes.NewForConfig(config)
	if err != nil {
		logger.Errorf(ctx, "NewForConfig failed: %s", err)
		return nil, err
	}

	return client, nil
}

func (c *client) cmpInstance(ctx context.Context, cce *ccesdk.Instance, bcc *bcc.Instance) ([]*Diff, error) {
	diffs := []*Diff{}

	if cce == nil || cce.Spec == nil || cce.Status == nil {
		return nil, errors.New("cceInstance or spec or status is nil")
	}

	if bcc == nil {
		return nil, errors.New("bccInstance is nil")
	}

	cmp := func(diffs []*Diff, name, want, got string) {
		if want != got {
			diffs = append(diffs, &Diff{
				Attribute: name,
				Want:      want,
				Got:       got,
			})
		}
	}

	cmp(diffs, "InstanceID", cce.Status.Machine.InstanceID, bcc.InstanceID)
	cmp(diffs, "InstanceName", cce.Spec.InstanceName, bcc.InstanceName)

	// TODO: 暂不处理 BBC
	cmp(diffs, "InstanceType", string(cce.Spec.InstanceType), string(bcc.InstanceType))

	cmp(diffs, "CPU", fmt.Sprintf("%d", cce.Spec.InstanceResource.CPU), fmt.Sprintf("%d", bcc.CPUCount))
	cmp(diffs, "MEM", fmt.Sprintf("%d", cce.Spec.InstanceResource.MEM), fmt.Sprintf("%d", bcc.MemoryCapacityInGB))
	// TODO: OpenBCC 只有 GPU+FPGA 卡 TotalCount
	// cmp(diffs, "GPU", fmt.Sprintf("%d", cce.Spec.InstanceResource.GPUCount), fmt.Sprintf("%d", bcc.CardCount))

	cmp(diffs, "LocalDiskSize", fmt.Sprintf("%d", cce.Spec.InstanceResource.LocalDiskSize), fmt.Sprintf("%d", bcc.LocalDiskSizeInGB))

	cmp(diffs, "VPCID", cce.Spec.VPCConfig.VPCID, bcc.VPCID)
	cmp(diffs, "VPCSubnetID", cce.Spec.VPCConfig.VPCSubnetID, bcc.SubnetID)

	// OpenBCC/LogicBCC status 相互转换
	// cmp(diffs, "Status", strings.ToLower(string(cce.Status.MachineStatus)), strings.ToLower(bcc.Status))

	cmp(diffs, "EIP", cce.Status.Machine.EIP, bcc.PublicIP)
	cmp(diffs, "VPCIP", cce.Status.Machine.VPCIP, bcc.InternalIP)

	cmp(diffs, "ImageID", cce.Spec.ImageID, bcc.ImageID)

	return diffs, nil
}

func (c *client) cmpBECInstance(ctx context.Context, cce *ccesdk.Instance, bec *bec.VMInstance) ([]*Diff, error) {
	diffs := []*Diff{}

	if cce == nil || cce.Spec == nil || cce.Status == nil {
		return nil, errors.New("cceInstance or spec or status is nil")
	}

	if bec == nil {
		return nil, errors.New("becInstance is nil")
	}

	cmp := func(diffs []*Diff, name, want, got string) {
		if want != got {
			diffs = append(diffs, &Diff{
				Attribute: name,
				Want:      want,
				Got:       got,
			})
		}
	}

	cmp(diffs, "InstanceID", cce.Status.Machine.InstanceID, bec.VMID)
	// BEC实例在 没有这个
	// cmp(diffs, "InstanceName", cce.Spec.InstanceName, bec.VMName)

	cmp(diffs, "InstanceType", string(cce.Spec.InstanceType), "BEC")

	cmp(diffs, "CPU", fmt.Sprintf("%d", cce.Spec.InstanceResource.CPU), fmt.Sprintf("%d", bec.CPU))
	cmp(diffs, "MEM", fmt.Sprintf("%d", cce.Spec.InstanceResource.MEM), fmt.Sprintf("%d", bec.MEM))
	// TODO:
	cmp(diffs, "GPU", fmt.Sprintf("%d", cce.Spec.InstanceResource.GPUCount), fmt.Sprintf("%d", bec.GPU))

	cmp(diffs, "RootDiskSize", fmt.Sprintf("%d", cce.Spec.InstanceResource.RootDiskSize), fmt.Sprintf("%d", bec.RootDiskSize))

	// cmp(diffs, "VPCID", cce.Spec.VPCConfig.VPCID, bec.VPCID)
	// cmp(diffs, "VPCSubnetID", cce.Spec.VPCConfig.VPCSubnetID, bec.SubnetID)

	cmp(diffs, "Status", strings.ToLower(string(cce.Status.MachineStatus)), strings.ToLower(string(bec.Status)))

	cmp(diffs, "EIP", cce.Status.Machine.EIP, bec.PublicIP)
	cmp(diffs, "VPCIP", cce.Status.Machine.VPCIP, bec.InternalIP)

	cmp(diffs, "ImageID", cce.Spec.ImageID, bec.OSImage.ImageID)

	cmp(diffs, "ClusterID", cce.Spec.ClusterID, bec.CCEClusterID)

	return diffs, nil
}

func (c *client) cmpK8SNode(ctx context.Context, cluster *ccesdk.Cluster, instance *ccesdk.Instance, node *corev1.Node) ([]*Diff, error) {
	diffs := []*Diff{}

	if cluster == nil {
		return nil, errors.New("cluster is nil")
	}

	if instance == nil {
		return nil, errors.New("cceInstance is nil")
	}

	if node == nil {
		return nil, errors.New("k8sNode is nil")
	}

	cmp := func(diffs []*Diff, name, want, got string) {
		if want != got {
			diffs = append(diffs, &Diff{
				Attribute: name,
				Want:      want,
				Got:       got,
			})
		}
	}

	//  K8S Node 状态 Ready
	cmp(diffs, "NodeStatus", string(corev1.NodeRunning), string(node.Status.Phase))

	// 磁盘挂载
	if diff, err := ephemeralStorage(ctx, instance, node); err != nil {
		logger.Errorf(ctx, "ephemeralStorage failed: %s", err)
	} else if diff != nil {
		diffs = append(diffs, diff)
	}

	// MaxPods
	if diff, err := maxPods(ctx, cluster, node); err != nil {
		logger.Errorf(ctx, "maxPods failed: %s", err)
	} else if diff != nil {
		diffs = append(diffs, diff)
	}

	// System Info
	if diffList, err := systemInfo(ctx, cluster, instance, node); err != nil {
		logger.Errorf(ctx, "systemInfo failed: %s", err)
	} else if diffList != nil {
		diffs = append(diffs, diffList...)
	}

	// Capacity
	if diff, err := capacityInfo(ctx, cluster, instance, node); err != nil {
		logger.Errorf(ctx, "capacityInfo failed: %s", err)
	} else if diff != nil {
		diffs = append(diffs, diff...)
	}

	// Allocatable
	if diff, err := allocatableInfo(ctx, cluster, instance, node); err != nil {
		logger.Errorf(ctx, "allocatableInfo failed: %s", err)
	} else if diff != nil {
		diffs = append(diffs, diff...)
	}

	// PodCIDR
	cmp(diffs, "NodePodCIDR", cluster.Spec.ContainerNetworkConfig.ClusterPodCIDR, node.Spec.PodCIDR)

	// TODO: 如果支持 Hostname 这里要改
	// VPC 路由
	nodeName := utils.GetNodeName(ctx, instance.Status.Machine.VPCIP, instance.Status.Machine.Hostname, instance.Spec.InstanceName, cluster.Spec.K8SCustomConfig.EnableHostname)
	if diff, err := c.vpcRoute(ctx, cluster, nodeName); err != nil {
		logger.Errorf(ctx, "vpcRoute failed: %s", err)
	} else if diff != nil {
		diffs = append(diffs, diff...)
	}

	// Label
	if diff, err := labels(ctx, cluster, instance, node); err != nil {
		logger.Errorf(ctx, "label failed: %s", err)
	} else if diff != nil {
		diffs = append(diffs, diff...)
	}

	// ProviderID
	if diff, err := providerID(ctx, cluster, instance, node); err != nil {
		logger.Errorf(ctx, "providerID failed: %s", err)
	} else if diff != nil {
		diffs = append(diffs, diff...)
	}

	// Taint
	if diff, err := taint(ctx, cluster, instance, node); err != nil {
		logger.Errorf(ctx, "taint failed: %s", err)
	} else if diff != nil {
		diffs = append(diffs, diff...)
	}

	// 移除动态配置、去掉 dynamic config 检查
	// Kubelet 动态配置
	// if diff, err := DynamicConfig(ctx, node, nodeName); err != nil {
	//	logger.Errorf(ctx, "dynamicConfig failed: %s", err)
	// } else if diff != nil {
	//	diffs = append(diffs, diff...)
	// }

	// TODO: 对比 node  taint是否有必要。如节点启动后，ccm 运行异常，导致污点没有去掉
	cmp(diffs, "Taints", "", "")

	return diffs, nil
}
