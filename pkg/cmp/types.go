// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2020/06/16 16:42:00, by ch<PERSON><PERSON><EMAIL>, create
*/
/*
定义 CCE Cluster/Instance 与 BCC Instance 及 K8S Node 对比方法
*/

package cmp

import (
	"context"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
)

// Interface - 定义 CCE Cluster/Instance 与实际 BCC Instance 及 K8S Node 比较
type Interface interface {
	InstanceCompareWithIaaS(ctx context.Context, clusterID, cceInstanceID string, option *bce.SignOption) ([]*Diff, error)
	InstanceCompareWithK8S(ctx context.Context, clusterID, cceInstanceID string, option *bce.SignOption) ([]*Diff, error)
}

// Diff - 检查结果, 返回非期望项
type Diff struct {
	Attribute string `json:"attribute"`
	Want      string `json:"want"`
	Got       string `json:"got"`
}
