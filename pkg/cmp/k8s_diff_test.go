// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2020/06/16 16:51:00, by <EMAIL>, create
*/
/*
cmp.Interface 实现
*/

package cmp

import (
	"context"
	"reflect"
	"testing"

	gocmp "github.com/google/go-cmp/cmp"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"

	ccesdk "icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/ccev2"
	ccetypes "icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/utils"
)

func Test_maxPods(t *testing.T) {
	type args struct {
		ctx     context.Context
		cluster *ccesdk.Cluster
		node    *corev1.Node
	}
	tests := []struct {
		name    string
		args    args
		want    *Diff
		wantErr bool
	}{
		{
			name: "max pods 相等",
			args: args{
				ctx: context.TODO(),
				cluster: &ccesdk.Cluster{
					Spec: &ccesdk.ClusterSpec{
						ContainerNetworkConfig: ccetypes.ContainerNetworkConfig{
							MaxPodsPerNode: 64,
						},
					},
				},
				node: &corev1.Node{
					Status: corev1.NodeStatus{
						Capacity: corev1.ResourceList{
							corev1.ResourcePods: *(resource.NewQuantity(64, resource.DecimalSI)),
						},
					},
				},
			},
			want:    nil,
			wantErr: false,
		},
		{
			name: "max pods 不相等",
			args: args{
				ctx: context.TODO(),
				cluster: &ccesdk.Cluster{
					Spec: &ccesdk.ClusterSpec{
						ContainerNetworkConfig: ccetypes.ContainerNetworkConfig{
							MaxPodsPerNode: 64,
						},
					},
				},
				node: &corev1.Node{
					Status: corev1.NodeStatus{
						Capacity: corev1.ResourceList{
							corev1.ResourcePods: *(resource.NewQuantity(256, resource.DecimalSI)),
						},
					},
				},
			},
			want: &Diff{
				Attribute: "MaxPods",
				Got:       "256",
				Want:      "64",
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := maxPods(tt.args.ctx, tt.args.cluster, tt.args.node)
			if err != nil {
				if tt.wantErr {
					return
				}

				t.Errorf("maxPods() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if tt.wantErr {
				t.Errorf("maxPods() failed, wantErr=true got=false")
				return
			}

			if !gocmp.Equal(got, tt.want) {
				t.Errorf("maxPods() = Diff=%v", gocmp.Diff(got, tt.want))
			}
		})
	}
}

func Test_ephemeralStorage(t *testing.T) {
	type args struct {
		ctx  context.Context
		cce  *ccesdk.Instance
		node *corev1.Node
	}
	tests := []struct {
		name    string
		args    args
		want    *Diff
		wantErr bool
	}{
		{
			name: "RootDisk 匹配",
			args: args{
				ctx: context.TODO(),
				cce: &ccesdk.Instance{
					Spec: &ccesdk.InstanceSpec{
						InstanceResource: ccetypes.InstanceResource{
							RootDiskSize: 100,
						},
					},
				},
				node: &corev1.Node{
					Status: corev1.NodeStatus{
						Capacity: corev1.ResourceList{
							corev1.ResourceEphemeralStorage: *(resource.NewQuantity(95*1000*1000*1000, resource.DecimalSI)),
						},
					},
				},
			},
			want:    nil,
			wantErr: false,
		},
		{
			name: "RootDisk 不匹配",
			args: args{
				ctx: context.TODO(),
				cce: &ccesdk.Instance{
					Spec: &ccesdk.InstanceSpec{
						InstanceResource: ccetypes.InstanceResource{
							RootDiskSize: 100,
						},
					},
				},
				node: &corev1.Node{
					Status: corev1.NodeStatus{
						Capacity: corev1.ResourceList{
							corev1.ResourceEphemeralStorage: *(resource.NewQuantity(111*1000*1000*1000, resource.DecimalSI)),
						},
					},
				},
			},
			want: &Diff{
				Attribute: "EphemeralStorage",
				Got:       "111",
				Want:      "cds: 0 or root: 100",
			},
			wantErr: false,
		},
		{
			name: "CDS 匹配",
			args: args{
				ctx: context.TODO(),
				cce: &ccesdk.Instance{
					Spec: &ccesdk.InstanceSpec{
						InstanceResource: ccetypes.InstanceResource{
							RootDiskSize: 100,
							CDSList: ccetypes.CDSConfigList{
								ccetypes.CDSConfig{
									CDSSize: 300,
									Path:    "/data",
								},
							},
						},
					},
				},
				node: &corev1.Node{
					Status: corev1.NodeStatus{
						Capacity: corev1.ResourceList{
							corev1.ResourceEphemeralStorage: *(resource.NewQuantity(299*1000*1000*1000, resource.DecimalSI)),
						},
					},
				},
			},
			want:    nil,
			wantErr: false,
		},
		{
			name: "root,CDS 都不匹配",
			args: args{
				ctx: context.TODO(),
				cce: &ccesdk.Instance{
					Spec: &ccesdk.InstanceSpec{
						InstanceResource: ccetypes.InstanceResource{
							RootDiskSize: 100,
							CDSList: ccetypes.CDSConfigList{
								ccetypes.CDSConfig{
									CDSSize: 300,
								},
							},
						},
					},
				},
				node: &corev1.Node{
					Status: corev1.NodeStatus{
						Capacity: corev1.ResourceList{
							corev1.ResourceEphemeralStorage: *(resource.NewQuantity(200*1000*1000*1000, resource.DecimalSI)),
						},
					},
				},
			},
			want: &Diff{
				Attribute: "EphemeralStorage",
				Got:       "200",
				Want:      "cds: 0 or root: 100",
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := ephemeralStorage(tt.args.ctx, tt.args.cce, tt.args.node)
			if err != nil {
				if tt.wantErr {
					return
				}

				t.Errorf("ephemeralStorage() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if tt.wantErr {
				t.Errorf("ephemeralStorage() failed, wantErr=true got=false")
				return
			}

			if !gocmp.Equal(got, tt.want) {
				t.Errorf("ephemeralStorage() = Diff=%v", gocmp.Diff(got, tt.want))
			}
		})
	}
}

func TestSystemInfo(t *testing.T) {
	type args struct {
		ctx      context.Context
		cluster  *ccesdk.Cluster
		instance *ccesdk.Instance
		node     *corev1.Node
	}

	tests := []struct {
		name    string
		args    args
		want    []*Diff
		wantErr bool
	}{
		{
			name: "system info 一致",
			args: args{
				ctx: context.TODO(),
				cluster: &ccesdk.Cluster{
					Spec: &ccesdk.ClusterSpec{
						K8SVersion: "1.13.10",
					},
				},
				instance: &ccesdk.Instance{
					Spec: &ccesdk.InstanceSpec{
						InstanceOS: ccetypes.InstanceOS{
							OSName:    "CentOS",
							OSType:    "linux",
							OSVersion: "7.3",
						},
						RuntimeType:    "docker",
						RuntimeVersion: "18.09.07",
					},
				},
				node: &corev1.Node{
					Status: corev1.NodeStatus{
						NodeInfo: corev1.NodeSystemInfo{
							OperatingSystem:         "linux",
							OSImage:                 "CentOS Linux 7 (Core)",
							ContainerRuntimeVersion: "docker://18.9.7",
							KubeletVersion:          "v1.13.10",
							KubeProxyVersion:        "v1.13.10",
						},
					},
				},
			},
			want:    make([]*Diff, 0),
			wantErr: false,
		},
		{
			name: "system info 不一致",
			args: args{
				ctx: context.TODO(),
				cluster: &ccesdk.Cluster{
					Spec: &ccesdk.ClusterSpec{
						K8SVersion: "1.13.4",
					},
				},
				instance: &ccesdk.Instance{
					Spec: &ccesdk.InstanceSpec{
						InstanceOS: ccetypes.InstanceOS{
							OSName:    "Ubuntu",
							OSType:    "windows",
							OSVersion: "7.3",
						},
						RuntimeType:    "docker",
						RuntimeVersion: "18.09.07",
					},
				},
				node: &corev1.Node{
					Status: corev1.NodeStatus{
						NodeInfo: corev1.NodeSystemInfo{
							OperatingSystem:         "linux",
							OSImage:                 "CentOS Linux 7 (Core)",
							ContainerRuntimeVersion: "docker://18.9.7",
							KubeletVersion:          "v1.13.10",
							KubeProxyVersion:        "v1.13.10",
						},
					},
				},
			},
			want: []*Diff{
				{
					Attribute: "OSType",
					Got:       "linux",
					Want:      "windows",
				},
				{
					Attribute: "KubeletVersion",
					Got:       "v1.13.10",
					Want:      "1.13.4",
				},
				{
					Attribute: "KubeProxyVersion",
					Got:       "v1.13.10",
					Want:      "1.13.4",
				},
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := systemInfo(tt.args.ctx, tt.args.cluster, tt.args.instance, tt.args.node)
			if (err != nil) != tt.wantErr {
				t.Errorf("systemInfo() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("systemInfo() = Diff=%v", gocmp.Diff(got, tt.want))
			}
		})
	}
}

func TestCapacityInfo(t *testing.T) {
	type args struct {
		ctx      context.Context
		cluster  *ccesdk.Cluster
		instance *ccesdk.Instance
		node     *corev1.Node
	}
	diffList := make([]*Diff, 0)

	diffList = append(diffList, &Diff{
		Attribute: "CPU",
		Got:       "2",
		Want:      "1",
	})

	diffList = append(diffList, &Diff{
		Attribute: "MEM",
		Got:       "5039089664",
		Want:      "1",
	})

	tests := []struct {
		name    string
		args    args
		want    []*Diff
		wantErr bool
	}{
		{
			name: "capacityInfo 一致",
			args: args{
				ctx: context.TODO(),
				cluster: &ccesdk.Cluster{
					Spec: &ccesdk.ClusterSpec{
						K8SVersion: "1.13.4",
					},
				},
				instance: &ccesdk.Instance{
					Spec: &ccesdk.InstanceSpec{
						InstanceResource: ccetypes.InstanceResource{
							CPU: 1,
							MEM: 1,
						},
					},
				},
				node: &corev1.Node{
					Status: corev1.NodeStatus{
						Capacity: corev1.ResourceList{
							corev1.ResourceCPU:    *(resource.NewQuantity(1, resource.DecimalSI)),
							corev1.ResourceMemory: *(resource.NewQuantity(1039089664, resource.DecimalSI)),
						},
					},
				},
			},
			want:    make([]*Diff, 0),
			wantErr: false,
		},
		{
			name: "capacityInfo 不一致",
			args: args{
				ctx: context.TODO(),
				cluster: &ccesdk.Cluster{
					Spec: &ccesdk.ClusterSpec{
						K8SVersion: "1.13.4",
					},
				},
				instance: &ccesdk.Instance{
					Spec: &ccesdk.InstanceSpec{
						InstanceResource: ccetypes.InstanceResource{
							CPU: 1,
							MEM: 1,
						},
					},
				},
				node: &corev1.Node{
					Status: corev1.NodeStatus{
						Capacity: corev1.ResourceList{
							corev1.ResourceCPU:    *(resource.NewQuantity(2, resource.DecimalSI)),
							corev1.ResourceMemory: *(resource.NewQuantity(5039089664, resource.DecimalSI)),
						},
					},
				},
			},
			want:    diffList,
			wantErr: false,
		},
		{
			name: "capacityInfo 不一致（BBC）",
			args: args{
				ctx: context.TODO(),
				cluster: &ccesdk.Cluster{
					Spec: &ccesdk.ClusterSpec{
						K8SVersion: "1.13.4",
					},
				},
				instance: &ccesdk.Instance{
					Spec: &ccesdk.InstanceSpec{
						MachineType: ccetypes.MachineTypeBBC,
						InstanceResource: ccetypes.InstanceResource{
							CPU: 1,
							MEM: 1,
						},
					},
				},
				node: &corev1.Node{
					Status: corev1.NodeStatus{
						Capacity: corev1.ResourceList{
							corev1.ResourceCPU:    *(resource.NewQuantity(2, resource.DecimalSI)),
							corev1.ResourceMemory: *(resource.NewQuantity(5039089664, resource.DecimalSI)),
						},
					},
				},
			},
			want:    make([]*Diff, 0),
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := capacityInfo(tt.args.ctx, tt.args.cluster, tt.args.instance, tt.args.node)
			if (err != nil) != tt.wantErr {
				t.Errorf("capacityInfo() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("capacityInfo() = %v, want %v", utils.ToJSON(got), utils.ToJSON(tt.want))
			}
		})
	}
}

func Test_client_dynamicConfig(t *testing.T) {
	type args struct {
		ctx   context.Context
		node  *corev1.Node
		vpcip string
	}
	tests := []struct {
		name    string
		args    args
		want    []*Diff
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "kubelet 动态配置生效",
			args: args{
				ctx: context.TODO(),
				node: &corev1.Node{
					Spec: corev1.NodeSpec{
						ConfigSource: &corev1.NodeConfigSource{
							ConfigMap: &corev1.ConfigMapNodeConfigSource{
								Namespace: "kube-system",
								Name:      "node-config-*************",
							},
						},
					},
				},
				vpcip: "*************",
			},
			want:    []*Diff{},
			wantErr: false,
		},
		{
			name: "kubelet 动态配置未生效",
			args: args{
				ctx: context.TODO(),
				node: &corev1.Node{
					Spec: corev1.NodeSpec{},
				},
			},
			want: []*Diff{
				{
					Attribute: "KubeletDynamicConfig",
					Want:      "exists",
					Got:       "nil",
				},
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := DynamicConfig(tt.args.ctx, tt.args.node, tt.args.vpcip)
			if (err != nil) != tt.wantErr {
				t.Errorf("client.dynamicConfig() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("client.dynamicConfig() = %v, want %v", got, tt.want)
			}
		})
	}
}
