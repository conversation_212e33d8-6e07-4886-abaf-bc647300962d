// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2020/06/16 16:51:00, by <EMAIL>, create
*/
/*
cmp.Interface 实现
*/

package cmp

import (
	"context"
	"testing"

	gocmp "github.com/google/go-cmp/cmp"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bcc"
	ccesdk "icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/ccev2"
)

func Test_client_cmpInstance(t *testing.T) {
	type fields struct {
		config    *Config
		bccClient bcc.Interface
		cceClient ccesdk.Interface
	}
	type args struct {
		ctx context.Context
		cce *ccesdk.Instance
		bcc *bcc.Instance
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    []*Diff
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "对比项一致",
			args: args{
				ctx: context.TODO(),
				cce: &ccesdk.Instance{
					Spec: &ccesdk.InstanceSpec{
						InstanceName: "instance-name",
					},
					Status: &ccesdk.InstanceStatus{},
				},
				bcc: &bcc.Instance{
					InstanceName: "instance-name",
				},
			},
			want:    []*Diff{},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &client{
				config:    tt.fields.config,
				bccClient: tt.fields.bccClient,
				cceClient: tt.fields.cceClient,
			}
			got, err := c.cmpInstance(tt.args.ctx, tt.args.cce, tt.args.bcc)
			if err != nil {
				if tt.wantErr {
					return
				}

				t.Errorf("cmpInstance() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if tt.wantErr {
				t.Errorf("cmpInstance() failed, wantErr=true got=false")
				return
			}

			if !gocmp.Equal(got, tt.want) {
				t.Errorf("cmpInstance() = Diff=%v", gocmp.Diff(got, tt.want))
			}
		})
	}
}
