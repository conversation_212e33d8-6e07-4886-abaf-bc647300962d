package metrics

import (
	"testing"

	"github.com/prometheus/client_golang/prometheus"
)

func Test_InstanceControllerStepUsageTime(t *testing.T) {
	cases := []struct {
		name   string
		labels prometheus.Labels
		d      float64
	}{
		{
			name: "one label",
			labels: prometheus.Labels{
				LabelStep:       "xx",
				LabelClusterID:  "xx",
				LabelInstanceID: "xx",
			},
			d: 10,
		},
	}

	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			InstanceControllerStepUsageTime(c.labels, c.d)
		})
	}
}
