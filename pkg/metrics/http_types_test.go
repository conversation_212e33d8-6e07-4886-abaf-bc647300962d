package metrics

import (
	"testing"

	"github.com/prometheus/client_golang/prometheus"
)

func Test_SetMetricsNotPanic(t *testing.T) {
	cases := []struct {
		name   string
		labels prometheus.Labels
		d      float64
	}{
		{
			name: "one label",
			labels: prometheus.Labels{
				LabelNameMethod: "GET",
			},
			d: 10,
		},
		{
			name: "full label",
			labels: prometheus.Labels{
				LabelNameMethod:      "GET",
				LabelProxyHost:       "www.baidu.com",
				LabelNameServiceName: "test",
				LabelNamePath:        "/v1/route",
			},
			d: 20,
		},
		{
			name:   "other label",
			labels: prometheus.Labels{"bbbb": "200"},
			d:      30,
		},
	}

	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			HTTPRequestCounter(c.labels)
			HTTPRequestErrorCounter(c.labels)
			HTTPRequestLatency(c.labels, c.d)

			HTTPRequestCounterV2(c.labels)
			HTTPRequestErrorCounterV2(c.labels)
			HTTPRequestLatencyV2(c.labels, c.d)
		})
	}
}
