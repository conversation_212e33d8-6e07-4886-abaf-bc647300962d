// Copyright 2018 Baidu Inc. All rights reserved.
// Use of this source code is governed by a xxx
// license that can be found in the LICENSE file.

/*
modification history
--------------------
2021/04/08 15:18:00, by <EMAIL>, create

MonitorService 自定义 Prometheus Metrics

参考文档: https://pkg.go.dev/github.com/prometheus/client_golang/prometheus
*/

package metrics

import (
	"regexp"
	"strings"

	"github.com/prometheus/client_golang/prometheus"
)

func init() {
	prometheus.MustRegister(httpRequestCounter)
	prometheus.MustRegister(httpRequestErrorCounter)
	prometheus.MustRegister(httpRequestLatency)
}

// HTTP Request Metric Name
const (
	MetricCCEHTTPRequestCount      string = "cce_http_request_count"
	MetricCCEHTTPRequestErrorCount string = "cce_http_request_err_count"
	MetricCCEHTTPRequestLatency    string = "cce_http_request_latency"
)

// HTTP Request Label Name
const (
	LabelNameServiceName string = "service_name"
	LabelNameMethod      string = "method"
	LabelNamePath        string = "path"
	LabelProxyHost       string = "proxy_host"
	LabelStatusCode      string = "code"
)

var (
	httpRequestCounterLabels = []string{
		LabelNameServiceName,
		LabelNameMethod,
		LabelNamePath,
		LabelProxyHost,
	}

	// 避免直接通过对象操作
	httpRequestCounter = prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Name: MetricCCEHTTPRequestCount,
			Help: " HTTP request count",
		},
		httpRequestCounterLabels,
	)

	httpRequestErrorCounterLabels = []string{
		LabelNameServiceName,
		LabelNameMethod,
		LabelNamePath,
		LabelProxyHost,
		LabelStatusCode,
	}

	httpRequestErrorCounter = prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Name: MetricCCEHTTPRequestErrorCount,
			Help: "HTTP request err count",
		},
		httpRequestErrorCounterLabels,
	)

	httpRequestLatencyLabels = []string{
		LabelNameServiceName,
		LabelNameMethod,
		LabelNamePath,
		LabelProxyHost,
	}

	httpRequestLatency = prometheus.NewHistogramVec(
		prometheus.HistogramOpts{
			Name:    MetricCCEHTTPRequestLatency,
			Help:    "CCE http api request latency in seconds",
			Buckets: prometheus.ExponentialBuckets(0.005, 2, 10),
		}, httpRequestLatencyLabels,
	)

	proxyHostModPathRegex = regexp.MustCompile(`^/(v1|v2|v3|v4|v5|v6)/(eni|vpc|cce|bcc|bbc|subnet|route|securityGroup|acl|nat|IPv6Gateway|eip|endpoint|blb|appblb|lbdc|service|instance|tag|image|vm|app|monitor)$`)
)

func ensureKey(keys []string, labels prometheus.Labels) prometheus.Labels {
	ls := prometheus.Labels{}
	for _, key := range keys {
		ls[key] = labels[key]
	}

	// 除了cce-gateway proxy模式请求, 其它http server暂时去掉 path 标签
	if _, ok := ls[LabelNamePath]; ok {
		ls[LabelNamePath] = ""
		if _, ok := ls[LabelProxyHost]; ok {
			// 降低时序数量，path取前2段
			// case1:  "/v1/eni/eni-id/xxxxx" => "/v1/eni"
			// case2:  ""/v1/eni" => ""/v1/eni"

			tmpPathArr := strings.Split(labels[LabelNamePath], "/")
			if len(tmpPathArr) > 3 {
				tmpPathArr = tmpPathArr[0:3]
			}
			tmpPath := strings.Join(tmpPathArr, "/")

			if proxyHostModPathRegex.MatchString(tmpPath) {
				// 改写ls[LabelNamePath]的值
				ls[LabelNamePath] = tmpPath
			}
		}
	}
	return ls
}

func ensureKeyV2(keys []string, labels prometheus.Labels) prometheus.Labels {
	ls := prometheus.Labels{}
	for _, key := range keys {
		ls[key] = labels[key]
	}

	// 为了避免path label的高基数问题，去掉path中的url参数，目前只能比较定制的去掉
	if _, ok := ls[LabelNamePath]; ok {
		tmpPathArr := strings.Split(labels[LabelNamePath], "/")
		tmpPath := ""
		for i := 0; i < len(tmpPathArr); i++ {
			v := tmpPathArr[i]
			if strings.Contains(v, "cce-ig-") {
				tmpPath += "/" + "instanceGroupID"
				continue
			} else if strings.Contains(v, "cce-") {
				tmpPath += "/" + "clusterID"
				continue
			} else if strings.Contains(v, "i-") {
				tmpPath += "/" + "instanceID"
				continue
			} else if strings.Contains(v, "vpc-") {
				tmpPath += "/" + "vpcID"
				continue
			} else if strings.Contains(v, "g-") {
				tmpPath += "/" + "vpcID"
				continue
			}
			tmpPath += "/" + v
			if v == "node" || v == "plugin" || v == "port" || v == "dataset" || v == "workflow" {
				i++
			} else if v == "logconfigs" {
				i += 2
			}
		}

		ls[LabelNamePath] = tmpPath
	}
	return ls
}

// HTTPRequestCounter set
func HTTPRequestCounter(labels prometheus.Labels) {
	httpRequestCounter.With(ensureKey(httpRequestCounterLabels, labels)).Inc()
}

// HTTPRequestErrorCounter set
func HTTPRequestErrorCounter(labels prometheus.Labels) {
	httpRequestErrorCounter.With(ensureKey(httpRequestErrorCounterLabels, labels)).Inc()
}

// HTTPRequestLatency request latency
func HTTPRequestLatency(labels prometheus.Labels, duration float64) {
	httpRequestLatency.With(ensureKey(httpRequestLatencyLabels, labels)).Observe(duration)
}

// HTTPRequestCounterV2 set
func HTTPRequestCounterV2(labels prometheus.Labels) {
	httpRequestCounter.With(ensureKey(httpRequestCounterLabels, labels)).Inc()
}

// HTTPRequestErrorCounterV2 set
func HTTPRequestErrorCounterV2(labels prometheus.Labels) {
	httpRequestErrorCounter.With(ensureKeyV2(httpRequestErrorCounterLabels, labels)).Inc()
}

// HTTPRequestLatencyV2 request latency
func HTTPRequestLatencyV2(labels prometheus.Labels, duration float64) {
	httpRequestLatency.With(ensureKey(httpRequestLatencyLabels, labels)).Observe(duration)
}
