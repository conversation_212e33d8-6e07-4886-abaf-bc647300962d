package metrics

import (
	"testing"

	"github.com/prometheus/client_golang/prometheus"
)

func Test_ControllerNotPanic(t *testing.T) {
	cases := []struct {
		name   string
		labels prometheus.Labels
	}{
		{
			name: "one label",
			labels: prometheus.Labels{
				LabelStep: "test",
			},
		},
	}

	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			ControllerReconcileFailedCounter(c.labels)
		})
	}
}
