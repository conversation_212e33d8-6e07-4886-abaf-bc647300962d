// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2022/03/04 下午 13:22, by <EMAIL>, create
*/
/*
DESCRIPTION
CCE Workflow Controller Metrics 指标定义。
*/

package metrics

import (
	"github.com/prometheus/client_golang/prometheus"
)

const (
	// MetricsCCEWorkflowControllerWorkflowCount - Workflow 数量
	MetricsCCEWorkflowControllerWorkflowCount string = "cce_workflow_controller_workflow_count"
)

const (
	// LabelWorkflowType -  Workflow type
	LabelWorkflowType = "workflow_type"

	// LabelWorkflowPhase - Workflow Phase
	LabelWorkflowPhase = "workflow_phase"
)

func init() {
	prometheus.MustRegister(cceWorkflowControllerWorkflowCount)
}

var (
	// 统计 Workflow 数量
	cceWorkflowControllerWorkflowCount = prometheus.NewGaugeVec(
		prometheus.GaugeOpts{
			Name: MetricsCCEWorkflowControllerWorkflowCount,
			Help: "cce workflow count",
		},
		cceWorkflowControllerWorkflowCountLabels,
	)

	// 统计 Workflow 数量 Labels
	cceWorkflowControllerWorkflowCountLabels = []string{
		LabelWorkflowType,
		LabelWorkflowPhase,
	}
)

// CCEWorkflowControllerWorkflowCount - 设置 Metrics 值
func CCEWorkflowControllerWorkflowCount(labels prometheus.Labels, count int) {
	ls := ensureKey(cceWorkflowControllerWorkflowCountLabels, labels)
	cceWorkflowControllerWorkflowCount.With(ls).Set(float64(count))
}
