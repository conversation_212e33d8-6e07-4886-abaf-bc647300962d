// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2021/11/29 下午7:22, by <EMAIL>, create
*/
/*
DESCRIPTION
*/

package metrics

import "github.com/prometheus/client_golang/prometheus"

const (
	// MetricCCEInstanceGCChannelLength -
	MetricCCEInstanceGCChannelLength string = "cce_instance_gc_manager_channel_length"

	// MetricCCEInstanceGCUsageTime -
	MetricCCEInstanceGCUsageTime string = "cce_instance_gc_manager_usage_time"

	// MetricCCEInstanceGCChannelInCount -
	MetricCCEInstanceGCChannelInCount string = "cce_instance_gc_manager_channel_in_count"

	// MetricCCEInstanceGCChannelOutCount -
	MetricCCEInstanceGCChannelOutCount string = "cce_instance_gc_manager_channel_out_count"

	// MetricsCCEInstanceGCManagerInstanceCount - 竞价实例总数, 通过 Label 区分不同类型:
	MetricsCCEInstanceGCManagerInstanceCount string = "cce_instance_gc_manager_instance_count"

	// MetricsCCEInstanceRedundantMachineCount - 冗余 Machine 统计, 通过 Label 区分不同属性
	MetricsCCEInstanceRedundantMachineCount string = "cce_instance_gc_manager_redundant_machine_count"
)

const (
	// LabelChannel -
	LabelChannel = "channel"

	// LabelCCEGCManagerInspectorType -  MetricsCCEInstanceGCManagerInstanceCount Labels
	LabelCCEGCManagerInspectorType = "inspector_type"

	// LabelCCEGCManagerInstanceType - MetricsCCEInstanceGCManagerInstanceCount Status
	LabelCCEGCManagerInstanceType = "type"
)

// LabelCCEGCManagerInstanceType - 枚举类型
const (
	LabelCCEGCManagerInstanceTypeTotal        = "total"        // 竞价实例总量
	LabelCCEGCManagerInstanceTypeDeleting     = "deleting"     // 竞价实例 deleting 总量
	LabelCCEGCManagerInstanceTypeDeleteFailed = "deleteFailed" // 竞价实例 deleteFailed 总量
	LabelCCEGCManagerInstanceTypePreempted    = "preempted"    // Preempted 数量
	LabelCCEGCManagerInstanceTypeDrained      = "drained"      // Drained 数量
	LabelCCEGCManagerInstanceTypeReady        = "ready"        // Ready 数量
	LabelCCEGCManagerInstanceTypeNotReady     = "not_ready"    // NotReady 数量
	LabelCCEGCManagerInstanceTypeLeaked       = "leaked"       // 泄漏数量

	LabelCCEGCManagerInstanceTypeMachineNoInstance  = "machine_no_instance"  // 找不到 Instance 的 Machine
	LabelCCEGCManagerInstanceTypeMachineHasInstance = "machine_has_instance" // 存在 Instance 的 Machine
	LabelCCEGCManagerInstanceTypeMachineLeaked      = "machine_leaked"       // 从 "machine_no_instance" 中确认泄露的 Machine
)

func init() {
	prometheus.MustRegister(instanceGCChannelLength)
	prometheus.MustRegister(instanceGCUsageTime)
	prometheus.MustRegister(instanceGCChannelInCounter)
	prometheus.MustRegister(instanceGCChannelOutCounter)
	prometheus.MustRegister(instanceGCInstanceCount)
	prometheus.MustRegister(instanceGCRedundantMachinesCount)
}

var (
	// channel 长度
	instanceGCMangerLabels = []string{
		LabelChannel,
	}

	instanceGCChannelLength = prometheus.NewGaugeVec(
		prometheus.GaugeOpts{
			Name: MetricCCEInstanceGCChannelLength,
			Help: "cce instance gc manager channel length",
		},
		instanceGCMangerLabels,
	)

	// 每个 instance 处理时间
	instanceGCUsageTime = prometheus.NewHistogramVec(
		prometheus.HistogramOpts{
			Name:    MetricCCEInstanceGCUsageTime,
			Help:    "cce instance gc usage time in seconds",
			Buckets: prometheus.ExponentialBuckets(0.1, 2, 14),
		}, instanceGCMangerLabels,
	)

	// 出入 channel count
	instanceGCChannelInCounter = prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Name: MetricCCEInstanceGCChannelInCount,
			Help: "cce instance gc manager channel in count",
		},
		instanceGCMangerLabels,
	)

	instanceGCChannelOutCounter = prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Name: MetricCCEInstanceGCChannelOutCount,
			Help: "cce instance gc manager channel out count",
		},
		instanceGCMangerLabels,
	)

	instanceGCMangerInstanceCountLabels = []string{
		LabelClusterID,
		LabelCCEGCManagerInstanceType,
	}

	// 统计 BID Instance 数量
	instanceGCInstanceCount = prometheus.NewGaugeVec(
		prometheus.GaugeOpts{
			Name: MetricsCCEInstanceGCManagerInstanceCount,
			Help: "cce instance gc manager bid instance count",
		},
		instanceGCMangerInstanceCountLabels,
	)

	instanceGCMangerRedundantMachineLabels = []string{
		LabelClusterID,
		LabelCCEGCManagerInstanceType,
	}

	// 统计集群冗余机器数量
	instanceGCRedundantMachinesCount = prometheus.NewGaugeVec(
		prometheus.GaugeOpts{
			Name: MetricsCCEInstanceRedundantMachineCount,
			Help: "cce instance redundant machines",
		},
		instanceGCMangerRedundantMachineLabels,
	)
)

// SetInstanceGCChannelLength - set
func SetInstanceGCChannelLength(labels prometheus.Labels, count int) {
	instanceGCChannelLength.With(ensureKey(instanceGCMangerLabels, labels)).Set(float64(count))
}

// IncInstanceGCChannelLength - inc
func IncInstanceGCChannelLength(labels prometheus.Labels) {
	instanceGCChannelLength.With(ensureKey(instanceGCMangerLabels, labels)).Inc()
}

// DecInstanceGCChannelLength - dec
func DecInstanceGCChannelLength(labels prometheus.Labels) {
	instanceGCChannelLength.With(ensureKey(instanceGCMangerLabels, labels)).Dec()
}

// GetInstanceGCChannelLength - 返回给其他的 Register 使用
func GetInstanceGCChannelLength() *prometheus.GaugeVec {
	return instanceGCChannelLength
}

// InstanceGCUsageTime -
func InstanceGCUsageTime(labels prometheus.Labels, duration float64) {
	instanceGCUsageTime.With(ensureKey(instanceGCMangerLabels, labels)).Observe(duration)
}

// InstanceGCChannelInCounter -
func InstanceGCChannelInCounter(labels prometheus.Labels) {
	instanceGCChannelInCounter.With(ensureKey(instanceGCMangerLabels, labels)).Inc()
}

// InstanceGCChannelOutCounter -
func InstanceGCChannelOutCounter(labels prometheus.Labels) {
	instanceGCChannelOutCounter.With(ensureKey(instanceGCMangerLabels, labels)).Inc()
}

// InstanceGCManagerInstanceCount -
func InstanceGCManagerInstanceCount(labels prometheus.Labels, count int) {
	instanceGCInstanceCount.With(ensureKey(instanceGCMangerInstanceCountLabels, labels)).Set(float64(count))
}

// InstanceGCManagerRedundantMachineCount -
func InstanceGCManagerRedundantMachineCount(labels prometheus.Labels, count int) {
	instanceGCRedundantMachinesCount.With(ensureKey(instanceGCMangerInstanceCountLabels, labels)).Set(float64(count))
}
