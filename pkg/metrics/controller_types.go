package metrics

import "github.com/prometheus/client_golang/prometheus"

const (
	MetricCCEControllerReconcileFailed string = "cce_controller_reconcile_failed_count"
)

const (
	LabelStep = "step"
)

func init() {
	prometheus.MustRegister(controllerReconcileFailedCounter)
}

var (
	// 这里主要是记录 reconcile 出错的步骤，出错概率总体来说还是比较小, 所以设置上 cluster_id instance_id 没什么问题
	controllerReconcileFailedCounterLabels = []string{LabelStep, LabelClusterID, LabelInstanceID}
	controllerReconcileFailedCounter       = prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Name: MetricCCEControllerReconcileFailed,
			Help: "CCE controller reconcile failed count",
		},
		controllerReconcileFailedCounterLabels,
	)
)

// ControllerReconcileFailedCounter set
func ControllerReconcileFailedCounter(labels prometheus.Labels) {
	controllerReconcileFailedCounter.With(ensureKey(controllerReconcileFailedCounterLabels, labels)).Inc()
}

// GetControllerReconcileFailedCounter 返回给其他的 Register 使用
func GetControllerReconcileFailedCounter() *prometheus.CounterVec {
	return controllerReconcileFailedCounter
}
