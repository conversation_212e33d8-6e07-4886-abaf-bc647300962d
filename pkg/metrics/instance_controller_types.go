// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2022/03/04 下午 13:22, by <EMAIL>, create
*/
/*
DESCRIPTION
CCE Workflow Controller Metrics 指标定义。
*/

package metrics

import (
	"github.com/prometheus/client_golang/prometheus"
)

const (
	MetricsCCEInstanceControllerStepUsageTime string = "cce_instance_controller_step_usage_time"
	MetricCCEOPCount                          string = "cce_resource_operation_count"
	MetricCCEOPDuration                       string = "cce_resource_operation_duration_seconds"
)

const (
	LabelType   string = "type"
	LabelStatus string = "status"
	LabelId     string = "id"
)

func init() {
	prometheus.MustRegister(instanceControllerStepUsageTime)
}

var (
	instanceControllerStepUsageTimeLabels = []string{LabelStep, LabelClusterID, LabelInstanceID}
	instanceControllerStepUsageTime       = prometheus.NewGaugeVec(
		prometheus.GaugeOpts{
			Name: MetricsCCEInstanceControllerStepUsageTime,
			Help: "CCE instance controller step usage time",
		}, instanceControllerStepUsageTimeLabels,
	)

	opCounterLabels = []string{
		LabelNameServiceName,
		LabelType,
		LabelStatus,
		LabelId,
		AccountId,
	}
	opCounter = prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Name: MetricCCEOPCount,
			Help: " CCE op count",
		}, opCounterLabels,
	)
	opDuration = prometheus.NewGaugeVec(
		prometheus.GaugeOpts{
			Name: MetricCCEOPDuration,
			Help: " CCE op duration",
		}, opCounterLabels,
	)
)

func InstanceControllerStepUsageTime(labels prometheus.Labels, duration float64) {
	instanceControllerStepUsageTime.With(labels).Set(duration)
}

func CCEOpCounter(labels prometheus.Labels) {
	opCounter.With(labels).Inc()
}

func CCEOpDuration(labels prometheus.Labels, duration float64) {
	opDuration.With(labels).Set(duration)
}

// GetControllerReconcileFailedCounter 返回给其他的 Register 使用
func GetInstanceControllerStepUsageTime() *prometheus.GaugeVec {
	return instanceControllerStepUsageTime
}

func GetOpCounter() *prometheus.CounterVec {
	return opCounter
}

func GetOpDuration() *prometheus.GaugeVec {
	return opDuration
}
