package logger

import "context"

const (
	loggerFuncCallDepth = 3

	DefaultLoggerDir = "logs"
)

type Interface interface {
	Infof(context.Context, string, ...any)
	Warnf(context.Context, string, ...any)
	Errorf(context.Context, string, ...any)
}

var (
	logger   Interface
	loggerEx InterfaceEx
)

// init 初始化日志组件
func init() {
	logger = NewStdoutLogger("common", loggerFuncCallDepth)
	loggerEx = WrapExpansion(logger)
}

// SetLogger 设置日志组件
func SetLogger(loggerT Interface) {
	if loggerT != nil {
		logger = loggerT
		loggerEx = WrapExpansion(loggerT)
	}
}

// Infof 打印Info日志
func Infof(ctx context.Context, format string, v ...any) {
	logger.Infof(ctx, format, v...)
}

// Warnf 打印Warn日志
func Warnf(ctx context.Context, format string, v ...any) {
	logger.Warnf(ctx, format, v...)
}

// Errorf 打印Error日志
func Errorf(ctx context.Context, format string, v ...any) {
	logger.Errorf(ctx, format, v...)
}

// WithValues - WithValues get expansion logger with values set
func WithValues(keyAndValues ...any) InterfaceEx {
	return loggerEx.WithValues(keyAndValues...)
}
