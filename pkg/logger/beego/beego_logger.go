package beego

import (
	"context"
	"fmt"
	"os"
	"path/filepath"

	"github.com/astaxie/beego/logs"

	ctxlog "icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
)

const (
	logFuncCallDepth = 4
)

type Logger struct {
	beeLogger *logs.BeeLogger
}

// NewLogger 创建日志实例
func NewLogger(logFile string) ctxlog.Interface {
	var beeLogger *logs.BeeLogger
	beeLogger = logs.GetBeeLogger()
	beeLogger.EnableFuncCallDepth(true)
	beeLogger.SetLogFuncCallDepth(logFuncCallDepth)

	// 打印到指定路径文件
	if logFile != "" {
		dirPath := filepath.Dir(logFile)
		if err := os.MkdirAll(dirPath, 0755); err != nil {
			fmt.Printf("mkdir %s failed: %v\n", dirPath, err)
		}

		// 保留一个月日志
		config := fmt.Sprintf(`{"filename": "%s", "level":7, "daily":true, "maxdays": 30}`, logFile)
		if err := beeLogger.SetLogger(logs.AdapterFile, config); err != nil {
			panic(fmt.Sprintf("failed to set log: %v", err))
		}
	}

	return &Logger{
		beeLogger: beeLogger,
	}
}

// Infof 打印Info日志
func (l *Logger) Infof(ctx context.Context, format string, v ...any) {
	l.beeLogger.Info(ctxlog.CtxSprintf(ctx, format, v...))
}

// Warnf 打印Warn日志
func (l *Logger) Warnf(ctx context.Context, format string, v ...any) {
	l.beeLogger.Warning(ctxlog.CtxSprintf(ctx, format, v...))
}

// Errorf 打印Error日志
func (l *Logger) Errorf(ctx context.Context, format string, v ...any) {
	l.beeLogger.Error(ctxlog.CtxSprintf(ctx, format, v...))
}
