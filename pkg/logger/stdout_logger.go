package logger

import (
	"context"
	"fmt"
	"log"
	"os"
)

type StdoutLogger struct {
	log.Logger
	loggerFuncCallDepth int // 在计算文件名和行号时跟踪日志调用者的深度
}

// NewStdoutLogger 初始化Logger
func NewStdoutLogger(prefix string, loggerFuncCallDepth int) *StdoutLogger {
	return &StdoutLogger{
		Logger:              *log.New(os.Stdout, "["+prefix+"] -- ", log.Ldate|log.Ltime|log.Lmicroseconds|log.Lshortfile),
		loggerFuncCallDepth: loggerFuncCallDepth,
	}
}

// Infof 打印Info日志
func (logger *StdoutLogger) Infof(ctx context.Context, format string, v ...any) {
	if ctx != nil {
		if requestID := ctx.Value(RequestID); requestID != nil {
			_ = logger.Output(logger.loggerFuncCallDepth, fmt.Sprintf("["+requestID.(string)+"] INFO -- "+format+"\n", v...))
			return
		}
	}
	_ = logger.Output(logger.loggerFuncCallDepth, fmt.Sprintf("INFO -- "+format+"\n", v...))
}

// Warnf 打印Warn日志
func (logger *StdoutLogger) Warnf(ctx context.Context, format string, v ...any) {
	if ctx != nil {
		if requestID := ctx.Value(RequestID); requestID != nil {
			_ = logger.Output(logger.loggerFuncCallDepth, fmt.Sprintf("["+requestID.(string)+"] WARN -- "+format+"\n", v...))
			return
		}
	}
	_ = logger.Output(logger.loggerFuncCallDepth, fmt.Sprintf("WARN -- "+format+"\n", v...))
}

// Errorf 打印Error日志
func (logger *StdoutLogger) Errorf(ctx context.Context, format string, v ...any) {
	if ctx != nil {
		if requestID := ctx.Value(RequestID); requestID != nil {
			_ = logger.Output(logger.loggerFuncCallDepth, fmt.Sprintf("["+requestID.(string)+"] ERROR -- "+format+"\n", v...))
			return
		}
	}
	_ = logger.Output(logger.loggerFuncCallDepth, fmt.Sprintf("ERROR -- "+format+"\n", v...))
}
