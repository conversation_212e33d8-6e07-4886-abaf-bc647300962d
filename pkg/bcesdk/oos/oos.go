package oos

import (
	"bytes"
	"context"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"time"

	"k8s.io/apimachinery/pkg/util/wait"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/deployer/types"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
)

func (c *Client) CreateOOSTask(ctx context.Context, args *CreateOOSTaskRequest, option *bce.SignOption) (*CreateOOSTaskResponse, error) {
	if args == nil {
		return nil, errors.New("args is nil")
	}

	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}

	postContent, err := json.Marshal(args)
	if err != nil {
		return nil, err
	}

	logger.Infof(ctx, "postContent: %s", string(postContent))

	req, err := bce.NewRequest("POST", c.GetURL(getCreateOOSTaskURI(), params), bytes.NewBuffer(postContent))
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	result := new(CreateOOSTaskResponse)
	err = json.Unmarshal(bodyContent, result)
	if err != nil {
		return nil, err
	}

	return result, nil
}

func (c *Client) GetOOSTask(ctx context.Context, taskID string, option *bce.SignOption) (*CreateOOSTaskResponse, error) {
	if taskID == "" {
		return nil, errors.New("taskID is nil")
	}

	req, err := bce.NewRequest("GET", getOOSURIDetail(taskID), nil)

	if err != nil {
		return nil, err
	}
	resp, err := c.SendRequest(ctx, req, option)

	if err != nil {
		return nil, err
	}
	bodyContent, err := resp.GetBodyContent()

	if err != nil {
		return nil, err
	}
	result := new(CreateOOSTaskResponse)
	err = json.Unmarshal(bodyContent, result)
	if err != nil {
		return nil, err
	}
	return result, nil
}

func (c *Client) CreateOOSExecutionV2(ctx context.Context, args *CreateOOSExecutionRequestV2, option *bce.SignOption) (*OOSExecutionDetailV2, error) {
	if args == nil {
		return nil, errors.New("args is nil")
	}

	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}

	postContent, err := json.Marshal(args)
	if err != nil {
		return nil, err
	}

	logger.Infof(ctx, "postContent: %s", string(postContent))

	req, err := bce.NewRequest("POST", c.GetURL(getCreateOOSExecutionRequestV2URI(), params), bytes.NewBuffer(postContent))
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	result := new(OOSExecutionDetailV2)
	err = json.Unmarshal(bodyContent, result)
	if err != nil {
		return nil, err
	}

	return result, nil
}

func (c *Client) GetOOSExecutionV2(ctx context.Context, taskID string, option *bce.SignOption) (*OOSExecutionDetailV2, error) {
	if taskID == "" {
		return nil, errors.New("taskID is nil")
	}

	params := map[string]string{
		// "clientToken": c.GenerateClientToken(),
		"id": taskID,
	}

	req, err := bce.NewRequest("GET", c.GetURL(getOOSExecutionDetail(taskID), params), nil)

	if err != nil {
		return nil, err
	}
	resp, err := c.SendRequest(ctx, req, option)

	if err != nil {
		return nil, err
	}
	bodyContent, err := resp.GetBodyContent()

	if err != nil {
		return nil, err
	}
	result := new(OOSExecutionDetailV2)
	err = json.Unmarshal(bodyContent, result)
	if err != nil {
		return nil, err
	}
	return result, nil
}

func (c *Client) GetOOSAgent(ctx context.Context, args *GetOOSAgentRequest, option *bce.SignOption) (*GetOOSAgentResponse, error) {
	if args == nil {
		return nil, errors.New("args is nil")
	}

	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}

	postContent, err := json.Marshal(args)
	if err != nil {
		return nil, err
	}

	logger.Infof(ctx, "postContent: %s", string(postContent))

	req, err := bce.NewRequest("POST", c.GetURL(getGetOOSAgentURI(), params), bytes.NewBuffer(postContent))
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	result := new(GetOOSAgentResponse)
	err = json.Unmarshal(bodyContent, result)
	if err != nil {
		return nil, err
	}

	return result, nil
}

func (c *Client) GetOOSExecutionListV2(ctx context.Context, args *OOSExecutionListRequest, option *bce.SignOption) (*OOSExecutionListResponseV2, error) {
	if args == nil {
		return nil, errors.New("args is nil")
	}

	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}

	postContent, err := json.Marshal(args)
	if err != nil {
		return nil, err
	}

	logger.Infof(ctx, "postContent: %s", string(postContent))

	req, err := bce.NewRequest("POST", c.GetURL(getOOSExecutionList(), params), bytes.NewBuffer(postContent))
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	result := new(OOSExecutionListResponseV2)
	err = json.Unmarshal(bodyContent, result)
	if err != nil {
		return nil, err
	}

	return result, nil
}

// 检查 bsm agent 是否 online
func (c *Client) CheckOOSClientOnline(ctx context.Context, instanceUUID, instanceMachineID string, option *bce.SignOption) (bool, error) {
	var OOSDefaultRetry = wait.Backoff{
		Steps:    4,
		Duration: 5 * time.Second,
		Factor:   1.2,
		Jitter:   0.1,
	}
	var getOOSAgentArgs = &GetOOSAgentRequest{
		Agents: []string{instanceUUID},
		Instances: []AgentInstance{
			{
				ID:         instanceUUID,
				InstanceId: instanceMachineID,
			},
		},
	}

	var fn = func() (bool, error) {
		agents, err := c.GetOOSAgent(ctx, getOOSAgentArgs, option)
		logger.Infof(ctx, "get bsm agent: %v, err: %s", agents, err)
		if agents == nil || err != nil {
			logger.Errorf(ctx, "get bsm agent failed: %v", err)
			return false, nil
		}
		if len(agents.Result.Agents) == 0 {
			logger.Errorf(ctx, fmt.Sprintf("oos anget not found for instance: %s", instanceMachineID))
			return false, nil
		}

		if agents.Result.Agents[0].State == "ONLINE" {
			logger.Infof(ctx, fmt.Sprintf("agent %s, %s is ONLINE", instanceMachineID, instanceUUID))
			return true, nil
		} else if agents.Result.Agents[0].State != "ONLINE" {
			logger.Errorf(ctx, fmt.Sprintf("agent %s, %s is OFFLINE", instanceMachineID, instanceUUID))
			return false, nil
		}
		return false, fmt.Errorf(fmt.Sprintf("agent %s, %s is OFFLINE", instanceMachineID, instanceUUID))
	}

	// 成功返回：(true, nil)
	// 失败重试返回：(false, nil)
	// 失败不重试返回：(false, error) or (true, error)
	retryErr := wait.ExponentialBackoff(OOSDefaultRetry, fn)

	if retryErr != nil {
		return false, retryErr
	}

	return true, nil
}

func (c *Client) ExecOOSTask(ctx context.Context, scripts, OOSTaskName string, instanceConfig *types.Instance, option *bce.SignOption) (string, string, error) {
	// // 该任务执行函数，往往在初始化 execClient 就开始执行任务，因为在初始化时已经检测 agent 是 online 了，所以此处不在重复检查是否 online
	// online, err := c.CheckOOSClientOnline(ctx, instanceConfig.InstanceUUID, instanceConfig.MachineID, option)
	// if err != nil {
	//	logger.Errorf(ctx, "CheckOOSClientOnline err: %v", err)
	//	return "", "", err
	// }
	// if !online {
	//	logger.Errorf(ctx, "CheckOOSClientOnline err: %v", err)
	//	return "", "", fmt.Errorf("bsm agent is not online")
	// }

	args, err := c.getCreateExecutionTaskRequest(ctx, scripts, OOSTaskName, instanceConfig)
	if err != nil {
		logger.Errorf(ctx, "getCreateExecutionTaskRequest err: %v", err)
		return "", "", err
	}
	logger.Infof(ctx, "args: %v", args)
	detail, err := c.CreateOOSExecutionV2(ctx, args, option)
	if err != nil {
		logger.Errorf(ctx, "CreateOOSExecutionV2 err: %v", err)
		return "", "", err
	}
	if detail == nil || detail.Result.Id == "" {
		logger.Errorf(ctx, "CreateOOSExecutionV2 result is nil")
		return "", "", errors.New("CreateOOSExecutionV2 result is nil")
	}
	executionId := detail.Result.Id

	maxRetry := 5
	for i := 0; i < maxRetry; i++ {
		executionDetail, err := c.GetOOSExecutionV2(ctx, executionId, option)
		if err != nil {
			msg := fmt.Sprintf("get task: %s execution err: %v", executionId, err)
			logger.Errorf(ctx, msg)
			return "", "", fmt.Errorf(msg)
		}
		executionDetailJson, err := json.Marshal(executionDetail)
		if err != nil {
			logger.Warnf(ctx, "Marshal execution detail err: %v", err)
			time.Sleep(3 * time.Second)
			continue
		}
		logger.Infof(ctx, "execution detail json: %s", string(executionDetailJson))
		if len(executionDetail.Result.Tasks) == 0 {
			logger.Infof(ctx, "task %s execution err: Tasks is nil", executionId)
			time.Sleep(3 * time.Second)
			continue
		}
		if executionDetail.Result.Tasks[0].State == "SUCCESS" {
			logger.Infof(ctx, "task %s execution is done", executionId)
			if executionDetail.Result.Tasks[0].OutputContext.ExitCode == 0 {
				return executionDetail.Result.Tasks[0].OutputContext.Stdout, "", nil
			}
			logger.Errorf(ctx, "task %s execution err: exit code: %d", executionId, executionDetail.Result.Tasks[0].OutputContext.ExitCode)
			logger.Errorf(ctx, "stderr is %s", executionDetail.Result.Tasks[0].OutputContext.Stderr)
			return executionDetail.Result.Tasks[0].OutputContext.Stdout, executionDetail.Result.Tasks[0].OutputContext.Stderr,
				fmt.Errorf(fmt.Sprintf("oos task %s, exit code: %d, stderr: %s", executionId, executionDetail.Result.Tasks[0].OutputContext.ExitCode, executionDetail.Result.Tasks[0].OutputContext.Stderr))
		}
		if executionDetail.Result.Tasks[0].State == "FAILED" {
			logger.Errorf(ctx, "task %s execution FAILED: exit code: %d", executionId, executionDetail.Result.Tasks[0].OutputContext.ExitCode)
			logger.Errorf(ctx, "stderr is %s", executionDetail.Result.Tasks[0].OutputContext.Stderr)
			return executionDetail.Result.Tasks[0].OutputContext.Stdout, executionDetail.Result.Tasks[0].OutputContext.Stderr,
				fmt.Errorf(fmt.Sprintf("oos task %s FAILED, exit code: %d, stderr: %s", executionId, executionDetail.Result.Tasks[0].OutputContext.ExitCode, executionDetail.Result.Tasks[0].OutputContext.Stderr))
		}

		logger.Infof(ctx, "wait task %s, state is %s", executionId, executionDetail.Result.Tasks[0].State)
		if i == (maxRetry - 1) {
			logger.Errorf(ctx, fmt.Sprintf("timeout for execution: {%s}", executionId))
			return executionDetail.Result.Tasks[0].OutputContext.Stdout, executionDetail.Result.Tasks[0].OutputContext.Stderr,
				fmt.Errorf(fmt.Sprintf("timeout for execution: {%s}", executionId))
		}
		time.Sleep(3 * time.Second)
	}

	return "", "", fmt.Errorf("timeout for execution: {%s}", OOSTaskName)
}

func (c *Client) getCreateExecutionTaskRequest(ctx context.Context, scripts, ExecutionName string, instanceConfig *types.Instance) (*CreateOOSExecutionRequestV2, error) {
	encodeString := base64.StdEncoding.EncodeToString([]byte(scripts))

	// 支持脚本执行结果输出到 stdout && stderr
	writeScripts := fmt.Sprintf("mkdir -p /deploy/ && echo '%s' | base64 -d > /deploy/%s.sh && chmod a+x /deploy/%s.sh && /deploy/%s.sh",
		encodeString, ExecutionName, ExecutionName, ExecutionName)
	oosScripts := []string{}
	oosScripts = append(oosScripts, writeScripts)
	oosScripts = append(oosScripts, "exit_code=$?")
	oosScripts = append(oosScripts, "exit $exit_code")
	scriptString := strings.Join(oosScripts, "\n") + "\n"
	/**
	exit_code=$?
	exit $exit_code
	*/
	logger.Infof(ctx, "scripts: %s", scriptString)
	createOOSExecutionRequestV2 := CreateOOSExecutionRequestV2{
		Template: Template{
			Name:   c.getOOSTaskName(ExecutionName, instanceConfig.CCEInstanceID, instanceConfig.MachineID),
			Region: instanceConfig.Region,
			Linear: true,
			Operators: []Operator{
				{
					Name:          "run_scripts",
					Description:   "description",
					Operator:      "BCE::Agent::ExecuteShell",
					Retries:       0,
					RetryInterval: 3000,
					// 毫秒
					Timeout: 60000,
					Properties: map[string]any{
						"content": scriptString,
						"user":    "root",
						"workDir": "/",
						"__workerSelectors__": map[string]string{
							"id":      instanceConfig.InstanceUUID,
							"shortId": instanceConfig.MachineID,
						},
					},
				},
			},
		},
	}

	return &createOOSExecutionRequestV2, nil
}

func (c *Client) getOOSTaskName(OOSTaskName, CCEInstanceID, MachineID string) string {
	return fmt.Sprintf("%s-%s-%s", CCEInstanceID, MachineID, OOSTaskName)
}
