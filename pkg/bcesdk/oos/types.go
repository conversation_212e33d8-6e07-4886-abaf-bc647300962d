// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2019/11/26 15:30:00, by <EMAIL>, create
*/
/*
文件定义 BLB OpenAPI 相关接口
*/

package oos

import (
	"context"
	"time"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
)

//go:generate mockgen -destination=./mock/mock_client.go -package=mock icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/oos Interface

// Interface 定义 BLB OpenAPI 相关 Interface
type OOSInterface interface {
	SetDebug(bool)
	GetOOSTask(ctx context.Context, taskID string, option *bce.SignOption) (*CreateOOSTaskResponse, error)
	CreateOOSExecutionV2(ctx context.Context, args *CreateOOSExecutionRequestV2, option *bce.SignOption) (*OOSExecutionDetailV2, error)
	GetOOSExecutionV2(ctx context.Context, taskID string, option *bce.SignOption) (*OOSExecutionDetailV2, error)
	GetOOSAgent(ctx context.Context, args *GetOOSAgentRequest, option *bce.SignOption) (*GetOOSAgentResponse, error)
	CreateOOSTask(ctx context.Context, args *CreateOOSTaskRequest, option *bce.SignOption) (*CreateOOSTaskResponse, error)
	GetOOSExecutionListV2(ctx context.Context, args *OOSExecutionListRequest, option *bce.SignOption) (*OOSExecutionListResponseV2, error)
}

const (
	// https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/KzHUM_sAtc/8YYDzrTwHn/MlmvryIiNJq4Hu
	ResourceHeaderHexKey = "Hvw1j33tecGBBkVY"
)

type CreateOOSTaskRequest struct {
	ShareType     string   `json:"shareType"`
	ExecutionMode string   `json:"executionMode"`
	Description   string   `json:"description"`
	TemplateId    string   `json:"templateId"`
	ExecutionName string   `json:"executionName"`
	Tags          []string `json:"tags"`
	TemplateName  string   `json:"templateName"`
	RegionId      string   `json:"regionId"`
	Parameters    string   `json:"parameters"`
}

type Params struct {
	Region         string      `json:"region"`
	Targets        Targets     `json:"targets"`
	CommandContent string      `json:"commandContent"`
	CommandType    string      `json:"commandType"`
	TemplateName   string      `json:"templateName"`
	ExecutionName  string      `json:"executionName"`
	RateControl    RateControl `json:"rateControl"`
}

type Targets struct {
	Type        string     `json:"type"`
	Region      string     `json:"region"`
	ResourceIds []Resource `json:"resourceIds"`
}

type Resource struct {
	InstanceId   string `json:"instanceId"`
	InstanceUUID string `json:"instanceUUID"`
}

type RateControl struct {
	Mode        string `json:"mode"`
	Concurrency int    `json:"concurrency"`
	MaxErrors   int    `json:"maxErrors"`
}

type CreateOOSTaskResponse struct {
	Success bool   `json:"success"`
	Msg     string `json:"msg"`
	Code    int    `json:"code"`
	Result  []Res  `json:"result"`
}

type Res struct {
	Id                int       `json:"id"`
	ExecutionId       string    `json:"executionId"`
	ParentExecutionId string    `json:"parentExecutionId"`
	IsParent          int       `json:"isParent"`
	UserId            string    `json:"userId"`
	Outputs           string    `json:"outputs"`
	Status            string    `json:"status"`
	Description       string    `json:"description"`
	Timeout           int       `json:"timeout"`
	Rollback          int       `json:"rollback"`
	Deleted           int       `json:"deleted"`
	StartTime         time.Time `json:"startTime"`
	EndTime           time.Time `json:"endTime"`
	Tags              []any     `json:"tags"`
}

func getCreateOOSTaskURI() string {
	return "/api/logic/oos/v1/execution/create/public/new"
}

func getOOSURIDetail(taskID string) string {
	return "/api/logic/oos/v1/execution/new/" + taskID
}
