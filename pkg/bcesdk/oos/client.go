package oos

import (
	"context"
	"time"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/retrypolicy"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
)

// Endpoint contains all endpoints of Baidu Cloud BCC.
// 新增 yq endpoints
var Endpoint = map[string]string{
	"bj":  "oos.bj.baidubce.com",
	"gz":  "oos.gz.baidubce.com",
	"su":  "oos.su.baidubce.com",
	"fwh": "oos.fwh.baidubce.com",
	"bd":  "oos.bd.baidubce.com",
	"yq":  "oos.yq.baidubce.com",
}

// var _ Interface = &Client{}

// Client is the bos client implemention for Baidu Cloud BOS API.
type Client struct {
	*bce.Client
}

// NewClient 初始化 BLB Client
func NewClient(config *bce.Config) *Client {
	return &Client{
		Client: bce.NewClient(config),
	}
}

// GetURL generates the full URL of http request for Baidu Cloud BOS API.
func (c *Client) GetURL(version string, params map[string]string) string {
	host := c.Endpoint
	if host == "" {
		host = Endpoint[c.GetRegion()]
	}
	uriPath := version
	return c.Client.GetURL(host, uriPath, params)
}

// SetDebug 是否开启 Debug 模式
func (c *Client) SetDebug(debug bool) {
	c.Client.SetDebug(debug)
}

func BCEConfigWithRetry(ctx context.Context, endpoint string, timeout int, region string, retry int) *bce.Config {
	// endpoint 为空没有关系，最多请求会不通。如果这个方法返回nil，没有error，调用方会空指针
	if endpoint == "" {
		logger.Errorf(ctx, "Generate bce.Config: endpoint is empty")
	}

	return &bce.Config{
		Checksum:    true,
		Endpoint:    endpoint,
		Timeout:     time.Duration(timeout) * time.Second,
		Region:      region,
		RetryPolicy: retrypolicy.NewIntervalRetryPolicy(ctx, retry, 0),
	}
}
