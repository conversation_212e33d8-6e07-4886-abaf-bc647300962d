// Code generated by MockGen. DO NOT EDIT.
// Source: icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/oos (interfaces: Interface)

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	bce "icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
	oos "icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/oos"
)

// MockInterface is a mock of Interface interface.
type MockInterface struct {
	ctrl     *gomock.Controller
	recorder *MockInterfaceMockRecorder
}

// MockInterfaceMockRecorder is the mock recorder for MockInterface.
type MockInterfaceMockRecorder struct {
	mock *MockInterface
}

// NewMockInterface creates a new mock instance.
func NewMockInterface(ctrl *gomock.Controller) *MockInterface {
	mock := &MockInterface{ctrl: ctrl}
	mock.recorder = &MockInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockInterface) EXPECT() *MockInterfaceMockRecorder {
	return m.recorder
}

// CreateOOSExecutionV2 mocks base method.
func (m *MockInterface) CreateOOSExecutionV2(arg0 context.Context, arg1 *oos.CreateOOSExecutionRequestV2, arg2 *bce.SignOption) (*oos.OOSExecutionDetailV2, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateOOSExecutionV2", arg0, arg1, arg2)
	ret0, _ := ret[0].(*oos.OOSExecutionDetailV2)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateOOSExecutionV2 indicates an expected call of CreateOOSExecutionV2.
func (mr *MockInterfaceMockRecorder) CreateOOSExecutionV2(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateOOSExecutionV2", reflect.TypeOf((*MockInterface)(nil).CreateOOSExecutionV2), arg0, arg1, arg2)
}

// CreateOOSTask mocks base method.
func (m *MockInterface) CreateOOSTask(arg0 context.Context, arg1 *oos.CreateOOSTaskRequest, arg2 *bce.SignOption) (*oos.CreateOOSTaskResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateOOSTask", arg0, arg1, arg2)
	ret0, _ := ret[0].(*oos.CreateOOSTaskResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateOOSTask indicates an expected call of CreateOOSTask.
func (mr *MockInterfaceMockRecorder) CreateOOSTask(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateOOSTask", reflect.TypeOf((*MockInterface)(nil).CreateOOSTask), arg0, arg1, arg2)
}

// GetOOSAgent mocks base method.
func (m *MockInterface) GetOOSAgent(arg0 context.Context, arg1 *oos.GetOOSAgentRequest, arg2 *bce.SignOption) (*oos.GetOOSAgentResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOOSAgent", arg0, arg1, arg2)
	ret0, _ := ret[0].(*oos.GetOOSAgentResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOOSAgent indicates an expected call of GetOOSAgent.
func (mr *MockInterfaceMockRecorder) GetOOSAgent(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOOSAgent", reflect.TypeOf((*MockInterface)(nil).GetOOSAgent), arg0, arg1, arg2)
}

// GetOOSExecutionListV2 mocks base method.
func (m *MockInterface) GetOOSExecutionListV2(arg0 context.Context, arg1 *oos.OOSExecutionListRequest, arg2 *bce.SignOption) (*oos.OOSExecutionListResponseV2, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOOSExecutionListV2", arg0, arg1, arg2)
	ret0, _ := ret[0].(*oos.OOSExecutionListResponseV2)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOOSExecutionListV2 indicates an expected call of GetOOSExecutionListV2.
func (mr *MockInterfaceMockRecorder) GetOOSExecutionListV2(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOOSExecutionListV2", reflect.TypeOf((*MockInterface)(nil).GetOOSExecutionListV2), arg0, arg1, arg2)
}

// GetOOSExecutionV2 mocks base method.
func (m *MockInterface) GetOOSExecutionV2(arg0 context.Context, arg1 string, arg2 *bce.SignOption) (*oos.OOSExecutionDetailV2, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOOSExecutionV2", arg0, arg1, arg2)
	ret0, _ := ret[0].(*oos.OOSExecutionDetailV2)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOOSExecutionV2 indicates an expected call of GetOOSExecutionV2.
func (mr *MockInterfaceMockRecorder) GetOOSExecutionV2(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOOSExecutionV2", reflect.TypeOf((*MockInterface)(nil).GetOOSExecutionV2), arg0, arg1, arg2)
}

// GetOOSTask mocks base method.
func (m *MockInterface) GetOOSTask(arg0 context.Context, arg1 string, arg2 *bce.SignOption) (*oos.CreateOOSTaskResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOOSTask", arg0, arg1, arg2)
	ret0, _ := ret[0].(*oos.CreateOOSTaskResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOOSTask indicates an expected call of GetOOSTask.
func (mr *MockInterfaceMockRecorder) GetOOSTask(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOOSTask", reflect.TypeOf((*MockInterface)(nil).GetOOSTask), arg0, arg1, arg2)
}

// SetDebug mocks base method.
func (m *MockInterface) SetDebug(arg0 bool) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SetDebug", arg0)
}

// SetDebug indicates an expected call of SetDebug.
func (mr *MockInterfaceMockRecorder) SetDebug(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetDebug", reflect.TypeOf((*MockInterface)(nil).SetDebug), arg0)
}
