package oos

func getCreateOOSExecutionRequestV2URI() string {
	return "/api/logic/oos/v2/execution"
}

func getOOSExecutionDetail(taskID string) string {
	return "/api/logic/oos/v2/execution"
}

func getGetOOSAgentURI() string {
	return "/api/logic/oos/v1/agent/batch"
}

func getOOSExecutionList() string {
	return "/api/logic/oos/v2/execution/list"
}

// create oos execution
type CreateOOSExecutionRequestV2 struct {
	Template Template `json:"template"`
}

type Template struct {
	Name      string     `json:"name"`
	Region    string     `json:"region"`
	Linear    bool       `json:"linear"`
	Operators []Operator `json:"operators"`
}

type Operator struct {
	Name          string `json:"name"`
	Description   string `json:"description"`
	Operator      string `json:"operator"`
	Retries       int    `json:"retries"`
	RetryInterval int    `json:"retryInterval"`
	Timeout       int    `json:"timeout"`

	// Properties    []Properties `json:"properties"`
	Properties map[string]any `json:"properties"`
}

type Properties struct {
	Name  string `json:"name"`
	Value any    `json:"value"`
}

type Event struct {
	Resource ResourceV2 `json:"resource"`
}

type ResourceV2 struct {
	Type string           `json:"type"`
	Id   map[string][]any `json:"id"`
}

type InstanceId struct {
	Id      string `json:"id"`
	ShortId string `json:"shortId"`
}

// detail
type OOSExecutionDetailV2 struct {
	Success bool   `json:"success"`
	Msg     string `json:"msg"`
	Code    int    `json:"code"`
	Result  Result `json:"result"`
}

type Result struct {
	Id                string           `json:"id"`
	Template          Template         `json:"template"`
	CreatedTimestamp  int64            `json:"createdTimestamp"`
	UpdatedTimestamp  int64            `json:"updatedTimestamp"`
	FinishedTimestamp int64            `json:"finishedTimestamp"`
	State             string           `json:"state"`
	Properties        resultProperties `json:"properties"`
	Tasks             []Task           `json:"tasks"`
	Reason            string           `json:"reason"`
}

type OOSExecutionListResponseV2 struct {
	Success bool       `json:"success"`
	Msg     string     `json:"msg"`
	Code    int        `json:"code"`
	Result  ListResult `json:"result"`
}

type ListResult struct {
	Executions []Result `json:"executions"`
	OrderBy    string   `json:"orderBy"`
	Order      string   `json:"order"`
	PageNo     int      `json:"pageNo"`
	PageSize   int      `json:"pageSize"`
	TotalCount int      `json:"totalCount"`
}

type ExecutionListResult struct {
}

type resultProperties struct {
	InstanceId []InstanceId `json:"instanceId"`
}

type Task struct {
	Id                string        `json:"id"`
	Dag               Dag           `json:"dag"`
	CreatedTimestamp  int64         `json:"createdTimestamp"`
	UpdatedTimestamp  int64         `json:"updatedTimestamp"`
	FinishedTimestamp int64         `json:"finishedTimestamp"`
	State             string        `json:"state"`
	Operator          Operator      `json:"operator"`
	OutputContext     OutputContext `json:"outputContext"`
	Tries             int           `json:"tries"`
}

type OutputContext struct {
	ExitCode int    `json:"exitCode"`
	Stderr   string `json:"stderr"`
	Stdout   string `json:"stdout"`
}

type Dag struct {
	Id string `json:"id"`
}

type GetOOSAgentRequest struct {
	Agents    []string        `json:"agents"`
	Instances []AgentInstance `json:"instances"`
}

type AgentInstance struct {
	ID         string `json:"id"`
	InstanceId string `json:"instanceId"`
}

type GetOOSAgentResponse struct {
	Success bool                      `json:"success"`
	Msg     string                    `json:"msg"`
	Result  GetOOSAgentResponseResult `json:"result"`
}

type Agent struct {
	Id      string `json:"id"`
	State   string `json:"state"`
	Version string `json:"version"`
}

type GetOOSAgentResponseResult struct {
	Agents []Agent `json:"agents"`
}

type OOSExecutionListRequest struct {
	Template          TemplateList `json:"template"`
	State             string       `json:"state"`
	CronExecutionName string       `json:"cronExecutionName"`
	StartTime         int64        `json:"startTime"`
	EndTime           int64        `json:"endTime"`
	Sort              string       `json:"sort"`
	Ascending         bool         `json:"ascending"`
	PageNo            int          `json:"pageNo"`
	PageSize          int          `json:"pageSize"`
}

type TemplateList struct {
	Type string `json:"type"`
	Name string `json:"name"`
}
