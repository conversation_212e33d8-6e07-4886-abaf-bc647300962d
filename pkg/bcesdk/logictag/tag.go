package logictag

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"strconv"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
)

func (c *Client) SetDebug(debug bool) {
	c.Client.SetDebug(debug)
}

// DeleteTagAssociations 本接口用于删除资源与标签的绑定关系
//
//	每次只能解绑一个资源
func (c *Client) DeleteTagAssociations(ctx context.Context, args *DeleteTagAssociationsArgs, option *bce.SignOption) error {
	if args == nil {
		return errors.New("args is nil")
	}
	params := map[string]string{
		"deleteTagAssociation": "",
		"clientToken":          c.GenerateClientToken(),
	}

	putContent, err := json.Marshal(args)
	if err != nil {
		return err
	}

	req, err := bce.NewRequest("PUT", c.GetURL("/api/logical/tag/v1", params), bytes.NewBuffer(putContent))
	if err != nil {
		return err
	}

	_, err = c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}
	return nil
}

// QueryUserTagsInfoList 本接口标签与资源关联关系的双向查询
//
//	可根据标签键，标签值，资源ID等多种条件进行查询
func (c *Client) QueryUserTagsInfoList(ctx context.Context, strongAssociation bool, args *QueryUserTagListArgs, option *bce.SignOption) (*AssociationsResult, error) {
	if args == nil {
		return nil, errors.New("args is nil")
	}
	params := map[string]string{
		"queryFullList":     "",
		"strongAssociation": strconv.FormatBool(strongAssociation),
		"clientToken":       c.GenerateClientToken(),
	}

	postContent, err := json.Marshal(args)
	if err != nil {
		return nil, err
	}

	req, err := bce.NewRequest("POST", c.GetURL("/api/logical/tag/v1", params), bytes.NewBuffer(postContent))
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}
	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var result AssociationsResult
	err = json.Unmarshal(bodyContent, &result)
	if err != nil {
		return nil, err
	}

	return &result, nil
}

// UpdateResourceAssociations 本接口用于更新资源与标签的绑定关系
//
//	传参是需传入资源的全量标签信息，如原有3个标签，现删除1个标签的情况下新增2个标签，则应传入全量4个标签
func (c *Client) UpdateResourceAssociations(ctx context.Context, args *CreateAndAssignArgs, option *bce.SignOption) error {
	if args == nil {
		return errors.New("args is nil")
	}
	params := map[string]string{
		"createAndAssign": "",
		"clientToken":     c.GenerateClientToken(),
	}

	postContent, err := json.Marshal(args)
	if err != nil {
		return err
	}

	req, err := bce.NewRequest("POST", c.GetURL("/api/logical/tag/v1", params), bytes.NewBuffer(postContent))
	if err != nil {
		return err
	}

	_, err = c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}
	return nil
}
