// Code generated by MockGen. DO NOT EDIT.
// Source: icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/logictag (interfaces: Interface)

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	bce "icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
	logictag "icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/logictag"
)

// MockInterface is a mock of Interface interface.
type MockInterface struct {
	ctrl     *gomock.Controller
	recorder *MockInterfaceMockRecorder
}

// MockInterfaceMockRecorder is the mock recorder for MockInterface.
type MockInterfaceMockRecorder struct {
	mock *MockInterface
}

// NewMockInterface creates a new mock instance.
func NewMockInterface(ctrl *gomock.Controller) *MockInterface {
	mock := &MockInterface{ctrl: ctrl}
	mock.recorder = &MockInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockInterface) EXPECT() *MockInterfaceMockRecorder {
	return m.recorder
}

// DeleteTagAssociations mocks base method.
func (m *MockInterface) DeleteTagAssociations(arg0 context.Context, arg1 *logictag.DeleteTagAssociationsArgs, arg2 *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteTagAssociations", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteTagAssociations indicates an expected call of DeleteTagAssociations.
func (mr *MockInterfaceMockRecorder) DeleteTagAssociations(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteTagAssociations", reflect.TypeOf((*MockInterface)(nil).DeleteTagAssociations), arg0, arg1, arg2)
}

// QueryUserTagsInfoList mocks base method.
func (m *MockInterface) QueryUserTagsInfoList(arg0 context.Context, arg1 bool, arg2 *logictag.QueryUserTagListArgs, arg3 *bce.SignOption) (*logictag.AssociationsResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryUserTagsInfoList", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*logictag.AssociationsResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryUserTagsInfoList indicates an expected call of QueryUserTagsInfoList.
func (mr *MockInterfaceMockRecorder) QueryUserTagsInfoList(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryUserTagsInfoList", reflect.TypeOf((*MockInterface)(nil).QueryUserTagsInfoList), arg0, arg1, arg2, arg3)
}

// SetDebug mocks base method.
func (m *MockInterface) SetDebug(arg0 bool) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SetDebug", arg0)
}

// SetDebug indicates an expected call of SetDebug.
func (mr *MockInterfaceMockRecorder) SetDebug(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetDebug", reflect.TypeOf((*MockInterface)(nil).SetDebug), arg0)
}

// UpdateResourceAssociations mocks base method.
func (m *MockInterface) UpdateResourceAssociations(arg0 context.Context, arg1 *logictag.CreateAndAssignArgs, arg2 *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateResourceAssociations", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateResourceAssociations indicates an expected call of UpdateResourceAssociations.
func (mr *MockInterfaceMockRecorder) UpdateResourceAssociations(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateResourceAssociations", reflect.TypeOf((*MockInterface)(nil).UpdateResourceAssociations), arg0, arg1, arg2)
}
