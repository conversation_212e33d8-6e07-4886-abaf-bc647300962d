package logictag

import (
	"context"
	"errors"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
)

type FakeClient struct {
	tags   []Tag
	tagMap map[Tag][]Resource
}

func NewFakeClient() *FakeClient {
	return &FakeClient{
		tags:   make([]Tag, 0),
		tagMap: make(map[Tag][]Resource),
	}
}

func (f *FakeClient) SetDebug(debug bool) {
	return
}

func (f *FakeClient) CreateTag(ctx context.Context, args *CreateTagsArgs, option *bce.SignOption) error {
	if args == nil {
		return errors.New("args is nil")
	}
	for _, tag := range args.Tags {
		if !checkTagListContains(f.tags, tag) {
			f.tags = append(f.tags, tag)
			f.tagMap[tag] = make([]Resource, 0)
		}
	}
	return nil
}

func checkTagListContains(list []Tag, tag Tag) bool {
	for _, candidate := range list {
		if candidate == tag {
			return true
		}
	}
	return false
}

func (f *FakeClient) DeleteTags(ctx context.Context, tags []Tag, option *bce.SignOption) error {
	for _, toDeleteTag := range tags {
		tempTags := make([]Tag, 0)
		for _, tag := range f.tags {
			if tag != toDeleteTag {
				tempTags = append(tempTags, tag)
			}
		}
		f.tags = tempTags
		f.tagMap[toDeleteTag] = make([]Resource, 0)
	}
	return nil
}

func checkResourceContains(list []Resource, resource Resource) bool {
	for _, candidate := range list {
		if candidate == resource {
			return true
		}
	}
	return false
}

func (f *FakeClient) DeleteTagAssociations(ctx context.Context, args *DeleteTagAssociationsArgs, option *bce.SignOption) error {
	if args == nil {
		return errors.New("args is nil")
	}

	for tag, resources := range f.tagMap {
		resourceList := make([]Resource, 0)
		for _, targetResource := range resources {
			if targetResource != args.Resource {
				resourceList = append(resourceList, targetResource)
			}
		}
		f.tagMap[tag] = resourceList
	}

	return nil
}

func (f *FakeClient) QueryUserTagsInfoList(ctx context.Context, strongAssociation bool, args *QueryUserTagListArgs, option *bce.SignOption) (*AssociationsResult, error) {
	if args == nil {
		return nil, errors.New("args is nil")
	}
	return &AssociationsResult{}, nil
}

func (f *FakeClient) UpdateResourceAssociations(ctx context.Context, args *CreateAndAssignArgs, option *bce.SignOption) error {
	if args == nil {
		return errors.New("args is nil")
	}
	return nil
}
