package logictag

import (
	"context"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
)

//go:generate mockgen -destination=./mock/mock_client.go -package=mock icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/logictag Interface

// Interface tag interface
// 标签服务的同学推荐云上服务使用内部接口
// http://bce.console.baidu-int.com/bce-doc/doc/view/f8ab7877-b1bf-447b-b0c4-469137fc8152#%E5%85%A8%E9%87%8F%E6%9B%B4%E6%96%B0%E6%A0%87%E7%AD%BE%E4%BF%A1%E6%81%AF
type Interface interface {
	SetDebug(bool)

	// ListBoundResources(ctx context.Context, args *ListBoundResourcesParams, option *bce.SignOption) (*ListBoundResourcesResult, error)
	DeleteTagAssociations(ctx context.Context, args *DeleteTagAssociationsArgs, option *bce.SignOption) error

	QueryUserTagsInfoList(ctx context.Context, strongAssociation bool, args *QueryUserTagListArgs, option *bce.SignOption) (*AssociationsResult, error)
	UpdateResourceAssociations(ctx context.Context, args *CreateAndAssignArgs, option *bce.SignOption) error
}

type ServiceType string

const (
	ServiceTypeBLB ServiceType = "BLB"
	ServiceTypeEIP ServiceType = "EIP"
	ServiceTypeBCC ServiceType = "BCC"
	ServiceTypeCCE ServiceType = "CCE"
	ServiceTypeBEC ServiceType = "BEC"
)

type Tag struct {
	TagKey   string `json:"tagKey"`
	TagValue string `json:"tagValue"`
}

type Resource struct {
	// 资源uuid 如 cce-xxxx
	ResourceUUID string `json:"resourceUuid"`

	// 服务类型 CCE
	ServiceType string `json:"serviceType"`

	// 区域 bj
	Region string `json:"region"`
}

type CreateTagsArgs struct {
	// 待创建的标签键值对列表 必需
	Tags []Tag `json:"tags"`
}

type DeleteTagAssociationsArgs struct {
	// 待解绑的资源
	Resource Resource `json:"resource"`
}

type QueryUserTagListArgs struct {
	// 要查询的region，可填多个 可选
	Regions []string `json:"regions"`

	// 要查询的标签值 可选
	TagValue *string `json:"tagValue"`

	// 要查询的标签键 可选
	TagKey *string `json:"tagKey"`

	// 要查询的服务类型，可填多个 可选
	ServiceTypes []string `json:"serviceTypes"`

	// 要查询的资源ID，可填多个 可选
	ResourceIDs []string `json:"resourceIds"`
}

type AssociationsResult struct {
	// 标签与资源完整关联关系
	TagAssociationFulls []FullTagAssociation `json:"tagAssociationFulls"`
}

type FullTagAssociation struct {
	// 服务类型 如 BCC
	ServiceType string `json:"serviceType"`

	// 标签所属账户ID
	AccountID string `json:"accountId"`

	// 资源的ID 如 i-LGzaQZWI
	ResourceID string `json:"resourceId"`

	ResourceUUID string `json:"resourceUuid"`

	// 标签内部ID，无需关注此字段
	TagID int `json:"tagId"`

	// 标签值
	TagValue string `json:"tagValue"`

	// 标签键
	TagKey string `json:"tagKey"`

	// 资源所在region
	Region string `json:"region"`

	// 关联类型 associationType
	AssociationType int `json:"associationType"`
}

type CreateAndAssignArgs struct {
	// 资源及对应标签	必需
	Resources []ResourceWithTag `json:"resources"`
}

type ResourceWithTag struct {
	// 服务类型 示例 CCE 必需
	ServiceType string `json:"serviceType"`

	// 资源ID 示例 cce-db523ce9 必需
	ResourceID string `json:"resourceId"`

	// 资源UUID 示例 cce-db523ce9 必需
	ResourceUUID string `json:"resourceUuid"`

	// 关联类型，填写floating即可 必需
	AssociationType string `json:"associationType"`

	// 资源所在region，全局服务可填global 必需
	Region string `json:"region"`

	// 资源的全量标签信息 必需
	Tags []Tag `json:"tags"`
}
