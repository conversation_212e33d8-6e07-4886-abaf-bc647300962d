// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2019/11/27 10:50:00, by <EMAIL>, create
*/
/*
本 Package 包含 EIP InternalAPI SDK 的 Interface 定义和实现
参考文档: http://gollum.baidu.com/EIP#EIP-METASERVER-API
*/

package logicimage

import (
	"context"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
)

//go:generate mockgen -destination=./mock/mock_client.go -package=mock icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/logicimage Interface

// Interface 定义 EIP InternalAPI 的 interface
type Interface interface {
	SetDebug(flag bool)
	GetImages(ctx context.Context, listImageRequest *ImageListRequest, option *bce.SignOption) (*ImageListResponse, error)
	GetImage(ctx context.Context, id string, option *bce.SignOption) (*Image, error)
}

const GlancePath = "api/logical/bcc/v1/glance"

// ImageType 镜像类型
type ImageType string

const (
	// ImageTypeSystem "系统镜像/公共镜像"
	ImageTypeSystem ImageType = "common"

	// ImageTypeCustom "自定义镜像"
	ImageTypeCustom ImageType = "custom"

	// ImageTypeSharing 共享镜像
	ImageTypeSharing ImageType = "sharing"

	// ImageTypeBBCSharing BBC共享镜像
	ImageTypeBBCSharing ImageType = "bbcSharing"

	// ImageTypeGPUSystem GPU 公共镜像
	ImageTypeGPUSystem ImageType = "gpuBccImage"

	// ImageTypeVGPUSystem vGPU 镜像
	ImageTypeVGPUSystem ImageType = "vgpuBccImage"

	// ImageTypeGPUCustom GPU 自定义镜像
	ImageTypeGPUCustom ImageType = "gpuBccCustom"

	// ImageTypeBBCSystem BBC 公共镜像
	ImageTypeBBCSystem ImageType = "bbcCommon"

	// ImageTypeBBCCustom BBC 自定义镜像
	ImageTypeBBCCustom ImageType = "bbcCustom"

	// ImageTypeIntegration 集成镜像
	ImageTypeIntegration ImageType = "integration"

	ImageTypeFpgaImage ImageType = "fpgaImage"

	ImageTypeFpgaCustom ImageType = "fpgaCustom"

	// ImageTypeService 服务镜像，目前glance接口还没有提供查询的方式，这个类型是跟大数据 pingo 约定的值，会绕过校验
	ImageTypeService ImageType = "service"

	ImageTypeUnknow ImageType = "unknow"
)

type ImageListRequest struct {
	ImageType   ImageType   `json:"imageType"`
	ImageTypes  []ImageType `json:"imageTypes"`
	FpgaImageID string      `json:"fpgaImageId"`
	InstanceIDs []string    `json:"instanceIds"`
}

type ImageListResponse struct {
	OrderBy    string  `json:"orderBy"`
	Order      string  `json:"order"`
	PageNo     int     `json:"pageNo"`
	PageSize   int     `json:"pageSize"`
	TotalCount int     `json:"totalCount"`
	Result     []Image `json:"result"`
}

type Image struct {
	ImageUUID  string    `json:"id"`
	ImageID    string    `json:"imageId"`
	Name       string    `json:"name"`
	ImageType  ImageType `json:"imageType"`
	SnapshotID string    `json:"snapshotId"`
	CPU        int       `json:"cpu"`
	Memory     float32   `json:"memory"`
	OsType     string    `json:"osType"`
	OsVersion  string    `json:"osVersion"`
	OsName     string    `json:"osName"`
	OsBuild    string    `json:"osBuild"`
	OsLang     string    `json:"osLang"`
	DiskSize   int       `json:"diskSize"`

	Status              string  `json:"status"`
	MinMem              float32 `json:"minMem"`
	MinCpu              int     `json:"minCpu"`
	MinDiskGb           int     `json:"minDiskGb"`
	Desc                string  `json:"desc"`
	OsArch              string  `json:"osArch"`
	EphemeralSize       int     `json:"ephemeralSize"`
	ImageDescription    string  `json:"imageDescription"`
	ShareToUserNumLimit int     `json:"shareToUserNumLimit"`
	SharedToUserNum     int     `json:"sharedToUserNum"`
	FpgaType            string  `json:"fpgaType"`
	FromAccountID       string  `json:"fromAccountId"`
	SpecialVersion      string  `json:"specialVersion"`
}
