package logicimage

import (
	"context"
	"encoding/json"
	"errors"
	"strings"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
)

// GetImages - 根据 imageType 获取 images
// PARAMS:
//   - ctx: The context to trace request
//   - listImageRequest: 请求参数
//
// RETURNS:
//
//	*ImageListResponse: 镜像列表返回
//	error: nil if succeed, error if fail
func (c *Client) GetImages(ctx context.Context, listImageRequest *ImageListRequest, option *bce.SignOption) (*ImageListResponse, error) {
	if listImageRequest == nil {
		return nil, errors.New("listImageRequest is nil")
	}

	params := map[string]string{}

	if listImageRequest.ImageType != "" {
		params["imageType"] = string(listImageRequest.ImageType)
	}

	imageTypes := []string{}
	for _, t := range listImageRequest.ImageTypes {
		imageTypes = append(imageTypes, string(t))
	}

	if len(imageTypes) != 0 {
		params["imageTypes"] = strings.Join(imageTypes, ",")
	}

	if listImageRequest.FpgaImageID != "" {
		params["fpgaImageId"] = listImageRequest.FpgaImageID
	}

	params["orderBy"] = "osVersion"
	params["order"] = "desc"

	req, err := bce.NewRequest("GET", c.GetURL(GlancePath, params), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var imageList *ImageListResponse
	err = json.Unmarshal(bodyContent, &imageList)
	if err != nil {
		return nil, err
	}

	return imageList, nil
}

// GetImage - 通过 ImageID/ImageUUID 获取 Image 详细信息
// PARAMS:
//   - ctx: The context to trace request
//   - id: image ID or UUID
//
// RETURNS:
//
//	*Image: image details
//	error: nil if succeed, error if fail
func (c *Client) GetImage(ctx context.Context, id string, option *bce.SignOption) (*Image, error) {
	if id == "" {
		return nil, errors.New("image ID is empty")
	}

	params := map[string]string{}

	req, err := bce.NewRequest("GET", c.GetURL("api/logical/bcc/v1/glance/"+id, params), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var image *Image
	err = json.Unmarshal(bodyContent, &image)
	if err != nil {
		return nil, err
	}

	return image, nil
}
