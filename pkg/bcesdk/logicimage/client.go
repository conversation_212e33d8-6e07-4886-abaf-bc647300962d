// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2019/11/27 10:50:00, by <EMAIL>, create
*/
/*
本 Package 包含 EIP InternalAPI SDK 的 Interface 定义和实现
参考文档: http://gollum.baidu.com/EIP#EIP-METASERVER-API
*/

package logicimage

import (
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/util"
)

// Endpoints 包含所有地域的 endpoints
var Endpoints = map[string]string{
	"bj":      "",
	"gz":      "bcclogic.gz.bce-internal.baidu.com",
	"su":      "bcclogic.su.bce-internal.baidu.com",
	"hkg":     "",
	"fwh":     "",
	"bd":      "",
	"sandbox": "",
}

// Client is the logic image client implementation for Interface
type Client struct {
	*bce.Client
}

// NewClient return client
func NewClient(config *bce.Config) *Client {
	return &Client{
		Client: bce.NewClient(config),
	}
}

// GetURL generates the full URL of http request for BaiDu Cloud API
func (c *Client) GetURL(version string, params map[string]string) string {
	host := c.Endpoint
	if host == "" {
		host = Endpoints[c.GetRegion()]
	}
	uriPath := version
	return c.Client.GetURL(host, uriPath, params)
}

// SetDebug open or close Debug Mode
func (c *Client) SetDebug(debug bool) {
	c.Client.SetDebug(debug)
}

// NewSignOption return logic image specified sign option
func NewSignOption() *bce.SignOption {
	option := &bce.SignOption{}
	option.AddHeader("x-bce-request-id", util.GetRequestID())
	option.AddHeadersToSign("host", "x-bce-date")
	return option
}
