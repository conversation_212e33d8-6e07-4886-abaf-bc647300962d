package logicbcc

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
)

// AssignTags - 对齐 bcc tags
func (c *Client) AssignTags(ctx context.Context, assignReq *AssignTagsRequest, option *bce.SignOption) error {
	if assignReq == nil {
		return errors.New("GetFlavors failed: assignReq is nil")
	}

	params := map[string]string{}

	postContent, err := json.Marshal(assignReq)
	if err != nil {
		return err
	}

	req, err := bce.NewRequest("PUT", c.GetURL("/api/logical/bcc/v1/instance/tag/assign", params), bytes.NewBuffer(postContent))
	if err != nil {
		return err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}

	_, err = resp.GetBodyContent()
	return err
}
