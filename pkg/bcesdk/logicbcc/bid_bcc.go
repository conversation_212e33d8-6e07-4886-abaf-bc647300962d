package logicbcc

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
)

func (c *Client) GetBidEvents(ctx context.Context, getReq *GetBidEventsRequest, option *bce.SignOption) (*GetBidEventsResponse, error) {
	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}

	if getReq == nil {
		return nil, errors.New("args is nil")
	}

	postContent, err := json.Marshal(getReq)
	if err != nil {
		return nil, err
	}

	req, err := bce.NewRequest("POST", c.GetURL("api/logical/bcc/v1/instance/bid/events", params), bytes.NewBuffer(postContent))
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	result := new(GetBidEventsResponse)
	err = json.Unmarshal(bodyContent, result)
	if err != nil {
		return nil, err
	}

	return result, nil
}
