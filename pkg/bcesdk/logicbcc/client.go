package logicbcc

import (
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/util"
)

// Endpoints 各地域 endpoints
var Endpoints = map[string]string{
	"bj":      "bcclogic.bce-internal.baidu.com",
	"gz":      "bcclogic.gz.bce-internal.baidu.com",
	"su":      "bcclogic.su.bce-internal.baidu.com",
	"hkg":     "bcclogic.hkg.bce.baidu-int.com",
	"fwh":     "bcclogic.fwh.bce.baidu-int.com",
	"bd":      "bcclogic.bdbl.bce.baidu-int.com",
	"sandbox": "bcc.bj.qasandbox.baidu-int.com",
}

var _ Interface = &Client{}

// Client is the logic BCC client implementation for Interface
type Client struct {
	*bce.Client
}

// NewClient return client
func NewClient(config *bce.Config) *Client {
	return &Client{
		Client: bce.NewClient(config),
	}
}

// GetURL generates the full URL of http request for BaiDu Cloud API
func (c *Client) GetURL(version string, params map[string]string) string {
	host := c.Endpoint
	if host == "" {
		host = Endpoints[c.GetRegion()]
	}
	uriPath := version
	return c.Client.GetURL(host, uriPath, params)
}

// NewSignOption return BCC specified sign option
func NewSignOption() *bce.SignOption {
	option := &bce.SignOption{}
	option.AddHeader("x-bce-request-id", util.GetRequestID())
	option.AddHeadersToSign("host", "x-bce-date")

	return option
}
