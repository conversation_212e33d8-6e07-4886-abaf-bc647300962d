package logicbcc

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
)

// BBC 接口文档： http://gollum.baidu.com/BbcOpenAPI
// GetBBC - 获取 BBC 详情
func (c *Client) GetBBC(ctx context.Context, serverID string, option *bce.SignOption) (*Server, error) {
	if serverID == "" {
		return nil, errors.New("GetServer failed: serverID is nil")
	}

	params := map[string]string{}

	url := fmt.Sprintf("/api/logical/bcc/v1/bbc/instance/%s", serverID)
	req, err := bce.NewRequest("GET", c.GetURL(url, params), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	server := new(Server)
	err = json.Unmarshal(bodyContent, server)
	if err != nil {
		return nil, err
	}

	return server, nil
}

// DeleteBBCs - 删除 BBCs
func (c *Client) DeleteBBCs(ctx context.Context, serverIDs []string, option *bce.SignOption) error {
	if len(serverIDs) <= 0 {
		return errors.New("serverIDs is empty")
	}

	params := map[string]string{}

	reqBody := &struct {
		ServerIDs []string `json:"serverIds"`
	}{
		ServerIDs: serverIDs,
	}

	postContent, err := json.Marshal(reqBody)
	if err != nil {
		return err
	}

	req, err := bce.NewRequest("POST", c.GetURL("/api/logical/bcc/v1/bbc/delete", params), bytes.NewBuffer(postContent))
	if err != nil {
		return err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}

	_, err = resp.GetBodyContent()
	return err
}

// ReinstallBBCs 重装 BBCs
func (c *Client) ReinstallBBCs(ctx context.Context, args *ReinstallBBCArgs, option *bce.SignOption) error {
	if args == nil {
		return errors.New("ReinstallBBCs failed: args is nil")
	}

	params := map[string]string{}

	postContent, err := json.Marshal(args)
	if err != nil {
		return err
	}

	req, err := bce.NewRequest("PUT", c.GetURL("/api/logical/bcc/v1/bbc/rebuild", params), bytes.NewBuffer(postContent))
	if err != nil {
		return err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}

	_, err = resp.GetBodyContent()
	return err
}

// CreateBBC
func (c *Client) CreateBBC(ctx context.Context, args *CreateServersArgs, option *bce.SignOption) (*CreateServersResponse, error) {
	if args == nil {
		return nil, errors.New("CreateBBC failed: args is nil")
	}

	params := map[string]string{}

	// params["from"] = "api"

	postContent, err := json.Marshal(args)
	if err != nil {
		return nil, err
	}

	req, err := bce.NewRequest("POST", c.GetURL("/api/logical/bcc/v1/bbc/create", params), bytes.NewBuffer(postContent))
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	response := new(CreateServersResponse)
	err = json.Unmarshal(bodyContent, response)
	if err != nil {
		return nil, err
	}
	return response, nil
}
