// Code generated by MockGen. DO NOT EDIT.
// Source: icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/logicbcc (interfaces: Interface)

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	gomock "github.com/golang/mock/gomock"
	bce "icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
	logicbcc "icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/logicbcc"
	reflect "reflect"
)

// MockInterface is a mock of Interface interface
type MockInterface struct {
	ctrl     *gomock.Controller
	recorder *MockInterfaceMockRecorder
}

// MockInterfaceMockRecorder is the mock recorder for MockInterface
type MockInterfaceMockRecorder struct {
	mock *MockInterface
}

// NewMockInterface creates a new mock instance
func NewMockInterface(ctrl *gomock.Controller) *MockInterface {
	mock := &MockInterface{ctrl: ctrl}
	mock.recorder = &MockInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use
func (m *MockInterface) EXPECT() *MockInterfaceMockRecorder {
	return m.recorder
}

// AssignTags mocks base method
func (m *MockInterface) AssignTags(arg0 context.Context, arg1 *logicbcc.AssignTagsRequest, arg2 *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AssignTags", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// AssignTags indicates an expected call of AssignTags
func (mr *MockInterfaceMockRecorder) AssignTags(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AssignTags", reflect.TypeOf((*MockInterface)(nil).AssignTags), arg0, arg1, arg2)
}

// BatchUpdateApplication mocks base method
func (m *MockInterface) BatchUpdateApplication(arg0 context.Context, arg1 *logicbcc.BatchUpdateApplicationArgs, arg2 *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchUpdateApplication", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchUpdateApplication indicates an expected call of BatchUpdateApplication
func (mr *MockInterfaceMockRecorder) BatchUpdateApplication(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchUpdateApplication", reflect.TypeOf((*MockInterface)(nil).BatchUpdateApplication), arg0, arg1, arg2)
}

// CreateBBC mocks base method
func (m *MockInterface) CreateBBC(arg0 context.Context, arg1 *logicbcc.CreateServersArgs, arg2 *bce.SignOption) (*logicbcc.CreateServersResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateBBC", arg0, arg1, arg2)
	ret0, _ := ret[0].(*logicbcc.CreateServersResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateBBC indicates an expected call of CreateBBC
func (mr *MockInterfaceMockRecorder) CreateBBC(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateBBC", reflect.TypeOf((*MockInterface)(nil).CreateBBC), arg0, arg1, arg2)
}

// CreateServers mocks base method
func (m *MockInterface) CreateServers(arg0 context.Context, arg1 *logicbcc.CreateServersArgs, arg2 string, arg3 *bce.SignOption) (*logicbcc.CreateServersResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateServers", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*logicbcc.CreateServersResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateServers indicates an expected call of CreateServers
func (mr *MockInterfaceMockRecorder) CreateServers(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateServers", reflect.TypeOf((*MockInterface)(nil).CreateServers), arg0, arg1, arg2, arg3)
}

// DeleteBBCs mocks base method
func (m *MockInterface) DeleteBBCs(arg0 context.Context, arg1 []string, arg2 *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteBBCs", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteBBCs indicates an expected call of DeleteBBCs
func (mr *MockInterfaceMockRecorder) DeleteBBCs(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteBBCs", reflect.TypeOf((*MockInterface)(nil).DeleteBBCs), arg0, arg1, arg2)
}

// DeleteServers mocks base method
func (m *MockInterface) DeleteServers(arg0 context.Context, arg1 *logicbcc.DeleteServersArgs, arg2 *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteServers", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteServers indicates an expected call of DeleteServers
func (mr *MockInterfaceMockRecorder) DeleteServers(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteServers", reflect.TypeOf((*MockInterface)(nil).DeleteServers), arg0, arg1, arg2)
}

// GetBBC mocks base method
func (m *MockInterface) GetBBC(arg0 context.Context, arg1 string, arg2 *bce.SignOption) (*logicbcc.Server, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBBC", arg0, arg1, arg2)
	ret0, _ := ret[0].(*logicbcc.Server)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBBC indicates an expected call of GetBBC
func (mr *MockInterfaceMockRecorder) GetBBC(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBBC", reflect.TypeOf((*MockInterface)(nil).GetBBC), arg0, arg1, arg2)
}

// GetBidEvents mocks base method
func (m *MockInterface) GetBidEvents(arg0 context.Context, arg1 *logicbcc.GetBidEventsRequest, arg2 *bce.SignOption) (*logicbcc.GetBidEventsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBidEvents", arg0, arg1, arg2)
	ret0, _ := ret[0].(*logicbcc.GetBidEventsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBidEvents indicates an expected call of GetBidEvents
func (mr *MockInterfaceMockRecorder) GetBidEvents(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBidEvents", reflect.TypeOf((*MockInterface)(nil).GetBidEvents), arg0, arg1, arg2)
}

// GetFlavors mocks base method
func (m *MockInterface) GetFlavors(arg0 context.Context, arg1 *logicbcc.GetFlavorsArgs, arg2 *bce.SignOption) (*logicbcc.GetFlavorsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFlavors", arg0, arg1, arg2)
	ret0, _ := ret[0].(*logicbcc.GetFlavorsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFlavors indicates an expected call of GetFlavors
func (mr *MockInterfaceMockRecorder) GetFlavors(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFlavors", reflect.TypeOf((*MockInterface)(nil).GetFlavors), arg0, arg1, arg2)
}

// GetQuota mocks base method
func (m *MockInterface) GetQuota(arg0 context.Context, arg1 logicbcc.QuotaType, arg2 bool, arg3 *bce.SignOption) (*logicbcc.GetQuotaResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetQuota", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*logicbcc.GetQuotaResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetQuota indicates an expected call of GetQuota
func (mr *MockInterfaceMockRecorder) GetQuota(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetQuota", reflect.TypeOf((*MockInterface)(nil).GetQuota), arg0, arg1, arg2, arg3)
}

// GetServerByID mocks base method
func (m *MockInterface) GetServerByID(arg0 context.Context, arg1 string, arg2 *bce.SignOption) (*logicbcc.Server, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetServerByID", arg0, arg1, arg2)
	ret0, _ := ret[0].(*logicbcc.Server)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetServerByID indicates an expected call of GetServerByID
func (mr *MockInterfaceMockRecorder) GetServerByID(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetServerByID", reflect.TypeOf((*MockInterface)(nil).GetServerByID), arg0, arg1, arg2)
}

// GetServers mocks base method
func (m *MockInterface) GetServers(arg0 context.Context, arg1 *logicbcc.GetServersArgs, arg2 *bce.SignOption) (*logicbcc.GetServersResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetServers", arg0, arg1, arg2)
	ret0, _ := ret[0].(*logicbcc.GetServersResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetServers indicates an expected call of GetServers
func (mr *MockInterfaceMockRecorder) GetServers(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetServers", reflect.TypeOf((*MockInterface)(nil).GetServers), arg0, arg1, arg2)
}

// ListCDS mocks base method
func (m *MockInterface) ListCDS(arg0 context.Context, arg1, arg2 string, arg3 *bce.SignOption) (*logicbcc.ListCDSResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListCDS", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*logicbcc.ListCDSResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListCDS indicates an expected call of ListCDS
func (mr *MockInterfaceMockRecorder) ListCDS(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListCDS", reflect.TypeOf((*MockInterface)(nil).ListCDS), arg0, arg1, arg2, arg3)
}

// ListServersByIDs mocks base method
func (m *MockInterface) ListServersByIDs(arg0 context.Context, arg1 []string, arg2 *bce.SignOption) (*logicbcc.ListServersResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListServersByIDs", arg0, arg1, arg2)
	ret0, _ := ret[0].(*logicbcc.ListServersResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListServersByIDs indicates an expected call of ListServersByIDs
func (mr *MockInterfaceMockRecorder) ListServersByIDs(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListServersByIDs", reflect.TypeOf((*MockInterface)(nil).ListServersByIDs), arg0, arg1, arg2)
}

// QueryBCCPrice mocks base method
func (m *MockInterface) QueryBCCPrice(arg0 context.Context, arg1 *logicbcc.QueryBCCPriceRequest, arg2 *bce.SignOption) (*logicbcc.QueryBCCPriceResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryBCCPrice", arg0, arg1, arg2)
	ret0, _ := ret[0].(*logicbcc.QueryBCCPriceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryBCCPrice indicates an expected call of QueryBCCPrice
func (mr *MockInterfaceMockRecorder) QueryBCCPrice(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryBCCPrice", reflect.TypeOf((*MockInterface)(nil).QueryBCCPrice), arg0, arg1, arg2)
}

// ReinstallBBCs mocks base method
func (m *MockInterface) ReinstallBBCs(arg0 context.Context, arg1 *logicbcc.ReinstallBBCArgs, arg2 *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ReinstallBBCs", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// ReinstallBBCs indicates an expected call of ReinstallBBCs
func (mr *MockInterfaceMockRecorder) ReinstallBBCs(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReinstallBBCs", reflect.TypeOf((*MockInterface)(nil).ReinstallBBCs), arg0, arg1, arg2)
}

// ReinstallServers mocks base method
func (m *MockInterface) ReinstallServers(arg0 context.Context, arg1 *logicbcc.ReinstallServersArgs, arg2 *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ReinstallServers", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// ReinstallServers indicates an expected call of ReinstallServers
func (mr *MockInterfaceMockRecorder) ReinstallServers(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReinstallServers", reflect.TypeOf((*MockInterface)(nil).ReinstallServers), arg0, arg1, arg2)
}

// RenameServers mocks base method
func (m *MockInterface) RenameServers(arg0 context.Context, arg1 *logicbcc.RenameServersArgs, arg2 *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RenameServers", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// RenameServers indicates an expected call of RenameServers
func (mr *MockInterfaceMockRecorder) RenameServers(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RenameServers", reflect.TypeOf((*MockInterface)(nil).RenameServers), arg0, arg1, arg2)
}

// SetDebug mocks base method
func (m *MockInterface) SetDebug(arg0 bool) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SetDebug", arg0)
}

// SetDebug indicates an expected call of SetDebug
func (mr *MockInterfaceMockRecorder) SetDebug(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetDebug", reflect.TypeOf((*MockInterface)(nil).SetDebug), arg0)
}
