package logicbcc

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
)

func (c *Client) GetQuota(ctx context.Context, quotaType QuotaType, needGlobalQuota bool, option *bce.SignOption) (*GetQuotaResponse, error) {
	params := map[string]string{
		"needGlobalQuota": strconv.FormatBool(needGlobalQuota),
	}

	url := fmt.Sprintf("/api/logical/bcc/v1/quota/%s", quotaType)
	req, err := bce.NewRequest("GET", c.GetURL(url, params), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	result := new(GetQuotaResponse)
	err = json.Unmarshal(bodyContent, result)
	if err != nil {
		return nil, err
	}

	return result, nil
}
