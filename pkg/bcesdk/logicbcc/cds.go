package logicbcc

import (
	"context"
	"encoding/json"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
)

func (c *Client) ListCDS(ctx context.Context, instanceUUID, diskType string, option *bce.SignOption) (*ListCDSResponse, error) {
	params := map[string]string{}

	if instanceUUID != "" {
		params["instanceUuid"] = instanceUUID
	}

	if diskType != "" {
		params["diskType"] = diskType
	}

	req, err := bce.NewRequest("GET", c.GetURL("/api/logical/bcc/v1/cinder", params), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	result := new(ListCDSResponse)
	err = json.Unmarshal(bodyContent, result)
	if err != nil {
		return nil, err
	}

	return result, nil
}
