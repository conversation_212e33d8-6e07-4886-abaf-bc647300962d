package usersetting

import (
	"bytes"
	"context"
	"encoding/json"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
)

// CheckWhiteList - 检查白名单
func (c *Client) CheckWhiteList(ctx context.Context, args *FeatureAclRequest, option *bce.SignOption) (*FeatureAclResponse, error) {
	params := map[string]string{}

	postContent, err := json.Marshal(args)
	if err != nil {
		return nil, err
	}

	req, err := bce.NewRequest("POST", c.GetURL("settings/acl/get", params), bytes.NewBuffer(postContent))
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var response *FeatureAclResponse
	err = json.Unmarshal(bodyContent, &response)
	if err != nil {
		return nil, err
	}

	return response, nil
}

// GetUserQuotas - 获取用户的各种资源的配额
func (c *Client) GetUserQuotas(ctx context.Context, args *QuotaRequest, option *bce.SignOption) (*QuotaResponse, error) {
	params := map[string]string{}

	postContent, err := json.Marshal(args)
	if err != nil {
		return nil, err
	}

	req, err := bce.NewRequest("POST", c.GetURL("settings/quota/list", params), bytes.NewBuffer(postContent))
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var response *QuotaResponse
	err = json.Unmarshal(bodyContent, &response)
	if err != nil {
		return nil, err
	}

	return response, nil
}
