package usersetting

import (
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/util"
)

// Endpoints 包含所有地域的 endpoints
var Endpoints = map[string]string{
	"bj":      "settings.bce-internal.baidu.com/v1",
	"gz":      "settings.bce-internal.baidu.com/v1",
	"su":      "settings.bce-internal.baidu.com/v1",
	"hkg":     "settings.bce-internal.baidu.com/v1",
	"fwh":     "settings.bce-internal.baidu.com/v1",
	"bd":      "settings.bce-internal.baidu.com/v1",
	"sandbox": "************:8690/v1",
}

// Client is the bos client implemention for Baidu Cloud BOS API.
type Client struct {
	*bce.Client
}

// NewClient 初始化 Client
func NewClient(config *bce.Config) *Client {
	return &Client{
		Client: bce.NewClient(config),
	}
}

// GetURL 返回完成的 URL 链接
func (c *Client) GetURL(version string, params map[string]string) string {
	host := c.Endpoint
	if host == "" {
		host = Endpoints[c.GetRegion()]
	}
	uriPath := version
	return c.Client.GetURL(host, uriPath, params)
}

// SetDebug 开启或关闭 Debug 模式
func (c *Client) SetDebug(debug bool) {
	c.Client.SetDebug(debug)
}

// NewSignOption return BLB specified sign option
func NewSignOption() *bce.SignOption {
	option := &bce.SignOption{}
	option.AddHeader("x-bce-request-id", util.GetRequestID())
	option.AddHeadersToSign("host", "x-bce-date")

	return option
}
