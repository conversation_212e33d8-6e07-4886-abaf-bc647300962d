// Code generated by MockGen. DO NOT EDIT.
// Source: icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/usersetting (interfaces: Interface)

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	gomock "github.com/golang/mock/gomock"
	bce "icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
	usersetting "icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/usersetting"
	reflect "reflect"
)

// MockInterface is a mock of Interface interface
type MockInterface struct {
	ctrl     *gomock.Controller
	recorder *MockInterfaceMockRecorder
}

// MockInterfaceMockRecorder is the mock recorder for MockInterface
type MockInterfaceMockRecorder struct {
	mock *MockInterface
}

// NewMockInterface creates a new mock instance
func NewMockInterface(ctrl *gomock.Controller) *MockInterface {
	mock := &MockInterface{ctrl: ctrl}
	mock.recorder = &MockInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use
func (m *MockInterface) EXPECT() *MockInterfaceMockRecorder {
	return m.recorder
}

// CheckWhiteList mocks base method
func (m *MockInterface) CheckWhiteList(arg0 context.Context, arg1 *usersetting.FeatureAclRequest, arg2 *bce.SignOption) (*usersetting.FeatureAclResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckWhiteList", arg0, arg1, arg2)
	ret0, _ := ret[0].(*usersetting.FeatureAclResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckWhiteList indicates an expected call of CheckWhiteList
func (mr *MockInterfaceMockRecorder) CheckWhiteList(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckWhiteList", reflect.TypeOf((*MockInterface)(nil).CheckWhiteList), arg0, arg1, arg2)
}

// GetUserQuotas mocks base method
func (m *MockInterface) GetUserQuotas(arg0 context.Context, arg1 *usersetting.QuotaRequest, arg2 *bce.SignOption) (*usersetting.QuotaResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserQuotas", arg0, arg1, arg2)
	ret0, _ := ret[0].(*usersetting.QuotaResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserQuotas indicates an expected call of GetUserQuotas
func (mr *MockInterfaceMockRecorder) GetUserQuotas(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserQuotas", reflect.TypeOf((*MockInterface)(nil).GetUserQuotas), arg0, arg1, arg2)
}

// SetDebug mocks base method
func (m *MockInterface) SetDebug(arg0 bool) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SetDebug", arg0)
}

// SetDebug indicates an expected call of SetDebug
func (mr *MockInterfaceMockRecorder) SetDebug(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetDebug", reflect.TypeOf((*MockInterface)(nil).SetDebug), arg0)
}
