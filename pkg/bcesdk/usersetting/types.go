package usersetting

import (
	"context"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
)

//go:generate mockgen -destination=./mock/mock_client.go -package=mock icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/usersetting Interface

// Interface - 定义 user-setting 接口
type Interface interface {
	SetDebug(debug bool)
	CheckWhiteList(ctx context.Context, args *FeatureAclRequest, option *bce.SignOption) (*FeatureAclResponse, error)
	GetUserQuotas(ctx context.Context, args *QuotaRequest, option *bce.SignOption) (*QuotaResponse, error)
}

// FeatureAclRequest - 查询白名单请求参数
type FeatureAclRequest struct {
	FeatureType string `json:"featureType"`
	Region      string `json:"region"`
	AclType     string `json:"aclType"`
	AclName     string `json:"aclName"`
}

// FeatureAclResponse - 查询白名单返回
type FeatureAclResponse struct {
	IsExist bool `json:"isExist"`
}

// QuotaRequest - 查询用户配额请求参数
type QuotaRequest struct {
	UserType   UserType    `json:"userType"`
	UserValue  string      `json:"userValue"`
	QuotaTypes []QuotaType `json:"quotaTypes"`
}

// QuotaResponse - 查询用户配额返回
type QuotaResponse struct {
	QuotaType2Quota map[QuotaType]string `json:"quotaType2quota"`
}

// UserType 用户类型
type UserType string

const (
	// UserTypeAccountID 用户类型 AccountId
	UserTypeAccountID UserType = "AccountId"

	// UserTypeAK 用户类型 Ak
	UserTypeAK UserType = "Ak"
)

// QuotaType 配额类型
type QuotaType string

const (
	// QuotaTypeCCECluster cluster 配额
	QuotaTypeCCECluster QuotaType = "clusterQuota"

	// QuotaTypeCCENode 集群内 node 配额类型
	QuotaTypeCCENode QuotaType = "slaveQuota"

	// QuotaTypeNamespace namespace 配额类型
	QuotaTypeNamespace QuotaType = "namespaceQuota"

	// QuotaTypeENI ENI 配额类型
	QuotaTypeENI QuotaType = "eniQuota"

	// QuotaTypeClusterSnapshot 集群快照配额
	QuotaTypeClusterSnapshot QuotaType = "ClusterSnapshotQuota"

	// QuotaTypePrepayInstance 预付费 instance quota (IaaS)
	QuotaTypePrepayInstance QuotaType = "prepayInstance"

	// QuotaTypePostpayInstance 后付费 instance quota (IaaS)
	QuotaTypePostpayInstance QuotaType = "postpayInstance"
)

type FeatureType string

const (
	FeatureTypeAllOSImage              FeatureType = "EnableAllOSImage"
	FeatureTypeManagedPro              FeatureType = "EnableManagedPro"
	FeatureTypeExistingNodeENI         FeatureType = "enableExistingNodeENI"
	FeatureTypeEnableCLuster126        FeatureType = "EnableK8s1.26"
	FeatureTypeEnableLargerClusterSize FeatureType = "EnableLargerClusterSize"
)
