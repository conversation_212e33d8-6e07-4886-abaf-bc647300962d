package resmanager

import (
	"context"

	rmapi "github.com/baidubce/bce-sdk-go/services/resmanager"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
)

//go:generate mockgen -destination=./mock/mock_client.go -package=mock icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/resmanager Interface

type Interface interface {
	BindResourceToGroup(ctx context.Context, args *rmapi.BindResourceToGroupArgs, option *bce.SignOption, force bool) (*rmapi.BindResourceResult, error)
	GetResourceGroup(ctx context.Context, name string, option *bce.SignOption) (*rmapi.GroupList, error)
	QueryGroupResource(ctx context.Context, args *QueryGroupResourceRequest, option *bce.SignOption) (*ResourceGroupsPageInfo, error)
	SendClusterBindingMsg(ctx context.Context, msg *SyncResMessage) error
	UnBindResourceFromResourceGroup(ctx context.Context, args *rmapi.BindResourceToGroupArgs, option *bce.SignOption, force bool) error
}
