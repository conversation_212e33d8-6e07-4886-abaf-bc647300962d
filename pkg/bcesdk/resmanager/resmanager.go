package resmanager

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"strconv"

	rmapi "github.com/baidubce/bce-sdk-go/services/resmanager"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
)

func (c *Client) UnBindResourceFromResourceGroup(ctx context.Context, args *rmapi.BindResourceToGroupArgs, option *bce.SignOption, force bool) error {
	if args == nil {
		return errors.New("unset args")
	}

	if len(args.Bindings) == 0 {
		return errors.New("unset bindings")
	}
	postContent, err := json.Marshal(args)
	if err != nil {
		logger.Errorf(ctx, "args invalid: %v", err)
		return err
	}
	params := map[string]string{"force": strconv.FormatBool(force)}
	req, err := bce.NewRequest("PUT", c.GetURL(c.Endpoint, "/v1/res/resource", params), bytes.<PERSON>Buffer(postContent))
	if err != nil {
		logger.Errorf(ctx, "build request to unbind resource from resourceGroup failed: %v", err)
		return err
	}

	_, err = c.SendRequest(ctx, req, option)
	if err != nil {
		logger.Errorf(ctx, "unbind resource from resourceGroup failed: %v", err)
		return err
	}
	return nil
}

func (c *Client) BindResourceToGroup(ctx context.Context, args *rmapi.BindResourceToGroupArgs,
	option *bce.SignOption, force bool) (*rmapi.BindResourceResult, error) {
	if args == nil {
		return nil, errors.New("unset args")
	}

	if len(args.Bindings) == 0 {
		return nil, errors.New("unset Bindings")
	}

	postContent, err := json.Marshal(args)
	if err != nil {
		logger.Errorf(ctx, "marshal args err: %v", err)
		return nil, err
	}
	params := map[string]string{"force": strconv.FormatBool(force)}
	req, err := bce.NewRequest("POST", c.GetURL(c.Endpoint, "/v1/res/resource", params), bytes.NewBuffer(postContent))
	if err != nil {
		logger.Errorf(ctx, "build request to bind resource from resourceGroup failed: %v", err)
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		logger.Errorf(ctx, "bind resource from resourceGroup failed: %v", err)
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var result rmapi.BindResourceResult
	if err := json.Unmarshal(bodyContent, &result); err != nil {
		logger.Errorf(ctx, "unmarshal bindResourceResult err: %v", err)
		return nil, err
	}

	return &result, nil
}

func (c *Client) GetResourceGroup(ctx context.Context, name string, option *bce.SignOption) (*rmapi.GroupList, error) {
	params := map[string]string{
		"name": name,
	}

	req, err := bce.NewRequest("GET", c.GetURL(c.Endpoint, "v1/res/group", params), nil)
	if err != nil {
		logger.Errorf(ctx, "build request to get resourceGroup failed: %v", err)
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		logger.Errorf(ctx, "get resourceGroup failed: %v", err)
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var result rmapi.GroupList
	if err := json.Unmarshal(bodyContent, &result); err != nil {
		return nil, err
	}

	return &result, nil
}

func (c *Client) QueryGroupResource(ctx context.Context, args *QueryGroupResourceRequest, option *bce.SignOption) (*ResourceGroupsPageInfo, error) {
	if args == nil {
		return nil, errors.New("unset args")
	}
	if args.GroupID == "" {
		return nil, errors.New("unset groupId")
	}
	params := map[string]string{
		"queryGroupResource": "",
		"groupId":            args.GroupID,
	}

	if args.ID != "" {
		params["id"] = args.ID
	}
	if args.Name != nil && len(*args.Name) != 0 {
		params["name"] = *args.Name
	}
	if args.Types != "" {
		params["types"] = args.Types
	}
	if args.Regions != "" {
		params["regions"] = args.Regions
	}
	if args.IsCurrent != nil {
		params["isCurrent"] = strconv.FormatBool(*args.IsCurrent)
	}
	if args.Tag != "" {
		params["tag"] = args.Tag
	}
	if args.PageSize > 0 {
		params["pageSize"] = strconv.Itoa(args.PageSize)
	}
	if args.PageNo > 0 {
		params["pageNo"] = strconv.Itoa(args.PageNo)
	}
	putContent, err := json.Marshal(args)
	if err != nil {
		return nil, err
	}

	req, err := bce.NewRequest("GET", c.GetURL(c.Endpoint, "v1/res/resource", params), bytes.NewBuffer(putContent))
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var result ResourceGroupsPageInfo
	err = json.Unmarshal(bodyContent, &result)
	if err != nil {
		return nil, err
	}

	return &result, nil
}
