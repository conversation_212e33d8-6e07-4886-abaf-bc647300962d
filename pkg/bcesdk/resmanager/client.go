package resmanager

import (
	"context"
	"crypto/tls"
	"crypto/x509"
	"os"
	"time"

	"github.com/IBM/sarama"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
)

type Client struct {
	*bce.Client
	config      *sarama.Config
	kafkaConfig *ResourceManagerKafkaConfig
}

func NewClient(ctx context.Context, config *bce.Config, kafkaConfig *ResourceManagerKafkaConfig) (*Client, error) {
	bceClient := bce.NewClient(config)
	conf, err := initKafkaConf(kafkaConfig.CrtPath, kafkaConfig.KeyPath, kafkaConfig.CaPath, ctx)
	if err != nil {
		logger.Errorf(ctx, "initKafkaConf failed: %v", err)
		return nil, err
	}
	return &Client{Client: bceClient, config: conf, kafkaConfig: kafkaConfig}, nil
}

func initKafkaConf(crtPath string, keyPath string, caPath string, ctx context.Context) (*sarama.Config, error) {
	conf := sarama.NewConfig()
	tlsConfig, err := createTlsConfiguration(crtPath, keyPath, caPath)
	if err != nil {
		logger.Errorf(ctx, "createTlsConfiguration failed: %v", err)
		return nil, err
	}
	conf.Net.TLS.Enable = true
	conf.Net.TLS.Config = tlsConfig
	conf.Producer.Return.Successes = true
	conf.Producer.RequiredAcks = sarama.WaitForAll
	conf.Metadata.RefreshFrequency = 10 * time.Second
	conf.Net.DialTimeout = 10 * time.Second
	return conf, nil
}

func createTlsConfiguration(crtPath string, keyPath string, caPath string) (*tls.Config, error) {
	tlsConfig := tls.Config{}
	// Load client cert
	cert, err := tls.LoadX509KeyPair(crtPath, keyPath)
	if err != nil {
		return nil, err
	}
	tlsConfig.Certificates = []tls.Certificate{cert}
	caCertPool := x509.NewCertPool()
	// Load CA cert
	caCert, err := os.ReadFile(caPath)
	if err != nil {
		return nil, err
	}
	caCertPool.AppendCertsFromPEM(caCert)
	tlsConfig.RootCAs = caCertPool
	tlsConfig.InsecureSkipVerify = true
	return &tlsConfig, nil
}
