package resmanager

import (
	"context"
	"encoding/json"

	"github.com/IBM/sarama"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
)

func (c *Client) SendClusterBindingMsg(ctx context.Context, msg *SyncResMessage) error {
	producer, err := sarama.NewSyncProducer([]string{c.kafkaConfig.Endpoint}, c.config)
	if err != nil {
		logger.Errorf(ctx, "error creating sync producer: %v", err)
		return err
	}
	defer func() {
		if err := producer.Close(); err != nil {
			logger.Errorf(ctx, "err closing producer: %v", err)
		}
	}()
	arr, err := json.Marshal(msg)
	if err != nil {
		logger.Errorf(ctx, "convert msg to []byte failed: %v", err)
		return err
	}
	kafkaMsg := &sarama.ProducerMessage{
		Topic: c.kafkaConfig.Topic,
		Value: sarama.ByteEncoder(arr),
	}

	partition, offset, err := producer.SendMessage(kafkaMsg)
	if err != nil {
		logger.Errorf(ctx, "Send message failed: %v", err)
		return err
	}
	logger.Infof(ctx, "Message is stored in topic(%s)/partition(%d)/offset(%d)\n", c.kafkaConfig.Topic, partition, offset)
	return nil
}
