package resmanager

type ResourceManagerKafkaConfig struct {
	CaPath   string `json:"CaPath,omitempty"`
	CrtPath  string `json:"CrtPath,omitempty"`
	KeyPath  string `json:"KeyPath,omitempty"`
	Endpoint string `json:"Endpoint,omitempty"`
	Topic    string `json:"Topic,omitempty"`
}

type SyncResMessage struct {
	SyncType  string     `json:"syncType"`
	Resources []Resource `json:"resources"`
}

type Resource struct {
	AccountID string `json:"accountId"` // 账户ID
	UserID    string `json:"userId"`    // 用户ID
	ID        string `json:"id"`        // 资源短ID
	UUID      string `json:"uuid"`      // 资源长ID
	AuthID    string `json:"authId"`    // 资源的鉴权id
	Name      string `json:"name"`      // 资源名称
	Type      string `json:"type"`      // 资源类型
	Region    string `json:"region"`    // 资源所在区域
	URL       string `json:"url"`       // 资源对应的url路径
	Summary   string `json:"summary"`   // 资源概要
}

type QueryGroupResourceRequest struct {
	GroupID   string  `json:"groupId"`             // 资源所属资源组的ID，标准查询
	ID        string  `json:"id,omitempty"`        // 资源的标识ID，可选查询
	Types     string  `json:"types,omitempty"`     // 资源的类型描述，可选查询
	Regions   string  `json:"regions,omitempty"`   // 资源的地域描述，可选查询
	Name      *string `json:"name,omitempty"`      // 资源的名称描述，可选查询
	IsCurrent *bool   `json:"isCurrent,omitempty"` // 是否只显示当前的资源，可选查询，默认为false
	Tag       string  `json:"tag,omitempty"`       // 资源的标签，标签之间用逗号分割，可选查询
	PageNo    int     `json:"pageNo,omitempty"`    // 分页参数，页码，默认为1
	PageSize  int     `json:"pageSize,omitempty"`  // 分页参数，每页显示的条目数，默认为10
}

type ResourceGroupsPageInfo struct {
	Infos []ResourceGroupsInfo `json:"infos"` // 资源分组相关的列表信息
	Total int                  `json:"total"` // 总数
}

type ResourceGroupsInfo struct {
	AccountID string   `json:"accountId"` // 账户ID
	UserID    string   `json:"userId"`    // 用户ID
	Name      string   `json:"name"`      // 资源名称
	UUID      string   `json:"uuid"`      // 资源的UUID
	ID        string   `json:"id"`        // 资源的ID
	Type      string   `json:"type"`      // 资源类型
	Region    string   `json:"region"`    // 资源地区
	URL       string   `json:"url"`       // 资源链接
	Summary   string   `json:"summary"`   // 资源的描述
	Tags      []Tag    `json:"tags"`      // 资源对应的标签列表
	GroupIDs  []string `json:"groupIds"`  // 资源分组的分组列表
}

type Tag struct {
	Key   string `json:"key"`
	Value string `json:"value"`
}
