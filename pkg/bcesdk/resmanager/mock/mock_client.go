// Code generated by MockGen. DO NOT EDIT.
// Source: icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/resmanager (interfaces: Interface)

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	resmanager "github.com/baidubce/bce-sdk-go/services/resmanager"
	gomock "github.com/golang/mock/gomock"
	bce "icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
	resmanager0 "icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/resmanager"
)

// MockInterface is a mock of Interface interface.
type MockInterface struct {
	ctrl     *gomock.Controller
	recorder *MockInterfaceMockRecorder
}

// MockInterfaceMockRecorder is the mock recorder for MockInterface.
type MockInterfaceMockRecorder struct {
	mock *MockInterface
}

// NewMockInterface creates a new mock instance.
func NewMockInterface(ctrl *gomock.Controller) *MockInterface {
	mock := &MockInterface{ctrl: ctrl}
	mock.recorder = &MockInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockInterface) EXPECT() *MockInterfaceMockRecorder {
	return m.recorder
}

// BindResourceToGroup mocks base method.
func (m *MockInterface) BindResourceToGroup(arg0 context.Context, arg1 *resmanager.BindResourceToGroupArgs, arg2 *bce.SignOption, arg3 bool) (*resmanager.BindResourceResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BindResourceToGroup", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*resmanager.BindResourceResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BindResourceToGroup indicates an expected call of BindResourceToGroup.
func (mr *MockInterfaceMockRecorder) BindResourceToGroup(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BindResourceToGroup", reflect.TypeOf((*MockInterface)(nil).BindResourceToGroup), arg0, arg1, arg2, arg3)
}

// GetResourceGroup mocks base method.
func (m *MockInterface) GetResourceGroup(arg0 context.Context, arg1 string, arg2 *bce.SignOption) (*resmanager.GroupList, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetResourceGroup", arg0, arg1, arg2)
	ret0, _ := ret[0].(*resmanager.GroupList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetResourceGroup indicates an expected call of GetResourceGroup.
func (mr *MockInterfaceMockRecorder) GetResourceGroup(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetResourceGroup", reflect.TypeOf((*MockInterface)(nil).GetResourceGroup), arg0, arg1, arg2)
}

// QueryGroupResource mocks base method.
func (m *MockInterface) QueryGroupResource(arg0 context.Context, arg1 *resmanager0.QueryGroupResourceRequest, arg2 *bce.SignOption) (*resmanager0.ResourceGroupsPageInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryGroupResource", arg0, arg1, arg2)
	ret0, _ := ret[0].(*resmanager0.ResourceGroupsPageInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryGroupResource indicates an expected call of QueryGroupResource.
func (mr *MockInterfaceMockRecorder) QueryGroupResource(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryGroupResource", reflect.TypeOf((*MockInterface)(nil).QueryGroupResource), arg0, arg1, arg2)
}
