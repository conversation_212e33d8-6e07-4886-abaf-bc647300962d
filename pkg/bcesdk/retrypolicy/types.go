// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2020/03/19 20:19:00, by <EMAIL>, create
*/
/*
DESCRIPTION
定义 bce-sdk-go 重试策略 Interface
*/

package retrypolicy

import (
	"context"
	"time"
)

// RetryPolicy defined an interface for retrying of bce.Client.
type RetryPolicy interface {
	// 最大重试次数
	GetMaxErrorRetry() int

	// retriesAttempted 重试前等待时间
	GetDelayBeforeNextRetry(ctx context.Context, err error, retriesAttempted int) time.Duration
}
