package quota

import (
	"context"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/util"
)

// Endpoints 包含所有地域的 endpoints
var Endpoints = map[string]string{
	"bj":      "http://quota-center.baidubce.com",
	"gz":      "http://quota-center.baidubce.com",
	"su":      "http://quota-center.baidubce.com",
	"hkg":     "http://quota-center.baidubce.com",
	"fwh":     "http://quota-center.baidubce.com",
	"bd":      "http://quota-center.baidubce.com",
	"sandbox": "http://************:19696",
}

var _ Interface = &Client{}

// Client is the neutron client implemention for Interface.
type Client struct {
	*bce.Client
}

// NewClient return client
func NewClient(ctx context.Context, config *bce.Config) *Client {
	return &Client{
		Client: bce.NewClient(config),
	}
}

// GetURL generates the full URL of http request for Baidu Cloud  API.
func (c *Client) GetURL(version string, params map[string]string) string {
	host := c.Endpoint
	if host == "" {
		host = Endpoints[c.GetRegion()]
	}
	uriPath := version
	return c.Client.GetURL(host, uriPath, params)
}

// SetDebug open or close Debug Mode
func (c *Client) SetDebug(debug bool) {
	c.Client.SetDebug(debug)
}

// NewSignOption return a app blb specified sign option
func NewSignOption() *bce.SignOption {
	option := &bce.SignOption{}
	option.AddHeader("x-bce-request-id", util.GetRequestID())
	option.AddHeadersToSign("host", "x-bce-date")

	return option
}
