package quota

import (
	"context"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
)

//go:generate mockgen -destination=./mock/mock_client.go -package=mock icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/quota Interface

// Interface quota APIs
type Interface interface {
	SetDebug(debug bool)

	// 保留子网  https://cloud.baidu.com/doc/QUOTA_CENTER/s/Bks05q9pb
	GetQuotas(ctx context.Context, request GetQuotasRequest, option *bce.SignOption) (*GetQuotasResponse, error)
}

type GetQuotasRequest struct {
	Version     string
	Type        string
	ServiceType QuotaServiceType
	Region      string
	Name        QuotaName
	Marker      string
	MaxKeys     int
}

type GetQuotasResponse struct {
	Marker      string      `json:"marker,omitempty"`
	IsTruncated bool        `json:"isTruncated,omitempty"`
	NextMarker  string      `json:"nextMarker,omitempty"`
	MaxKeys     int         `json:"maxKeys,omitempty"`
	Result      []QuotaData `json:"result,omitempty"`
}

type QuotaData struct {
	ProductType string           `json:"productType"`
	ServiceType QuotaServiceType `json:"serviceType"`
	Type        string           `json:"type"`
	Region      string           `json:"region"`
	Name        string           `json:"name"`
	Description string           `json:"description"`
	Value       string           `json:"value"`
	Used        string           `json:"used"`
}

type QuotaServiceType string

const (
	ServiceTypeBLB QuotaServiceType = "BLB"
	ServiceTypeEIP QuotaServiceType = "EIP"
)

type QuotaName string

const (
	QuotaNameBLBInstance QuotaName = "blbInstanceQuota"
	QuotaNameEIPInstance QuotaName = "eipInstanceQuota"
)
