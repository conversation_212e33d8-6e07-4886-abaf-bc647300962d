package internalvpc

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
)

func (c *Client) GetENIs(ctx context.Context, request *GetENIsRequest, option *bce.SignOption) (*GetENIsResponse, error) {
	params := map[string]string{}

	postContent, err := json.Marshal(request)
	if err != nil {
		return nil, err
	}

	req, err := bce.NewRequest("POST", c.GetURL("/v1/api/logical/network/eni/list", params), bytes.NewBuffer(postContent))
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	respContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var result GetENIsResponse
	err = json.Unmarshal(respContent, &result)
	if err != nil {
		return nil, err
	}

	return &result, nil
}

func (c *Client) AttachENI(ctx context.Context, eniID, deviceID string, option *bce.SignOption) error {
	params := map[string]string{
		"action": "attach",
	}

	reqBody := &struct {
		DeviceID string `json:"deviceId"`
	}{
		DeviceID: deviceID,
	}

	postContent, err := json.Marshal(reqBody)
	if err != nil {
		return err
	}

	url := fmt.Sprintf("/v1/api/logical/network/eni/%s", eniID)
	req, err := bce.NewRequest("PUT", c.GetURL(url, params), bytes.NewBuffer(postContent))
	if err != nil {
		return err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}

	_, err = resp.GetBodyContent()
	return err
}

func (c *Client) DetachENI(ctx context.Context, eniID, deviceID string, option *bce.SignOption) error {
	params := map[string]string{
		"action": "detach",
	}

	reqBody := &struct {
		DeviceID string `json:"deviceId"`
	}{
		DeviceID: deviceID,
	}

	postContent, err := json.Marshal(reqBody)
	if err != nil {
		return err
	}

	url := fmt.Sprintf("/v1/api/logical/network/eni/%s", eniID)
	req, err := bce.NewRequest("PUT", c.GetURL(url, params), bytes.NewBuffer(postContent))
	if err != nil {
		return err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}

	_, err = resp.GetBodyContent()
	return err
}

func (c *Client) DeleteENI(ctx context.Context, eniID string, option *bce.SignOption) error {
	params := map[string]string{}

	url := fmt.Sprintf("/v1/api/logical/network/eni/%s", eniID)
	req, err := bce.NewRequest("DELETE", c.GetURL(url, params), nil)
	if err != nil {
		return err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}

	_, err = resp.GetBodyContent()
	return err
}
