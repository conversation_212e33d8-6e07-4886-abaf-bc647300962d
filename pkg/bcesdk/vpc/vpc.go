/*
Copyright 2018 The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package vpc

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strconv"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
)

// CreateVPC create VPC
// https://cloud.baidu.com/doc/VPC/API.html#.E6.9F.A5.E8.AF.A2VPC.E5.88.97.E8.A1.A8
func (c *Client) CreateVPC(ctx context.Context, args *CreateVPCArgs, option *bce.SignOption) (string, error) {
	params := map[string]string{
		"clientToken": c.<PERSON>rate<PERSON>lientToken(),
	}

	postContent, err := json.Marshal(args)
	if err != nil {
		return "", err
	}

	req, err := bce.NewRequest("POST", c.GetURL("v1/vpc", params), bytes.NewBuffer(postContent))
	if err != nil {
		return "", err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return "", err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return "", err
	}

	var vpcResq CreateVPCResponse
	err = json.Unmarshal(bodyContent, &vpcResq)
	if err != nil {
		return "", err
	}

	return vpcResq.VPCID, nil
}

// ListVPC list all vpcs
func (c *Client) ListVPC(ctx context.Context, args *ListVPCArgs, option *bce.SignOption) ([]*VPC, error) {
	if args == nil {
		args = &ListVPCArgs{}
	}

	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
		"isDefault":   strconv.FormatBool(args.IsDefault),
	}

	req, err := bce.NewRequest("GET", c.GetURL("v1/vpc", params), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var lvResp *ListVPCResponse
	err = json.Unmarshal(bodyContent, &lvResp)
	if err != nil {
		return nil, err
	}

	return lvResp.VPCs, nil
}

// DescribeVPC describes a VPC via vpcId
func (c *Client) DescribeVPC(ctx context.Context, vpcID string, option *bce.SignOption) (*DescribeVPCResponse, error) {
	if vpcID == "" {
		return nil, errors.New("DescribeVPC failed: VPC ID cannot be empty")
	}

	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}

	req, err := bce.NewRequest("GET", c.GetURL("v1/vpc/"+vpcID, params), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var describeVPCResp *DescribeVPCResponse
	err = json.Unmarshal(bodyContent, &describeVPCResp)
	if err != nil {
		return nil, err
	}

	return describeVPCResp, nil
}

// DescribeVPC describes a VPC via vpcId
func (c *Client) DescribeVPCV2(ctx context.Context, vpcID string, option *bce.SignOption) (*DescribeVPCResponse, error) {
	if vpcID == "" {
		return nil, errors.New("DescribeVPC failed: VPC ID cannot be empty")
	}

	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}

	req, err := bce.NewRequest("GET", c.GetURL("v2/vpc/"+vpcID, params), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var describeVPCResp *DescribeVPCResponse
	err = json.Unmarshal(bodyContent, &describeVPCResp)
	if err != nil {
		return nil, err
	}

	return describeVPCResp, nil
}

// DeleteNormalSecurityGroup delete a normal security group
func (c *Client) DeleteNormalSecurityGroup(ctx context.Context, securityGroupID string, option *bce.SignOption) error {
	if securityGroupID == "" {
		return errors.New("DeleteNormalSecurityGroup failed: VPC ID cannot be empty")
	}

	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}
	url := fmt.Sprintf("v2/securityGroup/%s", securityGroupID)
	req, err := bce.NewRequest("DELETE", c.GetURL(url, params), nil)
	if err != nil {
		return err
	}
	_, err = c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}
	return nil
}
