package vpc

import (
	"context"
	"time"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/tag"
)

//go:generate mockgen -destination=./mock/mock_client.go -package=mock icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/vpc Interface

// Interface vpc interface
type Interface interface {
	SetDebug(debug bool)

	CreateVPC(ctx context.Context, args *CreateVPCArgs, option *bce.SignOption) (string, error)
	ListVPC(ctx context.Context, args *ListVPCArgs, option *bce.SignOption) ([]*VPC, error)
	DescribeVPC(ctx context.Context, vpcID string, option *bce.SignOption) (*DescribeVPCResponse, error)
	DescribeVPCV2(ctx context.Context, vpcID string, option *bce.SignOption) (*DescribeVPCResponse, error)

	CreateSubnet(ctx context.Context, args *CreateSubnetArgs, option *bce.SignOption) (string, error)
	ListSubnet(ctx context.Context, args *ListSubnetArgs, option *bce.SignOption) ([]*Subnet, error)
	DescribeSubnet(ctx context.Context, subnetID string, option *bce.SignOption) (*Subnet, error)
	DeleteSubnet(ctx context.Context, subnetID string, option *bce.SignOption) error

	ListRouteTable(ctx context.Context, args *ListRouteArgs, option *bce.SignOption) ([]RouteRule, error)
	DeleteRoute(ctx context.Context, routeID string, option *bce.SignOption) error
	CreateRouteRule(ctx context.Context, args *CreateRouteRuleArgs, option *bce.SignOption) (string, error)

	DeleteNormalSecurityGroup(ctx context.Context, securityGroupID string, option *bce.SignOption) error
}

// VPC type define
type VPC struct {
	VPCID       string     `json:"vpcId"`
	Name        string     `json:"name"`
	CIDR        string     `json:"cidr"`
	IPv6CIDR    string     `json:"ipv6Cidr,omitempty"`
	Description string     `json:"description"`
	IsDefault   bool       `json:"isDefault"`
	Subnets     []*Subnet  `json:"subnets,omitempty"`
	Tags        []*tag.Tag `json:"tags,omitempty"`

	// bec
	RegionID string `json:"regionId,omitempty"`

	Region          string    `json:"region,omitempty"`
	City            string    `json:"city,omitempty"`
	ServiceProvider string    `json:"serviceProvider,omitempty"`
	CreatedTime     time.Time `json:"createdTime,omitempty"`
}

// Subnet define subnet of vpc
type Subnet struct {
	SubnetID       string     `json:"subnetId"`
	Name           string     `json:"name"`
	ZoneName       string     `json:"zoneName,omitempty"`
	CIDR           string     `json:"cidr"`
	IPv6CIDR       string     `json:"ipv6Cidr,omitempty"`
	VPCID          string     `json:"vpcId"`
	SubnetType     SubnetType `json:"subnetType,omitempty"`
	AvailableIPNum int        `json:"availableIp,omitempty"`
	Description    string     `json:"description"`

	// bec
	RegionID string `json:"regionId"`
}

// SubnetType VPC Subnet type
type SubnetType string

const (
	// SubnetTypeBCC VPC Subnet BCC
	SubnetTypeBCC SubnetType = "BCC"

	// SubnetTypeBBC VPC Subnet BBC
	SubnetTypeBBC SubnetType = "BBC"

	// SubnetTypeBCCNAT VPC Subnet BCC_NAT
	SubnetTypeBCCNAT SubnetType = "BCC_NAT"

	RouteTypeInstance = "custom"

	EdgeRegion = "edge"
)

// CreateVPCArgs define args for creating vpc
type CreateVPCArgs struct {
	Name        string `json:"name"`
	Description string `json:"description,omitempty"`
	CIDR        string `json:"cidr"`
	EnableIPv6  bool   `json:"enableIpv6"`
}

// CreateVPCResponse define response
type CreateVPCResponse struct {
	VPCID string `json:"vpcId"`
}

// ListVPCArgs args
type ListVPCArgs struct {
	IsDefault bool `json:"isDefault"`
}

// ListVPCResponse define list vpc response
type ListVPCResponse struct {
	VPCs []*VPC `json:"vpcs"`
}

// DescribeVPCResponse define describe vpc response
type DescribeVPCResponse struct {
	ShowVPCModel *VPC `json:"vpc"`
}

// CreateSubnetArgs define args create a subnet
type CreateSubnetArgs struct {
	Name        string     `json:"name"`
	EnableIPv6  string     `json:"enableIpv6"`
	ZoneName    string     `json:"zoneName"`
	CIDR        string     `json:"cidr"`
	VPCID       string     `json:"vpcId"`
	SubnetType  SubnetType `json:"subnetType,omitempty"`
	Description string     `json:"description,omitempty"`
}

// CreateSubnetResponse define response of creating a subnet
type CreateSubnetResponse struct {
	SubnetID string `json:"subnetId"`
}

// ListSubnetArgs define args of List subnet
type ListSubnetArgs struct {
	VPCID      string     `json:"vpcId"`
	ZoneName   string     `json:"zoneName"`
	SubnetType SubnetType `json:"subnetType"`
	Marker     string     `json:"marker,omitempty"`
	MaxKeys    int        `json:"maxKeys,omitempty"`
}

// ListSubnetResponse for list subnet response
type ListSubnetResponse struct {
	Subnets     []*Subnet `json:"subnets"`
	Marker      string    `json:"marker"`
	IsTruncated bool      `json:"isTruncated"`
	NextMarker  string    `json:"nextMarker"`
	MaxKeys     int       `json:"maxKeys"`
}

// DescribeSubnetResponse for describe subnet response
type DescribeSubnetResponse struct {
	Subnet *Subnet `json:"subnet"`
}

// DescribeRouteTableArgs define args for describing a route table
type DescribeRouteTableArgs struct {
	RouteTableID string `json:"routeTableId,omitempty"`
	VPCID        string `json:"vpcId,omitempty"`
}

// DescribeRouteTableResponse for describe route table response
type DescribeRouteTableResponse struct {
	RouteTableID string       `json:"routeTableId"`
	VPCID        string       `json:"vpcId"`
	RouteRules   []*RouteRule `json:"routeRules"`
}

// RouteRule define route
type RouteRule struct {
	RouteRuleID        string `json:"routeRuleId"`
	RouteTableID       string `json:"routeTableId"`
	SourceAddress      string `json:"sourceAddress"`
	DestinationAddress string `json:"destinationAddress"`
	NexthopID          string `json:"nexthopId"`
	NexthopType        string `json:"nexthopType"`
	Description        string `json:"description"`
}

// ListRouteArgs define listroute args
type ListRouteArgs struct {
	RouteTableID string `json:"routeTableId,omitempty"`
	VpcID        string `json:"vpcId,omitempty"`
}

// ListRouteResponse define response of list route
type ListRouteResponse struct {
	RouteTableID string      `json:"routeTableId"`
	VpcID        string      `json:"vpcId"`
	RouteRules   []RouteRule `json:"routeRules"`
}

// CreateRouteRuleArgs define args create route
// http://gollum.baidu.com/Logical-Network-API#创建路由规则
type CreateRouteRuleArgs struct {
	RouteTableID  string `json:"routeTableId"`
	SourceAddress string `json:"sourceAddress"`

	// 源地址，源地址可以是0.0.0.0/0，
	// 否则匹配路由表的流量源必须属于该VPC下某子网，
	// 源地址选择自定义时，自定义网段需在已有子网范围内
	DestinationAddress string `json:"destinationAddress"`

	// 目的地址，目的地址可以是0.0.0.0/0，
	// 否则目的地址不能与本VPC cidr重叠
	// （目的网段或本VPC cidr为0.0.0.0/0时例外）
	NexthopID string `json:"nexthopId,omitempty"`

	// 下一跳id，当nexthopType是本地网关类型时，
	// 该字段可以为空
	NexthopType string `json:"nexthopType"`

	// 路由类型。Bcc类型是"custom"；
	// VPN类型是"vpn"；NAT类型是"nat"；本地网关类型是"defaultGateway"
	Description string `json:"description"`

	// 取值 4、6，表示 IPv4 还是 IPv6 的路由，默认为 4
	IPVersion int `json:"ipVersion"`
}

// CreateRouteResponse define response of creating route
type CreateRouteResponse struct {
	RouteRuleID string `json:"routeRuleId"`
}

type (
	NatGatewaySpecType string

	PaymentTimingType string

	NatStatusType string

	EipAction string
)

const (
	NAT_GATEWAY_SPEC_SMALL  NatGatewaySpecType = "small"
	NAT_GATEWAY_SPEC_MEDIUM NatGatewaySpecType = "medium"
	NAT_GATEWAY_SPEC_LARGE  NatGatewaySpecType = "large"

	PAYMENT_TIMING_PREPAID  PaymentTimingType = "Prepaid"
	PAYMENT_TIMING_POSTPAID PaymentTimingType = "Postpaid"

	EIP_ACTION_BIND   EipAction = "bind"
	EIP_ACTION_UNBIND EipAction = "unbind"
)

// CreateNatGatewayArgs defines the structure of the input parameters for the CreateNatGateway api
type CreateNatGatewayArgs struct {
	// 必需参数 NAT网关的名称，由大小写字母、数字以及-_ /.特殊字符组成，必须以字母开头，长度1-65
	Name string `json:"name,omitempty"`

	// 必需参数 VPC的ID
	VpcId string `json:"vpcId,omitempty"`

	// 必需参数 NAT网关的大小，有small(最多支持绑定5个公网IP)、medium(最多支持绑定10个公网IP)、large(最多支持绑定15个公网IP)三种
	Spec NatGatewaySpecType `json:"spec,omitempty"`

	// 非必需参数 关联NAT网关SNAT的EIP或者共享带宽中的一个或多个EIP
	Eips []string `json:"eips,omitempty"`

	// 非必需参数 关联NAT网关DNAT的EIP或者共享带宽中的一个或多个EIP
	DnatEips []string `json:"dnatEips,omitempty"`

	// 必需参数 计费信息
	Billing *Billing `json:"billing,omitempty"`
}

type Billing struct {
	// 付款时间，预支付（Prepaid）和后支付（Postpaid）
	PaymentTiming PaymentTimingType `json:"paymentTiming,omitempty"`

	// 保留信息，支付方式为后支付时不需要设置，预支付时必须设置
	Reservation *Reservation `json:"reservation,omitempty"`
}

type Reservation struct {
	// 时长，[1,2,3,4,5,6,7,8,9,12,24,36]
	ReservationLength int `json:"reservationLength,omitempty"`

	// 时间单位，month，当前仅支持按月
	ReservationTimeUnit string `json:"reservationTimeUnit,omitempty"`
}

// CreateNatGatewayResult defines the structure of the output parameters for the CreateNatGateway api
type CreateNatGatewayResult struct {
	// 创建的NAT的ID
	NatId string `json:"natId,omitempty"`
}

// ListNatGatewayArgs defines the structure of the input parameters for the ListNatGateway api
type ListNatGatewayArgs struct {
	// 必需参数 vpc的ID
	VpcId string

	// 非必需参数 指定查询的NAT的Id
	NatId string

	// 非必需参数 指定查询的NAT的名称
	Name string

	// 非必需参数 指定查询的NAT绑定的EIP
	Ip string

	// 非必需参数 批量获取列表的查询的起始位置，是一个由系统生成的字符串
	Marker string

	// 非必需参数 每页包含的最大数量，最大数量不超过1000。缺省值为1000
	MaxKeys int
}

// ListNatGatewayResult defines the structure of the output parameters for the ListNatGateway api
type ListNatGatewayResult struct {
	// 包含查询结果的列表
	Nats []NAT `json:"nats,omitempty"`

	// 标记查询的起始位置，若结果列表为空，此项不存在
	Marker string `json:"marker,omitempty"`

	// true表示后面还有数据，false表示已经是最后一页
	IsTruncated bool `json:"isTruncated,omitempty"`

	// 获取下一页所需要传递的marker值。当isTruncated为false时，该域不出现
	NextMarker string `json:"nextMarker,omitempty"`

	// 每页包含的最大数量
	MaxKeys int `json:"maxKeys,omitempty"`
}

// NAT is the result for getNatGatewayDetail api.
type NAT struct {
	// NAT网关的ID
	Id string `json:"id,omitempty"`

	// NAT网关名称
	Name string `json:"name,omitempty"`

	// NAT网关所属VPC的ID
	VpcId string `json:"vpcId,omitempty"`

	// NAT网关的大小，有small(最多支持绑定5个公网IP)、medium(最多支持绑定10个公网IP)、large(最多支持绑定15个公网IP)三种
	Spec NatGatewaySpecType `json:"spec,omitempty"`

	// NAT网关的状态
	Status NatStatusType `json:"status,omitempty"`

	// NAT网关绑定的EIP的IP地址列表
	Eips []string `json:"eips,omitempty"`

	// 付费方式 预付费Prepaid 后付费Postpaid
	PaymentTiming string `json:"paymentTiming,omitempty"`

	// 过期时间
	ExpiredTime string `json:"expiredTime,omitempty"`
}

// UpdateNatGatewayArgs defines the structure of the input parameters for the UpdateNatGateway api
type UpdateNatGatewayArgs struct {
	Name string `json:"name,omitempty"`
}

type BindAndUnbindSnatEipsArgs struct {
	Eips []string `json:"eips,omitempty"`
}

type BindAndUnbindDnatEipsArgs struct {
	DnatEips []string `json:"dnatEips,omitempty"`
}

type CreateNatSnatRuleArgs struct {
	// 必需参数 名称，由大小写字母、数字以及-_ /.特殊字符组成，必须以字母开头，长度1-65
	RuleName string `json:"ruleName,omitempty"`

	// 必需参数 公网IPs，关联在NAT网关SNAT上的EIP或共享带宽中的IPs
	PublicIpsAddress []string `json:"publicIpsAddress,omitempty"`

	// 必需参数 内网IP/网段
	SourceCIDR string `json:"sourceCIDR,omitempty"`
}

type CreateNatSnatRuleResponse struct {
	// 创建的SNAT规则的ID
	RuleID string `json:"ruleId,omitempty"`
}

type UpdateNatSnatRuleArgs struct {
	// 非必需参数 名称，由大小写字母、数字以及-_ /.特殊字符组成，必须以字母开头，长度1-65
	RuleName string `json:"ruleName,omitempty"`

	// 非必需参数 公网IPs，关联在NAT网关SNAT上的EIP或共享带宽中的IPs
	PublicIpsAddress []string `json:"publicIpsAddress,omitempty"`

	// 非必需参数 内网IP/网段
	SourceCIDR string `json:"sourceCIDR,omitempty"`
}

type ListNatSnatRuleArgs struct {
	// 非必需参数 批量获取列表的查询的起始位置，是一个由系统生成的字符串
	Marker string `json:"marker,omitempty"`

	// 非必需参数 每页包含的最大数量，最大数量通常不超过1000。缺省值为1000
	MaxKeys int `json:"maxKeys,omitempty"`
}

type ListNatSnatRuleResponse struct {
	// 标记查询的起始位置
	Marker string `json:"marker,omitempty"`

	// 每页包含的最大数量
	MaxKeys int `json:"maxKeys,omitempty"`

	// true表示后面还有数据，false表示已经是最后一页
	IsTruncated bool `json:"isTruncated,omitempty"`

	// 获取下一页所需要传递的marker值。当isTruncated为false时，该域不出现
	NextMarker string `json:"nextMarker,omitempty"`

	// SNAT规则列表
	Rules []SnatRule `json:"rules,omitempty"`
}

type SnatRule struct {
	// SNAT规则的ID
	RuleID string `json:"ruleId,omitempty"`

	// 名称，由大小写字母、数字以及-_ /.特殊字符组成，必须以字母开头，长度1-65
	RuleName string `json:"ruleName,omitempty"`

	// 公网IPs，关联在NAT网关SNAT上的EIP或共享带宽中的IPs
	PublicIpsAddress []string `json:"publicIpsAddress,omitempty"`

	// 内网IP/网段
	SourceCIDR string `json:"sourceCIDR,omitempty"`

	// 规则状态
	Status string `json:"status,omitempty"`
}

type CreateNatDnatRuleArgs struct {
	// 非必需参数 名称，由大小写字母、数字以及-_ /.特殊字符组成，必须以字母开头，长度1-655
	RuleName string `json:"ruleName,omitempty"`

	// 必需参数 公网IP，关联在NAT网关DNAT上的EIP或共享带宽中的IP
	PublicIpsAddress string `json:"publicIpAddress,omitempty"`

	// 必需参数 内网IP
	PrivateIpsAddress string `json:"privateIpAddress,omitempty"`

	// 必需参数 协议，支持TCP、UDP、all
	Protocol string `json:"protocol,omitempty"`

	// 非必需参数 协议为TCP、UDP必须传
	PublicPort string `json:"publicPort,omitempty"`

	// 非必需参数 内网端口(1-65535)，协议为TCP、UDP必须传
	PrivatePort string `json:"privatePort,omitempty"`
}

type CreateNatDnatRuleResponse struct {
	// 创建的DNAT规则的ID
	RuleID string `json:"ruleId,omitempty"`
}

type UpdateNatDnatRuleArgs struct {
	// 非必需参数 名称，由大小写字母、数字以及-_ /.特殊字符组成，必须以字母开头，长度1-655
	RuleName string `json:"ruleName,omitempty"`

	// 非必需参数 公网IP，关联在NAT网关DNAT上的EIP或共享带宽中的IP
	PublicIpsAddress string `json:"publicIpAddress,omitempty"`

	// 非必需参数 内网IP
	PrivateIpsAddress string `json:"privateIpAddress,omitempty"`

	// 非必需参数 协议，支持TCP、UDP、all
	Protocol string `json:"protocol,omitempty"`

	// 非必需参数 协议为TCP、UDP必须传
	PublicPort string `json:"publicPort,omitempty"`

	// 非必需参数 内网端口(1-65535)，协议为TCP、UDP必须传
	PrivatePort string `json:"privatePort,omitempty"`
}

type ListNatDnatRuleArgs struct {
	// 非必需参数 批量获取列表的查询的起始位置，是一个由系统生成的字符串
	Marker string `json:"marker,omitempty"`

	// 非必需参数 每页包含的最大数量，最大数量通常不超过1000。缺省值为1000
	MaxKeys int `json:"maxKeys,omitempty"`
}

type ListNatDnatRuleResponse struct {
	// 标记查询的起始位置
	Marker string `json:"marker,omitempty"`

	// 每页包含的最大数量
	MaxKeys int `json:"maxKeys,omitempty"`

	// true表示后面还有数据，false表示已经是最后一页
	IsTruncated bool `json:"isTruncated,omitempty"`

	// 获取下一页所需要传递的marker值。当isTruncated为false时，该域不出现
	NextMarker string `json:"nextMarker,omitempty"`

	// SNAT规则列表
	Rules []DnatRule `json:"rules,omitempty"`
}

type DnatRule struct {
	// DNAT规则的ID
	RuleID string `json:"ruleId,omitempty"`

	// 名称，由大小写字母、数字以及-_ /.特殊字符组成，必须以字母开头，长度1-655
	RuleName string `json:"ruleName,omitempty"`

	// 公网IP，关联在NAT网关DNAT上的EIP或共享带宽中的IP
	PublicIpsAddress string `json:"publicIpAddress,omitempty"`

	// 内网IP
	PrivateIpsAddress string `json:"privateIpAddress,omitempty"`

	// 协议，支持TCP、UDP、all
	Protocol string `json:"protocol,omitempty"`

	// 协议为TCP、UDP必须传
	PublicPort int `json:"publicPort,omitempty"`

	// 内网端口(1-65535)，协议为TCP、UDP必须传
	PrivatePort int `json:"privatePort,omitempty"`

	// 规则状态
	Status string `json:"status,omitempty"`
}
