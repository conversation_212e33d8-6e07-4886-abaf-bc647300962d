/*
Copyright 2018 The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package vpc

import (
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
)

// Endpoints contains all endpoints of Baidu Cloud BCC.
var Endpoints = map[string]string{
	"bj":      "bcc.bj.baidubce.com",
	"gz":      "bcc.gz.baidubce.com",
	"su":      "bcc.su.baidubce.com",
	"hkg":     "bcc.hkg.baidubce.com",
	"fwh":     "bcc.fwh.baidubce.com",
	"bd":      "bcc.bd.baidubce.com",
	"sandbox": "bcc.bj.qasandbox.baidu-int.com",
}

// Client is the bos client implemention for Baidu Cloud BOS API.
type Client struct {
	*bce.Client
}

// NewClient for VPC Client
func NewClient(config *bce.Config) *Client {
	bceClient := bce.NewClient(config)
	return &Client{
		Client: bceClient,
	}
}

// SetDebug 是否开启 debug 模式
func (c *Client) SetDebug(debug bool) {
	c.Client.SetDebug(debug)
}

// GetURL generates the full URL of http request for Baidu Cloud BOS API.
func (c *Client) GetURL(objectKey string, params map[string]string) string {
	host := c.Endpoint
	if host == "" {
		host = Endpoints[c.GetRegion()]
	}
	uriPath := objectKey
	return c.Client.GetURL(host, uriPath, params)
}
