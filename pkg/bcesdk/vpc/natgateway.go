package vpc

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"strconv"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
)

// CreateNatGateway - create a new nat gateway
func (c *Client) CreateNatGateway(ctx context.Context, args *CreateNatGatewayArgs, option *bce.SignOption) (*CreateNatGatewayResult, error) {
	if args == nil {
		return nil, errors.New("CreateNatGateway fail: the args cannot be nil")
	}

	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}

	postContent, err := json.Marshal(args)
	if err != nil {
		return nil, err
	}

	req, err := bce.NewRequest("POST", c.GetURL("v1/nat", params), bytes.NewBuffer(postContent))
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var createNatGatewayResult *CreateNatGatewayResult
	err = json.Unmarshal(bodyContent, &createNatGatewayResult)
	if err != nil {
		return nil, err
	}

	return createNatGatewayResult, nil
}

// ListNatGateway - list all nat gateways with the specific parameters
func (c *Client) ListNatGateway(ctx context.Context, args *ListNatGatewayArgs, option *bce.SignOption) (*ListNatGatewayResult, error) {
	if args == nil {
		return nil, errors.New("ListNatGateway fail: the args cannot be nil")
	}
	if args.MaxKeys == 0 {
		args.MaxKeys = 1000
	}

	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
		"vpcId":       args.VpcId,
	}

	if args.NatId != "" {
		params["natId"] = args.NatId
	}
	if args.Name != "" {
		params["name"] = args.Name
	}
	if args.Ip != "" {
		params["ip"] = args.Ip
	}
	if args.Marker != "" {
		params["marker"] = args.Marker
	}
	if args.MaxKeys != 0 {
		params["maxKeys"] = strconv.Itoa(args.MaxKeys)
	}

	req, err := bce.NewRequest("GET", c.GetURL("v1/nat", params), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var result *ListNatGatewayResult
	err = json.Unmarshal(bodyContent, &result)

	if err != nil {
		return nil, err
	}
	return result, nil
}

// GetNatGatewayDetail - get details of the specific nat gateway
func (c *Client) GetNatGatewayDetail(ctx context.Context, natId string, option *bce.SignOption) (*NAT, error) {
	if natId == "" {
		return nil, errors.New("GetNatGatewayDetail failed: natId cannot be empty")
	}

	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}

	req, err := bce.NewRequest("GET", c.GetURL("v1/nat/"+natId, params), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var nat *NAT
	err = json.Unmarshal(bodyContent, &nat)
	if err != nil {
		return nil, err
	}

	return nat, nil
}

// UpdateNatGateway - update the specified nat gateway - only name
func (c *Client) UpdateNatGateway(ctx context.Context, natId string, args *UpdateNatGatewayArgs, option *bce.SignOption) error {
	if args == nil {
		return errors.New("UpdateNatGateway fail: the args cannot be nil")
	}

	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}

	postContent, err := json.Marshal(args)
	if err != nil {
		return err
	}

	req, err := bce.NewRequest("PUT", c.GetURL("v1/nat/"+natId, params), bytes.NewBuffer(postContent))
	if err != nil {
		return err
	}

	_, err = c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}

	return nil
}

// DeleteNatGateway - delete the specific nat gateway
func (c *Client) DeleteNatGateway(ctx context.Context, natId string, option *bce.SignOption) error {
	if natId == "" {
		return errors.New("DeleteNatGateway failed: natId cannot be empty")
	}

	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}

	req, err := bce.NewRequest("DELETE", c.GetURL("v1/nat/"+natId, params), nil)
	if err != nil {
		return err
	}

	_, err = c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}

	return nil
}

// BindSnatEips - bind eips for the specific nat gateway snat
func (c *Client) BindSnatEips(ctx context.Context, natId string, args *BindAndUnbindSnatEipsArgs, option *bce.SignOption) error {
	if args == nil {
		return errors.New("BindSnatEips fail: the args cannot be nil")
	}

	params := map[string]string{
		string(EIP_ACTION_BIND): "",
		"clientToken":           c.GenerateClientToken(),
	}

	postContent, err := json.Marshal(args)
	if err != nil {
		return err
	}

	req, err := bce.NewRequest("PUT", c.GetURL("v1/nat/"+natId, params), bytes.NewBuffer(postContent))
	if err != nil {
		return err
	}

	_, err = c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}

	return nil
}

// UnbindSnatEips - bind eips for the specific nat gateway snat
func (c *Client) UnbindSnatEips(ctx context.Context, natId string, args *BindAndUnbindSnatEipsArgs, option *bce.SignOption) error {
	if args == nil {
		return errors.New("UnbindSnatEips fail: the args cannot be nil")
	}

	params := map[string]string{
		string(EIP_ACTION_UNBIND): "",
		"clientToken":             c.GenerateClientToken(),
	}

	postContent, err := json.Marshal(args)
	if err != nil {
		return err
	}

	req, err := bce.NewRequest("PUT", c.GetURL("v1/nat/"+natId, params), bytes.NewBuffer(postContent))
	if err != nil {
		return err
	}

	_, err = c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}

	return nil
}

// BindDnatEips - bind eips for the specific nat gateway dnat
func (c *Client) BindDnatEips(ctx context.Context, natId string, args *BindAndUnbindDnatEipsArgs, option *bce.SignOption) error {
	if args == nil {
		return errors.New("BindSnatEips fail: the args cannot be nil")
	}

	params := map[string]string{
		string(EIP_ACTION_BIND): "",
		"clientToken":           c.GenerateClientToken(),
	}

	postContent, err := json.Marshal(args)
	if err != nil {
		return err
	}

	req, err := bce.NewRequest("PUT", c.GetURL("v1/nat/"+natId, params), bytes.NewBuffer(postContent))
	if err != nil {
		return err
	}

	_, err = c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}

	return nil
}

// UnbindDnatEips - bind eips for the specific nat gateway dnat
func (c *Client) UnbindDnatEips(ctx context.Context, natId string, args *BindAndUnbindDnatEipsArgs, option *bce.SignOption) error {
	if args == nil {
		return errors.New("UnbindSnatEips fail: the args cannot be nil")
	}

	params := map[string]string{
		string(EIP_ACTION_UNBIND): "",
		"clientToken":             c.GenerateClientToken(),
	}

	postContent, err := json.Marshal(args)
	if err != nil {
		return err
	}

	req, err := bce.NewRequest("PUT", c.GetURL("v1/nat/"+natId, params), bytes.NewBuffer(postContent))
	if err != nil {
		return err
	}

	_, err = c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}

	return nil
}

func (c *Client) CreateNatSnatRule(ctx context.Context, natId string, args *CreateNatSnatRuleArgs, option *bce.SignOption) (string, error) {
	if args == nil {
		return "", errors.New("CreateNatSnatRule fail: the args cannot be nil")
	}

	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}

	postContent, err := json.Marshal(args)
	if err != nil {
		return "", err
	}

	req, err := bce.NewRequest("POST", c.GetURL("v1/nat/"+natId+"/snatRule", params), bytes.NewBuffer(postContent))
	if err != nil {
		return "", err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return "", err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return "", err
	}

	var createNatGatewayResult *CreateNatSnatRuleResponse
	err = json.Unmarshal(bodyContent, &createNatGatewayResult)
	if err != nil {
		return "", err
	}

	return createNatGatewayResult.RuleID, nil
}

func (c *Client) DeleteNatSnatRule(ctx context.Context, natId string, ruleId string, option *bce.SignOption) error {
	if natId == "" {
		return errors.New("DeleteNatSnatRule fail: natId cannot be empty")
	}
	if ruleId == "" {
		return errors.New("DeleteNatSnatRule fail: ruleId cannot be empty")
	}

	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}

	req, err := bce.NewRequest("DELETE", c.GetURL("v1/nat/"+natId+"/snatRule/"+ruleId, params), nil)
	if err != nil {
		return err
	}

	_, err = c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}

	return nil
}

func (c *Client) UpdateNatSnatRule(ctx context.Context, natId string, ruleId string, args *UpdateNatSnatRuleArgs, option *bce.SignOption) error {
	if natId == "" {
		return errors.New("UpdateNatSnatRule fail: natId cannot be empty")
	}
	if ruleId == "" {
		return errors.New("UpdateNatSnatRule fail: ruleId cannot be empty")
	}
	if args == nil {
		return errors.New("UpdateNatSnatRule fail: args cannot be nil")
	}

	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}

	postContent, err := json.Marshal(args)
	if err != nil {
		return err
	}

	req, err := bce.NewRequest("PUT", c.GetURL("v1/nat/"+natId+"/snatRule/"+ruleId, params), bytes.NewBuffer(postContent))
	if err != nil {
		return err
	}

	_, err = c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}

	return nil
}

func (c *Client) ListNatSnatRules(ctx context.Context, natId string, args *ListNatSnatRuleArgs, option *bce.SignOption) (*ListNatSnatRuleResponse, error) {
	if natId == "" {
		return nil, errors.New("ListNatSnatRules fail: natId cannot be empty")
	}
	if args == nil {
		return nil, errors.New("ListNatSnatRules fail: args cannot be nil")
	}
	if args.MaxKeys == 0 {
		args.MaxKeys = 1000
	}

	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}
	if args.Marker != "" {
		params["marker"] = args.Marker
	}
	if args.MaxKeys != 0 {
		params["maxKeys"] = strconv.Itoa(args.MaxKeys)
	}

	req, err := bce.NewRequest("GET", c.GetURL("v1/nat/"+natId+"/snatRule", params), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var result *ListNatSnatRuleResponse
	err = json.Unmarshal(bodyContent, &result)

	if err != nil {
		return nil, err
	}
	return result, nil
}

func (c *Client) CreateNatDnatRule(ctx context.Context, natId string, args *CreateNatDnatRuleArgs, option *bce.SignOption) (string, error) {
	if args == nil {
		return "", errors.New("CreateNatDnatRule fail: the args cannot be nil")
	}

	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}

	postContent, err := json.Marshal(args)
	if err != nil {
		return "", err
	}

	req, err := bce.NewRequest("POST", c.GetURL("v1/nat/"+natId+"/dnatRule", params), bytes.NewBuffer(postContent))
	if err != nil {
		return "", err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return "", err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return "", err
	}

	var createNatGatewayResult *CreateNatDnatRuleResponse
	err = json.Unmarshal(bodyContent, &createNatGatewayResult)
	if err != nil {
		return "", err
	}

	return createNatGatewayResult.RuleID, nil
}

func (c *Client) DeleteNatDnatRule(ctx context.Context, natId string, ruleId string, option *bce.SignOption) error {
	if natId == "" {
		return errors.New("DeleteNatDnatRule fail: natId cannot be empty")
	}
	if ruleId == "" {
		return errors.New("DeleteNatDnatRule fail: ruleId cannot be empty")
	}

	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}

	req, err := bce.NewRequest("DELETE", c.GetURL("v1/nat/"+natId+"/dnatRule/"+ruleId, params), nil)
	if err != nil {
		return err
	}

	_, err = c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}

	return nil
}

func (c *Client) UpdateNatDnatRule(ctx context.Context, natId string, ruleId string, args *UpdateNatDnatRuleArgs, option *bce.SignOption) error {
	if natId == "" {
		return errors.New("UpdateNatDnatRule fail: natId cannot be empty")
	}
	if ruleId == "" {
		return errors.New("UpdateNatDnatRule fail: ruleId cannot be empty")
	}
	if args == nil {
		return errors.New("UpdateNatDnatRule fail: args cannot be nil")
	}

	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}

	postContent, err := json.Marshal(args)
	if err != nil {
		return err
	}

	req, err := bce.NewRequest("PUT", c.GetURL("v1/nat/"+natId+"/dnatRule/"+ruleId, params), bytes.NewBuffer(postContent))
	if err != nil {
		return err
	}

	_, err = c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}

	return nil
}

func (c *Client) ListNatDnatRules(ctx context.Context, natId string, args *ListNatDnatRuleArgs, option *bce.SignOption) (*ListNatDnatRuleResponse, error) {
	if natId == "" {
		return nil, errors.New("ListNatDnatRules fail: natId cannot be empty")
	}
	if args == nil {
		return nil, errors.New("ListNatDnatRules fail: args cannot be nil")
	}
	if args.MaxKeys <= 0 {
		args.MaxKeys = 1000
	}

	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}
	if args.Marker != "" {
		params["marker"] = args.Marker
	}
	if args.MaxKeys != 0 {
		params["maxKeys"] = strconv.Itoa(args.MaxKeys)
	}

	req, err := bce.NewRequest("GET", c.GetURL("v1/nat/"+natId+"/dnatRule", params), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var result *ListNatDnatRuleResponse
	err = json.Unmarshal(bodyContent, &result)

	if err != nil {
		return nil, err
	}
	return result, nil
}
