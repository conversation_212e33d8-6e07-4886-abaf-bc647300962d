package util

import (
	"net"
	"testing"
)

func TestIsConflictCIDRString(t *testing.T) {
	type args struct {
		cidr1 string
		cidr2 string
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		{
			name: "CIDR 冲突",
			args: args{
				cidr1: "***********/16",
				cidr2: "***********/24",
			},
			want: true,
		},
		{
			name: "CIDR 冲突",
			args: args{
				cidr1: "***********/16",
				cidr2: "***********/24",
			},
			want: true,
		},
		{
			name: "CIDR 冲突",
			args: args{
				cidr1: "***********/16",
				cidr2: "0.0.0.0/0",
			},
			want: true,
		},
		{
			name: "CIDR 不冲突",
			args: args{
				cidr1: "***********/16",
				cidr2: "192.169.0.0/16",
			},
			want: false,
		},
		{
			name: "相同 CIDR 冲突",
			args: args{
				cidr1: "***********/16",
				cidr2: "***********/16",
			},
			want: true,
		},
		{
			name: "CIDR 冲突",
			args: args{
				cidr1: "fc00::/16",
				cidr2: "fc00::2020:0010/128",
			},
			want: true,
		},
		{
			name: "CIDR 不冲突",
			args: args{
				cidr1: "fd00::/8",
				cidr2: "fe80::/10",
			},
			want: false,
		},
		{
			name: "CIDR 不冲突",
			args: args{
				cidr1: "fc00:2020::/32",
				cidr2: "fc00:2019::/32",
			},
			want: false,
		},
		{
			name: "IPv4/IPv6 CIDR 不冲突",
			args: args{
				cidr1: "***********/16",
				cidr2: "fc00:2019::/32",
			},
			want: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := IsConflictCIDRString(tt.args.cidr1, tt.args.cidr2); got != tt.want {
				t.Errorf("IsConflictCIDRString() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestIsPrivateNetCIDRString(t *testing.T) {
	type args struct {
		cidr string
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		{
			args: args{
				cidr: "10.0.0.0/24",
			},
			want: true,
		},
		{
			args: args{
				cidr: "10.0.0.0/8",
			},
			want: true,
		},
		{
			args: args{
				cidr: "10.0.0.0/6",
			},
			want: false,
		},
		{
			args: args{
				cidr: "***********/24",
			},
			want: true,
		},
		{
			args: args{
				cidr: "***********/24",
			},
			want: false,
		},
		{
			args: args{
				cidr: "**********/24",
			},
			want: true,
		},
		{
			args: args{
				cidr: "**********/12",
			},
			want: true,
		},
		{
			args: args{
				cidr: "*******/24",
			},
			want: false,
		},
		{
			args: args{
				cidr: "fd00::/7",
			},
			want: true,
		},
		{
			args: args{
				cidr: "2001:da8::/32",
			},
			want: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := IsPrivateNetCIDRString(tt.args.cidr); got != tt.want {
				t.Errorf("IsPrivateNetCIDRString() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestIsPrivateNetIP(t *testing.T) {
	type args struct {
		ip string
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		{
			name: "private net ip",
			args: args{
				ip: "**********",
			},
			want: true,
		},
		{
			name: "public net ip",
			args: args{
				ip: "**********",
			},
			want: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := IsPrivateNetIP(tt.args.ip); got != tt.want {
				t.Errorf("IsPrivateNetIP() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestGenerateRandomIPv6SubCIDR(t *testing.T) {
	cidrs := []struct {
		name     string
		cidr     string
		maskSize int
	}{
		{
			name:     "random local subnet with interfaceID",
			cidr:     "fd00::/8",
			maskSize: 112,
		},
		{
			name:     "random local subnet without interfaceID",
			cidr:     "fd00::/8",
			maskSize: 64,
		},
	}

	for _, c := range cidrs {
		t.Run(c.name, func(t *testing.T) {
			_, cidr, _ := net.ParseCIDR(c.cidr)
			actual := GenerateRandomIPv6SubCIDR(cidr, c.maskSize)
			size, _ := actual.Mask.Size()
			if size != c.maskSize {
				t.Errorf("unexpected generated cidr: %s", actual.String())
			}
			t.Logf("generated cidr: %s", actual.String())
		})
	}
}
