package qualify

import (
	"context"
	"encoding/json"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
)

func (c *client) RealNameAuthenticate(ctx context.Context, accountID string, option *bce.SignOption) (*RealNameAuthenticateResponse, error) {
	params := map[string]string{
		"accountId": accountID,
	}

	req, err := bce.NewRequest("GET", c.GetURL("qualify/v2/qualification/realname", params), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	result := new(RealNameAuthenticateResponse)
	err = json.Unmarshal(bodyContent, result)
	if err != nil {
		return nil, err
	}

	return result, nil
}
