package qualify

import (
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/util"
)

var Endpoints = map[string]string{
	"bj":      "qualify.bce-internal.baidu.com",
	"gz":      "qualify.bce-internal.baidu.com",
	"su":      "qualify.bce-internal.baidu.com",
	"hkg":     "qualify.bce-internal.baidu.com",
	"fwh":     "qualify.bce-internal.baidu.com",
	"bd":      "qualify.bce-internal.baidu.com",
	"sandbox": "gzns-store-sandbox089.gzns.baidu.com:8291",
}

var _ Interface = &client{}

// client is the logic BCC client implementation for Interface
type client struct {
	*bce.Client
}

// NewClient return client
func NewClient(config *bce.Config) *client {
	return &client{
		Client: bce.NewClient(config),
	}
}

// GetURL generates the full URL of http request for BaiDu Cloud API
func (c *client) GetURL(version string, params map[string]string) string {
	host := c.Endpoint
	if host == "" {
		host = Endpoints[c.GetRegion()]
	}
	uriPath := version
	return c.Client.GetURL(host, uriPath, params)
}

// NewSignOption return specified sign option
func NewSignOption() *bce.SignOption {
	option := &bce.SignOption{}
	option.AddHeader("x-bce-request-id", util.GetRequestID())
	option.AddHeadersToSign("host", "x-bce-date")

	return option
}
