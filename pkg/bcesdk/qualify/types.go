package qualify

import (
	"context"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
)

//go:generate mockgen -destination=./mock/mock_client.go -package=mock icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/qualify Interface

type Interface interface {
	// 参考：http://wiki.baidu.com/pages/viewpage.action?pageId=*********
	RealNameAuthenticate(ctx context.Context, accountID string, option *bce.SignOption) (*RealNameAuthenticateResponse, error)
}

type RealNameAuthenticateResponse struct {
	QualifyType              QualifyType `json:"qualifyType"`
	Name                     string      `json:"name"`
	Status                   Status      `json:"status"`
	PrimePassFlag            bool        `json:"primePassFlag"`
	SeniorPassFlag           bool        `json:"seniorPassFlag"`
	PersonalPrimePassFlag    bool        `json:"personalPrimePassFlag"`
	PersonalSeniorPassFlag   bool        `json:"personalSeniorPassFlag"`
	EnterprisePrimePassFlag  bool        `json:"enterprisePrimePassFlag"`
	EnterpriseSeniorPassFlag bool        `json:"enterpriseSeniorPassFlag"`
	// AuditPassTime            int64       `json:"auditPassTime"`
}

type QualifyType string

const (
	QualifyTypePersonal   QualifyType = "PERSONAL"
	QualifyTypeEnterprise QualifyType = "ENTERPRISE"
	QualifyTypeWhite      QualifyType = "WHITE"
)

type Status string

const (
	StatusReturn Status = "RETURN"
	StatusPass   Status = "PASS"
	StatusAudit  Status = "AUDIT"
	StatusNone   Status = "NONE"
)
