// Code generated by MockGen. DO NOT EDIT.
// Source: icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/qualify (interfaces: Interface)

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	gomock "github.com/golang/mock/gomock"
	bce "icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
	qualify "icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/qualify"
	reflect "reflect"
)

// MockInterface is a mock of Interface interface
type MockInterface struct {
	ctrl     *gomock.Controller
	recorder *MockInterfaceMockRecorder
}

// MockInterfaceMockRecorder is the mock recorder for MockInterface
type MockInterfaceMockRecorder struct {
	mock *MockInterface
}

// NewMockInterface creates a new mock instance
func NewMockInterface(ctrl *gomock.Controller) *MockInterface {
	mock := &MockInterface{ctrl: ctrl}
	mock.recorder = &MockInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use
func (m *MockInterface) EXPECT() *MockInterfaceMockRecorder {
	return m.recorder
}

// RealNameAuthenticate mocks base method
func (m *MockInterface) RealNameAuthenticate(arg0 context.Context, arg1 string, arg2 *bce.SignOption) (*qualify.RealNameAuthenticateResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RealNameAuthenticate", arg0, arg1, arg2)
	ret0, _ := ret[0].(*qualify.RealNameAuthenticateResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RealNameAuthenticate indicates an expected call of RealNameAuthenticate
func (mr *MockInterfaceMockRecorder) RealNameAuthenticate(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RealNameAuthenticate", reflect.TypeOf((*MockInterface)(nil).RealNameAuthenticate), arg0, arg1, arg2)
}
