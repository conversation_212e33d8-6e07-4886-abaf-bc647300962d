/**
 * @Author: pansiyuan02
 * @Description:
 * @File:  client
 * @Version: 1.0.0
 * @Date: 2021/1/8 1:38 下午
 */
package tag

import "icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"

// Client is the tag client implemention for Baidu Cloud TAG API.
type Client struct {
	*bce.Client
}

func NewClient(config *bce.Config) *Client {
	bceClient := bce.NewClient(config)
	return &Client{Client: bceClient}
}

// GetURL generates the full URL of http request for Baidu Cloud BOS API.
func (c *Client) GetURL(version string, params map[string]string) string {
	host := c.Endpoint
	uriPath := version
	return c.Client.GetURL(host, uriPath, params)
}
