// Code generated by MockGen. DO NOT EDIT.
// Source: icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/tag (interfaces: Interface)

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	bce "icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
	tag "icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/tag"
)

// MockInterface is a mock of Interface interface.
type MockInterface struct {
	ctrl     *gomock.Controller
	recorder *MockInterfaceMockRecorder
}

// MockInterfaceMockRecorder is the mock recorder for MockInterface.
type MockInterfaceMockRecorder struct {
	mock *MockInterface
}

// NewMockInterface creates a new mock instance.
func NewMockInterface(ctrl *gomock.Controller) *MockInterface {
	mock := &MockInterface{ctrl: ctrl}
	mock.recorder = &MockInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockInterface) EXPECT() *MockInterfaceMockRecorder {
	return m.recorder
}

// BindResourcesInBatch mocks base method.
func (m *MockInterface) BindResourcesInBatch(arg0 context.Context, arg1 *tag.BindResourceInBatchArgs, arg2 *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BindResourcesInBatch", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// BindResourcesInBatch indicates an expected call of BindResourcesInBatch.
func (mr *MockInterfaceMockRecorder) BindResourcesInBatch(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BindResourcesInBatch", reflect.TypeOf((*MockInterface)(nil).BindResourcesInBatch), arg0, arg1, arg2)
}

// CreateTag mocks base method.
func (m *MockInterface) CreateTag(arg0 context.Context, arg1 *tag.CreateTagsArgs, arg2 *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateTag", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateTag indicates an expected call of CreateTag.
func (mr *MockInterfaceMockRecorder) CreateTag(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateTag", reflect.TypeOf((*MockInterface)(nil).CreateTag), arg0, arg1, arg2)
}

// DeleteTagAssociations mocks base method.
func (m *MockInterface) DeleteTagAssociations(arg0 context.Context, arg1 *tag.DeleteTagAssociationsArgs, arg2 *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteTagAssociations", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteTagAssociations indicates an expected call of DeleteTagAssociations.
func (mr *MockInterfaceMockRecorder) DeleteTagAssociations(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteTagAssociations", reflect.TypeOf((*MockInterface)(nil).DeleteTagAssociations), arg0, arg1, arg2)
}

// DeleteTags mocks base method.
func (m *MockInterface) DeleteTags(arg0 context.Context, arg1 []tag.Tag, arg2 *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteTags", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteTags indicates an expected call of DeleteTags.
func (mr *MockInterfaceMockRecorder) DeleteTags(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteTags", reflect.TypeOf((*MockInterface)(nil).DeleteTags), arg0, arg1, arg2)
}

// ListBoundResources mocks base method.
func (m *MockInterface) ListBoundResources(arg0 context.Context, arg1 *tag.ListBoundResourcesParams, arg2 *bce.SignOption) (*tag.ListBoundResourcesResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListBoundResources", arg0, arg1, arg2)
	ret0, _ := ret[0].(*tag.ListBoundResourcesResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListBoundResources indicates an expected call of ListBoundResources.
func (mr *MockInterfaceMockRecorder) ListBoundResources(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListBoundResources", reflect.TypeOf((*MockInterface)(nil).ListBoundResources), arg0, arg1, arg2)
}

// ListTags mocks base method.
func (m *MockInterface) ListTags(arg0 context.Context, arg1 tag.ListTagsParams, arg2 *bce.SignOption) (*tag.ListTagsResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListTags", arg0, arg1, arg2)
	ret0, _ := ret[0].(*tag.ListTagsResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListTags indicates an expected call of ListTags.
func (mr *MockInterfaceMockRecorder) ListTags(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListTags", reflect.TypeOf((*MockInterface)(nil).ListTags), arg0, arg1, arg2)
}

// QueryUserTagsInfoList mocks base method.
func (m *MockInterface) QueryUserTagsInfoList(arg0 context.Context, arg1 bool, arg2 *tag.QueryUserTagListArgs, arg3 *bce.SignOption) (*tag.AssociationsResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryUserTagsInfoList", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*tag.AssociationsResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryUserTagsInfoList indicates an expected call of QueryUserTagsInfoList.
func (mr *MockInterfaceMockRecorder) QueryUserTagsInfoList(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryUserTagsInfoList", reflect.TypeOf((*MockInterface)(nil).QueryUserTagsInfoList), arg0, arg1, arg2, arg3)
}

// SetDebug mocks base method.
func (m *MockInterface) SetDebug(arg0 bool) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SetDebug", arg0)
}

// SetDebug indicates an expected call of SetDebug.
func (mr *MockInterfaceMockRecorder) SetDebug(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetDebug", reflect.TypeOf((*MockInterface)(nil).SetDebug), arg0)
}

// UnbindResourcesInBatch mocks base method.
func (m *MockInterface) UnbindResourcesInBatch(arg0 context.Context, arg1 *tag.UnbindResourceInBatchArgs, arg2 *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UnbindResourcesInBatch", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// UnbindResourcesInBatch indicates an expected call of UnbindResourcesInBatch.
func (mr *MockInterfaceMockRecorder) UnbindResourcesInBatch(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UnbindResourcesInBatch", reflect.TypeOf((*MockInterface)(nil).UnbindResourcesInBatch), arg0, arg1, arg2)
}

// UpdateResourceAssociations mocks base method.
func (m *MockInterface) UpdateResourceAssociations(arg0 context.Context, arg1 *tag.CreateAndAssignArgs, arg2 *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateResourceAssociations", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateResourceAssociations indicates an expected call of UpdateResourceAssociations.
func (mr *MockInterfaceMockRecorder) UpdateResourceAssociations(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateResourceAssociations", reflect.TypeOf((*MockInterface)(nil).UpdateResourceAssociations), arg0, arg1, arg2)
}
