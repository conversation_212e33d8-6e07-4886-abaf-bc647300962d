package tag

import (
	"context"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
)

//go:generate mockgen -destination=./mock/mock_client.go -package=mock icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/tag Interface

// Interface vpc interface
type Interface interface {
	SetDebug(bool)

	CreateTag(ctx context.Context, args *CreateTagsArgs, option *bce.SignOption) error
	DeleteTags(ctx context.Context, tags []Tag, option *bce.SignOption) error
	ListTags(ctx context.Context, args ListTagsParams, option *bce.SignOption) (*ListTagsResult, error)
	ListBoundResources(ctx context.Context, args *ListBoundResourcesParams, option *bce.SignOption) (*ListBoundResourcesResult, error)
	BindResourcesInBatch(ctx context.Context, args *BindResourceInBatchArgs, option *bce.SignOption) error
	UnbindResourcesInBatch(ctx context.Context, args *UnbindResourceInBatchArgs, option *bce.SignOption) error
	DeleteTagAssociations(ctx context.Context, args *DeleteTagAssociationsArgs, option *bce.SignOption) error
	QueryUserTagsInfoList(ctx context.Context, strongAssociation bool, args *QueryUserTagListArgs, option *bce.SignOption) (*AssociationsResult, error)
	UpdateResourceAssociations(ctx context.Context, args *CreateAndAssignArgs, option *bce.SignOption) error
}

type ServiceType string

const (
	ServiceTypeBLB ServiceType = "BLB"
	ServiceTypeEIP ServiceType = "EIP"
	ServiceTypeBCC ServiceType = "BCC"
	ServiceTypeBBC ServiceType = "BBC"
	ServiceTypeCCE ServiceType = "CCE"
	ServiceTypeBEC ServiceType = "BEC"
)

type Tag struct {
	TagKey   string `json:"tagKey"`
	TagValue string `json:"tagValue"`
}

type ListBoundResourcesParams struct {
	TagKey       string
	TagValue     *string // 待查询的标签值，该项为nil表示查询所有标签值，为空表示待查询的标签值为空字符串
	Region       *string // 待查询的region，该项为nil使用client配置的region，为空表示查询所有region
	ResourceType string  // 待查询的资源类型，该项不出现或为空表示查询所有资源类型
}

type BoundResource struct {
	ResourceType string `json:"resourceType"`
	ResourceID   string `json:"resourceId"`
	Region       string `json:"region"`
}

type ListBoundResourcesResultItem struct {
	TagKey    string          `json:"tagKey"`
	TagValue  string          `json:"tagValue"`
	Resources []BoundResource `json:"resources"`
}

type ListBoundResourcesResult struct {
	TagResources []*ListBoundResourcesResultItem `json:"tagResources"`
}

type ListTagsParams struct {
	TagKey   string
	TagValue *string
}

type ListTagsResult struct {
	Tags []*Tag `json:"tags"`
}

type Resource struct {
	// 资源id 如 eip-cjy1
	ResourceID string `json:"resourceId"`

	// 服务类型 EIP
	ServiceType string `json:"serviceType"`

	// 区域 bj
	Region string `json:"region"`
}

type CreateTagsArgs struct {
	// 待创建的标签键值对列表 必需
	Tags []Tag `json:"tags"`
}

type BindResourceInBatchArgs struct {
	// 标签键 如新沙盒测试 必需
	TagKey string `json:"tagKey"`

	// 标签值 必需
	TagValue string `json:"tagValue"`

	// 服务类型 如 EIP 必需
	ServiceType string `json:"serviceType"`

	// 资源列表 必需
	Resources []Resource `json:"resources"`
}

type UnbindResourceInBatchArgs struct {
	// 标签键 必需
	TagKey string `json:"tagKey"`

	// 标签值 必需
	TagValue string `json:"tagValue"`

	// 服务类型 必需
	ServiceType string `json:"serviceType"`

	// 资源列表 必需
	Resources []Resource `json:"resources"`
}

type DeleteTagAssociationsArgs struct {
	// 待解绑的资源
	Resource Resource `json:"resource"`
}

type QueryUserTagListArgs struct {
	// 要查询的region，可填多个 可选
	Regions []string `json:"regions"`

	// 要查询的标签值 可选
	TagValue *string `json:"tagValue"`

	// 要查询的标签键 可选
	TagKey *string `json:"tagKey"`

	// 要查询的服务类型，可填多个 可选
	ServiceTypes []string `json:"serviceTypes"`

	// 要查询的资源ID，可填多个 可选
	ResourceIDs []string `json:"resourceIds"`
}

type AssociationsResult struct {
	// 标签与资源完整关联关系
	TagAssociationFulls []FullTagAssociation `json:"tagAssociationFulls"`
}

type FullTagAssociation struct {
	// 服务类型 如 BCC
	ServiceType string `json:"serviceType"`

	// 标签所属账户ID
	AccountID string `json:"accountId"`

	// 资源的ID 如 i-LGzaQZWI
	ResourceID string `json:"resourceId"`

	// 标签内部ID，无需关注此字段
	TagID int `json:"tagId"`

	// 标签值
	TagValue string `json:"tagValue"`

	// 标签键
	TagKey string `json:"tagKey"`

	// 资源所在region
	Region string `json:"region"`

	// 关联类型 associationType
	AssociationType int `json:"associationType"`
}

type CreateAndAssignArgs struct {
	// 资源及对应标签	必需
	Resources []ResourceWithTag `json:"resources"`
}

type ResourceWithTag struct {
	// 服务类型 示例 BLB 必需
	ServiceType string `json:"serviceType"`

	// 资源ID 示例 lb-db523ce9 必需
	ResourceID string `json:"resourceId"`

	// 关联类型，填写floating即可 必需
	AssociationType string `json:"associationType"`

	// 资源所在region，全局服务可填global 必需
	Region string `json:"region"`

	// 资源的全量标签信息 必需
	Tags []Tag `json:"tags"`
}
