/**
 * @Author: pansiyuan02
 * @Description:
 * @File:  tag
 * @Version: 1.0.0
 * @Date: 2021/1/8 1:39 下午
 */
package tag

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"strconv"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
)

func (c *Client) SetDebug(debug bool) {
	c.Client.SetDebug(debug)
}

// CreateTag 本接口用于创建一个或多个标签
//
//	为具体产品资源绑定标签请参考各产品API
//	若待创建的标签已经存在，则跳过该标签的创建操作
//	标签的键和值需要符合规范，具体规则见Tag，且标签的键不能为空
func (c *Client) CreateTag(ctx context.Context, args *CreateTagsArgs, option *bce.SignOption) error {
	if args == nil {
		return errors.New("args is nil")
	}

	params := map[string]string{
		"create":      "",
		"clientToken": c.<PERSON>oken(),
	}

	putContent, err := json.Marshal(args)
	if err != nil {
		return err
	}

	req, err := bce.NewRequest("PUT", c.GetURL("tag", params), bytes.NewBuffer(putContent))
	if err != nil {
		return err
	}

	_, err = c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}
	return nil
}

// DeleteTags 本接口用于删除一个或多个标签
//
//	删除标签时若有资源绑定待删除的标签，则会自动解绑
//	若待删除的标签不存在，则跳过该标签的删除操作
//	不能删除默认标签。如果待删除的标签的键为"默认标签"值为空字符串，则会跳过对默认标签的删除
func (c *Client) DeleteTags(ctx context.Context, tags []Tag, option *bce.SignOption) error {
	if len(tags) == 0 {
		return nil
	}

	toBeDeleted := map[string]any{
		"tags": tags,
	}

	params := map[string]string{
		"delete":      "",
		"clientToken": c.GenerateClientToken(),
	}

	putContent, err := json.Marshal(toBeDeleted)
	if err != nil {
		return err
	}

	req, err := bce.NewRequest("PUT", c.GetURL("tag", params), bytes.NewBuffer(putContent))
	if err != nil {
		return err
	}

	_, err = c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}
	return nil
}

// ListTags 本接口用于列出所有符合条件的标签键值对
//
//	标签的键和标签的值可以作为筛选条件
//	本接口返回所有符合条件的结果，不分页
func (c *Client) ListTags(ctx context.Context, args ListTagsParams, option *bce.SignOption) (*ListTagsResult, error) {
	params := map[string]string{
		"tagKey":      args.TagKey,
		"clientToken": c.GenerateClientToken(),
	}

	if args.TagValue != nil {
		params["tagValue"] = *args.TagValue
	}

	req, err := bce.NewRequest("GET", c.GetURL("tag", params), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var r ListTagsResult
	err = json.Unmarshal(bodyContent, &r)
	if err != nil {
		return nil, err
	}

	return &r, nil
}

// ListBoundResources 本接口用于列出所有符合条件的标签下绑定的资源
//
//	若符合条件的标签下无资源，则该标签不出现的返回结果中
//	本接口返回绑定的资源id，资源详情可通过各产品API查询
func (c *Client) ListBoundResources(ctx context.Context, args *ListBoundResourcesParams, option *bce.SignOption) (*ListBoundResourcesResult, error) {
	if args == nil {
		return nil, errors.New("nil ListBoundResourcesParams")
	}

	params := map[string]string{
		"tagKey":       args.TagKey,
		"region":       c.Region,
		"resourceType": args.ResourceType,
		"clientToken":  c.GenerateClientToken(),
	}

	if args.TagValue != nil {
		params["tagValue"] = *args.TagValue
	}
	if args.Region != nil {
		params["region"] = *args.Region
	}

	req, err := bce.NewRequest("GET", c.GetURL("tag/tagResources", params), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var r ListBoundResourcesResult
	err = json.Unmarshal(bodyContent, &r)
	if err != nil {
		return nil, err
	}

	return &r, nil
}

// BindResourcesInBatch 本接口用于批量绑定资源
//
//	一次只能操作一个标签
func (c *Client) BindResourcesInBatch(ctx context.Context, args *BindResourceInBatchArgs, option *bce.SignOption) error {
	if args == nil {
		return errors.New("args is nil")
	}

	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}

	postContent, err := json.Marshal(args)
	if err != nil {
		return err
	}

	req, err := bce.NewRequest("POST", c.GetURL("resource", params), bytes.NewBuffer(postContent))
	if err != nil {
		return err
	}

	_, err = c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}
	return nil
}

// UnbindResourcesInBatch 本接口用于批量解绑资源
//
//	一次只能操作一个标签
func (c *Client) UnbindResourcesInBatch(ctx context.Context, args *UnbindResourceInBatchArgs, option *bce.SignOption) error {
	if args == nil {
		return errors.New("args is nil")
	}
	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}

	putContent, err := json.Marshal(args)
	if err != nil {
		return err
	}

	req, err := bce.NewRequest("PUT", c.GetURL("resource", params), bytes.NewBuffer(putContent))
	if err != nil {
		return err
	}

	_, err = c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}
	return nil
}

// DeleteTagAssociations 本接口用于删除资源与标签的绑定关系
//
//	每次只能解绑一个资源
func (c *Client) DeleteTagAssociations(ctx context.Context, args *DeleteTagAssociationsArgs, option *bce.SignOption) error {
	if args == nil {
		return errors.New("args is nil")
	}
	params := map[string]string{
		"deleteTagAssociation": "",
		"clientToken":          c.GenerateClientToken(),
	}

	putContent, err := json.Marshal(args)
	if err != nil {
		return err
	}

	req, err := bce.NewRequest("PUT", c.GetURL("tag", params), bytes.NewBuffer(putContent))
	if err != nil {
		return err
	}

	_, err = c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}
	return nil
}

// QueryUserTagsInfoList 本接口标签与资源关联关系的双向查询
//
//	可根据标签键，标签值，资源ID等多种条件进行查询
func (c *Client) QueryUserTagsInfoList(ctx context.Context, strongAssociation bool, args *QueryUserTagListArgs, option *bce.SignOption) (*AssociationsResult, error) {
	if args == nil {
		return nil, errors.New("args is nil")
	}
	params := map[string]string{
		"queryFullList":     "",
		"strongAssociation": strconv.FormatBool(strongAssociation),
		"clientToken":       c.GenerateClientToken(),
	}

	postContent, err := json.Marshal(args)
	if err != nil {
		return nil, err
	}

	req, err := bce.NewRequest("POST", c.GetURL("tag", params), bytes.NewBuffer(postContent))
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}
	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var result AssociationsResult
	err = json.Unmarshal(bodyContent, &result)
	if err != nil {
		return nil, err
	}

	return &result, nil
}

// UpdateResourceAssociations 本接口用于更新资源与标签的绑定关系
//
//	传参是需传入资源的全量标签信息，如原有3个标签，现删除1个标签的情况下新增2个标签，则应传入全量4个标签
func (c *Client) UpdateResourceAssociations(ctx context.Context, args *CreateAndAssignArgs, option *bce.SignOption) error {
	if args == nil {
		return errors.New("args is nil")
	}
	params := map[string]string{
		"createAndAssign": "",
		"clientToken":     c.GenerateClientToken(),
	}

	postContent, err := json.Marshal(args)
	if err != nil {
		return err
	}

	req, err := bce.NewRequest("POST", c.GetURL("tag", params), bytes.NewBuffer(postContent))
	if err != nil {
		return err
	}

	_, err = c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}
	return nil
}
