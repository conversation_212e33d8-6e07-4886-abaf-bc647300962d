package logicalbcc

import (
	"context"
	"time"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
)

//go:generate mockgen -destination=./mock/mock_client.go -package=mock icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/logicalbcc Interface

// Interface for Logical bcc ,including compute \ nova ... apis
type Interface interface {
	SetDebug(debug bool)

	CreateServers(ctx context.Context, args *CreateServerArgs, clientToken string, option *bce.SignOption) (*CreateServersResponse, error)
	GetServer(ctx context.Context, serverUUID string, option *bce.SignOption) (*ServerAbstract, error)
	GetServers(ctx context.Context, option *bce.SignOption) ([]ServerAbstract, error)
	DeleteServers(ctx context.Context, serverUUIDs []string, option *bce.SignOption) error
	GetOrder(ctx context.Context, orderID string, option *bce.SignOption) (*Order, error)
	DeleteOrder(ctx context.Context, orderID string, option *bce.SignOption) error
}

// CreateServerArgs 创建虚机请求参数
type CreateServerArgs struct {
	OrderID             string         `json:"transaction_id"`
	ServerList          []ServerConfig `json:"servers"`
	Action              ServerAction   `json:"action"`
	Source              ServerSource   `json:"source"`
	AvailableZone       string         `json:"availability_zone"`
	Callback            string         `json:"callback"`
	NetworkType         string         `json:"network_type"`
	NoahNode            string         `json:"noah_node"`
	HypervisorDedicated string         `json:"hypervisor-dedicated"`
}

// ServerConfig 创建虚机参数
type ServerConfig struct {
	Name             string            `json:"name"`
	AdminPass        string            `json:"adminPass"`
	ImageUUID        string            `json:"imageRef"`
	SubnetUUID       string            `json:"subnet_id"`
	CreateFloatingIP bool              `json:"create_floatingip"`
	Count            int               `json:"count"`
	ServerFlavor     ServerFlavor      `json:"flavor"`
	ServerCds        []ServerCds       `json:"ebs"`
	Metadata         map[string]string `json:"metadata"`
	SecurityGroups   []SecurityGroup   `json:"security_groups"`
	OsSchedulerHints OsSchedulerHints  `json:"os:scheduler_hints"`
}

// ServerFlavor 虚机硬件配置
type ServerFlavor struct {
	CPU       int `json:"cpu"`
	Memory    int `json:"memory"`
	Disk      int `json:"disk"`
	Ephemeral int `json:"ephemeral"`
}

// ServerCds 虚机 CDS 参数
type ServerCds struct {
	ID          string `json:"id"`
	Name        string `json:"name"`
	Count       string `json:"count"`
	Size        string `json:"size"`
	Description string `json:"description"`
	SnapshotID  string `json:"snapshot_id"`
}

// SecurityGroup 安全组信息
type SecurityGroup struct {
	Name string `json:"name"`
	UUID string `json:"uuid"`
}

// OsSchedulerHints Scheduler 调度
type OsSchedulerHints struct {
	Disks         []Disk       `json:"disks"`
	SchedulerTags []string     `json:"scheduler_tags"`
	PciRequests   []PciRequest `json:"pci_requests"`
	DiffHost      bool         `json:"different_host_flag"`
	RootOnCds     bool         `json:"root_on_cds_v2"`
}

// Disk 磁盘信息
type Disk struct {
	Type       string `json:"type"`
	CapacityGb int    `json:"capacity_gb"`
	Share      int    `json:"share"`
}

// PciRequest PCI 请求参数
type PciRequest struct {
	Count    string    `json:"count"`
	SpecList []PciSpec `json:"spec"`
}

// PciSpec PCI 参数
type PciSpec struct {
	VendorID  string `json:"vendor_id"`
	ProductID string `json:"product_id"`
}

// CreateServersResponse 创建虚机返回
type CreateServersResponse struct {
	OrderID string `json:"transaction_id"`
}

// Order 订单详情
type Order struct {
	OrderID     string      `json:"transaction_id"`
	OrderStatus OrderStatus `json:"status"`
	OrderAction OrderAction `json:"action"`
	Servers     []Server    `json:"servers"`
}

// Server 虚机详细信息
type Server struct {
	InstanceUUID string       `json:"id"`
	Name         string       `json:"name"`
	Status       ServerStatus `json:"status"`
	AdminPass    string       `json:"adminPass"`
	FixIP        string       `json:"fixip"`
	FloatingIP   string       `json:"floatingip"`
	UserID       string       `json:"user_id"`
	TenantID     string       `json:"tenant_id"`
	ImageUUID    string       `json:"image"`
	Flavor       string       `json:"flavor"`
	SysDisk      int          `json:"sys_disk"`
	CreatedTime  time.Time    `json:"created_time"`
}

// GetServerResponse 查询虚机返回
type GetServerResponse struct {
	ServerAbstract ServerAbstract `json:"server"`
}

// ServerAbstract 虚机摘要信息
type ServerAbstract struct {
	UUID        string       `json:"id"`
	Name        string       `json:"name"`
	Status      ServerStatus `json:"status"`
	Flavor      Flavor       `json:"flavor"`
	TenantID    string       `json:"tenant_id"`
	UserID      string       `json:"user_id"`
	CreatedTime string       `json:"created"`
	UpdatedTime string       `json:"updated"`
	Addresses   Addresses    `json:"addresses"`
}

type Flavor struct {
	CPU    int `json:"cpu"`
	Memory int `json:"memory"`
}

// Addresses 包含 fixed address 和 floating address
type Addresses struct {
	FixedAddr    Address `json:"fixed"`
	FloatingAddr Address `json:"floating"`
}

// Address 包含 IPV4 和 IPV6 address
type Address struct {
	AddrIPV4 string `json:"addr"`
	AddrIPV6 string `json:"addr_v6"`
}

// GetServersResponse 查询虚机（多台）返回
type GetServersResponse struct {
	Servers []ServerAbstract `json:"servers"`
}

// DeleteServerRequest 删除虚机请求
type DeleteServerRequest struct {
	Action      string   `json:"action"`
	ServerUUIDs []string `json:"servers"`
}

type OrderRequest struct {
	OrderType   OrderType   `json:"type"`
	OrderAction OrderAction `json:"action"`
	ErrMsg      string      `json:"errmsg"`
}

type OrderType string

const (
	OrderTypeInstance OrderType = "instance"
)

// ServerStatus 虚机状态
type ServerStatus string

const (
	// ServerStatusActive 虚机运行中
	ServerStatusActive ServerStatus = "ACTIVE"

	// ServerStatusBuild 虚机创建中
	ServerStatusBuild ServerStatus = "BUILD"

	// ServerStatusRebuild 虚机重装系统中
	ServerStatusRebuild ServerStatus = "REBUILD"

	// ServerStatusDeleted 虚机已删除
	ServerStatusDeleted ServerStatus = "DELETED"

	// ServerStatusSnapshot 创建快照
	ServerStatusSnapshot ServerStatus = "SNAPSHOT"

	// ServerStatusDeleteSnapshot 删除快照
	ServerStatusDeleteSnapshot ServerStatus = "DELETE_SNAPSHOT"

	// ServerStatusVolumeResize VOLUME_RESIZE
	ServerStatusVolumeResize ServerStatus = "VOLUME_RESIZE"

	// ServerStatusError 虚机异常
	ServerStatusError ServerStatus = "ERROR"

	// ServerStatusExpired 虚机欠费释放
	ServerStatusExpired ServerStatus = "EXPIRED"

	// ServerStatusReboot 虚机重启
	ServerStatusReboot ServerStatus = "REBOOT"

	// ServerStatusRecharge 虚机续费
	ServerStatusRecharge ServerStatus = "RECHARGE"

	// ServerStatusShutoff 虚机关机
	ServerStatusShutoff ServerStatus = "SHUTOFF"

	// ServerStatusStopped 虚机关机
	ServerStatusStopped ServerStatus = "STOPPED"

	// ServerStatusUnknown 虚机状态未知
	ServerStatusUnknown ServerStatus = "UNKNOWN"
)

// ServerSource 虚机创建来源
type ServerSource string

const (
	// ServerSourceCCE 虚机创建来源于 CCE
	ServerSourceCCE ServerSource = "cce"

	// ServerSourceBCC 虚机创建来源于 BCC
	ServerSourceBCC ServerSource = "bcc"
)

// ServerAction 虚机操作类型
type ServerAction string

const (
	// ServerActionCreate 创建虚机
	ServerActionCreate ServerAction = "create"

	// ServerActionDelete 删除虚机
	ServerActionDelete ServerAction = "delete"
)

// OrderStatus 订单状态
type OrderStatus string

const (
	// OrderStatusSuccess 订单创建成功
	OrderStatusSuccess OrderStatus = "succ"

	// OrderStatusOperating 订单创建中
	OrderStatusOperating OrderStatus = "operating"

	// OrderStatusDeleted 订单删除
	OrderStatusDeleted OrderStatus = "deleted"

	// OrderStatusError 订单创建失败
	OrderStatusError OrderStatus = "error"

	// OrderStatusUnknown 订单未知，也可以认为是一种异常状态，认为订单报错
	OrderStatusUnknown OrderStatus = "unknown"
)

// OrderAction 订单操作类型
type OrderAction string

const (
	// OrderActionCreate 创建虚机
	OrderActionCreate OrderAction = "CreateServers"
	OrderActionDelete OrderAction = "delete"
)

const (
	// ServiceName 获取 Endpoint 时提供的服务名
	ServiceName = "logical"
)
