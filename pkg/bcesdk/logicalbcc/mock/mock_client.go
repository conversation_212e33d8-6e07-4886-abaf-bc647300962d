// Code generated by MockGen. DO NOT EDIT.
// Source: icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/logicalbcc (interfaces: Interface)

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	gomock "github.com/golang/mock/gomock"
	bce "icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
	logicalbcc "icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/logicalbcc"
	reflect "reflect"
)

// MockInterface is a mock of Interface interface
type MockInterface struct {
	ctrl     *gomock.Controller
	recorder *MockInterfaceMockRecorder
}

// MockInterfaceMockRecorder is the mock recorder for MockInterface
type MockInterfaceMockRecorder struct {
	mock *MockInterface
}

// NewMockInterface creates a new mock instance
func NewMockInterface(ctrl *gomock.Controller) *MockInterface {
	mock := &MockInterface{ctrl: ctrl}
	mock.recorder = &MockInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use
func (m *MockInterface) EXPECT() *MockInterfaceMockRecorder {
	return m.recorder
}

// CreateServers mocks base method
func (m *MockInterface) CreateServers(arg0 context.Context, arg1 *logicalbcc.CreateServerArgs, arg2 string, arg3 *bce.SignOption) (*logicalbcc.CreateServersResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateServers", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*logicalbcc.CreateServersResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateServers indicates an expected call of CreateServers
func (mr *MockInterfaceMockRecorder) CreateServers(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateServers", reflect.TypeOf((*MockInterface)(nil).CreateServers), arg0, arg1, arg2, arg3)
}

// DeleteOrder mocks base method
func (m *MockInterface) DeleteOrder(arg0 context.Context, arg1 string, arg2 *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteOrder", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteOrder indicates an expected call of DeleteOrder
func (mr *MockInterfaceMockRecorder) DeleteOrder(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteOrder", reflect.TypeOf((*MockInterface)(nil).DeleteOrder), arg0, arg1, arg2)
}

// DeleteServers mocks base method
func (m *MockInterface) DeleteServers(arg0 context.Context, arg1 []string, arg2 *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteServers", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteServers indicates an expected call of DeleteServers
func (mr *MockInterfaceMockRecorder) DeleteServers(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteServers", reflect.TypeOf((*MockInterface)(nil).DeleteServers), arg0, arg1, arg2)
}

// GetOrder mocks base method
func (m *MockInterface) GetOrder(arg0 context.Context, arg1 string, arg2 *bce.SignOption) (*logicalbcc.Order, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOrder", arg0, arg1, arg2)
	ret0, _ := ret[0].(*logicalbcc.Order)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOrder indicates an expected call of GetOrder
func (mr *MockInterfaceMockRecorder) GetOrder(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOrder", reflect.TypeOf((*MockInterface)(nil).GetOrder), arg0, arg1, arg2)
}

// GetServer mocks base method
func (m *MockInterface) GetServer(arg0 context.Context, arg1 string, arg2 *bce.SignOption) (*logicalbcc.ServerAbstract, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetServer", arg0, arg1, arg2)
	ret0, _ := ret[0].(*logicalbcc.ServerAbstract)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetServer indicates an expected call of GetServer
func (mr *MockInterfaceMockRecorder) GetServer(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetServer", reflect.TypeOf((*MockInterface)(nil).GetServer), arg0, arg1, arg2)
}

// GetServers mocks base method
func (m *MockInterface) GetServers(arg0 context.Context, arg1 *bce.SignOption) ([]logicalbcc.ServerAbstract, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetServers", arg0, arg1)
	ret0, _ := ret[0].([]logicalbcc.ServerAbstract)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetServers indicates an expected call of GetServers
func (mr *MockInterfaceMockRecorder) GetServers(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetServers", reflect.TypeOf((*MockInterface)(nil).GetServers), arg0, arg1)
}

// SetDebug mocks base method
func (m *MockInterface) SetDebug(arg0 bool) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SetDebug", arg0)
}

// SetDebug indicates an expected call of SetDebug
func (mr *MockInterfaceMockRecorder) SetDebug(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetDebug", reflect.TypeOf((*MockInterface)(nil).SetDebug), arg0)
}
