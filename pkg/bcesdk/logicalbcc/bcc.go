package logicalbcc

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
)

// CreateServers - 创建虚机
//
// PARAMS:
//   - ctx: The context to trace request
//   - CreateServerArgs: 创建虚机请求
//
// RETURNS:
//
//	*CreateServersResponse: 订单号以及虚机ID列表
//	error: nil if succeed, error if fail
func (c *Client) CreateServers(ctx context.Context, args *CreateServerArgs, clientToken string, option *bce.SignOption) (*CreateServersResponse, error) {
	params := map[string]string{
		"clientToken": clientToken,
	}

	postContent, err := json.Marshal(args)
	if err != nil {
		return nil, err
	}

	req, err := bce.NewRequest("POST", c.GetURL("servers", params), bytes.NewBuffer(postContent))
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var createServersResp *CreateServersResponse
	err = json.Unmarshal(bodyContent, &createServersResp)
	if err != nil {
		return nil, err
	}

	return createServersResp, nil
}

// GetServer - 查询虚机信息
//
// PARAMS:
//   - ctx: The context to trace request
//   - serverUUID: 待查询虚机UUID
//
// RETURNS:
//
//	*ServerAbstract: 虚机摘要信息
//	error: nil if succeed, error if fail
func (c *Client) GetServer(ctx context.Context, serverUUID string, option *bce.SignOption) (*ServerAbstract, error) {
	params := map[string]string{}

	url := fmt.Sprintf("servers/%s", serverUUID)
	req, err := bce.NewRequest("GET", c.GetURL(url, params), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var serverResp *GetServerResponse
	err = json.Unmarshal(bodyContent, &serverResp)
	if err != nil {
		return nil, err
	}

	return &serverResp.ServerAbstract, nil
}

// GetServers - 查询多台虚机信息
//
// PARAMS:
//   - ctx: The context to trace request
//
// RETURNS:
//
//	[]ServerAbstract: 虚机摘要信息列表
//	error: nil if succeed, error if fail
func (c *Client) GetServers(ctx context.Context, option *bce.SignOption) ([]ServerAbstract, error) {
	params := map[string]string{
		"no-device-mapping-info": "1",
	}

	req, err := bce.NewRequest("GET", c.GetURL("servers/detail", params), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var getServersResponse *GetServersResponse
	err = json.Unmarshal(bodyContent, &getServersResponse)
	if err != nil {
		return nil, err
	}

	return getServersResponse.Servers, nil
}

// DeleteServers - 批量删除虚机
//
// PARAMS:
//   - ctx: The context to trace request
//   - serverUUIDs: 待删除虚机UUID列表
//
// RETURNS:
//
//	error: nil if succeed, error if fail
func (c *Client) DeleteServers(ctx context.Context, serverUUIDs []string, option *bce.SignOption) error {
	params := map[string]string{}

	deleteServerReq := &DeleteServerRequest{
		Action:      string(ServerActionDelete),
		ServerUUIDs: serverUUIDs,
	}
	postContent, err := json.Marshal(deleteServerReq)
	if err != nil {
		return err
	}

	req, err := bce.NewRequest("POST", c.GetURL("servers", params), bytes.NewBuffer(postContent))
	if err != nil {
		return err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}

	_, err = resp.GetBodyContent()
	return err
}

// GetOrder - 查询订单详情
//
// PARAMS:
//   - ctx: The context to trace request
//   - orderID: 订单ID
//
// RETURNS:
//
//	*Order: 订单详情
//	error: nil if succeed, error if fail
func (c *Client) GetOrder(ctx context.Context, orderID string, option *bce.SignOption) (*Order, error) {
	params := map[string]string{}

	url := fmt.Sprintf("transactions/%s", orderID)
	req, err := bce.NewRequest("GET", c.GetURL(url, params), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var transactionDetailResp *Order
	err = json.Unmarshal(bodyContent, &transactionDetailResp)
	if err != nil {
		return nil, err
	}

	return transactionDetailResp, nil
}

// DeleteOrder - 回滚订单
//
// PARAMS:
//   - ctx: The context to trace request
//   - orderID: 订单ID
//
// RETURNS:
//
//	error: nil if succeed, error if fail
func (c *Client) DeleteOrder(ctx context.Context, orderID string, option *bce.SignOption) error {
	params := map[string]string{}

	orderReq := OrderRequest{
		OrderType:   OrderTypeInstance,
		OrderAction: OrderActionDelete,
		ErrMsg:      "rollback transaction",
	}

	postContent, err := json.Marshal(orderReq)
	if err != nil {
		return err
	}

	url := fmt.Sprintf("transactions/%s", orderID)
	req, err := bce.NewRequest("POST", c.GetURL(url, params), bytes.NewBuffer(postContent))
	if err != nil {
		return err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}

	_, err = resp.GetBodyContent()
	return err
}
