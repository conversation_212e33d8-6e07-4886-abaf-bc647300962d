// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2020/05/18 14:36:00, by <EMAIL>, create
*/
/*
DESCRIPTION
CCE Web 服务统一 error handler 方法
*/

package errorcode

import (
	"context"
	"errors"
	"fmt"

	"github.com/astaxie/beego"
	uuid "github.com/satori/go.uuid"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
)

const (
	// RequestID 在 ctx 中不存在时, 后缀用于标识该 requestID 不起作用
	requestIDSuffix = "-cce"
)

// Response - 定义 cce-stack web 服务返回给前端结构体
// 参考: http://wiki.baidu.com/x/1i2sN
type Response struct {
	Code      string `json:"code,omitempty"`
	Message   string `json:"message,omitempty"`
	RequestID string `json:"requestId,omitempty"`
}

// ReconcileResponse controller reconcile 流程中暴露出去的信息
type ReconcileResponse struct {
	Code       string `json:"code,omitempty"`
	Message    string `json:"message,omitempty"`
	TraceID    string `json:"traceID,omitempty"` // message里可能有底层返回的requestID，这里用TraceID作区分
	Suggestion string `json:"suggestion,omitempty"`
}

// Level - 错误等级
type Level string

const (
	// LevelInfo - 用于表单错误提示
	LevelInfo Level = "cce.info"

	// LevelByUser - 用户可根据错误信息, 自行处理
	LevelByUser Level = "cce.warning"

	// LevelByAdmin - 用户需要提交工单进行处理
	LevelByAdmin Level = "cce.error"
)

// Interface - 定义 cce-stack 错误处理统一方法
type Interface interface {
	ErrorHandler(ctx context.Context, code *ErrorCode, level Level, format string, a ...any)
	CustomAbortJson(status int, body any)
}

// handler - beego 实现 errorcode.Interface
type handler struct {
	beego.Controller
}

// NewErrorHandler - 初始化 handler
func NewErrorHandler(ctx context.Context, controller beego.Controller) Interface {
	return &handler{
		Controller: controller,
	}
}

func (c *handler) ErrorHandler(ctx context.Context, code *ErrorCode, level Level, format string, a ...any) {
	body := c.response(ctx, code, level, format, a...)

	c.CustomAbortJson(code.StatusCode, body)
}

// response - 返回给前端 response
func (c *handler) response(ctx context.Context, code *ErrorCode, level Level, format string, a ...any) Response {
	// 默认 internalServerError
	if code == nil {
		code = NewInternalServerError()
	}

	// 获取 Message
	message := code.Message
	if format != "" {
		message = fmt.Sprintf(format, a...)
	}

	// 返回给前端结构体
	return Response{
		Code:      fmt.Sprintf("%s.%s", level, code.Code),
		Message:   message,
		RequestID: requestID(ctx),
	}
}

// controller step, 返回集群事件的报错信息，成功返回response，true
func ReconcileStep(ctx context.Context, errorCode error) (ReconcileResponse, bool) {
	matched := true
	var code *ErrorCode
	if !errors.As(errorCode, &code) {
		matched = false
		// 默认 internalServerError
		code = NewInternalServerError()
		code.Suggestion = SuggestCCETicket
		if errorCode != nil {
			code.Message = errorCode.Error()
		}
	}

	return ReconcileResponse{
		Code:       code.Code,
		Message:    code.Message,
		TraceID:    logger.GetRequestID(ctx),
		Suggestion: code.Suggestion,
	}, matched
}

// requestID - 从 context 获取 requestID, 兼容 monitor-service
func requestID(ctx context.Context) string {
	var requestID string
	if r := ctx.Value(logger.RequestID); r != nil {
		requestID = r.(string)
	} else if r := ctx.Value(logger.RequestID); r != nil {
		requestID = r.(string)
	} else {
		// 标识无用
		requestID = uuid.NewV4().String()
		requestID = requestID[0:len(requestID)-len(requestIDSuffix)-1] + requestIDSuffix
	}

	return requestID
}

func (c *handler) CustomAbortJson(status int, body any) {
	c.Controller.Ctx.Output.Status = status
	c.Controller.Data["json"] = body
	c.Controller.ServeJSON()
	panic(beego.ErrAbort)
}
