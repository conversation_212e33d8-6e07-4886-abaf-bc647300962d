package errorcode

import (
	"errors"
	"net/http"
	"testing"

	"github.com/google/go-cmp/cmp"
)

func TestGetCode(t *testing.T) {
	var err error
	var code *ErrorCode

	// ErrorCode as error
	err = NewInvalidParam("invalid")
	code = GetCode(err)
	if !cmp.Equal(code, err) {
		t.Fatalf("GetCode() = %+v, want = %+v, diff is %s ", code, err, cmp.Diff(code, err))
	}

	// AccessDenied ErrorCode generated in childrenAccessDenied()
	_, err = childrenAccessDenied()
	want := &ErrorCode{
		StatusCode: http.StatusForbidden,
		Code:       "AccessDenied",
		Message:    "childrenAccessDenied",
	}
	if err == nil {
		t.Errorf("childrenAccessDenied() err = %v, wantErr true", err)
		return
	}

	code = GetCode(err)
	if !cmp.Equal(code, want) {
		t.<PERSON><PERSON>("childrenAccessDenied() code = %v, want %v, diff is %s", code, want, cmp.Diff(code, want))
		return
	}

	// no ErrorCode generated in defaultChildren()
	_, err = defaultChildren()
	want = &ErrorCode{
		StatusCode: http.StatusInternalServerError,
		Code:       "InternalServerError",
		Message:    "InternalServerError",
	}
	if err == nil {
		t.Errorf("defaultChildren() err = %v, wantErr true", err)
		return
	}
	code = GetCode(err)
	if !cmp.Equal(code, want) {
		t.Errorf("defaultChildren() code = %v, want %v, diff is %s", code, want, cmp.Diff(code, want))
		return
	}

	// InvalidParam ErrorCode is delivered in multi-level func call
	_, err = recursiveChildrenInvalidParam(3)
	if err == nil {
		t.Errorf("recursiveChildrenInvalidParam() err = %v, wantErr true", err)
		return
	}
	want = &ErrorCode{
		StatusCode: http.StatusBadRequest,
		Code:       "InvalidParam",
		Message:    "depth is 0",
	}
	code = GetCode(err)
	if !cmp.Equal(code, want) {
		t.Errorf("recursiveChildrenInvalidParam() code = %v, want %v, diff is %s", code, want, cmp.Diff(code, want))
		return
	}
	wantErrStr := "depth 3 err: depth 2 err: depth 1 err: some error occurs"
	if err.Error() != wantErrStr {
		t.Errorf("recursiveChildrenInvalidParam() err = %v, want %v, diff is %s",
			err.Error(), wantErrStr, cmp.Diff(err.Error(), wantErrStr))
		return
	}
}

func childrenAccessDenied() (any, error) {
	err := errors.New("some error occurs")
	if err != nil {
		return nil, NewError(NewAccessDenied("childrenAccessDenied"), err)
	}
	return nil, nil
}

func defaultChildren() (any, error) {
	err := errors.New("some error occurs")
	if err != nil {
		return nil, err
	}
	return nil, nil
}

func recursiveChildrenInvalidParam(depth int) (any, error) {
	if depth == 0 {
		return nil, NewError(NewInvalidParam("depth is 0"), errors.New("some error occurs"))
	}
	_, err := recursiveChildrenInvalidParam(depth - 1)
	if err != nil {
		// ErrorCode is conveyed from err to returned err
		return nil, Errorf("depth %d err: %v", depth, err)
	}
	return nil, nil
}

func TestNewCode(t *testing.T) {
	type args struct {
		statusCode int
		code       string
	}
	tests := []struct {
		name string
		args args
		want *ErrorCode
	}{
		// All test cases.
		{
			name: "normal case",
			args: args{
				statusCode: http.StatusBadRequest,
				code:       "UnknownRequest",
			},
			want: &ErrorCode{
				StatusCode: http.StatusBadRequest,
				Code:       "UnknownRequest",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewCode(tt.args.statusCode, tt.args.code); !cmp.Equal(got, tt.want) {
				t.Errorf("NewCode() = %v, want %v, diff is %s", got, tt.want, cmp.Diff(got, tt.want))
			}
		})
	}
}

func TestErrorCode_WithMsg(t *testing.T) {
	type args struct {
		msg []string
	}
	tests := []struct {
		name string
		code *ErrorCode
		args args
		want *ErrorCode
	}{
		// All test cases.
		{
			name: "msg not set case",
			code: ErrorCodes[1].DeepCopy(),
			want: func() *ErrorCode {
				simpleCode := ErrorCodes[1].DeepCopy()
				simpleCode.Message = simpleCode.Code
				return simpleCode
			}(),
		},
		{
			name: "non-empty msg case",
			code: ErrorCodes[1].DeepCopy(),
			args: args{
				msg: []string{"i am error message"},
			},
			want: func() *ErrorCode {
				codeWithErrMsg := ErrorCodes[1].DeepCopy()
				codeWithErrMsg.Message = "i am error message"
				return codeWithErrMsg
			}(),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ec := tt.code
			if got := ec.WithMsg(tt.args.msg...); !cmp.Equal(got, tt.want) {
				t.Errorf("ErrorCode.WithMsg() = %v, want %v, diff is %s", got, tt.want, cmp.Diff(got, tt.want))
			}
		})
	}
}
