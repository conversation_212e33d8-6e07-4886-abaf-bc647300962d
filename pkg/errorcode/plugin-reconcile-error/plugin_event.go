package plugin_reconcile_error

import (
	"context"
	"fmt"

	uuid "github.com/satori/go.uuid"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/errorcode"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
)

type PluginReconcileEvent struct {
	errorcode.ReconcileResponse

	Region    string
	ClusterID string

	Kind      string
	Namespace string
	Name      string

	Phase string
}

func (event *PluginReconcileEvent) getEventStr() string {
	template := "[Region/Cluster] %s/%s\n" +
		"[Kind/Namespace/Name] %s/%s/%s\n" +
		"[Phase/ErrorCode/TraceId] %s/%s/%s\n" +
		"[Message] %s\n" +
		"[Suggestion] %s\n"
	return fmt.Sprintf(template,
		event.Region, event.ClusterID,
		event.Kind, event.Namespace, event.Name,
		event.Phase, event.Code, event.TraceID,
		event.Message,
		event.Suggestion)
}

type ControllerEventGenerator struct {
	region    string
	clusterID string
	kind      string
}

func NewControllerEventGenerator(region string, clusterID string, kind string) (*ControllerEventGenerator, error) {
	generator := ControllerEventGenerator{
		region:    region,
		clusterID: clusterID,
		kind:      kind,
	}
	return &generator, nil
}

func (generator *ControllerEventGenerator) GenerateServiceEventString(ctx context.Context, ns string, name string, err error, getErrorSuggestion func(error) string) string {
	var phase, code, message string

	if err != nil {
		pluginErr, ok := err.(PluginReconcileError)
		if ok {
			phase = pluginErr.Phase
			code = pluginErr.Code
			message = pluginErr.Message
		} else {
			message = err.Error()
		}
	}

	event := PluginReconcileEvent{
		Region:    generator.region,
		ClusterID: generator.clusterID,

		Kind:      generator.kind,
		Namespace: ns,
		Name:      name,

		Phase: phase,

		ReconcileResponse: errorcode.ReconcileResponse{
			Code:       code,
			Message:    message,
			TraceID:    requestID(ctx),
			Suggestion: getErrorSuggestion(err),
		},
	}

	return event.getEventStr()
}

const (
	// RequestID 在 ctx 中不存在时, 后缀用于标识该 requestID 不起作用
	requestIDSuffix = "-cce"
)

// requestID - 从 context 获取 requestID, 兼容 monitor-service
func requestID(ctx context.Context) string {
	var requestID string
	if r := ctx.Value(logger.RequestID); r != nil {
		requestID = r.(string)
	} else if r := ctx.Value(logger.RequestID); r != nil {
		requestID = r.(string)
	} else {
		// 标识无用
		requestID = uuid.NewV4().String()
		requestID = requestID[0:len(requestID)-len(requestIDSuffix)-1] + requestIDSuffix
	}
	return requestID
}
