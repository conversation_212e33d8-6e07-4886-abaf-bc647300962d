package plugin_reconcile_error

import (
	"fmt"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/errorcode"
)

type PluginReconcileError struct {
	errorcode.ErrorCode        // 错误分类(ErrorCode) 和错误信息 (Message)
	Phase               string // 当前 Error 发生在Controller处理资源的哪个阶段 该阶段内容由各个插件自行定义
	Err                 error  // 报错的原始Error
}

func (pluginReconcileError PluginReconcileError) Error() string {
	return pluginReconcileError.Err.Error()
}

// NewPluginReconcileErrorByType 根据用户输入的 Phase,ErrorType,Message 自动生成 PluginReconcileError;
// 主要用于各个插件自行定义的 Error 类型
func NewPluginReconcileErrorByType(phase string, errorType string, msg string) error {
	controllerError := PluginReconcileError{
		ErrorCode: errorcode.ErrorCode{
			Code:    errorType,
			Message: msg,
		},
		Phase: phase,
		Err:   fmt.Errorf(msg),
	}

	return controllerError
}

// NewPluginReconcileErrorByError 根据已有的 Error 生成 PluginReconcileError,通过error字段中的关键词来识别error
// 可以用于识别 IaaS 接口返回的各种错误
// getErrorType 函数参数由插件自行实现,由插件实现根据错误内容返回插件定义的错误类型
func NewPluginReconcileErrorByError(phase string, err error, getErrorType func(error) string) error {
	if err == nil {
		return err
	}

	pluginErr, ok := err.(PluginReconcileError)
	if ok {
		return pluginErr
	}

	controllerError := PluginReconcileError{
		ErrorCode: errorcode.ErrorCode{
			Code:    getErrorType(err),
			Message: err.Error(),
		},
		Phase: phase,
		Err:   err,
	}
	return controllerError
}
