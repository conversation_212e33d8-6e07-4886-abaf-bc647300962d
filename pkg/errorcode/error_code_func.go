// Copyright 2023 The baidubce Cloud Container Engine (CCE) AUTHORS. All rights reserved.

// Code generated by gen_error_code_func; DO NOT EDIT.

package errorcode

// Return InternalServerError error code with certain msg
// if no msg is specified, ErrorCode.Message is set to InternalServerError
func NewInternalServerError(msg ...string) *ErrorCode {
	ret := ErrorCodes[0].DeepCopy()
	if len(msg) == 0 {
		msg = []string{ret.Code}
	}
	ret.Message = msg[0]
	return ret
}

// Return AccessDenied error code with certain msg
// if no msg is specified, ErrorCode.Message is set to AccessDenied
func NewAccessDenied(msg ...string) *ErrorCode {
	ret := ErrorCodes[1].DeepCopy()
	if len(msg) == 0 {
		msg = []string{ret.Code}
	}
	ret.Message = msg[0]
	return ret
}

// Return InappropriateJSON error code with certain msg
// if no msg is specified, ErrorCode.Message is set to InappropriateJSON
func NewInappropriateJSON(msg ...string) *ErrorCode {
	ret := ErrorCodes[2].DeepCopy()
	if len(msg) == 0 {
		msg = []string{ret.Code}
	}
	ret.Message = msg[0]
	return ret
}

// Return InvalidAccessKeyID error code with certain msg
// if no msg is specified, ErrorCode.Message is set to InvalidAccessKeyID
func NewInvalidAccessKeyID(msg ...string) *ErrorCode {
	ret := ErrorCodes[3].DeepCopy()
	if len(msg) == 0 {
		msg = []string{ret.Code}
	}
	ret.Message = msg[0]
	return ret
}

// Return InvalidHTTPAuthHeader error code with certain msg
// if no msg is specified, ErrorCode.Message is set to InvalidHTTPAuthHeader
func NewInvalidHTTPAuthHeader(msg ...string) *ErrorCode {
	ret := ErrorCodes[4].DeepCopy()
	if len(msg) == 0 {
		msg = []string{ret.Code}
	}
	ret.Message = msg[0]
	return ret
}

// Return InvalidHTTPRequest error code with certain msg
// if no msg is specified, ErrorCode.Message is set to InvalidHTTPRequest
func NewInvalidHTTPRequest(msg ...string) *ErrorCode {
	ret := ErrorCodes[5].DeepCopy()
	if len(msg) == 0 {
		msg = []string{ret.Code}
	}
	ret.Message = msg[0]
	return ret
}

// Return InvalidURI error code with certain msg
// if no msg is specified, ErrorCode.Message is set to InvalidURI
func NewInvalidURI(msg ...string) *ErrorCode {
	ret := ErrorCodes[6].DeepCopy()
	if len(msg) == 0 {
		msg = []string{ret.Code}
	}
	ret.Message = msg[0]
	return ret
}

// Return MalformedJSON error code with certain msg
// if no msg is specified, ErrorCode.Message is set to MalformedJSON
func NewMalformedJSON(msg ...string) *ErrorCode {
	ret := ErrorCodes[7].DeepCopy()
	if len(msg) == 0 {
		msg = []string{ret.Code}
	}
	ret.Message = msg[0]
	return ret
}

// Return InvalidVersion error code with certain msg
// if no msg is specified, ErrorCode.Message is set to InvalidVersion
func NewInvalidVersion(msg ...string) *ErrorCode {
	ret := ErrorCodes[8].DeepCopy()
	if len(msg) == 0 {
		msg = []string{ret.Code}
	}
	ret.Message = msg[0]
	return ret
}

// Return OptInRequired error code with certain msg
// if no msg is specified, ErrorCode.Message is set to OptInRequired
func NewOptInRequired(msg ...string) *ErrorCode {
	ret := ErrorCodes[9].DeepCopy()
	if len(msg) == 0 {
		msg = []string{ret.Code}
	}
	ret.Message = msg[0]
	return ret
}

// Return PreconditionFailed error code with certain msg
// if no msg is specified, ErrorCode.Message is set to PreconditionFailed
func NewPreconditionFailed(msg ...string) *ErrorCode {
	ret := ErrorCodes[10].DeepCopy()
	if len(msg) == 0 {
		msg = []string{ret.Code}
	}
	ret.Message = msg[0]
	return ret
}

// Return RequestExpired error code with certain msg
// if no msg is specified, ErrorCode.Message is set to RequestExpired
func NewRequestExpired(msg ...string) *ErrorCode {
	ret := ErrorCodes[11].DeepCopy()
	if len(msg) == 0 {
		msg = []string{ret.Code}
	}
	ret.Message = msg[0]
	return ret
}

// Return IdempotentParameterMismatch error code with certain msg
// if no msg is specified, ErrorCode.Message is set to IdempotentParameterMismatch
func NewIdempotentParameterMismatch(msg ...string) *ErrorCode {
	ret := ErrorCodes[12].DeepCopy()
	if len(msg) == 0 {
		msg = []string{ret.Code}
	}
	ret.Message = msg[0]
	return ret
}

// Return SignatureDoesNotMatch error code with certain msg
// if no msg is specified, ErrorCode.Message is set to SignatureDoesNotMatch
func NewSignatureDoesNotMatch(msg ...string) *ErrorCode {
	ret := ErrorCodes[13].DeepCopy()
	if len(msg) == 0 {
		msg = []string{ret.Code}
	}
	ret.Message = msg[0]
	return ret
}

// Return InvalidParam error code with certain msg
// if no msg is specified, ErrorCode.Message is set to InvalidParam
func NewInvalidParam(msg ...string) *ErrorCode {
	ret := ErrorCodes[14].DeepCopy()
	if len(msg) == 0 {
		msg = []string{ret.Code}
	}
	ret.Message = msg[0]
	return ret
}

// Return NoSuchObject error code with certain msg
// if no msg is specified, ErrorCode.Message is set to NoSuchObject
func NewNoSuchObject(msg ...string) *ErrorCode {
	ret := ErrorCodes[15].DeepCopy()
	if len(msg) == 0 {
		msg = []string{ret.Code}
	}
	ret.Message = msg[0]
	return ret
}

// Return ObjectConflict error code with certain msg
// if no msg is specified, ErrorCode.Message is set to ObjectConflict
func NewObjectConflict(msg ...string) *ErrorCode {
	ret := ErrorCodes[16].DeepCopy()
	if len(msg) == 0 {
		msg = []string{ret.Code}
	}
	ret.Message = msg[0]
	return ret
}

// Return BuildNotComplete error code with certain msg
// if no msg is specified, ErrorCode.Message is set to BuildNotComplete
func NewBuildNotComplete(msg ...string) *ErrorCode {
	ret := ErrorCodes[17].DeepCopy()
	if len(msg) == 0 {
		msg = []string{ret.Code}
	}
	ret.Message = msg[0]
	return ret
}

// Return TagNotEmpty error code with certain msg
// if no msg is specified, ErrorCode.Message is set to TagNotEmpty
func NewTagNotEmpty(msg ...string) *ErrorCode {
	ret := ErrorCodes[18].DeepCopy()
	if len(msg) == 0 {
		msg = []string{ret.Code}
	}
	ret.Message = msg[0]
	return ret
}

// Return InvalidAuth error code with certain msg
// if no msg is specified, ErrorCode.Message is set to InvalidAuth
func NewInvalidAuth(msg ...string) *ErrorCode {
	ret := ErrorCodes[19].DeepCopy()
	if len(msg) == 0 {
		msg = []string{ret.Code}
	}
	ret.Message = msg[0]
	return ret
}

// Return ChartPrivateAccountNotFound error code with certain msg
// if no msg is specified, ErrorCode.Message is set to ChartPrivateAccountNotFound
func NewChartPrivateAccountNotFound(msg ...string) *ErrorCode {
	ret := ErrorCodes[20].DeepCopy()
	if len(msg) == 0 {
		msg = []string{ret.Code}
	}
	ret.Message = msg[0]
	return ret
}

// Return MethodNotAllowed error code with certain msg
// if no msg is specified, ErrorCode.Message is set to MethodNotAllowed
func NewMethodNotAllowed(msg ...string) *ErrorCode {
	ret := ErrorCodes[21].DeepCopy()
	if len(msg) == 0 {
		msg = []string{ret.Code}
	}
	ret.Message = msg[0]
	return ret
}

// Return ClientNotSupported error code with certain msg
// if no msg is specified, ErrorCode.Message is set to ClientNotSupported
func NewClientNotSupported(msg ...string) *ErrorCode {
	ret := ErrorCodes[22].DeepCopy()
	if len(msg) == 0 {
		msg = []string{ret.Code}
	}
	ret.Message = msg[0]
	return ret
}

// Return ChartPrivateRepoNotFound error code with certain msg
// if no msg is specified, ErrorCode.Message is set to ChartPrivateRepoNotFound
func NewChartPrivateRepoNotFound(msg ...string) *ErrorCode {
	ret := ErrorCodes[23].DeepCopy()
	if len(msg) == 0 {
		msg = []string{ret.Code}
	}
	ret.Message = msg[0]
	return ret
}

// Return ChartNotFound error code with certain msg
// if no msg is specified, ErrorCode.Message is set to ChartNotFound
func NewChartNotFound(msg ...string) *ErrorCode {
	ret := ErrorCodes[24].DeepCopy()
	if len(msg) == 0 {
		msg = []string{ret.Code}
	}
	ret.Message = msg[0]
	return ret
}

// Return ChartPackageBroken error code with certain msg
// if no msg is specified, ErrorCode.Message is set to ChartPackageBroken
func NewChartPackageBroken(msg ...string) *ErrorCode {
	ret := ErrorCodes[25].DeepCopy()
	if len(msg) == 0 {
		msg = []string{ret.Code}
	}
	ret.Message = msg[0]
	return ret
}

// Return ChartPackageTooLarge error code with certain msg
// if no msg is specified, ErrorCode.Message is set to ChartPackageTooLarge
func NewChartPackageTooLarge(msg ...string) *ErrorCode {
	ret := ErrorCodes[26].DeepCopy()
	if len(msg) == 0 {
		msg = []string{ret.Code}
	}
	ret.Message = msg[0]
	return ret
}

// Return ChartContentMismatch error code with certain msg
// if no msg is specified, ErrorCode.Message is set to ChartContentMismatch
func NewChartContentMismatch(msg ...string) *ErrorCode {
	ret := ErrorCodes[27].DeepCopy()
	if len(msg) == 0 {
		msg = []string{ret.Code}
	}
	ret.Message = msg[0]
	return ret
}

// Return ChartQuotaExceeded error code with certain msg
// if no msg is specified, ErrorCode.Message is set to ChartQuotaExceeded
func NewChartQuotaExceeded(msg ...string) *ErrorCode {
	ret := ErrorCodes[28].DeepCopy()
	if len(msg) == 0 {
		msg = []string{ret.Code}
	}
	ret.Message = msg[0]
	return ret
}

// Return ChartDeletionFailed error code with certain msg
// if no msg is specified, ErrorCode.Message is set to ChartDeletionFailed
func NewChartDeletionFailed(msg ...string) *ErrorCode {
	ret := ErrorCodes[29].DeepCopy()
	if len(msg) == 0 {
		msg = []string{ret.Code}
	}
	ret.Message = msg[0]
	return ret
}

// Return HelmReleaseNotFound error code with certain msg
// if no msg is specified, ErrorCode.Message is set to HelmReleaseNotFound
func NewHelmReleaseNotFound(msg ...string) *ErrorCode {
	ret := ErrorCodes[30].DeepCopy()
	if len(msg) == 0 {
		msg = []string{ret.Code}
	}
	ret.Message = msg[0]
	return ret
}

// Return HelmReleaseNameAlreadyExist error code with certain msg
// if no msg is specified, ErrorCode.Message is set to HelmReleaseNameAlreadyExist
func NewHelmReleaseNameAlreadyExist(msg ...string) *ErrorCode {
	ret := ErrorCodes[31].DeepCopy()
	if len(msg) == 0 {
		msg = []string{ret.Code}
	}
	ret.Message = msg[0]
	return ret
}

// Return HelmReleaseNameIllegal error code with certain msg
// if no msg is specified, ErrorCode.Message is set to HelmReleaseNameIllegal
func NewHelmReleaseNameIllegal(msg ...string) *ErrorCode {
	ret := ErrorCodes[32].DeepCopy()
	if len(msg) == 0 {
		msg = []string{ret.Code}
	}
	ret.Message = msg[0]
	return ret
}

// Return HelmReleaseValuesIllegal error code with certain msg
// if no msg is specified, ErrorCode.Message is set to HelmReleaseValuesIllegal
func NewHelmReleaseValuesIllegal(msg ...string) *ErrorCode {
	ret := ErrorCodes[33].DeepCopy()
	if len(msg) == 0 {
		msg = []string{ret.Code}
	}
	ret.Message = msg[0]
	return ret
}

// Return TillerNotFound error code with certain msg
// if no msg is specified, ErrorCode.Message is set to TillerNotFound
func NewTillerNotFound(msg ...string) *ErrorCode {
	ret := ErrorCodes[34].DeepCopy()
	if len(msg) == 0 {
		msg = []string{ret.Code}
	}
	ret.Message = msg[0]
	return ret
}

// Return TillerNotReady error code with certain msg
// if no msg is specified, ErrorCode.Message is set to TillerNotReady
func NewTillerNotReady(msg ...string) *ErrorCode {
	ret := ErrorCodes[35].DeepCopy()
	if len(msg) == 0 {
		msg = []string{ret.Code}
	}
	ret.Message = msg[0]
	return ret
}

// Return TillerNotCompatible error code with certain msg
// if no msg is specified, ErrorCode.Message is set to TillerNotCompatible
func NewTillerNotCompatible(msg ...string) *ErrorCode {
	ret := ErrorCodes[36].DeepCopy()
	if len(msg) == 0 {
		msg = []string{ret.Code}
	}
	ret.Message = msg[0]
	return ret
}

// Return ClusterMasterUnavailable error code with certain msg
// if no msg is specified, ErrorCode.Message is set to ClusterMasterUnavailable
func NewClusterMasterUnavailable(msg ...string) *ErrorCode {
	ret := ErrorCodes[37].DeepCopy()
	if len(msg) == 0 {
		msg = []string{ret.Code}
	}
	ret.Message = msg[0]
	return ret
}

// Return HelmClientError error code with certain msg
// if no msg is specified, ErrorCode.Message is set to HelmClientError
func NewHelmClientError(msg ...string) *ErrorCode {
	ret := ErrorCodes[38].DeepCopy()
	if len(msg) == 0 {
		msg = []string{ret.Code}
	}
	ret.Message = msg[0]
	return ret
}

// Return ClusterNotFound error code with certain msg
// if no msg is specified, ErrorCode.Message is set to ClusterNotFound
func NewClusterNotFound(msg ...string) *ErrorCode {
	ret := ErrorCodes[39].DeepCopy()
	if len(msg) == 0 {
		msg = []string{ret.Code}
	}
	ret.Message = msg[0]
	return ret
}

// Return ClusterNotExposedToPublic error code with certain msg
// if no msg is specified, ErrorCode.Message is set to ClusterNotExposedToPublic
func NewClusterNotExposedToPublic(msg ...string) *ErrorCode {
	ret := ErrorCodes[40].DeepCopy()
	if len(msg) == 0 {
		msg = []string{ret.Code}
	}
	ret.Message = msg[0]
	return ret
}

// Return InvaildUserID error code with certain msg
// if no msg is specified, ErrorCode.Message is set to InvaildUserID
func NewInvaildUserID(msg ...string) *ErrorCode {
	ret := ErrorCodes[41].DeepCopy()
	if len(msg) == 0 {
		msg = []string{ret.Code}
	}
	ret.Message = msg[0]
	return ret
}

// Return InvaildClusterName error code with certain msg
// if no msg is specified, ErrorCode.Message is set to InvaildClusterName
func NewInvaildClusterName(msg ...string) *ErrorCode {
	ret := ErrorCodes[42].DeepCopy()
	if len(msg) == 0 {
		msg = []string{ret.Code}
	}
	ret.Message = msg[0]
	return ret
}

// Return InstanceAlreadyExists error code with certain msg
// if no msg is specified, ErrorCode.Message is set to InstanceAlreadyExists
func NewInstanceAlreadyExists(msg ...string) *ErrorCode {
	ret := ErrorCodes[43].DeepCopy()
	if len(msg) == 0 {
		msg = []string{ret.Code}
	}
	ret.Message = msg[0]
	return ret
}

// Return InstancesCountBeyondLimit error code with certain msg
// if no msg is specified, ErrorCode.Message is set to InstancesCountBeyondLimit
func NewInstancesCountBeyondLimit(msg ...string) *ErrorCode {
	ret := ErrorCodes[44].DeepCopy()
	if len(msg) == 0 {
		msg = []string{ret.Code}
	}
	ret.Message = msg[0]
	return ret
}

// Return ExistedInstanceIDDuplicated error code with certain msg
// if no msg is specified, ErrorCode.Message is set to ExistedInstanceIDDuplicated
func NewExistedInstanceIDDuplicated(msg ...string) *ErrorCode {
	ret := ErrorCodes[45].DeepCopy()
	if len(msg) == 0 {
		msg = []string{ret.Code}
	}
	ret.Message = msg[0]
	return ret
}

// Return InstanceNotFound error code with certain msg
// if no msg is specified, ErrorCode.Message is set to InstanceNotFound
func NewInstanceNotFound(msg ...string) *ErrorCode {
	ret := ErrorCodes[46].DeepCopy()
	if len(msg) == 0 {
		msg = []string{ret.Code}
	}
	ret.Message = msg[0]
	return ret
}

// Return VPCIDNotExists error code with certain msg
// if no msg is specified, ErrorCode.Message is set to VPCIDNotExists
func NewVPCIDNotExists(msg ...string) *ErrorCode {
	ret := ErrorCodes[47].DeepCopy()
	if len(msg) == 0 {
		msg = []string{ret.Code}
	}
	ret.Message = msg[0]
	return ret
}

// Return MasterFlavorInvalid error code with certain msg
// if no msg is specified, ErrorCode.Message is set to MasterFlavorInvalid
func NewMasterFlavorInvalid(msg ...string) *ErrorCode {
	ret := ErrorCodes[48].DeepCopy()
	if len(msg) == 0 {
		msg = []string{ret.Code}
	}
	ret.Message = msg[0]
	return ret
}

// Return ClusterHAInvalid error code with certain msg
// if no msg is specified, ErrorCode.Message is set to ClusterHAInvalid
func NewClusterHAInvalid(msg ...string) *ErrorCode {
	ret := ErrorCodes[49].DeepCopy()
	if len(msg) == 0 {
		msg = []string{ret.Code}
	}
	ret.Message = msg[0]
	return ret
}

// Return ContainerNetworkCIDRInvalid error code with certain msg
// if no msg is specified, ErrorCode.Message is set to ContainerNetworkCIDRInvalid
func NewContainerNetworkCIDRInvalid(msg ...string) *ErrorCode {
	ret := ErrorCodes[50].DeepCopy()
	if len(msg) == 0 {
		msg = []string{ret.Code}
	}
	ret.Message = msg[0]
	return ret
}

// Return InstanceGroupNotFound error code with certain msg
// if no msg is specified, ErrorCode.Message is set to InstanceGroupNotFound
func NewInstanceGroupNotFound(msg ...string) *ErrorCode {
	ret := ErrorCodes[51].DeepCopy()
	if len(msg) == 0 {
		msg = []string{ret.Code}
	}
	ret.Message = msg[0]
	return ret
}

// Return ClientInfoImproperlyEncoded error code with certain msg
// if no msg is specified, ErrorCode.Message is set to ClientInfoImproperlyEncoded
func NewClientInfoImproperlyEncoded(msg ...string) *ErrorCode {
	ret := ErrorCodes[52].DeepCopy()
	if len(msg) == 0 {
		msg = []string{ret.Code}
	}
	ret.Message = msg[0]
	return ret
}

// Return RegionNotAvailable error code with certain msg
// if no msg is specified, ErrorCode.Message is set to RegionNotAvailable
func NewRegionNotAvailable(msg ...string) *ErrorCode {
	ret := ErrorCodes[53].DeepCopy()
	if len(msg) == 0 {
		msg = []string{ret.Code}
	}
	ret.Message = msg[0]
	return ret
}

// Return BLBNotAvailable error code with certain msg
// if no msg is specified, ErrorCode.Message is set to BLBNotAvailable
func NewBLBNotAvailable(msg ...string) *ErrorCode {
	ret := ErrorCodes[54].DeepCopy()
	if len(msg) == 0 {
		msg = []string{ret.Code}
	}
	ret.Message = msg[0]
	return ret
}

// Return MasterCannotDeletedOrRemoved error code with certain msg
// if no msg is specified, ErrorCode.Message is set to MasterCannotDeletedOrRemoved
func NewMasterCannotDeletedOrRemoved(msg ...string) *ErrorCode {
	ret := ErrorCodes[55].DeepCopy()
	if len(msg) == 0 {
		msg = []string{ret.Code}
	}
	ret.Message = msg[0]
	return ret
}

// Return ManagedClusterMinNodeNumLimit error code with certain msg
// if no msg is specified, ErrorCode.Message is set to ManagedClusterMinNodeNumLimit
func NewManagedClusterMinNodeNumLimit(msg ...string) *ErrorCode {
	ret := ErrorCodes[56].DeepCopy()
	if len(msg) == 0 {
		msg = []string{ret.Code}
	}
	ret.Message = msg[0]
	return ret
}

// Return ConflictNetworkTopology error code with certain msg
// if no msg is specified, ErrorCode.Message is set to ConflictNetworkTopology
func NewConflictNetworkTopology(msg ...string) *ErrorCode {
	ret := ErrorCodes[57].DeepCopy()
	if len(msg) == 0 {
		msg = []string{ret.Code}
	}
	ret.Message = msg[0]
	return ret
}

// Return CurrentPhaseCannotRetry error code with certain msg
// if no msg is specified, ErrorCode.Message is set to CurrentPhaseCannotRetry
func NewCurrentPhaseCannotRetry(msg ...string) *ErrorCode {
	ret := ErrorCodes[58].DeepCopy()
	if len(msg) == 0 {
		msg = []string{ret.Code}
	}
	ret.Message = msg[0]
	return ret
}

// Return AIJobMissingCPUOrMemoryRequest error code with certain msg
// if no msg is specified, ErrorCode.Message is set to AIJobMissingCPUOrMemoryRequest
func NewAIJobMissingCPUOrMemoryRequest(msg ...string) *ErrorCode {
	ret := ErrorCodes[59].DeepCopy()
	if len(msg) == 0 {
		msg = []string{ret.Code}
	}
	ret.Message = msg[0]
	return ret
}

// Return EssentialPluginNotFound error code with certain msg
// if no msg is specified, ErrorCode.Message is set to EssentialPluginNotFound
func NewEssentialPluginNotFound(msg ...string) *ErrorCode {
	ret := ErrorCodes[60].DeepCopy()
	if len(msg) == 0 {
		msg = []string{ret.Code}
	}
	ret.Message = msg[0]
	return ret
}

// Return AIOperatorNotFound error code with certain msg
// if no msg is specified, ErrorCode.Message is set to AIOperatorNotFound
func NewAIOperatorNotFound(msg ...string) *ErrorCode {
	ret := ErrorCodes[61].DeepCopy()
	if len(msg) == 0 {
		msg = []string{ret.Code}
	}
	ret.Message = msg[0]
	return ret
}

// Return AIQueueCannotDelete error code with certain msg
// if no msg is specified, ErrorCode.Message is set to AIQueueCannotDelete
func NewAIQueueCannotDelete(msg ...string) *ErrorCode {
	ret := ErrorCodes[62].DeepCopy()
	if len(msg) == 0 {
		msg = []string{ret.Code}
	}
	ret.Message = msg[0]
	return ret
}

// Return RBACUnauthorized error code with certain msg
// if no msg is specified, ErrorCode.Message is set to RBACUnauthorized
func NewRBACUnauthorized(msg ...string) *ErrorCode {
	ret := ErrorCodes[63].DeepCopy()
	if len(msg) == 0 {
		msg = []string{ret.Code}
	}
	ret.Message = msg[0]
	return ret
}

// Return MultiWorkflowExistInCluster error code with certain msg
// if no msg is specified, ErrorCode.Message is set to MultiWorkflowExistInCluster
func NewMultiWorkflowExistInCluster(msg ...string) *ErrorCode {
	ret := ErrorCodes[64].DeepCopy()
	if len(msg) == 0 {
		msg = []string{ret.Code}
	}
	ret.Message = msg[0]
	return ret
}

// Return TagNotExistWithRelationTag error code with certain msg
// if no msg is specified, ErrorCode.Message is set to TagNotExistWithRelationTag
func NewTagNotExistWithRelationTag(msg ...string) *ErrorCode {
	ret := ErrorCodes[65].DeepCopy()
	if len(msg) == 0 {
		msg = []string{ret.Code}
	}
	ret.Message = msg[0]
	return ret
}

// Return CCEClusterQuotaExceed error code with certain msg
// if no msg is specified, ErrorCode.Message is set to CCEClusterQuotaExceed
func NewCCEClusterQuotaExceed(msg ...string) *ErrorCode {
	ret := ErrorCodes[66].DeepCopy()
	if len(msg) == 0 {
		msg = []string{ret.Code}
	}
	ret.Message = msg[0]
	return ret
}

// Return CCEInstanceQuotaExceed error code with certain msg
// if no msg is specified, ErrorCode.Message is set to CCEInstanceQuotaExceed
func NewCCEInstanceQuotaExceed(msg ...string) *ErrorCode {
	ret := ErrorCodes[67].DeepCopy()
	if len(msg) == 0 {
		msg = []string{ret.Code}
	}
	ret.Message = msg[0]
	return ret
}

// Return NRNotInstalled error code with certain msg
// if no msg is specified, ErrorCode.Message is set to AIQueueCannotDelete
func NewNRNotInstalled(msg ...string) *ErrorCode {
	ret := ErrorCodes[68].DeepCopy()
	if len(msg) == 0 {
		msg = []string{ret.Code}
	}
	ret.Message = msg[0]
	return ret
}

func NewCCEInstanceNumberExceed(msg ...string) *ErrorCode {
	ret := ErrorCodes[69].DeepCopy()
	if len(msg) == 0 {
		msg = []string{ret.Code}
	}
	ret.Message = msg[0]
	return ret
}

// Return BCCLackedWhenCreateBLB error code with certain msg
// if no msg is specified, ErrorCode.Message is set to BCCLackedWhenCreateBLB
func NewBCCLackedWhenCreateBLB(msg ...string) *ErrorCode {
	ret := ReconcileErrorCodes[0].DeepCopy()
	if len(msg) == 0 {
		msg = []string{ret.Code}
	}
	ret.Message = msg[0]
	return ret
}

// Return AccountInsufficientBalance error code with certain msg
// if no msg is specified, ErrorCode.Message is set to AccountInsufficientBalance
func NewAccountInsufficientBalance(msg ...string) *ErrorCode {
	ret := ReconcileErrorCodes[1].DeepCopy()
	if len(msg) == 0 {
		msg = []string{ret.Code}
	}
	ret.Message = msg[0]
	return ret
}

// Return BLBPermissionDenied error code with certain msg
// if no msg is specified, ErrorCode.Message is set to BLBPermissionDenied
func NewBLBPermissionDenied(msg ...string) *ErrorCode {
	ret := ReconcileErrorCodes[2].DeepCopy()
	if len(msg) == 0 {
		msg = []string{ret.Code}
	}
	ret.Message = msg[0]
	return ret
}

// Return BLBQuotaExceeded error code with certain msg
// if no msg is specified, ErrorCode.Message is set to BLBQuotaExceeded
func NewBLBQuotaExceeded(msg ...string) *ErrorCode {
	ret := ReconcileErrorCodes[3].DeepCopy()
	if len(msg) == 0 {
		msg = []string{ret.Code}
	}
	ret.Message = msg[0]
	return ret
}

func NewOrderPermissionDenied(msg ...string) *ErrorCode {
	ret := &ErrorCode{
		Code:       "OrderPermissionDenied",
		Suggestion: "为该用户添加Order权限",
	}
	if len(msg) == 0 {
		msg = []string{ret.Code}
	}
	ret.Message = msg[0]
	return ret
}

// Return NeedFaceAuthentication error code with certain msg
// if no msg is specified, ErrorCode.Message is set to NeedFaceAuthentication
func NewNeedFaceAuthentication(msg ...string) *ErrorCode {
	ret := ReconcileErrorCodes[4].DeepCopy()
	if len(msg) == 0 {
		msg = []string{ret.Code}
	}
	ret.Message = msg[0]
	return ret
}

// Return ManagedMasterCreateFailed error code with certain msg
// if no msg is specified, ErrorCode.Message is set to ManagedMasterCreateFailed
func NewManagedMasterCreateFailed(msg ...string) *ErrorCode {
	ret := ReconcileErrorCodes[5].DeepCopy()
	if len(msg) == 0 {
		msg = []string{ret.Code}
	}
	ret.Message = msg[0]
	return ret
}

// Return CustomMasterCreateFailed error code with certain msg
// if no msg is specified, ErrorCode.Message is set to CustomMasterCreateFailed
func NewCustomMasterCreateFailed(msg ...string) *ErrorCode {
	ret := ReconcileErrorCodes[6].DeepCopy()
	if len(msg) == 0 {
		msg = []string{ret.Code}
	}
	ret.Message = msg[0]
	return ret
}

// Return ManagedMasterDeleteFailed error code with certain msg
// if no msg is specified, ErrorCode.Message is set to ManagedMasterDeleteFailed
func NewManagedMasterDeleteFailed(msg ...string) *ErrorCode {
	ret := ReconcileErrorCodes[7].DeepCopy()
	if len(msg) == 0 {
		msg = []string{ret.Code}
	}
	ret.Message = msg[0]
	return ret
}

// Return CustomMasterDeleteFailed error code with certain msg
// if no msg is specified, ErrorCode.Message is set to CustomMasterDeleteFailed
func NewCustomMasterDeleteFailed(msg ...string) *ErrorCode {
	ret := ReconcileErrorCodes[8].DeepCopy()
	if len(msg) == 0 {
		msg = []string{ret.Code}
	}
	ret.Message = msg[0]
	return ret
}

// Return ClusterNodeDeleteFailed error code with certain msg
// if no msg is specified, ErrorCode.Message is set to ClusterNodeDeleteFailed
func NewClusterNodeDeleteFailed(msg ...string) *ErrorCode {
	ret := ReconcileErrorCodes[9].DeepCopy()
	if len(msg) == 0 {
		msg = []string{ret.Code}
	}
	ret.Message = msg[0]
	return ret
}

// Return InstanceENINotDeleted error code with certain msg
// if no msg is specified, ErrorCode.Message is set to InstanceENINotDeleted
func NewInstanceENINotDeleted(msg ...string) *ErrorCode {
	ret := ReconcileErrorCodes[10].DeepCopy()
	if len(msg) == 0 {
		msg = []string{ret.Code}
	}
	ret.Message = msg[0]
	return ret
}

// Return InstanceVPCRouteNotDeleted error code with certain msg
// if no msg is specified, ErrorCode.Message is set to InstanceVPCRouteNotDeleted
func NewInstanceVPCRouteNotDeleted(msg ...string) *ErrorCode {
	ret := ReconcileErrorCodes[11].DeepCopy()
	if len(msg) == 0 {
		msg = []string{ret.Code}
	}
	ret.Message = msg[0]
	return ret
}

// Return InstanceBBCPrivateIPNotDeleted error code with certain msg
// if no msg is specified, ErrorCode.Message is set to InstanceBBCPrivateIPNotDeleted
func NewInstanceBBCPrivateIPNotDeleted(msg ...string) *ErrorCode {
	ret := ReconcileErrorCodes[12].DeepCopy()
	if len(msg) == 0 {
		msg = []string{ret.Code}
	}
	ret.Message = msg[0]
	return ret
}

// Return BCCNotExistAfterCreated error code with certain msg
// if no msg is specified, ErrorCode.Message is set to BCCNotExistAfterCreated
func NewBCCNotExistAfterCreated(msg ...string) *ErrorCode {
	ret := ReconcileErrorCodes[13].DeepCopy()
	if len(msg) == 0 {
		msg = []string{ret.Code}
	}
	ret.Message = msg[0]
	return ret
}

// Return BCCCannotDeletedIfPrepaid error code with certain msg
// if no msg is specified, ErrorCode.Message is set to BCCCannotDeletedIfPrepaid
func NewBCCCannotDeletedIfPrepaid(msg ...string) *ErrorCode {
	ret := ReconcileErrorCodes[14].DeepCopy()
	if len(msg) == 0 {
		msg = []string{ret.Code}
	}
	ret.Message = msg[0]
	return ret
}

// Return InstanceNoStock error code with certain msg
// if no msg is specified, ErrorCode.Message is set to InstanceNoStock
func NewInstanceNoStock(msg ...string) *ErrorCode {
	ret := ReconcileErrorCodes[15].DeepCopy()
	if len(msg) == 0 {
		msg = []string{ret.Code}
	}
	ret.Message = msg[0]
	return ret
}

// Return InstanceQuotaExceeded error code with certain msg
// if no msg is specified, ErrorCode.Message is set to InstanceQuotaExceeded
func NewInstanceQuotaExceeded(msg ...string) *ErrorCode {
	ret := ReconcileErrorCodes[16].DeepCopy()
	if len(msg) == 0 {
		msg = []string{ret.Code}
	}
	ret.Message = msg[0]
	return ret
}

// Return HostnameDuplicateInVPC error code with certain msg
// if no msg is specified, ErrorCode.Message is set to HostnameDuplicateInVPC
func NewHostnameDuplicateInVPC(msg ...string) *ErrorCode {
	ret := ReconcileErrorCodes[17].DeepCopy()
	if len(msg) == 0 {
		msg = []string{ret.Code}
	}
	ret.Message = msg[0]
	return ret
}

// Return InsufficientIPInSubnet error code with certain msg
// if no msg is specified, ErrorCode.Message is set to InsufficientIPInSubnet
func NewInsufficientIPInSubnet(msg ...string) *ErrorCode {
	ret := ReconcileErrorCodes[18].DeepCopy()
	if len(msg) == 0 {
		msg = []string{ret.Code}
	}
	ret.Message = msg[0]
	return ret
}

// Return VolumeNotAvailableInZone error code with certain msg
// if no msg is specified, ErrorCode.Message is set to VolumeNotAvailableInZone
func NewVolumeNotAvailableInZone(msg ...string) *ErrorCode {
	ret := ReconcileErrorCodes[19].DeepCopy()
	if len(msg) == 0 {
		msg = []string{ret.Code}
	}
	ret.Message = msg[0]
	return ret
}

// Return EIPByTrafficBandWidthQuotaExceeded error code with certain msg
// if no msg is specified, ErrorCode.Message is set to EIPByTrafficBandWidthQuotaExceeded
func NewEIPByTrafficBandWidthQuotaExceeded(msg ...string) *ErrorCode {
	ret := ReconcileErrorCodes[20].DeepCopy()
	if len(msg) == 0 {
		msg = []string{ret.Code}
	}
	ret.Message = msg[0]
	return ret
}

// Return DeleteInstanceWithUserCustomRoute error code with certain msg
// if no msg is specified, ErrorCode.Message is set to DeleteInstanceWithUserCustomRoute
func NewDeleteInstanceWithUserCustomRoute(msg ...string) *ErrorCode {
	ret := ReconcileErrorCodes[21].DeepCopy()
	if len(msg) == 0 {
		msg = []string{ret.Code}
	}
	ret.Message = msg[0]
	return ret
}

// Return InsufficientRootDisk error code with certain msg
// if no msg is specified, ErrorCode.Message is set to InsufficientRootDisk
func NewInsufficientRootDisk(msg ...string) *ErrorCode {
	ret := ReconcileErrorCodes[22].DeepCopy()
	if len(msg) == 0 {
		msg = []string{ret.Code}
	}
	ret.Message = msg[0]
	return ret
}

// Return BCCFixedIPNotExistAfterCreated error code with certain msg
// if no msg is specified, ErrorCode.Message is set to BCCFixedIPNotExistAfterCreated
func NewBCCFixedIPNotExistAfterCreated(msg ...string) *ErrorCode {
	ret := ReconcileErrorCodes[23].DeepCopy()
	if len(msg) == 0 {
		msg = []string{ret.Code}
	}
	ret.Message = msg[0]
	return ret
}

// Return InvalidBCCFlavorConfig error code with certain msg
// if no msg is specified, ErrorCode.Message is set to InvalidBCCFlavorConfig
func NewInvalidBCCFlavorConfig(msg ...string) *ErrorCode {
	ret := ReconcileErrorCodes[24].DeepCopy()
	if len(msg) == 0 {
		msg = []string{ret.Code}
	}
	ret.Message = msg[0]
	return ret
}

// Return CreateBLBError error code with certain msg
// if no msg is specified, ErrorCode.Message is set to CreateBLBError
func NewCreateBLBError(msg ...string) *ErrorCode {
	ret := ReconcileErrorCodes[25].DeepCopy()
	if len(msg) == 0 {
		msg = []string{ret.Code}
	}
	ret.Message = msg[0]
	return ret
}

// Return CreateEIPError error code with certain msg
// if no msg is specified, ErrorCode.Message is set to CreateEIPError
func NewCreateEIPError(msg ...string) *ErrorCode {
	ret := ReconcileErrorCodes[26].DeepCopy()
	if len(msg) == 0 {
		msg = []string{ret.Code}
	}
	ret.Message = msg[0]
	return ret
}

// Return TidyConfigurationNotExist error code with certain msg
// if no msg is specified, ErrorCode.Message is set to TidyConfigurationNotExist
func NewTidyConfigurationNotExist(msg ...string) *ErrorCode {
	ret := ReconcileErrorCodes[27].DeepCopy()
	if len(msg) == 0 {
		msg = []string{ret.Code}
	}
	ret.Message = msg[0]
	return ret
}

// Return TidyConfigurationInvalid error code with certain msg
// if no msg is specified, ErrorCode.Message is set to TidyConfigurationInvalid
func NewTidyConfigurationInvalid(msg ...string) *ErrorCode {
	ret := ReconcileErrorCodes[28].DeepCopy()
	if len(msg) == 0 {
		msg = []string{ret.Code}
	}
	ret.Message = msg[0]
	return ret
}

// Return TidyNotAllowedCurrently error code with certain msg
// if no msg is specified, ErrorCode.Message is set to TidyNotAllowedCurrently
func NewTidyNotAllowedCurrently(msg ...string) *ErrorCode {
	ret := ReconcileErrorCodes[29].DeepCopy()
	if len(msg) == 0 {
		msg = []string{ret.Code}
	}
	ret.Message = msg[0]
	return ret
}

// Return DeleteInstanceError error code with certain msg
// if no msg is specified, ErrorCode.Message is set to DeleteInstanceError
func NewDeleteInstanceError(msg ...string) *ErrorCode {
	ret := ReconcileErrorCodes[30].DeepCopy()
	if len(msg) == 0 {
		msg = []string{ret.Code}
	}
	ret.Message = msg[0]
	return ret
}

// Return KubeConfigExpiredOrInvalid error code with certain msg
// if no msg is specified, ErrorCode.Message is set to KubeConfigExpiredOrInvalid
func NewKubeConfigExpiredOrInvalid(msg ...string) *ErrorCode {
	ret := ReconcileErrorCodes[31].DeepCopy()
	if len(msg) == 0 {
		msg = []string{ret.Code}
	}
	ret.Message = msg[0]
	return ret
}

// Return SSHConnctionFailed error code with certain msg
// if no msg is specified, ErrorCode.Message is set to SSHConnctionFailed
func NewSSHConnctionFailed(msg ...string) *ErrorCode {
	ret := ReconcileErrorCodes[32].DeepCopy()
	if len(msg) == 0 {
		msg = []string{ret.Code}
	}
	ret.Message = msg[0]
	return ret
}

// Return K8SNodeNotReady error code with certain msg
// if no msg is specified, ErrorCode.Message is set to K8SNodeNotReady
func NewK8SNodeNotReady(msg ...string) *ErrorCode {
	ret := ReconcileErrorCodes[33].DeepCopy()
	if len(msg) == 0 {
		msg = []string{ret.Code}
	}
	ret.Message = msg[0]
	return ret
}

// Return DiskSizeQuotaExceededLimit error code with certain msg
// if no msg is specified, ErrorCode.Message is set to DiskSizeQuotaExceededLimit
func NewDiskSizeQuotaExceededLimit(msg ...string) *ErrorCode {
	ret := ReconcileErrorCodes[34].DeepCopy()
	if len(msg) == 0 {
		msg = []string{ret.Code}
	}
	ret.Message = msg[0]
	return ret
}

// Return InstanceNoStockInCurrentAZone error code with certain msg
// if no msg is specified, ErrorCode.Message is set to InstanceNoStockInCurrentAZone
func NewInstanceNoStockInCurrentAZone(msg ...string) *ErrorCode {
	ret := ReconcileErrorCodes[35].DeepCopy()
	if len(msg) == 0 {
		msg = []string{ret.Code}
	}
	ret.Message = msg[0]
	return ret
}

// Return BCCCreating error code with certain msg
// if no msg is specified, ErrorCode.Message is set to BCCCreating
func NewBCCCreating(msg ...string) *ErrorCode {
	ret := ReconcileErrorCodes[36].DeepCopy()
	if len(msg) == 0 {
		msg = []string{ret.Code}
	}
	ret.Message = msg[0]
	return ret
}

// Return CCEClusterQuotaExceeded error code with certain msg
// if no msg is specified, ErrorCode.Message is set to CCEClusterQuotaExceeded
func NewCCEClusterQuotaExceeded(msg ...string) *ErrorCode {
	ret := ReconcileErrorCodes[37].DeepCopy()
	if len(msg) == 0 {
		msg = []string{ret.Code}
	}
	ret.Message = msg[0]
	return ret
}

// Return NewLccConfigNotExist error code with certain msg
// if no msg is specified, ErrorCode.Message is set to CCEClusterQuotaExceeded
func NewLccConfigNotExist(msg ...string) *ErrorCode {
	ret := ReconcileErrorCodes[38].DeepCopy()
	if len(msg) == 0 {
		msg = []string{ret.Code}
	}
	ret.Message = msg[0]
	return ret
}
