// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2020/05/18 14:36:00, by <EMAIL>, create
*/
/*
DESCRIPTION
CCE Web 服务统一 error handler 方法
*/

package errorcode

import (
	"context"
	"errors"
	"reflect"
	"regexp"
	"testing"

	"github.com/astaxie/beego"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
)

func Test_requestID(t *testing.T) {
	type args struct {
		ctx context.Context
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		// TODO: Add test cases.
		{
			name: "RequestID in ctx: logger.RequestID",
			args: args{
				ctx: context.WithValue(context.TODO(), logger.RequestID, "chenhuan-requestid"),
			},
			want: "chenhuan-requestid",
		},
		{
			name: "RequestID in ctx: monitorutils.RequestID",
			args: args{
				ctx: context.WithValue(context.TODO(), logger.RequestID, "chenhuan-requestid"),
			},
			want: "chenhuan-requestid",
		},
		{
			name: "RequestID 不在 ctx 中",
			args: args{
				ctx: context.TODO(),
			},
			want: "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := requestID(tt.args.ctx); got != tt.want {
				if tt.name == "RequestID 不在 ctx 中" {
					reg := regexp.MustCompile(requestIDSuffix + "$")
					if reg.MatchString(got) {
						return
					}
				}

				t.Errorf("requestID() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_handler_response(t *testing.T) {
	type fields struct {
		Controller beego.Controller
	}
	type args struct {
		ctx    context.Context
		code   *ErrorCode
		level  Level
		format string
		a      []any
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   Response
	}{
		// TODO: Add test cases.
		{
			name: "InternalServerError, Level=Error",
			fields: fields{
				Controller: beego.Controller{},
			},
			args: args{
				ctx:    context.WithValue(context.TODO(), logger.RequestID, "request-id"),
				code:   NewInternalServerError(),
				level:  LevelByAdmin,
				format: "internal server error",
			},
			want: Response{
				Code:      "cce.error.InternalServerError",
				Message:   "internal server error",
				RequestID: "request-id",
			},
		},
		{
			name: "NewAccessDenied, Level=Warning",
			fields: fields{
				Controller: beego.Controller{},
			},
			args: args{
				ctx:   context.WithValue(context.TODO(), logger.RequestID, "request-id"),
				code:  NewAccessDenied(),
				level: LevelByUser,
			},
			want: Response{
				Code:      "cce.warning.AccessDenied",
				Message:   "AccessDenied",
				RequestID: "request-id",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &handler{
				Controller: tt.fields.Controller,
			}
			if got := c.response(tt.args.ctx, tt.args.code, tt.args.level, tt.args.format, tt.args.a...); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("handler.response() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestReconcileStep(t *testing.T) {
	type args struct {
		ctx       context.Context
		errorCode error
	}
	tests := []struct {
		name     string
		args     args
		response ReconcileResponse
		matched  bool
	}{
		// TODO: Add test cases.
		{
			name: "正常",
			args: args{
				ctx: context.TODO(),
				errorCode: &ErrorCode{
					Code:       NewAccountInsufficientBalance().Code,
					Message:    "bcc error info",
					Suggestion: NewAccountInsufficientBalance().Suggestion,
				},
			},
			response: ReconcileResponse{
				Code:       NewAccountInsufficientBalance().Code,
				Message:    "bcc error info",
				TraceID:    "",
				Suggestion: NewAccountInsufficientBalance().Suggestion,
			},
			matched: true,
		},
		{
			name: "errCode 不是ErrorCode类型",
			args: args{
				ctx:       context.TODO(),
				errorCode: errors.New("exec command error"),
			},
			response: ReconcileResponse{
				Code:       NewInternalServerError().Code,
				Message:    "exec command error",
				TraceID:    "",
				Suggestion: SuggestCCETicket,
			},
			matched: false,
		},
		{
			name: "errCode is nil",
			args: args{
				ctx:       context.TODO(),
				errorCode: nil,
			},
			response: ReconcileResponse{
				Code:       NewInternalServerError().Code,
				Message:    "InternalServerError",
				TraceID:    "",
				Suggestion: SuggestCCETicket,
			},
			matched: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got, matched := ReconcileStep(tt.args.ctx, tt.args.errorCode); !reflect.DeepEqual(got, tt.response) || matched != tt.matched {
				t.Errorf("ReconcileStep() = %v, want %v, matched: %t, want: %t", got, tt.response, matched, tt.matched)
			}
		})
	}
}
