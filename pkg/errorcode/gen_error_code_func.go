// Copyright 2019 The baidubce Cloud Container Engine (CCE) AUTHORS. All rights reserved.

//go:build ignore
// +build ignore

// gen_error_code_func generates functions return certain error code with custom message

// It is meant to be used by the baidubce Cloud Container Engine (CCE) authors in conjunction
// with the go generate tool before commiting code.

package main

import (
	"bytes"
	"errors"
	"flag"
	"go/format"
	"log"
	"os"
	"text/template"
	"time"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/errorcode"
)

var verbose = flag.Bool("v", false, "Print verbose log messages")

type templateData struct {
	filename            string
	Year                int
	ErrorCodes          []*errorcode.ErrorCode
	ReconcileErrorCodes []*errorcode.ErrorCode
	Tmpl                *template.Template
}

func (t *templateData) dump() error {
	if len(t.ErrorCodes) == 0 {
		logf("no error codes found, skip generating %s.", t.filename)
		return nil
	}

	if len(t.filename) == 0 {
		return errors.New("must specify dst filename")
	}

	var buf bytes.Buffer
	if err := t.Tmpl.Execute(&buf, t); err != nil {
		return err
	}

	clean, err := format.Source(buf.Bytes())
	if err != nil {
		return err
	}

	logf("Writing %v...", t.filename)
	return os.WriteFile(t.filename, clean, 0644)
}

func logf(fmt string, args ...any) {
	if *verbose {
		log.Printf(fmt, args...)
	}
}

func main() {
	flag.Parse()

	sourceData := &templateData{
		filename:            "error_code_func.go",
		Year:                time.Now().Year(),
		ErrorCodes:          errorcode.ErrorCodes,
		ReconcileErrorCodes: errorcode.ReconcileErrorCodes,
		Tmpl:                template.Must(template.New("source").Parse(source)),
	}

	if err := sourceData.dump(); err != nil {
		log.Fatal(err)
	}

	testSourceData := &templateData{
		filename:            "error_code_func_test.go",
		Year:                time.Now().Year(),
		ErrorCodes:          errorcode.ErrorCodes,
		ReconcileErrorCodes: errorcode.ReconcileErrorCodes,
		Tmpl:                template.Must(template.New("testSource").Parse(testSource)),
	}
	if err := testSourceData.dump(); err != nil {
		log.Fatal(err)
	}

	logf("Done.")
}

const (
	source = `// Copyright {{.Year}} The baidubce Cloud Container Engine (CCE) AUTHORS. All rights reserved.

// Code generated by gen_error_code_func; DO NOT EDIT.

package errorcode

{{range $index, $elem := .ErrorCodes}}
// Return {{$elem.Code}} error code with certain msg
// if no msg is specified, ErrorCode.Message is set to {{$elem.Code}}
func New{{$elem.Code}}(msg ...string) *ErrorCode {
	ret := ErrorCodes[{{$index}}].DeepCopy()
	if len(msg) == 0 {
		msg = []string{ret.Code}
	}
	ret.Message = msg[0]
	return ret
}
{{end}}

{{range $index, $elem := .ReconcileErrorCodes}}
// Return {{$elem.Code}} error code with certain msg
// if no msg is specified, ErrorCode.Message is set to {{$elem.Code}}
func New{{$elem.Code}}(msg ...string) *ErrorCode {
	ret := ReconcileErrorCodes[{{$index}}].DeepCopy()
	if len(msg) == 0 {
		msg = []string{ret.Code}
	}
	ret.Message = msg[0]
	return ret
}
{{end}}
`
	testSource = `// Copyright {{.Year}} The baidubce Cloud Container Engine (CCE) AUTHORS. All rights reserved.

// Code generated by gen_error_code_func; DO NOT EDIT.

package errorcode

import (
	"testing"

	"github.com/google/go-cmp/cmp"
)

{{range $index, $elem := .ErrorCodes}}
func TestNew{{$elem.Code}}(t *testing.T) {
	simpleCode := ErrorCodes[{{$index}}].DeepCopy()
	simpleCode.Message = simpleCode.Code

	codeWithErrMsg := ErrorCodes[{{$index}}].DeepCopy()
	errMsg := "i am error message"
	codeWithErrMsg.Message = errMsg

	type args struct {
		msg []string
	}
	tests := []struct {
		name         string
		args         args
		HasCCEPrefix bool
		want         *ErrorCode
	}{
		// All test cases.
		{
			name: "simple case",
			want: simpleCode,
		},
		{
			name: "non-empty msg case",
			args: args{
				msg: []string{errMsg},
			},
			want: codeWithErrMsg,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := New{{$elem.Code}}(tt.args.msg...); !cmp.Equal(got, tt.want) {
				t.Errorf("New{{$elem.Code}}() = %v, want %v, diff is %s", got, tt.want, cmp.Diff(got, tt.want))
			}
		})
	}
}
{{end}}

{{range $index, $elem := .ReconcileErrorCodes}}
func TestNew{{$elem.Code}}(t *testing.T) {
	simpleCode := ReconcileErrorCodes[{{$index}}].DeepCopy()
	simpleCode.Message = simpleCode.Code

	codeWithErrMsg := ReconcileErrorCodes[{{$index}}].DeepCopy()
	errMsg := "i am error message"
	codeWithErrMsg.Message = errMsg

	type args struct {
		msg []string
	}
	tests := []struct {
		name         string
		args         args
		HasCCEPrefix bool
		want         *ErrorCode
	}{
		// All test cases.
		{
			name: "simple case",
			want: simpleCode,
		},
		{
			name: "non-empty msg case",
			args: args{
				msg: []string{errMsg},
			},
			want: codeWithErrMsg,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := New{{$elem.Code}}(tt.args.msg...); !cmp.Equal(got, tt.want) {
				t.Errorf("New{{$elem.Code}}() = %v, want %v, diff is %s", got, tt.want, cmp.Diff(got, tt.want))
			}
		})
	}
}
{{end}}
`
)
