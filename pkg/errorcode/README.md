# errorcode package

实现了ErrorCode生成、传递和读取.

支持向`error`中嵌入/从`error`中提取ErrorCode，当底层生成的ErrorCode嵌入在`error`中返回后，在上层函数中可以直接读取内嵌在`error`的ErrorCode.

若`error`中没有内嵌ErrorCode，读取将返回默认 ErrorCode (InternalServerError)

## 说明

ErrorCode struct

```go
type ErrorCode struct {
    StatusCode   int    // http status
    Code         string // Code word, Like InternalServerError, AccessDenied, etc
    Message      string // extra message
    HasCCEPrefix bool // whether to add CCE. prefix before Code word string, typically for FE Code filter
}
```

可以在error_code.go中的 `ErrorCodes` slice中新增ErrorCode，make后会以 `New+ErrorCode名称` 自动生成函数，用来生成相应的ErrorCode，接受message作为参数（可以不传，不传的话默认message和Code word一致）.

如，新增如下ErrorCode

```go
&ErrorCode{
    StatusCode: http.StatusBadRequest,
    Code:       "MyErrorCode",
},
```

之后，可以通过 `NewMyErrorCode(msg)` 生成该ErrorCode，其中msg可以不传.

- `errorcode.NewError(code *ErrorCode, err error) error`: 将ErrorCode嵌入error中并返回
- `errorcode.Errorf(format string, args ...interface{}) error`: Wrap `fmt.Errorf`，实现ErrorCode在多级函数调用间的传递
- `errorcode.GetCode(err error) *errorcode.ErrorCode`: 从err中提取Code，若不存在，返回默认 ErrorCode (InternalServerError)

## 例子

`children()` 使用向原始error嵌入了ErrorCode后返回，通过`GetCode`获取内嵌在error中的ErrorCode

`defaultChildren()` 直接返回了原始error，`GetCode`返回默认ErrorCode

`recursiveChildren` 使用 `errorcode.Errorf` 将底层生成的Code经多级包装后返回顶层

```go
package main

import (
    "fmt"

    "icode.baidu.com/baidu/cce-test/e2e-test/pkg/errorcode"
)

func main() {
    // custom ErrorCode generated in children()
    _, err := children()
    if err != nil {
        code := errorcode.GetCode(err)
        fmt.Println(code) // {StatusCode:403, Code:"AccessDenied", Message:"error message"}
    }

    // no ErrorCode generated in defaultChildren()
    _, err = defaultChildren()
    if err != nil {
        code := errorcode.GetCode(err)
        fmt.Println(code) // {StatusCode:500, Code:"InternalServerError", Message:"InternalServerError"}
    }

    // ErrorCode is delivered in multi-level func call
    _, err = recursiveChildren(3)
    if err != nil {
        fmt.Println(err) // depth 3 err: depth 2 err: depth 1 err: some error occurs
        code := errorcode.GetCode(err)
        fmt.Println(code) // {StatusCode:400, Code:"InvalidParam", Message:"depth is 0"}
    }
}

func children() (interface{}, error) {
    err := fmt.Errorf("some error occurs")
    if err != nil {
        return nil, errorcode.NewError(errorcode.NewAccessDenied("error message"), err)
    }
    return nil, nil
}

func defaultChildren() (interface{}, error) {
    err := fmt.Errorf("some error occurs")
    if err != nil {
        return nil, err
    }
    return nil, nil
}

func recursiveChildren(depth int) (interface{}, error) {
    if depth == 0 {
        return nil, errorcode.NewError(errorcode.NewInvalidParam("depth is 0"), fmt.Errorf("some error occurs"))
    }
    _, err := recursiveChildren(depth - 1)
    if err != nil {
        // ErrorCode is conveyed from err to returned err
        return nil, errorcode.Errorf("depth %d err: %v", depth, err)
    }
    return nil, nil
}
```
