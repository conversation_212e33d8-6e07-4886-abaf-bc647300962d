//go:generate go run gen_error_code_func.go

package errorcode

import (
	"errors"
	"fmt"
	"net/http"

	"github.com/google/go-github/github"
)

// Error wraps error with an ErrorCode for response
type Error struct {
	Code *ErrorCode
	error
}

func (e *Error) String() string {
	return e.Error()
}

// Unwrap implements errors.Wrapper.
func (e *Error) Unwrap() error {
	return e.error
}

// NewError creates an Error with code and err
func NewError(code *ErrorCode, err error) error {
	return &Error{
		Code:  code,
		error: err,
	}
}

// GetCode deduce ErrorCode from err, that is,
// finds the first error in err's chain that is of type *Error and return its Code,
// or return default ErrorCode (InternalServerError)
func GetCode(err error) *ErrorCode {
	var e *Error
	if errors.As(err, &e) {
		if e.Code != nil {
			return e.Code
		}
	}
	var ec *ErrorCode
	if errors.As(err, &ec) {
		return ec
	}
	return NewInternalServerError()
}

// NewCode generates a new ErrorCode
func NewCode(statusCode int, code string) *ErrorCode {
	return &ErrorCode{
		StatusCode: statusCode,
		Code:       code,
	}
}

// Errorf wraps fmt.Errorf with ErrorCode aware
func Errorf(format string, args ...any) error {
	code := NewInternalServerError()
	for _, a := range args {
		err, ok := a.(error)
		if !ok {
			continue
		}
		var e *Error
		if errors.As(err, &e) {
			if e.Code != nil {
				code = e.Code
			}
		}
	}
	return NewError(code, fmt.Errorf(format, args...))
}

// ErrorCode - 定义 CCE 返回给前端错误码
type ErrorCode struct {
	StatusCode int
	Code       string
	Message    string
	Suggestion string

	// level is set to private to return default value if not set.
	// Use SetLevel/GetLevel to access it.
	level Level
}

func (ec *ErrorCode) SetLevel(l Level) *ErrorCode {
	if ec != nil {
		ec.level = l
	}
	return ec
}

func (ec *ErrorCode) GetLevel() Level {
	if ec != nil && ec.level != "" {
		return ec.level
	}
	return LevelByAdmin
}

// String print struct in string easy for human reading
func (ec *ErrorCode) String() string {
	return github.Stringify(ec)
}

// Error implements error interface
func (ec *ErrorCode) Error() string {
	return ec.Code + ": " + ec.Message
}

// DeepCopy generate a deep copy from ec
func (ec *ErrorCode) DeepCopy() *ErrorCode {
	return &ErrorCode{
		StatusCode: ec.StatusCode,
		Code:       ec.Code,
		Message:    ec.Message,
		Suggestion: ec.Suggestion,
		level:      ec.level,
	}
}

// WithMsg generates a deep copy of ec and replace its Message with msg
// if msg is not set, Message will be the same as Code string
func (ec *ErrorCode) WithMsg(msg ...string) *ErrorCode {
	cp := ec.DeepCopy()
	if len(msg) > 0 {
		cp.Message = msg[0]
	} else {
		cp.Message = ec.Code
	}
	return cp
}

// Equal return ture = ec the same as code
func (ec *ErrorCode) Equal(code *ErrorCode) bool {
	if code == nil {
		return false
	}

	if ec.StatusCode == code.StatusCode && ec.Code == code.Code && ec.level == code.level {
		return true
	}

	return false
}

// ErrorCodes - CCE 统一HTTP错误码
var ErrorCodes = []*ErrorCode{
	// bce public error code
	{
		StatusCode: http.StatusInternalServerError,
		Code:       "InternalServerError", // default ErrorCode
	},
	{
		StatusCode: http.StatusForbidden,
		Code:       "AccessDenied",
	},
	{
		StatusCode: http.StatusBadRequest,
		Code:       "InappropriateJSON",
	},
	{
		StatusCode: http.StatusForbidden,
		Code:       "InvalidAccessKeyID",
	},
	{
		StatusCode: http.StatusBadRequest,
		Code:       "InvalidHTTPAuthHeader",
	},
	{
		StatusCode: http.StatusBadRequest,
		Code:       "InvalidHTTPRequest",
	},
	{
		StatusCode: http.StatusBadRequest,
		Code:       "InvalidURI",
	},
	{
		StatusCode: http.StatusBadRequest,
		Code:       "MalformedJSON",
	},
	{
		StatusCode: http.StatusNotFound,
		Code:       "InvalidVersion",
	},
	{
		StatusCode: http.StatusForbidden,
		Code:       "OptInRequired",
	},
	{
		StatusCode: http.StatusPreconditionFailed,
		Code:       "PreconditionFailed",
	},
	{
		StatusCode: http.StatusBadRequest,
		Code:       "RequestExpired",
	},
	{
		StatusCode: http.StatusForbidden,
		Code:       "IdempotentParameterMismatch",
	},
	{
		StatusCode: http.StatusBadRequest,
		Code:       "SignatureDoesNotMatch",
	},

	// container-image-service specific ErrorCode
	{
		StatusCode: http.StatusBadRequest,
		Code:       "InvalidParam",
	},
	{
		StatusCode: http.StatusNotFound,
		Code:       "NoSuchObject",
	},
	{
		StatusCode: http.StatusConflict,
		Code:       "ObjectConflict",
	},
	{
		StatusCode: http.StatusNotFound,
		Code:       "BuildNotComplete",
	},
	{
		StatusCode: http.StatusForbidden,
		Code:       "TagNotEmpty",
	},
	{
		StatusCode: http.StatusUnauthorized,
		Code:       "InvalidAuth",
	},

	// cce-charts-service specific ErrorCode
	{
		StatusCode: http.StatusNotFound,
		Code:       "ChartPrivateAccountNotFound",
	},
	{
		StatusCode: http.StatusMethodNotAllowed,
		Code:       "MethodNotAllowed",
	},
	{
		StatusCode: http.StatusForbidden,
		Code:       "ClientNotSupported",
	},
	{
		StatusCode: http.StatusNotFound,
		Code:       "ChartPrivateRepoNotFound",
	},
	{
		StatusCode: http.StatusNotFound,
		Code:       "ChartNotFound",
	},
	{
		StatusCode: http.StatusBadRequest,
		Code:       "ChartPackageBroken",
	},
	{
		StatusCode: http.StatusRequestEntityTooLarge,
		Code:       "ChartPackageTooLarge",
	},
	{
		StatusCode: http.StatusConflict,
		Code:       "ChartContentMismatch",
	},
	{
		StatusCode: http.StatusForbidden,
		Code:       "ChartQuotaExceeded",
	},
	{
		StatusCode: http.StatusInternalServerError,
		Code:       "ChartDeletionFailed",
	},
	{
		StatusCode: http.StatusNotFound,
		Code:       "HelmReleaseNotFound",
	},
	{
		StatusCode: http.StatusConflict,
		Code:       "HelmReleaseNameAlreadyExist",
	},
	{
		StatusCode: http.StatusBadRequest,
		Code:       "HelmReleaseNameIllegal",
	},
	{
		StatusCode: http.StatusBadRequest,
		Code:       "HelmReleaseValuesIllegal",
	},
	{
		StatusCode: http.StatusNotFound,
		Code:       "TillerNotFound",
	},
	{
		StatusCode: http.StatusServiceUnavailable,
		Code:       "TillerNotReady",
	},
	{
		StatusCode: http.StatusServiceUnavailable,
		Code:       "TillerNotCompatible",
	},
	{
		StatusCode: http.StatusServiceUnavailable,
		Code:       "ClusterMasterUnavailable",
	},
	{
		StatusCode: http.StatusServiceUnavailable,
		Code:       "HelmClientError",
	},

	// cce-cluster-service specific ErrorCode
	{
		StatusCode: http.StatusNotFound,
		Code:       "ClusterNotFound",
	},
	{
		StatusCode: http.StatusNotFound,
		Code:       "ClusterNotExposedToPublic",
	},
	{
		StatusCode: http.StatusBadRequest,
		Code:       "InvaildUserID",
	},
	{
		StatusCode: http.StatusBadRequest,
		Code:       "InvaildClusterName",
	},
	{
		StatusCode: http.StatusBadRequest,
		Code:       "InstanceAlreadyExists",
	},
	{
		StatusCode: http.StatusBadRequest,
		Code:       "InstancesCountBeyondLimit",
	},
	{
		StatusCode: http.StatusBadRequest,
		Code:       "ExistedInstanceIDDuplicated",
	},
	{
		StatusCode: http.StatusNotFound,
		Code:       "InstanceNotFound",
	},
	{
		StatusCode: http.StatusNotFound,
		Code:       "VPCIDNotExists",
	},
	{
		StatusCode: http.StatusBadRequest,
		Code:       "MasterFlavorInvalid",
	},
	{
		StatusCode: http.StatusBadRequest,
		Code:       "ClusterHAInvalid",
	},
	{
		StatusCode: http.StatusBadRequest,
		Code:       "ContainerNetworkCIDRInvalid",
	},
	{
		StatusCode: http.StatusNotFound,
		Code:       "InstanceGroupNotFound",
	},
	{
		StatusCode: http.StatusBadRequest,
		Code:       "ClientInfoImproperlyEncoded",
	},
	{
		StatusCode: http.StatusPreconditionFailed,
		Code:       "RegionNotAvailable",
	},
	{
		StatusCode: http.StatusPreconditionFailed,
		Code:       "BLBNotAvailable",
	},
	{
		StatusCode: http.StatusForbidden,
		Code:       "MasterCannotDeletedOrRemoved",
	},
	{
		StatusCode: http.StatusForbidden,
		Code:       "ManagedClusterMinNodeNumLimit",
	},
	{
		StatusCode: http.StatusBadRequest,
		Code:       "ConflictNetworkTopology",
	},
	{
		StatusCode: http.StatusForbidden,
		Code:       "CurrentPhaseCannotRetry",
	},

	// AIJob
	NewCode(http.StatusBadRequest, "AIJobMissingCPUOrMemoryRequest"),
	NewCode(http.StatusPreconditionFailed, "EssentialPluginNotFound"),
	NewCode(http.StatusPreconditionFailed, "AIOperatorNotFound"),
	NewCode(http.StatusForbidden, "AIQueueCannotDelete"),
	{
		StatusCode: http.StatusForbidden,
		Code:       "RBACUnauthorized",
	},

	// Workflow
	{
		StatusCode: http.StatusBadRequest,
		Code:       "MultiWorkflowExistInCluster",
	},
	// Workflow
	{
		StatusCode: http.StatusBadRequest,
		Code:       "TagNotExistWithRelationTag",
	},
	{
		StatusCode: http.StatusPreconditionFailed,
		Code:       "CCEClusterQuotaExceed",
	},
	{
		StatusCode: http.StatusPreconditionFailed,
		Code:       "CCEInstanceQuotaExceed",
	},
	NewCode(http.StatusForbidden, "NRNotInstalled"),
	NewCode(http.StatusForbidden, "CCEInstanceNumberExceed"),
}
