// Copyright 2023 The baidubce Cloud Container Engine (CCE) AUTHORS. All rights reserved.

// Code generated by gen_error_code_func; DO NOT EDIT.

package errorcode

import (
	"testing"

	"github.com/google/go-cmp/cmp"
)

func TestNewInternalServerError(t *testing.T) {
	simpleCode := ErrorCodes[0].DeepCopy()
	simpleCode.Message = simpleCode.Code

	codeWithErrMsg := ErrorCodes[0].DeepCopy()
	errMsg := "i am error message"
	codeWithErrMsg.Message = errMsg

	type args struct {
		msg []string
	}
	tests := []struct {
		name         string
		args         args
		HasCCEPrefix bool
		want         *ErrorCode
	}{
		// All test cases.
		{
			name: "simple case",
			want: simpleCode,
		},
		{
			name: "non-empty msg case",
			args: args{
				msg: []string{errMsg},
			},
			want: codeWithErrMsg,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewInternalServerError(tt.args.msg...); !cmp.Equal(got, tt.want) {
				t.<PERSON>("NewInternalServerError() = %v, want %v, diff is %s", got, tt.want, cmp.Diff(got, tt.want))
			}
		})
	}
}

func TestNewAccessDenied(t *testing.T) {
	simpleCode := ErrorCodes[1].DeepCopy()
	simpleCode.Message = simpleCode.Code

	codeWithErrMsg := ErrorCodes[1].DeepCopy()
	errMsg := "i am error message"
	codeWithErrMsg.Message = errMsg

	type args struct {
		msg []string
	}
	tests := []struct {
		name         string
		args         args
		HasCCEPrefix bool
		want         *ErrorCode
	}{
		// All test cases.
		{
			name: "simple case",
			want: simpleCode,
		},
		{
			name: "non-empty msg case",
			args: args{
				msg: []string{errMsg},
			},
			want: codeWithErrMsg,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewAccessDenied(tt.args.msg...); !cmp.Equal(got, tt.want) {
				t.Errorf("NewAccessDenied() = %v, want %v, diff is %s", got, tt.want, cmp.Diff(got, tt.want))
			}
		})
	}
}

func TestNewInappropriateJSON(t *testing.T) {
	simpleCode := ErrorCodes[2].DeepCopy()
	simpleCode.Message = simpleCode.Code

	codeWithErrMsg := ErrorCodes[2].DeepCopy()
	errMsg := "i am error message"
	codeWithErrMsg.Message = errMsg

	type args struct {
		msg []string
	}
	tests := []struct {
		name         string
		args         args
		HasCCEPrefix bool
		want         *ErrorCode
	}{
		// All test cases.
		{
			name: "simple case",
			want: simpleCode,
		},
		{
			name: "non-empty msg case",
			args: args{
				msg: []string{errMsg},
			},
			want: codeWithErrMsg,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewInappropriateJSON(tt.args.msg...); !cmp.Equal(got, tt.want) {
				t.Errorf("NewInappropriateJSON() = %v, want %v, diff is %s", got, tt.want, cmp.Diff(got, tt.want))
			}
		})
	}
}

func TestNewInvalidAccessKeyID(t *testing.T) {
	simpleCode := ErrorCodes[3].DeepCopy()
	simpleCode.Message = simpleCode.Code

	codeWithErrMsg := ErrorCodes[3].DeepCopy()
	errMsg := "i am error message"
	codeWithErrMsg.Message = errMsg

	type args struct {
		msg []string
	}
	tests := []struct {
		name         string
		args         args
		HasCCEPrefix bool
		want         *ErrorCode
	}{
		// All test cases.
		{
			name: "simple case",
			want: simpleCode,
		},
		{
			name: "non-empty msg case",
			args: args{
				msg: []string{errMsg},
			},
			want: codeWithErrMsg,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewInvalidAccessKeyID(tt.args.msg...); !cmp.Equal(got, tt.want) {
				t.Errorf("NewInvalidAccessKeyID() = %v, want %v, diff is %s", got, tt.want, cmp.Diff(got, tt.want))
			}
		})
	}
}

func TestNewInvalidHTTPAuthHeader(t *testing.T) {
	simpleCode := ErrorCodes[4].DeepCopy()
	simpleCode.Message = simpleCode.Code

	codeWithErrMsg := ErrorCodes[4].DeepCopy()
	errMsg := "i am error message"
	codeWithErrMsg.Message = errMsg

	type args struct {
		msg []string
	}
	tests := []struct {
		name         string
		args         args
		HasCCEPrefix bool
		want         *ErrorCode
	}{
		// All test cases.
		{
			name: "simple case",
			want: simpleCode,
		},
		{
			name: "non-empty msg case",
			args: args{
				msg: []string{errMsg},
			},
			want: codeWithErrMsg,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewInvalidHTTPAuthHeader(tt.args.msg...); !cmp.Equal(got, tt.want) {
				t.Errorf("NewInvalidHTTPAuthHeader() = %v, want %v, diff is %s", got, tt.want, cmp.Diff(got, tt.want))
			}
		})
	}
}

func TestNewInvalidHTTPRequest(t *testing.T) {
	simpleCode := ErrorCodes[5].DeepCopy()
	simpleCode.Message = simpleCode.Code

	codeWithErrMsg := ErrorCodes[5].DeepCopy()
	errMsg := "i am error message"
	codeWithErrMsg.Message = errMsg

	type args struct {
		msg []string
	}
	tests := []struct {
		name         string
		args         args
		HasCCEPrefix bool
		want         *ErrorCode
	}{
		// All test cases.
		{
			name: "simple case",
			want: simpleCode,
		},
		{
			name: "non-empty msg case",
			args: args{
				msg: []string{errMsg},
			},
			want: codeWithErrMsg,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewInvalidHTTPRequest(tt.args.msg...); !cmp.Equal(got, tt.want) {
				t.Errorf("NewInvalidHTTPRequest() = %v, want %v, diff is %s", got, tt.want, cmp.Diff(got, tt.want))
			}
		})
	}
}

func TestNewInvalidURI(t *testing.T) {
	simpleCode := ErrorCodes[6].DeepCopy()
	simpleCode.Message = simpleCode.Code

	codeWithErrMsg := ErrorCodes[6].DeepCopy()
	errMsg := "i am error message"
	codeWithErrMsg.Message = errMsg

	type args struct {
		msg []string
	}
	tests := []struct {
		name         string
		args         args
		HasCCEPrefix bool
		want         *ErrorCode
	}{
		// All test cases.
		{
			name: "simple case",
			want: simpleCode,
		},
		{
			name: "non-empty msg case",
			args: args{
				msg: []string{errMsg},
			},
			want: codeWithErrMsg,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewInvalidURI(tt.args.msg...); !cmp.Equal(got, tt.want) {
				t.Errorf("NewInvalidURI() = %v, want %v, diff is %s", got, tt.want, cmp.Diff(got, tt.want))
			}
		})
	}
}

func TestNewMalformedJSON(t *testing.T) {
	simpleCode := ErrorCodes[7].DeepCopy()
	simpleCode.Message = simpleCode.Code

	codeWithErrMsg := ErrorCodes[7].DeepCopy()
	errMsg := "i am error message"
	codeWithErrMsg.Message = errMsg

	type args struct {
		msg []string
	}
	tests := []struct {
		name         string
		args         args
		HasCCEPrefix bool
		want         *ErrorCode
	}{
		// All test cases.
		{
			name: "simple case",
			want: simpleCode,
		},
		{
			name: "non-empty msg case",
			args: args{
				msg: []string{errMsg},
			},
			want: codeWithErrMsg,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewMalformedJSON(tt.args.msg...); !cmp.Equal(got, tt.want) {
				t.Errorf("NewMalformedJSON() = %v, want %v, diff is %s", got, tt.want, cmp.Diff(got, tt.want))
			}
		})
	}
}

func TestNewInvalidVersion(t *testing.T) {
	simpleCode := ErrorCodes[8].DeepCopy()
	simpleCode.Message = simpleCode.Code

	codeWithErrMsg := ErrorCodes[8].DeepCopy()
	errMsg := "i am error message"
	codeWithErrMsg.Message = errMsg

	type args struct {
		msg []string
	}
	tests := []struct {
		name         string
		args         args
		HasCCEPrefix bool
		want         *ErrorCode
	}{
		// All test cases.
		{
			name: "simple case",
			want: simpleCode,
		},
		{
			name: "non-empty msg case",
			args: args{
				msg: []string{errMsg},
			},
			want: codeWithErrMsg,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewInvalidVersion(tt.args.msg...); !cmp.Equal(got, tt.want) {
				t.Errorf("NewInvalidVersion() = %v, want %v, diff is %s", got, tt.want, cmp.Diff(got, tt.want))
			}
		})
	}
}

func TestNewOptInRequired(t *testing.T) {
	simpleCode := ErrorCodes[9].DeepCopy()
	simpleCode.Message = simpleCode.Code

	codeWithErrMsg := ErrorCodes[9].DeepCopy()
	errMsg := "i am error message"
	codeWithErrMsg.Message = errMsg

	type args struct {
		msg []string
	}
	tests := []struct {
		name         string
		args         args
		HasCCEPrefix bool
		want         *ErrorCode
	}{
		// All test cases.
		{
			name: "simple case",
			want: simpleCode,
		},
		{
			name: "non-empty msg case",
			args: args{
				msg: []string{errMsg},
			},
			want: codeWithErrMsg,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewOptInRequired(tt.args.msg...); !cmp.Equal(got, tt.want) {
				t.Errorf("NewOptInRequired() = %v, want %v, diff is %s", got, tt.want, cmp.Diff(got, tt.want))
			}
		})
	}
}

func TestNewPreconditionFailed(t *testing.T) {
	simpleCode := ErrorCodes[10].DeepCopy()
	simpleCode.Message = simpleCode.Code

	codeWithErrMsg := ErrorCodes[10].DeepCopy()
	errMsg := "i am error message"
	codeWithErrMsg.Message = errMsg

	type args struct {
		msg []string
	}
	tests := []struct {
		name         string
		args         args
		HasCCEPrefix bool
		want         *ErrorCode
	}{
		// All test cases.
		{
			name: "simple case",
			want: simpleCode,
		},
		{
			name: "non-empty msg case",
			args: args{
				msg: []string{errMsg},
			},
			want: codeWithErrMsg,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewPreconditionFailed(tt.args.msg...); !cmp.Equal(got, tt.want) {
				t.Errorf("NewPreconditionFailed() = %v, want %v, diff is %s", got, tt.want, cmp.Diff(got, tt.want))
			}
		})
	}
}

func TestNewRequestExpired(t *testing.T) {
	simpleCode := ErrorCodes[11].DeepCopy()
	simpleCode.Message = simpleCode.Code

	codeWithErrMsg := ErrorCodes[11].DeepCopy()
	errMsg := "i am error message"
	codeWithErrMsg.Message = errMsg

	type args struct {
		msg []string
	}
	tests := []struct {
		name         string
		args         args
		HasCCEPrefix bool
		want         *ErrorCode
	}{
		// All test cases.
		{
			name: "simple case",
			want: simpleCode,
		},
		{
			name: "non-empty msg case",
			args: args{
				msg: []string{errMsg},
			},
			want: codeWithErrMsg,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewRequestExpired(tt.args.msg...); !cmp.Equal(got, tt.want) {
				t.Errorf("NewRequestExpired() = %v, want %v, diff is %s", got, tt.want, cmp.Diff(got, tt.want))
			}
		})
	}
}

func TestNewIdempotentParameterMismatch(t *testing.T) {
	simpleCode := ErrorCodes[12].DeepCopy()
	simpleCode.Message = simpleCode.Code

	codeWithErrMsg := ErrorCodes[12].DeepCopy()
	errMsg := "i am error message"
	codeWithErrMsg.Message = errMsg

	type args struct {
		msg []string
	}
	tests := []struct {
		name         string
		args         args
		HasCCEPrefix bool
		want         *ErrorCode
	}{
		// All test cases.
		{
			name: "simple case",
			want: simpleCode,
		},
		{
			name: "non-empty msg case",
			args: args{
				msg: []string{errMsg},
			},
			want: codeWithErrMsg,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewIdempotentParameterMismatch(tt.args.msg...); !cmp.Equal(got, tt.want) {
				t.Errorf("NewIdempotentParameterMismatch() = %v, want %v, diff is %s", got, tt.want, cmp.Diff(got, tt.want))
			}
		})
	}
}

func TestNewSignatureDoesNotMatch(t *testing.T) {
	simpleCode := ErrorCodes[13].DeepCopy()
	simpleCode.Message = simpleCode.Code

	codeWithErrMsg := ErrorCodes[13].DeepCopy()
	errMsg := "i am error message"
	codeWithErrMsg.Message = errMsg

	type args struct {
		msg []string
	}
	tests := []struct {
		name         string
		args         args
		HasCCEPrefix bool
		want         *ErrorCode
	}{
		// All test cases.
		{
			name: "simple case",
			want: simpleCode,
		},
		{
			name: "non-empty msg case",
			args: args{
				msg: []string{errMsg},
			},
			want: codeWithErrMsg,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewSignatureDoesNotMatch(tt.args.msg...); !cmp.Equal(got, tt.want) {
				t.Errorf("NewSignatureDoesNotMatch() = %v, want %v, diff is %s", got, tt.want, cmp.Diff(got, tt.want))
			}
		})
	}
}

func TestNewInvalidParam(t *testing.T) {
	simpleCode := ErrorCodes[14].DeepCopy()
	simpleCode.Message = simpleCode.Code

	codeWithErrMsg := ErrorCodes[14].DeepCopy()
	errMsg := "i am error message"
	codeWithErrMsg.Message = errMsg

	type args struct {
		msg []string
	}
	tests := []struct {
		name         string
		args         args
		HasCCEPrefix bool
		want         *ErrorCode
	}{
		// All test cases.
		{
			name: "simple case",
			want: simpleCode,
		},
		{
			name: "non-empty msg case",
			args: args{
				msg: []string{errMsg},
			},
			want: codeWithErrMsg,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewInvalidParam(tt.args.msg...); !cmp.Equal(got, tt.want) {
				t.Errorf("NewInvalidParam() = %v, want %v, diff is %s", got, tt.want, cmp.Diff(got, tt.want))
			}
		})
	}
}

func TestNewNoSuchObject(t *testing.T) {
	simpleCode := ErrorCodes[15].DeepCopy()
	simpleCode.Message = simpleCode.Code

	codeWithErrMsg := ErrorCodes[15].DeepCopy()
	errMsg := "i am error message"
	codeWithErrMsg.Message = errMsg

	type args struct {
		msg []string
	}
	tests := []struct {
		name         string
		args         args
		HasCCEPrefix bool
		want         *ErrorCode
	}{
		// All test cases.
		{
			name: "simple case",
			want: simpleCode,
		},
		{
			name: "non-empty msg case",
			args: args{
				msg: []string{errMsg},
			},
			want: codeWithErrMsg,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewNoSuchObject(tt.args.msg...); !cmp.Equal(got, tt.want) {
				t.Errorf("NewNoSuchObject() = %v, want %v, diff is %s", got, tt.want, cmp.Diff(got, tt.want))
			}
		})
	}
}

func TestNewObjectConflict(t *testing.T) {
	simpleCode := ErrorCodes[16].DeepCopy()
	simpleCode.Message = simpleCode.Code

	codeWithErrMsg := ErrorCodes[16].DeepCopy()
	errMsg := "i am error message"
	codeWithErrMsg.Message = errMsg

	type args struct {
		msg []string
	}
	tests := []struct {
		name         string
		args         args
		HasCCEPrefix bool
		want         *ErrorCode
	}{
		// All test cases.
		{
			name: "simple case",
			want: simpleCode,
		},
		{
			name: "non-empty msg case",
			args: args{
				msg: []string{errMsg},
			},
			want: codeWithErrMsg,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewObjectConflict(tt.args.msg...); !cmp.Equal(got, tt.want) {
				t.Errorf("NewObjectConflict() = %v, want %v, diff is %s", got, tt.want, cmp.Diff(got, tt.want))
			}
		})
	}
}

func TestNewBuildNotComplete(t *testing.T) {
	simpleCode := ErrorCodes[17].DeepCopy()
	simpleCode.Message = simpleCode.Code

	codeWithErrMsg := ErrorCodes[17].DeepCopy()
	errMsg := "i am error message"
	codeWithErrMsg.Message = errMsg

	type args struct {
		msg []string
	}
	tests := []struct {
		name         string
		args         args
		HasCCEPrefix bool
		want         *ErrorCode
	}{
		// All test cases.
		{
			name: "simple case",
			want: simpleCode,
		},
		{
			name: "non-empty msg case",
			args: args{
				msg: []string{errMsg},
			},
			want: codeWithErrMsg,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewBuildNotComplete(tt.args.msg...); !cmp.Equal(got, tt.want) {
				t.Errorf("NewBuildNotComplete() = %v, want %v, diff is %s", got, tt.want, cmp.Diff(got, tt.want))
			}
		})
	}
}

func TestNewTagNotEmpty(t *testing.T) {
	simpleCode := ErrorCodes[18].DeepCopy()
	simpleCode.Message = simpleCode.Code

	codeWithErrMsg := ErrorCodes[18].DeepCopy()
	errMsg := "i am error message"
	codeWithErrMsg.Message = errMsg

	type args struct {
		msg []string
	}
	tests := []struct {
		name         string
		args         args
		HasCCEPrefix bool
		want         *ErrorCode
	}{
		// All test cases.
		{
			name: "simple case",
			want: simpleCode,
		},
		{
			name: "non-empty msg case",
			args: args{
				msg: []string{errMsg},
			},
			want: codeWithErrMsg,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewTagNotEmpty(tt.args.msg...); !cmp.Equal(got, tt.want) {
				t.Errorf("NewTagNotEmpty() = %v, want %v, diff is %s", got, tt.want, cmp.Diff(got, tt.want))
			}
		})
	}
}

func TestNewInvalidAuth(t *testing.T) {
	simpleCode := ErrorCodes[19].DeepCopy()
	simpleCode.Message = simpleCode.Code

	codeWithErrMsg := ErrorCodes[19].DeepCopy()
	errMsg := "i am error message"
	codeWithErrMsg.Message = errMsg

	type args struct {
		msg []string
	}
	tests := []struct {
		name         string
		args         args
		HasCCEPrefix bool
		want         *ErrorCode
	}{
		// All test cases.
		{
			name: "simple case",
			want: simpleCode,
		},
		{
			name: "non-empty msg case",
			args: args{
				msg: []string{errMsg},
			},
			want: codeWithErrMsg,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewInvalidAuth(tt.args.msg...); !cmp.Equal(got, tt.want) {
				t.Errorf("NewInvalidAuth() = %v, want %v, diff is %s", got, tt.want, cmp.Diff(got, tt.want))
			}
		})
	}
}

func TestNewChartPrivateAccountNotFound(t *testing.T) {
	simpleCode := ErrorCodes[20].DeepCopy()
	simpleCode.Message = simpleCode.Code

	codeWithErrMsg := ErrorCodes[20].DeepCopy()
	errMsg := "i am error message"
	codeWithErrMsg.Message = errMsg

	type args struct {
		msg []string
	}
	tests := []struct {
		name         string
		args         args
		HasCCEPrefix bool
		want         *ErrorCode
	}{
		// All test cases.
		{
			name: "simple case",
			want: simpleCode,
		},
		{
			name: "non-empty msg case",
			args: args{
				msg: []string{errMsg},
			},
			want: codeWithErrMsg,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewChartPrivateAccountNotFound(tt.args.msg...); !cmp.Equal(got, tt.want) {
				t.Errorf("NewChartPrivateAccountNotFound() = %v, want %v, diff is %s", got, tt.want, cmp.Diff(got, tt.want))
			}
		})
	}
}

func TestNewMethodNotAllowed(t *testing.T) {
	simpleCode := ErrorCodes[21].DeepCopy()
	simpleCode.Message = simpleCode.Code

	codeWithErrMsg := ErrorCodes[21].DeepCopy()
	errMsg := "i am error message"
	codeWithErrMsg.Message = errMsg

	type args struct {
		msg []string
	}
	tests := []struct {
		name         string
		args         args
		HasCCEPrefix bool
		want         *ErrorCode
	}{
		// All test cases.
		{
			name: "simple case",
			want: simpleCode,
		},
		{
			name: "non-empty msg case",
			args: args{
				msg: []string{errMsg},
			},
			want: codeWithErrMsg,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewMethodNotAllowed(tt.args.msg...); !cmp.Equal(got, tt.want) {
				t.Errorf("NewMethodNotAllowed() = %v, want %v, diff is %s", got, tt.want, cmp.Diff(got, tt.want))
			}
		})
	}
}

func TestNewClientNotSupported(t *testing.T) {
	simpleCode := ErrorCodes[22].DeepCopy()
	simpleCode.Message = simpleCode.Code

	codeWithErrMsg := ErrorCodes[22].DeepCopy()
	errMsg := "i am error message"
	codeWithErrMsg.Message = errMsg

	type args struct {
		msg []string
	}
	tests := []struct {
		name         string
		args         args
		HasCCEPrefix bool
		want         *ErrorCode
	}{
		// All test cases.
		{
			name: "simple case",
			want: simpleCode,
		},
		{
			name: "non-empty msg case",
			args: args{
				msg: []string{errMsg},
			},
			want: codeWithErrMsg,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewClientNotSupported(tt.args.msg...); !cmp.Equal(got, tt.want) {
				t.Errorf("NewClientNotSupported() = %v, want %v, diff is %s", got, tt.want, cmp.Diff(got, tt.want))
			}
		})
	}
}

func TestNewChartPrivateRepoNotFound(t *testing.T) {
	simpleCode := ErrorCodes[23].DeepCopy()
	simpleCode.Message = simpleCode.Code

	codeWithErrMsg := ErrorCodes[23].DeepCopy()
	errMsg := "i am error message"
	codeWithErrMsg.Message = errMsg

	type args struct {
		msg []string
	}
	tests := []struct {
		name         string
		args         args
		HasCCEPrefix bool
		want         *ErrorCode
	}{
		// All test cases.
		{
			name: "simple case",
			want: simpleCode,
		},
		{
			name: "non-empty msg case",
			args: args{
				msg: []string{errMsg},
			},
			want: codeWithErrMsg,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewChartPrivateRepoNotFound(tt.args.msg...); !cmp.Equal(got, tt.want) {
				t.Errorf("NewChartPrivateRepoNotFound() = %v, want %v, diff is %s", got, tt.want, cmp.Diff(got, tt.want))
			}
		})
	}
}

func TestNewChartNotFound(t *testing.T) {
	simpleCode := ErrorCodes[24].DeepCopy()
	simpleCode.Message = simpleCode.Code

	codeWithErrMsg := ErrorCodes[24].DeepCopy()
	errMsg := "i am error message"
	codeWithErrMsg.Message = errMsg

	type args struct {
		msg []string
	}
	tests := []struct {
		name         string
		args         args
		HasCCEPrefix bool
		want         *ErrorCode
	}{
		// All test cases.
		{
			name: "simple case",
			want: simpleCode,
		},
		{
			name: "non-empty msg case",
			args: args{
				msg: []string{errMsg},
			},
			want: codeWithErrMsg,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewChartNotFound(tt.args.msg...); !cmp.Equal(got, tt.want) {
				t.Errorf("NewChartNotFound() = %v, want %v, diff is %s", got, tt.want, cmp.Diff(got, tt.want))
			}
		})
	}
}

func TestNewChartPackageBroken(t *testing.T) {
	simpleCode := ErrorCodes[25].DeepCopy()
	simpleCode.Message = simpleCode.Code

	codeWithErrMsg := ErrorCodes[25].DeepCopy()
	errMsg := "i am error message"
	codeWithErrMsg.Message = errMsg

	type args struct {
		msg []string
	}
	tests := []struct {
		name         string
		args         args
		HasCCEPrefix bool
		want         *ErrorCode
	}{
		// All test cases.
		{
			name: "simple case",
			want: simpleCode,
		},
		{
			name: "non-empty msg case",
			args: args{
				msg: []string{errMsg},
			},
			want: codeWithErrMsg,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewChartPackageBroken(tt.args.msg...); !cmp.Equal(got, tt.want) {
				t.Errorf("NewChartPackageBroken() = %v, want %v, diff is %s", got, tt.want, cmp.Diff(got, tt.want))
			}
		})
	}
}

func TestNewChartPackageTooLarge(t *testing.T) {
	simpleCode := ErrorCodes[26].DeepCopy()
	simpleCode.Message = simpleCode.Code

	codeWithErrMsg := ErrorCodes[26].DeepCopy()
	errMsg := "i am error message"
	codeWithErrMsg.Message = errMsg

	type args struct {
		msg []string
	}
	tests := []struct {
		name         string
		args         args
		HasCCEPrefix bool
		want         *ErrorCode
	}{
		// All test cases.
		{
			name: "simple case",
			want: simpleCode,
		},
		{
			name: "non-empty msg case",
			args: args{
				msg: []string{errMsg},
			},
			want: codeWithErrMsg,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewChartPackageTooLarge(tt.args.msg...); !cmp.Equal(got, tt.want) {
				t.Errorf("NewChartPackageTooLarge() = %v, want %v, diff is %s", got, tt.want, cmp.Diff(got, tt.want))
			}
		})
	}
}

func TestNewChartContentMismatch(t *testing.T) {
	simpleCode := ErrorCodes[27].DeepCopy()
	simpleCode.Message = simpleCode.Code

	codeWithErrMsg := ErrorCodes[27].DeepCopy()
	errMsg := "i am error message"
	codeWithErrMsg.Message = errMsg

	type args struct {
		msg []string
	}
	tests := []struct {
		name         string
		args         args
		HasCCEPrefix bool
		want         *ErrorCode
	}{
		// All test cases.
		{
			name: "simple case",
			want: simpleCode,
		},
		{
			name: "non-empty msg case",
			args: args{
				msg: []string{errMsg},
			},
			want: codeWithErrMsg,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewChartContentMismatch(tt.args.msg...); !cmp.Equal(got, tt.want) {
				t.Errorf("NewChartContentMismatch() = %v, want %v, diff is %s", got, tt.want, cmp.Diff(got, tt.want))
			}
		})
	}
}

func TestNewChartQuotaExceeded(t *testing.T) {
	simpleCode := ErrorCodes[28].DeepCopy()
	simpleCode.Message = simpleCode.Code

	codeWithErrMsg := ErrorCodes[28].DeepCopy()
	errMsg := "i am error message"
	codeWithErrMsg.Message = errMsg

	type args struct {
		msg []string
	}
	tests := []struct {
		name         string
		args         args
		HasCCEPrefix bool
		want         *ErrorCode
	}{
		// All test cases.
		{
			name: "simple case",
			want: simpleCode,
		},
		{
			name: "non-empty msg case",
			args: args{
				msg: []string{errMsg},
			},
			want: codeWithErrMsg,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewChartQuotaExceeded(tt.args.msg...); !cmp.Equal(got, tt.want) {
				t.Errorf("NewChartQuotaExceeded() = %v, want %v, diff is %s", got, tt.want, cmp.Diff(got, tt.want))
			}
		})
	}
}

func TestNewChartDeletionFailed(t *testing.T) {
	simpleCode := ErrorCodes[29].DeepCopy()
	simpleCode.Message = simpleCode.Code

	codeWithErrMsg := ErrorCodes[29].DeepCopy()
	errMsg := "i am error message"
	codeWithErrMsg.Message = errMsg

	type args struct {
		msg []string
	}
	tests := []struct {
		name         string
		args         args
		HasCCEPrefix bool
		want         *ErrorCode
	}{
		// All test cases.
		{
			name: "simple case",
			want: simpleCode,
		},
		{
			name: "non-empty msg case",
			args: args{
				msg: []string{errMsg},
			},
			want: codeWithErrMsg,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewChartDeletionFailed(tt.args.msg...); !cmp.Equal(got, tt.want) {
				t.Errorf("NewChartDeletionFailed() = %v, want %v, diff is %s", got, tt.want, cmp.Diff(got, tt.want))
			}
		})
	}
}

func TestNewHelmReleaseNotFound(t *testing.T) {
	simpleCode := ErrorCodes[30].DeepCopy()
	simpleCode.Message = simpleCode.Code

	codeWithErrMsg := ErrorCodes[30].DeepCopy()
	errMsg := "i am error message"
	codeWithErrMsg.Message = errMsg

	type args struct {
		msg []string
	}
	tests := []struct {
		name         string
		args         args
		HasCCEPrefix bool
		want         *ErrorCode
	}{
		// All test cases.
		{
			name: "simple case",
			want: simpleCode,
		},
		{
			name: "non-empty msg case",
			args: args{
				msg: []string{errMsg},
			},
			want: codeWithErrMsg,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewHelmReleaseNotFound(tt.args.msg...); !cmp.Equal(got, tt.want) {
				t.Errorf("NewHelmReleaseNotFound() = %v, want %v, diff is %s", got, tt.want, cmp.Diff(got, tt.want))
			}
		})
	}
}

func TestNewHelmReleaseNameAlreadyExist(t *testing.T) {
	simpleCode := ErrorCodes[31].DeepCopy()
	simpleCode.Message = simpleCode.Code

	codeWithErrMsg := ErrorCodes[31].DeepCopy()
	errMsg := "i am error message"
	codeWithErrMsg.Message = errMsg

	type args struct {
		msg []string
	}
	tests := []struct {
		name         string
		args         args
		HasCCEPrefix bool
		want         *ErrorCode
	}{
		// All test cases.
		{
			name: "simple case",
			want: simpleCode,
		},
		{
			name: "non-empty msg case",
			args: args{
				msg: []string{errMsg},
			},
			want: codeWithErrMsg,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewHelmReleaseNameAlreadyExist(tt.args.msg...); !cmp.Equal(got, tt.want) {
				t.Errorf("NewHelmReleaseNameAlreadyExist() = %v, want %v, diff is %s", got, tt.want, cmp.Diff(got, tt.want))
			}
		})
	}
}

func TestNewHelmReleaseNameIllegal(t *testing.T) {
	simpleCode := ErrorCodes[32].DeepCopy()
	simpleCode.Message = simpleCode.Code

	codeWithErrMsg := ErrorCodes[32].DeepCopy()
	errMsg := "i am error message"
	codeWithErrMsg.Message = errMsg

	type args struct {
		msg []string
	}
	tests := []struct {
		name         string
		args         args
		HasCCEPrefix bool
		want         *ErrorCode
	}{
		// All test cases.
		{
			name: "simple case",
			want: simpleCode,
		},
		{
			name: "non-empty msg case",
			args: args{
				msg: []string{errMsg},
			},
			want: codeWithErrMsg,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewHelmReleaseNameIllegal(tt.args.msg...); !cmp.Equal(got, tt.want) {
				t.Errorf("NewHelmReleaseNameIllegal() = %v, want %v, diff is %s", got, tt.want, cmp.Diff(got, tt.want))
			}
		})
	}
}

func TestNewHelmReleaseValuesIllegal(t *testing.T) {
	simpleCode := ErrorCodes[33].DeepCopy()
	simpleCode.Message = simpleCode.Code

	codeWithErrMsg := ErrorCodes[33].DeepCopy()
	errMsg := "i am error message"
	codeWithErrMsg.Message = errMsg

	type args struct {
		msg []string
	}
	tests := []struct {
		name         string
		args         args
		HasCCEPrefix bool
		want         *ErrorCode
	}{
		// All test cases.
		{
			name: "simple case",
			want: simpleCode,
		},
		{
			name: "non-empty msg case",
			args: args{
				msg: []string{errMsg},
			},
			want: codeWithErrMsg,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewHelmReleaseValuesIllegal(tt.args.msg...); !cmp.Equal(got, tt.want) {
				t.Errorf("NewHelmReleaseValuesIllegal() = %v, want %v, diff is %s", got, tt.want, cmp.Diff(got, tt.want))
			}
		})
	}
}

func TestNewTillerNotFound(t *testing.T) {
	simpleCode := ErrorCodes[34].DeepCopy()
	simpleCode.Message = simpleCode.Code

	codeWithErrMsg := ErrorCodes[34].DeepCopy()
	errMsg := "i am error message"
	codeWithErrMsg.Message = errMsg

	type args struct {
		msg []string
	}
	tests := []struct {
		name         string
		args         args
		HasCCEPrefix bool
		want         *ErrorCode
	}{
		// All test cases.
		{
			name: "simple case",
			want: simpleCode,
		},
		{
			name: "non-empty msg case",
			args: args{
				msg: []string{errMsg},
			},
			want: codeWithErrMsg,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewTillerNotFound(tt.args.msg...); !cmp.Equal(got, tt.want) {
				t.Errorf("NewTillerNotFound() = %v, want %v, diff is %s", got, tt.want, cmp.Diff(got, tt.want))
			}
		})
	}
}

func TestNewTillerNotReady(t *testing.T) {
	simpleCode := ErrorCodes[35].DeepCopy()
	simpleCode.Message = simpleCode.Code

	codeWithErrMsg := ErrorCodes[35].DeepCopy()
	errMsg := "i am error message"
	codeWithErrMsg.Message = errMsg

	type args struct {
		msg []string
	}
	tests := []struct {
		name         string
		args         args
		HasCCEPrefix bool
		want         *ErrorCode
	}{
		// All test cases.
		{
			name: "simple case",
			want: simpleCode,
		},
		{
			name: "non-empty msg case",
			args: args{
				msg: []string{errMsg},
			},
			want: codeWithErrMsg,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewTillerNotReady(tt.args.msg...); !cmp.Equal(got, tt.want) {
				t.Errorf("NewTillerNotReady() = %v, want %v, diff is %s", got, tt.want, cmp.Diff(got, tt.want))
			}
		})
	}
}

func TestNewTillerNotCompatible(t *testing.T) {
	simpleCode := ErrorCodes[36].DeepCopy()
	simpleCode.Message = simpleCode.Code

	codeWithErrMsg := ErrorCodes[36].DeepCopy()
	errMsg := "i am error message"
	codeWithErrMsg.Message = errMsg

	type args struct {
		msg []string
	}
	tests := []struct {
		name         string
		args         args
		HasCCEPrefix bool
		want         *ErrorCode
	}{
		// All test cases.
		{
			name: "simple case",
			want: simpleCode,
		},
		{
			name: "non-empty msg case",
			args: args{
				msg: []string{errMsg},
			},
			want: codeWithErrMsg,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewTillerNotCompatible(tt.args.msg...); !cmp.Equal(got, tt.want) {
				t.Errorf("NewTillerNotCompatible() = %v, want %v, diff is %s", got, tt.want, cmp.Diff(got, tt.want))
			}
		})
	}
}

func TestNewClusterMasterUnavailable(t *testing.T) {
	simpleCode := ErrorCodes[37].DeepCopy()
	simpleCode.Message = simpleCode.Code

	codeWithErrMsg := ErrorCodes[37].DeepCopy()
	errMsg := "i am error message"
	codeWithErrMsg.Message = errMsg

	type args struct {
		msg []string
	}
	tests := []struct {
		name         string
		args         args
		HasCCEPrefix bool
		want         *ErrorCode
	}{
		// All test cases.
		{
			name: "simple case",
			want: simpleCode,
		},
		{
			name: "non-empty msg case",
			args: args{
				msg: []string{errMsg},
			},
			want: codeWithErrMsg,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewClusterMasterUnavailable(tt.args.msg...); !cmp.Equal(got, tt.want) {
				t.Errorf("NewClusterMasterUnavailable() = %v, want %v, diff is %s", got, tt.want, cmp.Diff(got, tt.want))
			}
		})
	}
}

func TestNewHelmClientError(t *testing.T) {
	simpleCode := ErrorCodes[38].DeepCopy()
	simpleCode.Message = simpleCode.Code

	codeWithErrMsg := ErrorCodes[38].DeepCopy()
	errMsg := "i am error message"
	codeWithErrMsg.Message = errMsg

	type args struct {
		msg []string
	}
	tests := []struct {
		name         string
		args         args
		HasCCEPrefix bool
		want         *ErrorCode
	}{
		// All test cases.
		{
			name: "simple case",
			want: simpleCode,
		},
		{
			name: "non-empty msg case",
			args: args{
				msg: []string{errMsg},
			},
			want: codeWithErrMsg,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewHelmClientError(tt.args.msg...); !cmp.Equal(got, tt.want) {
				t.Errorf("NewHelmClientError() = %v, want %v, diff is %s", got, tt.want, cmp.Diff(got, tt.want))
			}
		})
	}
}

func TestNewClusterNotFound(t *testing.T) {
	simpleCode := ErrorCodes[39].DeepCopy()
	simpleCode.Message = simpleCode.Code

	codeWithErrMsg := ErrorCodes[39].DeepCopy()
	errMsg := "i am error message"
	codeWithErrMsg.Message = errMsg

	type args struct {
		msg []string
	}
	tests := []struct {
		name         string
		args         args
		HasCCEPrefix bool
		want         *ErrorCode
	}{
		// All test cases.
		{
			name: "simple case",
			want: simpleCode,
		},
		{
			name: "non-empty msg case",
			args: args{
				msg: []string{errMsg},
			},
			want: codeWithErrMsg,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewClusterNotFound(tt.args.msg...); !cmp.Equal(got, tt.want) {
				t.Errorf("NewClusterNotFound() = %v, want %v, diff is %s", got, tt.want, cmp.Diff(got, tt.want))
			}
		})
	}
}

func TestNewClusterNotExposedToPublic(t *testing.T) {
	simpleCode := ErrorCodes[40].DeepCopy()
	simpleCode.Message = simpleCode.Code

	codeWithErrMsg := ErrorCodes[40].DeepCopy()
	errMsg := "i am error message"
	codeWithErrMsg.Message = errMsg

	type args struct {
		msg []string
	}
	tests := []struct {
		name         string
		args         args
		HasCCEPrefix bool
		want         *ErrorCode
	}{
		// All test cases.
		{
			name: "simple case",
			want: simpleCode,
		},
		{
			name: "non-empty msg case",
			args: args{
				msg: []string{errMsg},
			},
			want: codeWithErrMsg,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewClusterNotExposedToPublic(tt.args.msg...); !cmp.Equal(got, tt.want) {
				t.Errorf("NewClusterNotExposedToPublic() = %v, want %v, diff is %s", got, tt.want, cmp.Diff(got, tt.want))
			}
		})
	}
}

func TestNewInvaildUserID(t *testing.T) {
	simpleCode := ErrorCodes[41].DeepCopy()
	simpleCode.Message = simpleCode.Code

	codeWithErrMsg := ErrorCodes[41].DeepCopy()
	errMsg := "i am error message"
	codeWithErrMsg.Message = errMsg

	type args struct {
		msg []string
	}
	tests := []struct {
		name         string
		args         args
		HasCCEPrefix bool
		want         *ErrorCode
	}{
		// All test cases.
		{
			name: "simple case",
			want: simpleCode,
		},
		{
			name: "non-empty msg case",
			args: args{
				msg: []string{errMsg},
			},
			want: codeWithErrMsg,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewInvaildUserID(tt.args.msg...); !cmp.Equal(got, tt.want) {
				t.Errorf("NewInvaildUserID() = %v, want %v, diff is %s", got, tt.want, cmp.Diff(got, tt.want))
			}
		})
	}
}

func TestNewInvaildClusterName(t *testing.T) {
	simpleCode := ErrorCodes[42].DeepCopy()
	simpleCode.Message = simpleCode.Code

	codeWithErrMsg := ErrorCodes[42].DeepCopy()
	errMsg := "i am error message"
	codeWithErrMsg.Message = errMsg

	type args struct {
		msg []string
	}
	tests := []struct {
		name         string
		args         args
		HasCCEPrefix bool
		want         *ErrorCode
	}{
		// All test cases.
		{
			name: "simple case",
			want: simpleCode,
		},
		{
			name: "non-empty msg case",
			args: args{
				msg: []string{errMsg},
			},
			want: codeWithErrMsg,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewInvaildClusterName(tt.args.msg...); !cmp.Equal(got, tt.want) {
				t.Errorf("NewInvaildClusterName() = %v, want %v, diff is %s", got, tt.want, cmp.Diff(got, tt.want))
			}
		})
	}
}

func TestNewInstanceAlreadyExists(t *testing.T) {
	simpleCode := ErrorCodes[43].DeepCopy()
	simpleCode.Message = simpleCode.Code

	codeWithErrMsg := ErrorCodes[43].DeepCopy()
	errMsg := "i am error message"
	codeWithErrMsg.Message = errMsg

	type args struct {
		msg []string
	}
	tests := []struct {
		name         string
		args         args
		HasCCEPrefix bool
		want         *ErrorCode
	}{
		// All test cases.
		{
			name: "simple case",
			want: simpleCode,
		},
		{
			name: "non-empty msg case",
			args: args{
				msg: []string{errMsg},
			},
			want: codeWithErrMsg,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewInstanceAlreadyExists(tt.args.msg...); !cmp.Equal(got, tt.want) {
				t.Errorf("NewInstanceAlreadyExists() = %v, want %v, diff is %s", got, tt.want, cmp.Diff(got, tt.want))
			}
		})
	}
}

func TestNewInstancesCountBeyondLimit(t *testing.T) {
	simpleCode := ErrorCodes[44].DeepCopy()
	simpleCode.Message = simpleCode.Code

	codeWithErrMsg := ErrorCodes[44].DeepCopy()
	errMsg := "i am error message"
	codeWithErrMsg.Message = errMsg

	type args struct {
		msg []string
	}
	tests := []struct {
		name         string
		args         args
		HasCCEPrefix bool
		want         *ErrorCode
	}{
		// All test cases.
		{
			name: "simple case",
			want: simpleCode,
		},
		{
			name: "non-empty msg case",
			args: args{
				msg: []string{errMsg},
			},
			want: codeWithErrMsg,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewInstancesCountBeyondLimit(tt.args.msg...); !cmp.Equal(got, tt.want) {
				t.Errorf("NewInstancesCountBeyondLimit() = %v, want %v, diff is %s", got, tt.want, cmp.Diff(got, tt.want))
			}
		})
	}
}

func TestNewExistedInstanceIDDuplicated(t *testing.T) {
	simpleCode := ErrorCodes[45].DeepCopy()
	simpleCode.Message = simpleCode.Code

	codeWithErrMsg := ErrorCodes[45].DeepCopy()
	errMsg := "i am error message"
	codeWithErrMsg.Message = errMsg

	type args struct {
		msg []string
	}
	tests := []struct {
		name         string
		args         args
		HasCCEPrefix bool
		want         *ErrorCode
	}{
		// All test cases.
		{
			name: "simple case",
			want: simpleCode,
		},
		{
			name: "non-empty msg case",
			args: args{
				msg: []string{errMsg},
			},
			want: codeWithErrMsg,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewExistedInstanceIDDuplicated(tt.args.msg...); !cmp.Equal(got, tt.want) {
				t.Errorf("NewExistedInstanceIDDuplicated() = %v, want %v, diff is %s", got, tt.want, cmp.Diff(got, tt.want))
			}
		})
	}
}

func TestNewInstanceNotFound(t *testing.T) {
	simpleCode := ErrorCodes[46].DeepCopy()
	simpleCode.Message = simpleCode.Code

	codeWithErrMsg := ErrorCodes[46].DeepCopy()
	errMsg := "i am error message"
	codeWithErrMsg.Message = errMsg

	type args struct {
		msg []string
	}
	tests := []struct {
		name         string
		args         args
		HasCCEPrefix bool
		want         *ErrorCode
	}{
		// All test cases.
		{
			name: "simple case",
			want: simpleCode,
		},
		{
			name: "non-empty msg case",
			args: args{
				msg: []string{errMsg},
			},
			want: codeWithErrMsg,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewInstanceNotFound(tt.args.msg...); !cmp.Equal(got, tt.want) {
				t.Errorf("NewInstanceNotFound() = %v, want %v, diff is %s", got, tt.want, cmp.Diff(got, tt.want))
			}
		})
	}
}

func TestNewVPCIDNotExists(t *testing.T) {
	simpleCode := ErrorCodes[47].DeepCopy()
	simpleCode.Message = simpleCode.Code

	codeWithErrMsg := ErrorCodes[47].DeepCopy()
	errMsg := "i am error message"
	codeWithErrMsg.Message = errMsg

	type args struct {
		msg []string
	}
	tests := []struct {
		name         string
		args         args
		HasCCEPrefix bool
		want         *ErrorCode
	}{
		// All test cases.
		{
			name: "simple case",
			want: simpleCode,
		},
		{
			name: "non-empty msg case",
			args: args{
				msg: []string{errMsg},
			},
			want: codeWithErrMsg,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewVPCIDNotExists(tt.args.msg...); !cmp.Equal(got, tt.want) {
				t.Errorf("NewVPCIDNotExists() = %v, want %v, diff is %s", got, tt.want, cmp.Diff(got, tt.want))
			}
		})
	}
}

func TestNewMasterFlavorInvalid(t *testing.T) {
	simpleCode := ErrorCodes[48].DeepCopy()
	simpleCode.Message = simpleCode.Code

	codeWithErrMsg := ErrorCodes[48].DeepCopy()
	errMsg := "i am error message"
	codeWithErrMsg.Message = errMsg

	type args struct {
		msg []string
	}
	tests := []struct {
		name         string
		args         args
		HasCCEPrefix bool
		want         *ErrorCode
	}{
		// All test cases.
		{
			name: "simple case",
			want: simpleCode,
		},
		{
			name: "non-empty msg case",
			args: args{
				msg: []string{errMsg},
			},
			want: codeWithErrMsg,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewMasterFlavorInvalid(tt.args.msg...); !cmp.Equal(got, tt.want) {
				t.Errorf("NewMasterFlavorInvalid() = %v, want %v, diff is %s", got, tt.want, cmp.Diff(got, tt.want))
			}
		})
	}
}

func TestNewClusterHAInvalid(t *testing.T) {
	simpleCode := ErrorCodes[49].DeepCopy()
	simpleCode.Message = simpleCode.Code

	codeWithErrMsg := ErrorCodes[49].DeepCopy()
	errMsg := "i am error message"
	codeWithErrMsg.Message = errMsg

	type args struct {
		msg []string
	}
	tests := []struct {
		name         string
		args         args
		HasCCEPrefix bool
		want         *ErrorCode
	}{
		// All test cases.
		{
			name: "simple case",
			want: simpleCode,
		},
		{
			name: "non-empty msg case",
			args: args{
				msg: []string{errMsg},
			},
			want: codeWithErrMsg,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewClusterHAInvalid(tt.args.msg...); !cmp.Equal(got, tt.want) {
				t.Errorf("NewClusterHAInvalid() = %v, want %v, diff is %s", got, tt.want, cmp.Diff(got, tt.want))
			}
		})
	}
}

func TestNewContainerNetworkCIDRInvalid(t *testing.T) {
	simpleCode := ErrorCodes[50].DeepCopy()
	simpleCode.Message = simpleCode.Code

	codeWithErrMsg := ErrorCodes[50].DeepCopy()
	errMsg := "i am error message"
	codeWithErrMsg.Message = errMsg

	type args struct {
		msg []string
	}
	tests := []struct {
		name         string
		args         args
		HasCCEPrefix bool
		want         *ErrorCode
	}{
		// All test cases.
		{
			name: "simple case",
			want: simpleCode,
		},
		{
			name: "non-empty msg case",
			args: args{
				msg: []string{errMsg},
			},
			want: codeWithErrMsg,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewContainerNetworkCIDRInvalid(tt.args.msg...); !cmp.Equal(got, tt.want) {
				t.Errorf("NewContainerNetworkCIDRInvalid() = %v, want %v, diff is %s", got, tt.want, cmp.Diff(got, tt.want))
			}
		})
	}
}

func TestNewInstanceGroupNotFound(t *testing.T) {
	simpleCode := ErrorCodes[51].DeepCopy()
	simpleCode.Message = simpleCode.Code

	codeWithErrMsg := ErrorCodes[51].DeepCopy()
	errMsg := "i am error message"
	codeWithErrMsg.Message = errMsg

	type args struct {
		msg []string
	}
	tests := []struct {
		name         string
		args         args
		HasCCEPrefix bool
		want         *ErrorCode
	}{
		// All test cases.
		{
			name: "simple case",
			want: simpleCode,
		},
		{
			name: "non-empty msg case",
			args: args{
				msg: []string{errMsg},
			},
			want: codeWithErrMsg,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewInstanceGroupNotFound(tt.args.msg...); !cmp.Equal(got, tt.want) {
				t.Errorf("NewInstanceGroupNotFound() = %v, want %v, diff is %s", got, tt.want, cmp.Diff(got, tt.want))
			}
		})
	}
}

func TestNewClientInfoImproperlyEncoded(t *testing.T) {
	simpleCode := ErrorCodes[52].DeepCopy()
	simpleCode.Message = simpleCode.Code

	codeWithErrMsg := ErrorCodes[52].DeepCopy()
	errMsg := "i am error message"
	codeWithErrMsg.Message = errMsg

	type args struct {
		msg []string
	}
	tests := []struct {
		name         string
		args         args
		HasCCEPrefix bool
		want         *ErrorCode
	}{
		// All test cases.
		{
			name: "simple case",
			want: simpleCode,
		},
		{
			name: "non-empty msg case",
			args: args{
				msg: []string{errMsg},
			},
			want: codeWithErrMsg,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewClientInfoImproperlyEncoded(tt.args.msg...); !cmp.Equal(got, tt.want) {
				t.Errorf("NewClientInfoImproperlyEncoded() = %v, want %v, diff is %s", got, tt.want, cmp.Diff(got, tt.want))
			}
		})
	}
}

func TestNewRegionNotAvailable(t *testing.T) {
	simpleCode := ErrorCodes[53].DeepCopy()
	simpleCode.Message = simpleCode.Code

	codeWithErrMsg := ErrorCodes[53].DeepCopy()
	errMsg := "i am error message"
	codeWithErrMsg.Message = errMsg

	type args struct {
		msg []string
	}
	tests := []struct {
		name         string
		args         args
		HasCCEPrefix bool
		want         *ErrorCode
	}{
		// All test cases.
		{
			name: "simple case",
			want: simpleCode,
		},
		{
			name: "non-empty msg case",
			args: args{
				msg: []string{errMsg},
			},
			want: codeWithErrMsg,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewRegionNotAvailable(tt.args.msg...); !cmp.Equal(got, tt.want) {
				t.Errorf("NewRegionNotAvailable() = %v, want %v, diff is %s", got, tt.want, cmp.Diff(got, tt.want))
			}
		})
	}
}

func TestNewBLBNotAvailable(t *testing.T) {
	simpleCode := ErrorCodes[54].DeepCopy()
	simpleCode.Message = simpleCode.Code

	codeWithErrMsg := ErrorCodes[54].DeepCopy()
	errMsg := "i am error message"
	codeWithErrMsg.Message = errMsg

	type args struct {
		msg []string
	}
	tests := []struct {
		name         string
		args         args
		HasCCEPrefix bool
		want         *ErrorCode
	}{
		// All test cases.
		{
			name: "simple case",
			want: simpleCode,
		},
		{
			name: "non-empty msg case",
			args: args{
				msg: []string{errMsg},
			},
			want: codeWithErrMsg,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewBLBNotAvailable(tt.args.msg...); !cmp.Equal(got, tt.want) {
				t.Errorf("NewBLBNotAvailable() = %v, want %v, diff is %s", got, tt.want, cmp.Diff(got, tt.want))
			}
		})
	}
}

func TestNewMasterCannotDeletedOrRemoved(t *testing.T) {
	simpleCode := ErrorCodes[55].DeepCopy()
	simpleCode.Message = simpleCode.Code

	codeWithErrMsg := ErrorCodes[55].DeepCopy()
	errMsg := "i am error message"
	codeWithErrMsg.Message = errMsg

	type args struct {
		msg []string
	}
	tests := []struct {
		name         string
		args         args
		HasCCEPrefix bool
		want         *ErrorCode
	}{
		// All test cases.
		{
			name: "simple case",
			want: simpleCode,
		},
		{
			name: "non-empty msg case",
			args: args{
				msg: []string{errMsg},
			},
			want: codeWithErrMsg,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewMasterCannotDeletedOrRemoved(tt.args.msg...); !cmp.Equal(got, tt.want) {
				t.Errorf("NewMasterCannotDeletedOrRemoved() = %v, want %v, diff is %s", got, tt.want, cmp.Diff(got, tt.want))
			}
		})
	}
}

func TestNewManagedClusterMinNodeNumLimit(t *testing.T) {
	simpleCode := ErrorCodes[56].DeepCopy()
	simpleCode.Message = simpleCode.Code

	codeWithErrMsg := ErrorCodes[56].DeepCopy()
	errMsg := "i am error message"
	codeWithErrMsg.Message = errMsg

	type args struct {
		msg []string
	}
	tests := []struct {
		name         string
		args         args
		HasCCEPrefix bool
		want         *ErrorCode
	}{
		// All test cases.
		{
			name: "simple case",
			want: simpleCode,
		},
		{
			name: "non-empty msg case",
			args: args{
				msg: []string{errMsg},
			},
			want: codeWithErrMsg,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewManagedClusterMinNodeNumLimit(tt.args.msg...); !cmp.Equal(got, tt.want) {
				t.Errorf("NewManagedClusterMinNodeNumLimit() = %v, want %v, diff is %s", got, tt.want, cmp.Diff(got, tt.want))
			}
		})
	}
}

func TestNewConflictNetworkTopology(t *testing.T) {
	simpleCode := ErrorCodes[57].DeepCopy()
	simpleCode.Message = simpleCode.Code

	codeWithErrMsg := ErrorCodes[57].DeepCopy()
	errMsg := "i am error message"
	codeWithErrMsg.Message = errMsg

	type args struct {
		msg []string
	}
	tests := []struct {
		name         string
		args         args
		HasCCEPrefix bool
		want         *ErrorCode
	}{
		// All test cases.
		{
			name: "simple case",
			want: simpleCode,
		},
		{
			name: "non-empty msg case",
			args: args{
				msg: []string{errMsg},
			},
			want: codeWithErrMsg,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewConflictNetworkTopology(tt.args.msg...); !cmp.Equal(got, tt.want) {
				t.Errorf("NewConflictNetworkTopology() = %v, want %v, diff is %s", got, tt.want, cmp.Diff(got, tt.want))
			}
		})
	}
}

func TestNewCurrentPhaseCannotRetry(t *testing.T) {
	simpleCode := ErrorCodes[58].DeepCopy()
	simpleCode.Message = simpleCode.Code

	codeWithErrMsg := ErrorCodes[58].DeepCopy()
	errMsg := "i am error message"
	codeWithErrMsg.Message = errMsg

	type args struct {
		msg []string
	}
	tests := []struct {
		name         string
		args         args
		HasCCEPrefix bool
		want         *ErrorCode
	}{
		// All test cases.
		{
			name: "simple case",
			want: simpleCode,
		},
		{
			name: "non-empty msg case",
			args: args{
				msg: []string{errMsg},
			},
			want: codeWithErrMsg,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewCurrentPhaseCannotRetry(tt.args.msg...); !cmp.Equal(got, tt.want) {
				t.Errorf("NewCurrentPhaseCannotRetry() = %v, want %v, diff is %s", got, tt.want, cmp.Diff(got, tt.want))
			}
		})
	}
}

func TestNewAIJobMissingCPUOrMemoryRequest(t *testing.T) {
	simpleCode := ErrorCodes[59].DeepCopy()
	simpleCode.Message = simpleCode.Code

	codeWithErrMsg := ErrorCodes[59].DeepCopy()
	errMsg := "i am error message"
	codeWithErrMsg.Message = errMsg

	type args struct {
		msg []string
	}
	tests := []struct {
		name         string
		args         args
		HasCCEPrefix bool
		want         *ErrorCode
	}{
		// All test cases.
		{
			name: "simple case",
			want: simpleCode,
		},
		{
			name: "non-empty msg case",
			args: args{
				msg: []string{errMsg},
			},
			want: codeWithErrMsg,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewAIJobMissingCPUOrMemoryRequest(tt.args.msg...); !cmp.Equal(got, tt.want) {
				t.Errorf("NewAIJobMissingCPUOrMemoryRequest() = %v, want %v, diff is %s", got, tt.want, cmp.Diff(got, tt.want))
			}
		})
	}
}

func TestNewEssentialPluginNotFound(t *testing.T) {
	simpleCode := ErrorCodes[60].DeepCopy()
	simpleCode.Message = simpleCode.Code

	codeWithErrMsg := ErrorCodes[60].DeepCopy()
	errMsg := "i am error message"
	codeWithErrMsg.Message = errMsg

	type args struct {
		msg []string
	}
	tests := []struct {
		name         string
		args         args
		HasCCEPrefix bool
		want         *ErrorCode
	}{
		// All test cases.
		{
			name: "simple case",
			want: simpleCode,
		},
		{
			name: "non-empty msg case",
			args: args{
				msg: []string{errMsg},
			},
			want: codeWithErrMsg,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewEssentialPluginNotFound(tt.args.msg...); !cmp.Equal(got, tt.want) {
				t.Errorf("NewEssentialPluginNotFound() = %v, want %v, diff is %s", got, tt.want, cmp.Diff(got, tt.want))
			}
		})
	}
}

func TestNewAIOperatorNotFound(t *testing.T) {
	simpleCode := ErrorCodes[61].DeepCopy()
	simpleCode.Message = simpleCode.Code

	codeWithErrMsg := ErrorCodes[61].DeepCopy()
	errMsg := "i am error message"
	codeWithErrMsg.Message = errMsg

	type args struct {
		msg []string
	}
	tests := []struct {
		name         string
		args         args
		HasCCEPrefix bool
		want         *ErrorCode
	}{
		// All test cases.
		{
			name: "simple case",
			want: simpleCode,
		},
		{
			name: "non-empty msg case",
			args: args{
				msg: []string{errMsg},
			},
			want: codeWithErrMsg,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewAIOperatorNotFound(tt.args.msg...); !cmp.Equal(got, tt.want) {
				t.Errorf("NewAIOperatorNotFound() = %v, want %v, diff is %s", got, tt.want, cmp.Diff(got, tt.want))
			}
		})
	}
}

func TestNewAIQueueCannotDelete(t *testing.T) {
	simpleCode := ErrorCodes[62].DeepCopy()
	simpleCode.Message = simpleCode.Code

	codeWithErrMsg := ErrorCodes[62].DeepCopy()
	errMsg := "i am error message"
	codeWithErrMsg.Message = errMsg

	type args struct {
		msg []string
	}
	tests := []struct {
		name         string
		args         args
		HasCCEPrefix bool
		want         *ErrorCode
	}{
		// All test cases.
		{
			name: "simple case",
			want: simpleCode,
		},
		{
			name: "non-empty msg case",
			args: args{
				msg: []string{errMsg},
			},
			want: codeWithErrMsg,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewAIQueueCannotDelete(tt.args.msg...); !cmp.Equal(got, tt.want) {
				t.Errorf("NewAIQueueCannotDelete() = %v, want %v, diff is %s", got, tt.want, cmp.Diff(got, tt.want))
			}
		})
	}
}

func TestNewRBACUnauthorized(t *testing.T) {
	simpleCode := ErrorCodes[63].DeepCopy()
	simpleCode.Message = simpleCode.Code

	codeWithErrMsg := ErrorCodes[63].DeepCopy()
	errMsg := "i am error message"
	codeWithErrMsg.Message = errMsg

	type args struct {
		msg []string
	}
	tests := []struct {
		name         string
		args         args
		HasCCEPrefix bool
		want         *ErrorCode
	}{
		// All test cases.
		{
			name: "simple case",
			want: simpleCode,
		},
		{
			name: "non-empty msg case",
			args: args{
				msg: []string{errMsg},
			},
			want: codeWithErrMsg,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewRBACUnauthorized(tt.args.msg...); !cmp.Equal(got, tt.want) {
				t.Errorf("NewRBACUnauthorized() = %v, want %v, diff is %s", got, tt.want, cmp.Diff(got, tt.want))
			}
		})
	}
}

func TestNewMultiWorkflowExistInCluster(t *testing.T) {
	simpleCode := ErrorCodes[64].DeepCopy()
	simpleCode.Message = simpleCode.Code

	codeWithErrMsg := ErrorCodes[64].DeepCopy()
	errMsg := "i am error message"
	codeWithErrMsg.Message = errMsg

	type args struct {
		msg []string
	}
	tests := []struct {
		name         string
		args         args
		HasCCEPrefix bool
		want         *ErrorCode
	}{
		// All test cases.
		{
			name: "simple case",
			want: simpleCode,
		},
		{
			name: "non-empty msg case",
			args: args{
				msg: []string{errMsg},
			},
			want: codeWithErrMsg,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewMultiWorkflowExistInCluster(tt.args.msg...); !cmp.Equal(got, tt.want) {
				t.Errorf("NewMultiWorkflowExistInCluster() = %v, want %v, diff is %s", got, tt.want, cmp.Diff(got, tt.want))
			}
		})
	}
}

func TestNewTagNotExistWithRelationTag(t *testing.T) {
	simpleCode := ErrorCodes[65].DeepCopy()
	simpleCode.Message = simpleCode.Code

	codeWithErrMsg := ErrorCodes[65].DeepCopy()
	errMsg := "i am error message"
	codeWithErrMsg.Message = errMsg

	type args struct {
		msg []string
	}
	tests := []struct {
		name         string
		args         args
		HasCCEPrefix bool
		want         *ErrorCode
	}{
		// All test cases.
		{
			name: "simple case",
			want: simpleCode,
		},
		{
			name: "non-empty msg case",
			args: args{
				msg: []string{errMsg},
			},
			want: codeWithErrMsg,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewTagNotExistWithRelationTag(tt.args.msg...); !cmp.Equal(got, tt.want) {
				t.Errorf("NewTagNotExistWithRelationTag() = %v, want %v, diff is %s", got, tt.want, cmp.Diff(got, tt.want))
			}
		})
	}
}

func TestNewCCEClusterQuotaExceed(t *testing.T) {
	simpleCode := ErrorCodes[66].DeepCopy()
	simpleCode.Message = simpleCode.Code

	codeWithErrMsg := ErrorCodes[66].DeepCopy()
	errMsg := "i am error message"
	codeWithErrMsg.Message = errMsg

	type args struct {
		msg []string
	}
	tests := []struct {
		name         string
		args         args
		HasCCEPrefix bool
		want         *ErrorCode
	}{
		// All test cases.
		{
			name: "simple case",
			want: simpleCode,
		},
		{
			name: "non-empty msg case",
			args: args{
				msg: []string{errMsg},
			},
			want: codeWithErrMsg,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewCCEClusterQuotaExceed(tt.args.msg...); !cmp.Equal(got, tt.want) {
				t.Errorf("NewCCEClusterQuotaExceed() = %v, want %v, diff is %s", got, tt.want, cmp.Diff(got, tt.want))
			}
		})
	}
}

func TestNewCCEInstanceQuotaExceed(t *testing.T) {
	simpleCode := ErrorCodes[67].DeepCopy()
	simpleCode.Message = simpleCode.Code

	codeWithErrMsg := ErrorCodes[67].DeepCopy()
	errMsg := "i am error message"
	codeWithErrMsg.Message = errMsg

	type args struct {
		msg []string
	}
	tests := []struct {
		name         string
		args         args
		HasCCEPrefix bool
		want         *ErrorCode
	}{
		// All test cases.
		{
			name: "simple case",
			want: simpleCode,
		},
		{
			name: "non-empty msg case",
			args: args{
				msg: []string{errMsg},
			},
			want: codeWithErrMsg,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewCCEInstanceQuotaExceed(tt.args.msg...); !cmp.Equal(got, tt.want) {
				t.Errorf("NewCCEInstanceQuotaExceed() = %v, want %v, diff is %s", got, tt.want, cmp.Diff(got, tt.want))
			}
		})
	}
}

func TestNewBCCLackedWhenCreateBLB(t *testing.T) {
	simpleCode := ReconcileErrorCodes[0].DeepCopy()
	simpleCode.Message = simpleCode.Code

	codeWithErrMsg := ReconcileErrorCodes[0].DeepCopy()
	errMsg := "i am error message"
	codeWithErrMsg.Message = errMsg

	type args struct {
		msg []string
	}
	tests := []struct {
		name         string
		args         args
		HasCCEPrefix bool
		want         *ErrorCode
	}{
		// All test cases.
		{
			name: "simple case",
			want: simpleCode,
		},
		{
			name: "non-empty msg case",
			args: args{
				msg: []string{errMsg},
			},
			want: codeWithErrMsg,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewBCCLackedWhenCreateBLB(tt.args.msg...); !cmp.Equal(got, tt.want) {
				t.Errorf("NewBCCLackedWhenCreateBLB() = %v, want %v, diff is %s", got, tt.want, cmp.Diff(got, tt.want))
			}
		})
	}
}

func TestNewAccountInsufficientBalance(t *testing.T) {
	simpleCode := ReconcileErrorCodes[1].DeepCopy()
	simpleCode.Message = simpleCode.Code

	codeWithErrMsg := ReconcileErrorCodes[1].DeepCopy()
	errMsg := "i am error message"
	codeWithErrMsg.Message = errMsg

	type args struct {
		msg []string
	}
	tests := []struct {
		name         string
		args         args
		HasCCEPrefix bool
		want         *ErrorCode
	}{
		// All test cases.
		{
			name: "simple case",
			want: simpleCode,
		},
		{
			name: "non-empty msg case",
			args: args{
				msg: []string{errMsg},
			},
			want: codeWithErrMsg,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewAccountInsufficientBalance(tt.args.msg...); !cmp.Equal(got, tt.want) {
				t.Errorf("NewAccountInsufficientBalance() = %v, want %v, diff is %s", got, tt.want, cmp.Diff(got, tt.want))
			}
		})
	}
}

func TestNewBLBPermissionDenied(t *testing.T) {
	simpleCode := ReconcileErrorCodes[2].DeepCopy()
	simpleCode.Message = simpleCode.Code

	codeWithErrMsg := ReconcileErrorCodes[2].DeepCopy()
	errMsg := "i am error message"
	codeWithErrMsg.Message = errMsg

	type args struct {
		msg []string
	}
	tests := []struct {
		name         string
		args         args
		HasCCEPrefix bool
		want         *ErrorCode
	}{
		// All test cases.
		{
			name: "simple case",
			want: simpleCode,
		},
		{
			name: "non-empty msg case",
			args: args{
				msg: []string{errMsg},
			},
			want: codeWithErrMsg,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewBLBPermissionDenied(tt.args.msg...); !cmp.Equal(got, tt.want) {
				t.Errorf("NewBLBPermissionDenied() = %v, want %v, diff is %s", got, tt.want, cmp.Diff(got, tt.want))
			}
		})
	}
}

func TestNewBLBQuotaExceeded(t *testing.T) {
	simpleCode := ReconcileErrorCodes[3].DeepCopy()
	simpleCode.Message = simpleCode.Code

	codeWithErrMsg := ReconcileErrorCodes[3].DeepCopy()
	errMsg := "i am error message"
	codeWithErrMsg.Message = errMsg

	type args struct {
		msg []string
	}
	tests := []struct {
		name         string
		args         args
		HasCCEPrefix bool
		want         *ErrorCode
	}{
		// All test cases.
		{
			name: "simple case",
			want: simpleCode,
		},
		{
			name: "non-empty msg case",
			args: args{
				msg: []string{errMsg},
			},
			want: codeWithErrMsg,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewBLBQuotaExceeded(tt.args.msg...); !cmp.Equal(got, tt.want) {
				t.Errorf("NewBLBQuotaExceeded() = %v, want %v, diff is %s", got, tt.want, cmp.Diff(got, tt.want))
			}
		})
	}
}

func TestNewNeedFaceAuthentication(t *testing.T) {
	simpleCode := ReconcileErrorCodes[4].DeepCopy()
	simpleCode.Message = simpleCode.Code

	codeWithErrMsg := ReconcileErrorCodes[4].DeepCopy()
	errMsg := "i am error message"
	codeWithErrMsg.Message = errMsg

	type args struct {
		msg []string
	}
	tests := []struct {
		name         string
		args         args
		HasCCEPrefix bool
		want         *ErrorCode
	}{
		// All test cases.
		{
			name: "simple case",
			want: simpleCode,
		},
		{
			name: "non-empty msg case",
			args: args{
				msg: []string{errMsg},
			},
			want: codeWithErrMsg,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewNeedFaceAuthentication(tt.args.msg...); !cmp.Equal(got, tt.want) {
				t.Errorf("NewNeedFaceAuthentication() = %v, want %v, diff is %s", got, tt.want, cmp.Diff(got, tt.want))
			}
		})
	}
}

func TestNewManagedMasterCreateFailed(t *testing.T) {
	simpleCode := ReconcileErrorCodes[5].DeepCopy()
	simpleCode.Message = simpleCode.Code

	codeWithErrMsg := ReconcileErrorCodes[5].DeepCopy()
	errMsg := "i am error message"
	codeWithErrMsg.Message = errMsg

	type args struct {
		msg []string
	}
	tests := []struct {
		name         string
		args         args
		HasCCEPrefix bool
		want         *ErrorCode
	}{
		// All test cases.
		{
			name: "simple case",
			want: simpleCode,
		},
		{
			name: "non-empty msg case",
			args: args{
				msg: []string{errMsg},
			},
			want: codeWithErrMsg,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewManagedMasterCreateFailed(tt.args.msg...); !cmp.Equal(got, tt.want) {
				t.Errorf("NewManagedMasterCreateFailed() = %v, want %v, diff is %s", got, tt.want, cmp.Diff(got, tt.want))
			}
		})
	}
}

func TestNewCustomMasterCreateFailed(t *testing.T) {
	simpleCode := ReconcileErrorCodes[6].DeepCopy()
	simpleCode.Message = simpleCode.Code

	codeWithErrMsg := ReconcileErrorCodes[6].DeepCopy()
	errMsg := "i am error message"
	codeWithErrMsg.Message = errMsg

	type args struct {
		msg []string
	}
	tests := []struct {
		name         string
		args         args
		HasCCEPrefix bool
		want         *ErrorCode
	}{
		// All test cases.
		{
			name: "simple case",
			want: simpleCode,
		},
		{
			name: "non-empty msg case",
			args: args{
				msg: []string{errMsg},
			},
			want: codeWithErrMsg,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewCustomMasterCreateFailed(tt.args.msg...); !cmp.Equal(got, tt.want) {
				t.Errorf("NewCustomMasterCreateFailed() = %v, want %v, diff is %s", got, tt.want, cmp.Diff(got, tt.want))
			}
		})
	}
}

func TestNewManagedMasterDeleteFailed(t *testing.T) {
	simpleCode := ReconcileErrorCodes[7].DeepCopy()
	simpleCode.Message = simpleCode.Code

	codeWithErrMsg := ReconcileErrorCodes[7].DeepCopy()
	errMsg := "i am error message"
	codeWithErrMsg.Message = errMsg

	type args struct {
		msg []string
	}
	tests := []struct {
		name         string
		args         args
		HasCCEPrefix bool
		want         *ErrorCode
	}{
		// All test cases.
		{
			name: "simple case",
			want: simpleCode,
		},
		{
			name: "non-empty msg case",
			args: args{
				msg: []string{errMsg},
			},
			want: codeWithErrMsg,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewManagedMasterDeleteFailed(tt.args.msg...); !cmp.Equal(got, tt.want) {
				t.Errorf("NewManagedMasterDeleteFailed() = %v, want %v, diff is %s", got, tt.want, cmp.Diff(got, tt.want))
			}
		})
	}
}

func TestNewCustomMasterDeleteFailed(t *testing.T) {
	simpleCode := ReconcileErrorCodes[8].DeepCopy()
	simpleCode.Message = simpleCode.Code

	codeWithErrMsg := ReconcileErrorCodes[8].DeepCopy()
	errMsg := "i am error message"
	codeWithErrMsg.Message = errMsg

	type args struct {
		msg []string
	}
	tests := []struct {
		name         string
		args         args
		HasCCEPrefix bool
		want         *ErrorCode
	}{
		// All test cases.
		{
			name: "simple case",
			want: simpleCode,
		},
		{
			name: "non-empty msg case",
			args: args{
				msg: []string{errMsg},
			},
			want: codeWithErrMsg,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewCustomMasterDeleteFailed(tt.args.msg...); !cmp.Equal(got, tt.want) {
				t.Errorf("NewCustomMasterDeleteFailed() = %v, want %v, diff is %s", got, tt.want, cmp.Diff(got, tt.want))
			}
		})
	}
}

func TestNewClusterNodeDeleteFailed(t *testing.T) {
	simpleCode := ReconcileErrorCodes[9].DeepCopy()
	simpleCode.Message = simpleCode.Code

	codeWithErrMsg := ReconcileErrorCodes[9].DeepCopy()
	errMsg := "i am error message"
	codeWithErrMsg.Message = errMsg

	type args struct {
		msg []string
	}
	tests := []struct {
		name         string
		args         args
		HasCCEPrefix bool
		want         *ErrorCode
	}{
		// All test cases.
		{
			name: "simple case",
			want: simpleCode,
		},
		{
			name: "non-empty msg case",
			args: args{
				msg: []string{errMsg},
			},
			want: codeWithErrMsg,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewClusterNodeDeleteFailed(tt.args.msg...); !cmp.Equal(got, tt.want) {
				t.Errorf("NewClusterNodeDeleteFailed() = %v, want %v, diff is %s", got, tt.want, cmp.Diff(got, tt.want))
			}
		})
	}
}

func TestNewInstanceENINotDeleted(t *testing.T) {
	simpleCode := ReconcileErrorCodes[10].DeepCopy()
	simpleCode.Message = simpleCode.Code

	codeWithErrMsg := ReconcileErrorCodes[10].DeepCopy()
	errMsg := "i am error message"
	codeWithErrMsg.Message = errMsg

	type args struct {
		msg []string
	}
	tests := []struct {
		name         string
		args         args
		HasCCEPrefix bool
		want         *ErrorCode
	}{
		// All test cases.
		{
			name: "simple case",
			want: simpleCode,
		},
		{
			name: "non-empty msg case",
			args: args{
				msg: []string{errMsg},
			},
			want: codeWithErrMsg,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewInstanceENINotDeleted(tt.args.msg...); !cmp.Equal(got, tt.want) {
				t.Errorf("NewInstanceENINotDeleted() = %v, want %v, diff is %s", got, tt.want, cmp.Diff(got, tt.want))
			}
		})
	}
}

func TestNewInstanceVPCRouteNotDeleted(t *testing.T) {
	simpleCode := ReconcileErrorCodes[11].DeepCopy()
	simpleCode.Message = simpleCode.Code

	codeWithErrMsg := ReconcileErrorCodes[11].DeepCopy()
	errMsg := "i am error message"
	codeWithErrMsg.Message = errMsg

	type args struct {
		msg []string
	}
	tests := []struct {
		name         string
		args         args
		HasCCEPrefix bool
		want         *ErrorCode
	}{
		// All test cases.
		{
			name: "simple case",
			want: simpleCode,
		},
		{
			name: "non-empty msg case",
			args: args{
				msg: []string{errMsg},
			},
			want: codeWithErrMsg,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewInstanceVPCRouteNotDeleted(tt.args.msg...); !cmp.Equal(got, tt.want) {
				t.Errorf("NewInstanceVPCRouteNotDeleted() = %v, want %v, diff is %s", got, tt.want, cmp.Diff(got, tt.want))
			}
		})
	}
}

func TestNewInstanceBBCPrivateIPNotDeleted(t *testing.T) {
	simpleCode := ReconcileErrorCodes[12].DeepCopy()
	simpleCode.Message = simpleCode.Code

	codeWithErrMsg := ReconcileErrorCodes[12].DeepCopy()
	errMsg := "i am error message"
	codeWithErrMsg.Message = errMsg

	type args struct {
		msg []string
	}
	tests := []struct {
		name         string
		args         args
		HasCCEPrefix bool
		want         *ErrorCode
	}{
		// All test cases.
		{
			name: "simple case",
			want: simpleCode,
		},
		{
			name: "non-empty msg case",
			args: args{
				msg: []string{errMsg},
			},
			want: codeWithErrMsg,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewInstanceBBCPrivateIPNotDeleted(tt.args.msg...); !cmp.Equal(got, tt.want) {
				t.Errorf("NewInstanceBBCPrivateIPNotDeleted() = %v, want %v, diff is %s", got, tt.want, cmp.Diff(got, tt.want))
			}
		})
	}
}

func TestNewBCCNotExistAfterCreated(t *testing.T) {
	simpleCode := ReconcileErrorCodes[13].DeepCopy()
	simpleCode.Message = simpleCode.Code

	codeWithErrMsg := ReconcileErrorCodes[13].DeepCopy()
	errMsg := "i am error message"
	codeWithErrMsg.Message = errMsg

	type args struct {
		msg []string
	}
	tests := []struct {
		name         string
		args         args
		HasCCEPrefix bool
		want         *ErrorCode
	}{
		// All test cases.
		{
			name: "simple case",
			want: simpleCode,
		},
		{
			name: "non-empty msg case",
			args: args{
				msg: []string{errMsg},
			},
			want: codeWithErrMsg,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewBCCNotExistAfterCreated(tt.args.msg...); !cmp.Equal(got, tt.want) {
				t.Errorf("NewBCCNotExistAfterCreated() = %v, want %v, diff is %s", got, tt.want, cmp.Diff(got, tt.want))
			}
		})
	}
}

func TestNewBCCCannotDeletedIfPrepaid(t *testing.T) {
	simpleCode := ReconcileErrorCodes[14].DeepCopy()
	simpleCode.Message = simpleCode.Code

	codeWithErrMsg := ReconcileErrorCodes[14].DeepCopy()
	errMsg := "i am error message"
	codeWithErrMsg.Message = errMsg

	type args struct {
		msg []string
	}
	tests := []struct {
		name         string
		args         args
		HasCCEPrefix bool
		want         *ErrorCode
	}{
		// All test cases.
		{
			name: "simple case",
			want: simpleCode,
		},
		{
			name: "non-empty msg case",
			args: args{
				msg: []string{errMsg},
			},
			want: codeWithErrMsg,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewBCCCannotDeletedIfPrepaid(tt.args.msg...); !cmp.Equal(got, tt.want) {
				t.Errorf("NewBCCCannotDeletedIfPrepaid() = %v, want %v, diff is %s", got, tt.want, cmp.Diff(got, tt.want))
			}
		})
	}
}

func TestNewInstanceNoStock(t *testing.T) {
	simpleCode := ReconcileErrorCodes[15].DeepCopy()
	simpleCode.Message = simpleCode.Code

	codeWithErrMsg := ReconcileErrorCodes[15].DeepCopy()
	errMsg := "i am error message"
	codeWithErrMsg.Message = errMsg

	type args struct {
		msg []string
	}
	tests := []struct {
		name         string
		args         args
		HasCCEPrefix bool
		want         *ErrorCode
	}{
		// All test cases.
		{
			name: "simple case",
			want: simpleCode,
		},
		{
			name: "non-empty msg case",
			args: args{
				msg: []string{errMsg},
			},
			want: codeWithErrMsg,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewInstanceNoStock(tt.args.msg...); !cmp.Equal(got, tt.want) {
				t.Errorf("NewInstanceNoStock() = %v, want %v, diff is %s", got, tt.want, cmp.Diff(got, tt.want))
			}
		})
	}
}

func TestNewInstanceQuotaExceeded(t *testing.T) {
	simpleCode := ReconcileErrorCodes[16].DeepCopy()
	simpleCode.Message = simpleCode.Code

	codeWithErrMsg := ReconcileErrorCodes[16].DeepCopy()
	errMsg := "i am error message"
	codeWithErrMsg.Message = errMsg

	type args struct {
		msg []string
	}
	tests := []struct {
		name         string
		args         args
		HasCCEPrefix bool
		want         *ErrorCode
	}{
		// All test cases.
		{
			name: "simple case",
			want: simpleCode,
		},
		{
			name: "non-empty msg case",
			args: args{
				msg: []string{errMsg},
			},
			want: codeWithErrMsg,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewInstanceQuotaExceeded(tt.args.msg...); !cmp.Equal(got, tt.want) {
				t.Errorf("NewInstanceQuotaExceeded() = %v, want %v, diff is %s", got, tt.want, cmp.Diff(got, tt.want))
			}
		})
	}
}

func TestNewHostnameDuplicateInVPC(t *testing.T) {
	simpleCode := ReconcileErrorCodes[17].DeepCopy()
	simpleCode.Message = simpleCode.Code

	codeWithErrMsg := ReconcileErrorCodes[17].DeepCopy()
	errMsg := "i am error message"
	codeWithErrMsg.Message = errMsg

	type args struct {
		msg []string
	}
	tests := []struct {
		name         string
		args         args
		HasCCEPrefix bool
		want         *ErrorCode
	}{
		// All test cases.
		{
			name: "simple case",
			want: simpleCode,
		},
		{
			name: "non-empty msg case",
			args: args{
				msg: []string{errMsg},
			},
			want: codeWithErrMsg,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewHostnameDuplicateInVPC(tt.args.msg...); !cmp.Equal(got, tt.want) {
				t.Errorf("NewHostnameDuplicateInVPC() = %v, want %v, diff is %s", got, tt.want, cmp.Diff(got, tt.want))
			}
		})
	}
}

func TestNewInsufficientIPInSubnet(t *testing.T) {
	simpleCode := ReconcileErrorCodes[18].DeepCopy()
	simpleCode.Message = simpleCode.Code

	codeWithErrMsg := ReconcileErrorCodes[18].DeepCopy()
	errMsg := "i am error message"
	codeWithErrMsg.Message = errMsg

	type args struct {
		msg []string
	}
	tests := []struct {
		name         string
		args         args
		HasCCEPrefix bool
		want         *ErrorCode
	}{
		// All test cases.
		{
			name: "simple case",
			want: simpleCode,
		},
		{
			name: "non-empty msg case",
			args: args{
				msg: []string{errMsg},
			},
			want: codeWithErrMsg,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewInsufficientIPInSubnet(tt.args.msg...); !cmp.Equal(got, tt.want) {
				t.Errorf("NewInsufficientIPInSubnet() = %v, want %v, diff is %s", got, tt.want, cmp.Diff(got, tt.want))
			}
		})
	}
}

func TestNewVolumeNotAvailableInZone(t *testing.T) {
	simpleCode := ReconcileErrorCodes[19].DeepCopy()
	simpleCode.Message = simpleCode.Code

	codeWithErrMsg := ReconcileErrorCodes[19].DeepCopy()
	errMsg := "i am error message"
	codeWithErrMsg.Message = errMsg

	type args struct {
		msg []string
	}
	tests := []struct {
		name         string
		args         args
		HasCCEPrefix bool
		want         *ErrorCode
	}{
		// All test cases.
		{
			name: "simple case",
			want: simpleCode,
		},
		{
			name: "non-empty msg case",
			args: args{
				msg: []string{errMsg},
			},
			want: codeWithErrMsg,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewVolumeNotAvailableInZone(tt.args.msg...); !cmp.Equal(got, tt.want) {
				t.Errorf("NewVolumeNotAvailableInZone() = %v, want %v, diff is %s", got, tt.want, cmp.Diff(got, tt.want))
			}
		})
	}
}

func TestNewEIPByTrafficBandWidthQuotaExceeded(t *testing.T) {
	simpleCode := ReconcileErrorCodes[20].DeepCopy()
	simpleCode.Message = simpleCode.Code

	codeWithErrMsg := ReconcileErrorCodes[20].DeepCopy()
	errMsg := "i am error message"
	codeWithErrMsg.Message = errMsg

	type args struct {
		msg []string
	}
	tests := []struct {
		name         string
		args         args
		HasCCEPrefix bool
		want         *ErrorCode
	}{
		// All test cases.
		{
			name: "simple case",
			want: simpleCode,
		},
		{
			name: "non-empty msg case",
			args: args{
				msg: []string{errMsg},
			},
			want: codeWithErrMsg,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewEIPByTrafficBandWidthQuotaExceeded(tt.args.msg...); !cmp.Equal(got, tt.want) {
				t.Errorf("NewEIPByTrafficBandWidthQuotaExceeded() = %v, want %v, diff is %s", got, tt.want, cmp.Diff(got, tt.want))
			}
		})
	}
}

func TestNewDeleteInstanceWithUserCustomRoute(t *testing.T) {
	simpleCode := ReconcileErrorCodes[21].DeepCopy()
	simpleCode.Message = simpleCode.Code

	codeWithErrMsg := ReconcileErrorCodes[21].DeepCopy()
	errMsg := "i am error message"
	codeWithErrMsg.Message = errMsg

	type args struct {
		msg []string
	}
	tests := []struct {
		name         string
		args         args
		HasCCEPrefix bool
		want         *ErrorCode
	}{
		// All test cases.
		{
			name: "simple case",
			want: simpleCode,
		},
		{
			name: "non-empty msg case",
			args: args{
				msg: []string{errMsg},
			},
			want: codeWithErrMsg,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewDeleteInstanceWithUserCustomRoute(tt.args.msg...); !cmp.Equal(got, tt.want) {
				t.Errorf("NewDeleteInstanceWithUserCustomRoute() = %v, want %v, diff is %s", got, tt.want, cmp.Diff(got, tt.want))
			}
		})
	}
}

func TestNewInsufficientRootDisk(t *testing.T) {
	simpleCode := ReconcileErrorCodes[22].DeepCopy()
	simpleCode.Message = simpleCode.Code

	codeWithErrMsg := ReconcileErrorCodes[22].DeepCopy()
	errMsg := "i am error message"
	codeWithErrMsg.Message = errMsg

	type args struct {
		msg []string
	}
	tests := []struct {
		name         string
		args         args
		HasCCEPrefix bool
		want         *ErrorCode
	}{
		// All test cases.
		{
			name: "simple case",
			want: simpleCode,
		},
		{
			name: "non-empty msg case",
			args: args{
				msg: []string{errMsg},
			},
			want: codeWithErrMsg,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewInsufficientRootDisk(tt.args.msg...); !cmp.Equal(got, tt.want) {
				t.Errorf("NewInsufficientRootDisk() = %v, want %v, diff is %s", got, tt.want, cmp.Diff(got, tt.want))
			}
		})
	}
}

func TestNewBCCFixedIPNotExistAfterCreated(t *testing.T) {
	simpleCode := ReconcileErrorCodes[23].DeepCopy()
	simpleCode.Message = simpleCode.Code

	codeWithErrMsg := ReconcileErrorCodes[23].DeepCopy()
	errMsg := "i am error message"
	codeWithErrMsg.Message = errMsg

	type args struct {
		msg []string
	}
	tests := []struct {
		name         string
		args         args
		HasCCEPrefix bool
		want         *ErrorCode
	}{
		// All test cases.
		{
			name: "simple case",
			want: simpleCode,
		},
		{
			name: "non-empty msg case",
			args: args{
				msg: []string{errMsg},
			},
			want: codeWithErrMsg,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewBCCFixedIPNotExistAfterCreated(tt.args.msg...); !cmp.Equal(got, tt.want) {
				t.Errorf("NewBCCFixedIPNotExistAfterCreated() = %v, want %v, diff is %s", got, tt.want, cmp.Diff(got, tt.want))
			}
		})
	}
}

func TestNewInvalidBCCFlavorConfig(t *testing.T) {
	simpleCode := ReconcileErrorCodes[24].DeepCopy()
	simpleCode.Message = simpleCode.Code

	codeWithErrMsg := ReconcileErrorCodes[24].DeepCopy()
	errMsg := "i am error message"
	codeWithErrMsg.Message = errMsg

	type args struct {
		msg []string
	}
	tests := []struct {
		name         string
		args         args
		HasCCEPrefix bool
		want         *ErrorCode
	}{
		// All test cases.
		{
			name: "simple case",
			want: simpleCode,
		},
		{
			name: "non-empty msg case",
			args: args{
				msg: []string{errMsg},
			},
			want: codeWithErrMsg,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewInvalidBCCFlavorConfig(tt.args.msg...); !cmp.Equal(got, tt.want) {
				t.Errorf("NewInvalidBCCFlavorConfig() = %v, want %v, diff is %s", got, tt.want, cmp.Diff(got, tt.want))
			}
		})
	}
}

func TestNewCreateBLBError(t *testing.T) {
	simpleCode := ReconcileErrorCodes[25].DeepCopy()
	simpleCode.Message = simpleCode.Code

	codeWithErrMsg := ReconcileErrorCodes[25].DeepCopy()
	errMsg := "i am error message"
	codeWithErrMsg.Message = errMsg

	type args struct {
		msg []string
	}
	tests := []struct {
		name         string
		args         args
		HasCCEPrefix bool
		want         *ErrorCode
	}{
		// All test cases.
		{
			name: "simple case",
			want: simpleCode,
		},
		{
			name: "non-empty msg case",
			args: args{
				msg: []string{errMsg},
			},
			want: codeWithErrMsg,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewCreateBLBError(tt.args.msg...); !cmp.Equal(got, tt.want) {
				t.Errorf("NewCreateBLBError() = %v, want %v, diff is %s", got, tt.want, cmp.Diff(got, tt.want))
			}
		})
	}
}

func TestNewCreateEIPError(t *testing.T) {
	simpleCode := ReconcileErrorCodes[26].DeepCopy()
	simpleCode.Message = simpleCode.Code

	codeWithErrMsg := ReconcileErrorCodes[26].DeepCopy()
	errMsg := "i am error message"
	codeWithErrMsg.Message = errMsg

	type args struct {
		msg []string
	}
	tests := []struct {
		name         string
		args         args
		HasCCEPrefix bool
		want         *ErrorCode
	}{
		// All test cases.
		{
			name: "simple case",
			want: simpleCode,
		},
		{
			name: "non-empty msg case",
			args: args{
				msg: []string{errMsg},
			},
			want: codeWithErrMsg,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewCreateEIPError(tt.args.msg...); !cmp.Equal(got, tt.want) {
				t.Errorf("NewCreateEIPError() = %v, want %v, diff is %s", got, tt.want, cmp.Diff(got, tt.want))
			}
		})
	}
}

func TestNewTidyConfigurationNotExist(t *testing.T) {
	simpleCode := ReconcileErrorCodes[27].DeepCopy()
	simpleCode.Message = simpleCode.Code

	codeWithErrMsg := ReconcileErrorCodes[27].DeepCopy()
	errMsg := "i am error message"
	codeWithErrMsg.Message = errMsg

	type args struct {
		msg []string
	}
	tests := []struct {
		name         string
		args         args
		HasCCEPrefix bool
		want         *ErrorCode
	}{
		// All test cases.
		{
			name: "simple case",
			want: simpleCode,
		},
		{
			name: "non-empty msg case",
			args: args{
				msg: []string{errMsg},
			},
			want: codeWithErrMsg,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewTidyConfigurationNotExist(tt.args.msg...); !cmp.Equal(got, tt.want) {
				t.Errorf("NewTidyConfigurationNotExist() = %v, want %v, diff is %s", got, tt.want, cmp.Diff(got, tt.want))
			}
		})
	}
}

func TestNewTidyConfigurationInvalid(t *testing.T) {
	simpleCode := ReconcileErrorCodes[28].DeepCopy()
	simpleCode.Message = simpleCode.Code

	codeWithErrMsg := ReconcileErrorCodes[28].DeepCopy()
	errMsg := "i am error message"
	codeWithErrMsg.Message = errMsg

	type args struct {
		msg []string
	}
	tests := []struct {
		name         string
		args         args
		HasCCEPrefix bool
		want         *ErrorCode
	}{
		// All test cases.
		{
			name: "simple case",
			want: simpleCode,
		},
		{
			name: "non-empty msg case",
			args: args{
				msg: []string{errMsg},
			},
			want: codeWithErrMsg,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewTidyConfigurationInvalid(tt.args.msg...); !cmp.Equal(got, tt.want) {
				t.Errorf("NewTidyConfigurationInvalid() = %v, want %v, diff is %s", got, tt.want, cmp.Diff(got, tt.want))
			}
		})
	}
}

func TestNewTidyNotAllowedCurrently(t *testing.T) {
	simpleCode := ReconcileErrorCodes[29].DeepCopy()
	simpleCode.Message = simpleCode.Code

	codeWithErrMsg := ReconcileErrorCodes[29].DeepCopy()
	errMsg := "i am error message"
	codeWithErrMsg.Message = errMsg

	type args struct {
		msg []string
	}
	tests := []struct {
		name         string
		args         args
		HasCCEPrefix bool
		want         *ErrorCode
	}{
		// All test cases.
		{
			name: "simple case",
			want: simpleCode,
		},
		{
			name: "non-empty msg case",
			args: args{
				msg: []string{errMsg},
			},
			want: codeWithErrMsg,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewTidyNotAllowedCurrently(tt.args.msg...); !cmp.Equal(got, tt.want) {
				t.Errorf("NewTidyNotAllowedCurrently() = %v, want %v, diff is %s", got, tt.want, cmp.Diff(got, tt.want))
			}
		})
	}
}

func TestNewDeleteInstanceError(t *testing.T) {
	simpleCode := ReconcileErrorCodes[30].DeepCopy()
	simpleCode.Message = simpleCode.Code

	codeWithErrMsg := ReconcileErrorCodes[30].DeepCopy()
	errMsg := "i am error message"
	codeWithErrMsg.Message = errMsg

	type args struct {
		msg []string
	}
	tests := []struct {
		name         string
		args         args
		HasCCEPrefix bool
		want         *ErrorCode
	}{
		// All test cases.
		{
			name: "simple case",
			want: simpleCode,
		},
		{
			name: "non-empty msg case",
			args: args{
				msg: []string{errMsg},
			},
			want: codeWithErrMsg,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewDeleteInstanceError(tt.args.msg...); !cmp.Equal(got, tt.want) {
				t.Errorf("NewDeleteInstanceError() = %v, want %v, diff is %s", got, tt.want, cmp.Diff(got, tt.want))
			}
		})
	}
}

func TestNewKubeConfigExpiredOrInvalid(t *testing.T) {
	simpleCode := ReconcileErrorCodes[31].DeepCopy()
	simpleCode.Message = simpleCode.Code

	codeWithErrMsg := ReconcileErrorCodes[31].DeepCopy()
	errMsg := "i am error message"
	codeWithErrMsg.Message = errMsg

	type args struct {
		msg []string
	}
	tests := []struct {
		name         string
		args         args
		HasCCEPrefix bool
		want         *ErrorCode
	}{
		// All test cases.
		{
			name: "simple case",
			want: simpleCode,
		},
		{
			name: "non-empty msg case",
			args: args{
				msg: []string{errMsg},
			},
			want: codeWithErrMsg,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewKubeConfigExpiredOrInvalid(tt.args.msg...); !cmp.Equal(got, tt.want) {
				t.Errorf("NewKubeConfigExpiredOrInvalid() = %v, want %v, diff is %s", got, tt.want, cmp.Diff(got, tt.want))
			}
		})
	}
}

func TestNewSSHConnctionFailed(t *testing.T) {
	simpleCode := ReconcileErrorCodes[32].DeepCopy()
	simpleCode.Message = simpleCode.Code

	codeWithErrMsg := ReconcileErrorCodes[32].DeepCopy()
	errMsg := "i am error message"
	codeWithErrMsg.Message = errMsg

	type args struct {
		msg []string
	}
	tests := []struct {
		name         string
		args         args
		HasCCEPrefix bool
		want         *ErrorCode
	}{
		// All test cases.
		{
			name: "simple case",
			want: simpleCode,
		},
		{
			name: "non-empty msg case",
			args: args{
				msg: []string{errMsg},
			},
			want: codeWithErrMsg,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewSSHConnctionFailed(tt.args.msg...); !cmp.Equal(got, tt.want) {
				t.Errorf("NewSSHConnctionFailed() = %v, want %v, diff is %s", got, tt.want, cmp.Diff(got, tt.want))
			}
		})
	}
}

func TestNewK8SNodeNotReady(t *testing.T) {
	simpleCode := ReconcileErrorCodes[33].DeepCopy()
	simpleCode.Message = simpleCode.Code

	codeWithErrMsg := ReconcileErrorCodes[33].DeepCopy()
	errMsg := "i am error message"
	codeWithErrMsg.Message = errMsg

	type args struct {
		msg []string
	}
	tests := []struct {
		name         string
		args         args
		HasCCEPrefix bool
		want         *ErrorCode
	}{
		// All test cases.
		{
			name: "simple case",
			want: simpleCode,
		},
		{
			name: "non-empty msg case",
			args: args{
				msg: []string{errMsg},
			},
			want: codeWithErrMsg,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewK8SNodeNotReady(tt.args.msg...); !cmp.Equal(got, tt.want) {
				t.Errorf("NewK8SNodeNotReady() = %v, want %v, diff is %s", got, tt.want, cmp.Diff(got, tt.want))
			}
		})
	}
}

func TestNewDiskSizeQuotaExceededLimit(t *testing.T) {
	simpleCode := ReconcileErrorCodes[34].DeepCopy()
	simpleCode.Message = simpleCode.Code

	codeWithErrMsg := ReconcileErrorCodes[34].DeepCopy()
	errMsg := "i am error message"
	codeWithErrMsg.Message = errMsg

	type args struct {
		msg []string
	}
	tests := []struct {
		name         string
		args         args
		HasCCEPrefix bool
		want         *ErrorCode
	}{
		// All test cases.
		{
			name: "simple case",
			want: simpleCode,
		},
		{
			name: "non-empty msg case",
			args: args{
				msg: []string{errMsg},
			},
			want: codeWithErrMsg,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewDiskSizeQuotaExceededLimit(tt.args.msg...); !cmp.Equal(got, tt.want) {
				t.Errorf("NewDiskSizeQuotaExceededLimit() = %v, want %v, diff is %s", got, tt.want, cmp.Diff(got, tt.want))
			}
		})
	}
}

func TestNewInstanceNoStockInCurrentAZone(t *testing.T) {
	simpleCode := ReconcileErrorCodes[35].DeepCopy()
	simpleCode.Message = simpleCode.Code

	codeWithErrMsg := ReconcileErrorCodes[35].DeepCopy()
	errMsg := "i am error message"
	codeWithErrMsg.Message = errMsg

	type args struct {
		msg []string
	}
	tests := []struct {
		name         string
		args         args
		HasCCEPrefix bool
		want         *ErrorCode
	}{
		// All test cases.
		{
			name: "simple case",
			want: simpleCode,
		},
		{
			name: "non-empty msg case",
			args: args{
				msg: []string{errMsg},
			},
			want: codeWithErrMsg,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewInstanceNoStockInCurrentAZone(tt.args.msg...); !cmp.Equal(got, tt.want) {
				t.Errorf("NewInstanceNoStockInCurrentAZone() = %v, want %v, diff is %s", got, tt.want, cmp.Diff(got, tt.want))
			}
		})
	}
}

func TestNewBCCCreating(t *testing.T) {
	simpleCode := ReconcileErrorCodes[36].DeepCopy()
	simpleCode.Message = simpleCode.Code

	codeWithErrMsg := ReconcileErrorCodes[36].DeepCopy()
	errMsg := "i am error message"
	codeWithErrMsg.Message = errMsg

	type args struct {
		msg []string
	}
	tests := []struct {
		name         string
		args         args
		HasCCEPrefix bool
		want         *ErrorCode
	}{
		// All test cases.
		{
			name: "simple case",
			want: simpleCode,
		},
		{
			name: "non-empty msg case",
			args: args{
				msg: []string{errMsg},
			},
			want: codeWithErrMsg,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewBCCCreating(tt.args.msg...); !cmp.Equal(got, tt.want) {
				t.Errorf("NewBCCCreating() = %v, want %v, diff is %s", got, tt.want, cmp.Diff(got, tt.want))
			}
		})
	}
}

func TestNewCCEClusterQuotaExceeded(t *testing.T) {
	simpleCode := ReconcileErrorCodes[37].DeepCopy()
	simpleCode.Message = simpleCode.Code

	codeWithErrMsg := ReconcileErrorCodes[37].DeepCopy()
	errMsg := "i am error message"
	codeWithErrMsg.Message = errMsg

	type args struct {
		msg []string
	}
	tests := []struct {
		name         string
		args         args
		HasCCEPrefix bool
		want         *ErrorCode
	}{
		// All test cases.
		{
			name: "simple case",
			want: simpleCode,
		},
		{
			name: "non-empty msg case",
			args: args{
				msg: []string{errMsg},
			},
			want: codeWithErrMsg,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewCCEClusterQuotaExceeded(tt.args.msg...); !cmp.Equal(got, tt.want) {
				t.Errorf("NewCCEClusterQuotaExceeded() = %v, want %v, diff is %s", got, tt.want, cmp.Diff(got, tt.want))
			}
		})
	}
}
