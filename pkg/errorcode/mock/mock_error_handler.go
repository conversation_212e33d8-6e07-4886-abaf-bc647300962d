// Code generated by MockGen. DO NOT EDIT.
// Source: ./error_handler.go

// Package mock_errorcode is a generated GoMock package.
package errorcode

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	errorcode "icode.baidu.com/baidu/cce-test/e2e-test/pkg/errorcode"
)

// MockInterface is a mock of Interface interface.
type MockInterface struct {
	ctrl     *gomock.Controller
	recorder *MockInterfaceMockRecorder
}

// MockInterfaceMockRecorder is the mock recorder for MockInterface.
type MockInterfaceMockRecorder struct {
	mock *MockInterface
}

// NewMockInterface creates a new mock instance.
func NewMockInterface(ctrl *gomock.Controller) *MockInterface {
	mock := &MockInterface{ctrl: ctrl}
	mock.recorder = &MockInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockInterface) EXPECT() *MockInterfaceMockRecorder {
	return m.recorder
}

// CustomAbortJson mocks base method.
func (m *MockInterface) CustomAbortJson(status int, body interface{}) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "CustomAbortJson", status, body)
}

// CustomAbortJson indicates an expected call of CustomAbortJson.
func (mr *MockInterfaceMockRecorder) CustomAbortJson(status, body interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CustomAbortJson", reflect.TypeOf((*MockInterface)(nil).CustomAbortJson), status, body)
}

// ErrorHandler mocks base method.
func (m *MockInterface) ErrorHandler(ctx context.Context, code *errorcode.ErrorCode, level errorcode.Level, format string, a ...interface{}) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, code, level, format}
	for _, a_2 := range a {
		varargs = append(varargs, a_2)
	}
	m.ctrl.Call(m, "ErrorHandler", varargs...)
}

// ErrorHandler indicates an expected call of ErrorHandler.
func (mr *MockInterfaceMockRecorder) ErrorHandler(ctx, code, level, format interface{}, a ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, code, level, format}, a...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ErrorHandler", reflect.TypeOf((*MockInterface)(nil).ErrorHandler), varargs...)
}
