//go:generate go run gen_error_code_func.go

package errorcode

const (
	SuggestCCETicket   = "请提工单，并附上完整的提示信息。"
	SuggestMessage     = "请参考message中信息解决，然后重试。如无法自助解决，" + SuggestCCETicket
	SuggestBCCStock    = "BCC/BBC资源不足。删除该节点，提工单解决资源不足问题，问题所属产品请选择：【云服务器BCC】，并附上完整的提示信息。"
	SuggestBCCRollback = "BCC/BBC订单回滚。删除该节点并提工单，问题所属产品请选择：【云服务器BCC】，并附上完整的提示信息。"
)

// ReconcileErrorCodes - CCE controller reconcile step 错误码
var ReconcileErrorCodes = []*ErrorCode{
	// Cluster step event
	{
		Code:       "BCCLackedWhenCreateBLB",
		Suggestion: "在该VPC(失败信息中vpcID对应的VPC) 下任意建一台BCC, 等BLB建完后再删除BCC。",
	},
	{
		Code:       "AccountInsufficientBalance",
		Suggestion: "请先充值 (余额100元以上)，然后清理失败的资源，并重新创建。",
	},
	{
		Code:       "BLBPermissionDenied",
		Suggestion: "为该用户添加BLB权限。",
	},
	{
		Code:       "BLBQuotaExceeded",
		Suggestion: "为该账号增加BLB配额。",
	},
	{
		Code:       "NeedFaceAuthentication",
		Suggestion: "下载百度智能云 APP，在\"我的-实名认证\"中完成人脸认证，然后重试。",
	},
	{
		Code:       "ManagedMasterCreateFailed",
		Suggestion: SuggestCCETicket,
	},
	{
		Code:       "CustomMasterCreateFailed",
		Suggestion: "查看Master节点的进度展示，根据失败信息提示解决问题，然后重试。",
	},
	{
		Code:       "ManagedMasterDeleteFailed",
		Suggestion: SuggestCCETicket,
	},
	{
		Code:       "CustomMasterDeleteFailed",
		Suggestion: "查看Master节点的进度展示，根据失败信息提示解决问题，然后重试。",
	},
	{
		Code:       "ClusterNodeDeleteFailed",
		Suggestion: "查看Node节点的进度展示，根据失败信息提示解决问题，然后重试。",
	},

	// Instance step event
	{
		Code:       "InstanceENINotDeleted",
		Suggestion: SuggestMessage, // 如果能在message里给出eni实例，这里就可以让用户清理？
	},
	{
		Code:       "InstanceVPCRouteNotDeleted",
		Suggestion: SuggestMessage,
	},
	{
		Code:       "InstanceBBCPrivateIPNotDeleted",
		Suggestion: SuggestMessage,
	},
	{
		Code:       "BCCNotExistAfterCreated",
		Suggestion: SuggestBCCRollback,
	},
	{
		Code:       "BCCCannotDeletedIfPrepaid",
		Suggestion: "预付费无法删除。重试移出节点操作，请勿勾选删除实例选项。", // 目前后端是自动移出了
	},
	{
		Code:       "InstanceNoStock",
		Suggestion: SuggestBCCStock,
	},
	{
		Code:       "InstanceQuotaExceeded",
		Suggestion: "为该账号增加BCC配额。",
	},
	{
		Code:       "HostnameDuplicateInVPC",
		Suggestion: "编辑节点YAML更换hostname，然后重试。",
	},
	{
		Code:       "InsufficientIPInSubnet",
		Suggestion: "删除该节点并重新创建，创建时选择有IP余量的子网。或者清理该子网内占用IP的无用资源，然后重试。",
	},
	{
		Code:       "VolumeNotAvailableInZone",
		Suggestion: "删除该节点并重新创建，创建时选择其他可用区。如无法自助解决，" + SuggestCCETicket,
	},
	{
		Code:       "EIPByTrafficBandWidthQuotaExceeded",
		Suggestion: "提工单提升按流量计费的 EIP 总带宽配额，问题所属产品请选择：【弹性公网IP EIP】。",
	},
	{
		Code:       "DeleteInstanceWithUserCustomRoute",
		Suggestion: "清理该节点对应BCC/BBC实例的路由规则，然后重试。",
	},
	{
		Code:       "InsufficientRootDisk",
		Suggestion: "编辑节点YAML，增加系统盘大小（rootDiskSize），然后重试。",
	},
	{
		Code:       "BCCFixedIPNotExistAfterCreated",
		Suggestion: "删除该节点并重新创建，创建时选择其他可用区或规格。如无法自助解决，" + SuggestCCETicket,
	},
	{
		Code:       "InvalidBCCFlavorConfig",
		Suggestion: SuggestCCETicket,
	},
	{
		Code:       "CreateBLBError",
		Suggestion: SuggestMessage,
	},
	{
		Code:       "CreateEIPError",
		Suggestion: SuggestMessage,
	},
	{
		Code:       "TidyConfigurationNotExist",
		Suggestion: "潮汐资源套餐不存在，请确认潮汐套餐存在后再重试。",
	},
	{
		Code:       "TidyConfigurationInvalid",
		Suggestion: "潮汐资源配置错误，请确认后再重试。",
	},
	{
		Code:       "TidyNotAllowedCurrently",
		Suggestion: "不在潮汐套餐购买的时间段内，请确认后再重试。",
	},
	{
		Code:       "DeleteInstanceError",
		Suggestion: "删除机器报错。请提工单，问题所属产品请选择：【云服务器BCC】。并附上完整的提示信息。",
	},

	// deployer code
	{
		Code:       "KubeConfigExpiredOrInvalid",
		Suggestion: SuggestCCETicket,
	},
	{
		Code:       "SSHConnctionFailed",
		Suggestion: "SSH连接失败，请自查：1.机器是否能正常登陆；2.填写的密码是否正确；3.机器是否有安全组或其他登陆限制。如无法自助解决，" + SuggestCCETicket,
	},
	{
		Code:       "K8SNodeNotReady",
		Suggestion: "通过kubectl describe node查看错误信息，或登陆机器通过journalctl -u kubelet查看日志排查。如无法自助解决，" + SuggestCCETicket,
	},
	{
		Code:       "DiskSizeQuotaExceededLimit",
		Suggestion: "云磁盘配额不足，请至配额管理申请配额。",
	},
	{
		Code:       "InstanceNoStockInCurrentAZone",
		Suggestion: "当前可用区无BCC资源，删除该节点并重新创建，创建时选择其他可用区。如无法自助解决，请提工单，问题所属产品请选择：【云服务器BCC】。",
	},
	{
		Code:       "BCCCreating",
		Suggestion: "BCC创建中，请稍后",
	},
	{
		Code:       "CCEClusterQuotaExceeded",
		Suggestion: "CCE集群配额不足，请为该账号增加CCE集群配额。",
	},
	{
		Code:       "LccConfigNotExist",
		Suggestion: "子网未正确关联LCC配置项，请确认LCC服务是否正常开通。如无法自助解决，" + SuggestCCETicket,
	},
}
