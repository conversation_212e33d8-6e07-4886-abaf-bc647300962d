package addon

import (
	"context"
	"reflect"
	"testing"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/plugin/clients"
)

func TestNewKunlunNvidia(t *testing.T) {
	type args struct {
		ctx  context.Context
		base *clients.Clients
		c    Config
	}
	tests := []struct {
		name    string
		args    args
		want    Interface
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "normal",
			args: args{
				ctx: context.TODO(),
				base: &clients.Clients{
					Config: &clients.Config{
						KunlunNvidiaImageID: "KunlunNvidiaImageID",
					},
				},
				c: nil,
			},
			want: &KunlunNvidia{
				base: &clients.Clients{
					Config: &clients.Config{
						KunlunNvidiaImageID: "KunlunNvidiaImageID",
					},
				},
				config: &KunlunNvidiaConfig{
					KunlunNvidiaImageID: "KunlunNvidiaImageID",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := NewKunlunNvidia(tt.args.ctx, tt.args.base, tt.args.c)
			if (err != nil) != tt.wantErr {
				t.Errorf("NewKunlunNvidia() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("NewKunlunNvidia() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestKunlunNvidia_RenderValuesYAML(t *testing.T) {
	valuesTemp := `
	image: {{.KunlunNvidiaImageID}}
`

	values := `
	image: KunlunNvidiaImageID
`
	type fields struct {
		base   *clients.Clients
		config *KunlunNvidiaConfig
	}
	type args struct {
		ctx        context.Context
		valuesTemp string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    string
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "normal",
			fields: fields{
				base: &clients.Clients{},
				config: &KunlunNvidiaConfig{
					KunlunNvidiaImageID: "KunlunNvidiaImageID",
				},
			},
			args: args{
				ctx:        context.TODO(),
				valuesTemp: valuesTemp,
			},
			want: values,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &KunlunNvidia{
				base:   tt.fields.base,
				config: tt.fields.config,
			}
			got, err := c.RenderValuesYAML(tt.args.ctx, tt.args.valuesTemp)
			if (err != nil) != tt.wantErr {
				t.Errorf("KunlunNvidia.RenderValuesYAML() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("KunlunNvidia.RenderValuesYAML() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestKunlunNvidia_Name(t *testing.T) {
	type fields struct {
		base   *clients.Clients
		config *KunlunNvidiaConfig
	}
	type args struct {
		ctx context.Context
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   Type
	}{
		// TODO: Add test cases.
		{
			name: "normal",
			fields: fields{
				base:   &clients.Clients{},
				config: &KunlunNvidiaConfig{},
			},
			args: args{
				ctx: context.TODO(),
			},
			want: AddonKunlunNvidia,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &KunlunNvidia{
				base:   tt.fields.base,
				config: tt.fields.config,
			}
			if got := c.Name(tt.args.ctx); got != tt.want {
				t.Errorf("KunlunNvidia.Name() = %v, want %v", got, tt.want)
			}
		})
	}
}
