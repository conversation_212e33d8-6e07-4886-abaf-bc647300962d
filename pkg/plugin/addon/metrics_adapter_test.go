/**
 * @Author: pansiyuan02
 * @Description:
 * @File:  cluster_autoscaler_test
 * @Version: 1.0.0
 * @Date: 2020/7/30 5:25 下午
 */
package addon

import (
	"context"
	"reflect"
	"testing"

	ccev1 "icode.baidu.com/baidu/cce-test/e2e-test/pkg/crd/api/v1"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/plugin/clients"
	ccetypes "icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
)

func TestNewMetricsAdapter(t *testing.T) {
	type args struct {
		ctx  context.Context
		base *clients.Clients
		c    Config
	}
	tests := []struct {
		name    string
		args    args
		want    Interface
		wantErr bool
	}{
		{
			name: "正常流程: amd64",
			args: args{
				ctx: context.Background(),
				base: &clients.Clients{
					Config: &clients.Config{
						MetricsAdapterImageID: "registry.baidubce.com/cce-plugin-pro/cce-metrics-adapter:1.0.0",
					},
					Cluster: &ccev1.Cluster{
						Spec: ccetypes.ClusterSpec{
							ClusterType: ccetypes.ClusterTypeNormal,
						},
					},
				},
			},
			want: &metricsAdapter{
				base: &clients.Clients{
					Config: &clients.Config{
						MetricsAdapterImageID: "registry.baidubce.com/cce-plugin-pro/cce-metrics-adapter:1.0.0",
					},
					Cluster: &ccev1.Cluster{
						Spec: ccetypes.ClusterSpec{
							ClusterType: ccetypes.ClusterTypeNormal,
						},
					},
				},
				config: &MetricsAdapterConfig{
					MetricsAdapterImageID:  "registry.baidubce.com/cce-plugin-pro/cce-metrics-adapter:1.0.0",
					MetricsAdapterReplicas: defaultMetricsAdapterReplicas,
				},
			},
			wantErr: false,
		},
		{
			name: "正常流程: arm64",
			args: args{
				ctx: context.Background(),
				base: &clients.Clients{
					Config: &clients.Config{
						MetricsAdapterImageID: "registry.baidubce.com/cce-plugin-pro/cce-metrics-adapter:1.0.0",
					},
					Cluster: &ccev1.Cluster{
						Spec: ccetypes.ClusterSpec{
							ClusterType: ccetypes.ClusterTypeARM,
						},
					},
				},
			},
			want: &metricsAdapter{
				base: &clients.Clients{
					Config: &clients.Config{
						MetricsAdapterImageID: "registry.baidubce.com/cce-plugin-pro/cce-metrics-adapter:1.0.0",
					},
					Cluster: &ccev1.Cluster{
						Spec: ccetypes.ClusterSpec{
							ClusterType: ccetypes.ClusterTypeARM,
						},
					},
				},
				config: &MetricsAdapterConfig{
					MetricsAdapterImageID:  "registry.baidubce.com/cce-plugin-pro/cce-metrics-adapter-arm64:1.0.0",
					MetricsAdapterReplicas: defaultMetricsAdapterReplicas,
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := NewMetricsAdapter(tt.args.ctx, tt.args.base, tt.args.c)
			if (err != nil) != tt.wantErr {
				t.Errorf("NewMetricsAdapter() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("NewMetricsAdapter() got = %v, want %v", got, tt.want)
			}
		})
	}
}
