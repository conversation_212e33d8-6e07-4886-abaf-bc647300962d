package addon

import (
	"context"
	"reflect"
	"testing"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/plugin/clients"
)

func TestNewNetworkInspector(t *testing.T) {
	type args struct {
		ctx  context.Context
		base *clients.Clients
		c    Config
	}
	tests := []struct {
		name    string
		args    args
		want    Interface
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "normal",
			args: args{
				ctx: context.TODO(),
				base: &clients.Clients{
					Config: &clients.Config{
						NetworkInspectorImageID:       "NetworkInspectorImageID",
						NetworkInspectorResultImageID: "NetworkInspectorResultImageID",
					},
				},
				c: nil,
			},
			want: &networkInspector{
				base: &clients.Clients{
					Config: &clients.Config{
						NetworkInspectorImageID:       "NetworkInspectorImageID",
						NetworkInspectorResultImageID: "NetworkInspectorResultImageID",
					},
				},
				config: &networkInspectorConfig{
					resultImageID:    "NetworkInspectorResultImageID",
					inspectorImageID: "NetworkInspectorImageID",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := NewNetworkInspector(tt.args.ctx, tt.args.base, tt.args.c)
			if (err != nil) != tt.wantErr {
				t.Errorf("NewNetworkInspector() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("NewNetworkInspector() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_networkInspector_RenderValuesYAML(t *testing.T) {
	valuesTemp := `
	InspectorImageID: {{.InspectorImageID}}
	ResultImageID: {{.ResultImageID}}
`

	values := `
	InspectorImageID: NetworkInspectorImageID
	ResultImageID: NetworkInspectorResultImageID
`
	type fields struct {
		base   *clients.Clients
		config *networkInspectorConfig
	}
	type args struct {
		ctx        context.Context
		valuesTemp string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    string
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "normal",
			fields: fields{
				base: &clients.Clients{},
				config: &networkInspectorConfig{
					resultImageID:    "NetworkInspectorResultImageID",
					inspectorImageID: "NetworkInspectorImageID",
				},
			},
			args: args{
				ctx:        context.TODO(),
				valuesTemp: valuesTemp,
			},
			want: values,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &networkInspector{
				base:   tt.fields.base,
				config: tt.fields.config,
			}
			got, err := c.RenderValuesYAML(tt.args.ctx, tt.args.valuesTemp)
			if (err != nil) != tt.wantErr {
				t.Errorf("networkInspector.RenderValuesYAML() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("networkInspector.RenderValuesYAML() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_networkInspector_Name(t *testing.T) {
	type fields struct {
		base   *clients.Clients
		config *networkInspectorConfig
	}
	type args struct {
		ctx context.Context
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   Type
	}{
		// TODO: Add test cases.
		{
			name: "normal",
			fields: fields{
				base:   &clients.Clients{},
				config: &networkInspectorConfig{},
			},
			args: args{
				ctx: context.TODO(),
			},
			want: AddonNetworkInspector,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &networkInspector{
				base:   tt.fields.base,
				config: tt.fields.config,
			}
			if got := c.Name(tt.args.ctx); got != tt.want {
				t.Errorf("networkInspector.Name() = %v, want %v", got, tt.want)
			}
		})
	}
}
