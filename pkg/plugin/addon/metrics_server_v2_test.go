// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2022/1/6 下午4:41, by <EMAIL>, create
*/
/*
DESCRIPTION
*/

package addon

import (
	"context"
	"reflect"
	"testing"

	ccev1 "icode.baidu.com/baidu/cce-test/e2e-test/pkg/crd/api/v1"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/plugin/clients"
	ccetypes "icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
)

func Test_metricsServerV2_RenderValuesYAML(t *testing.T) {
	type fields struct {
		base   *clients.Clients
		config *MetricsServerConfigV2
	}
	type args struct {
		ctx        context.Context
		valuesTemp string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    string
		wantErr bool
	}{
		{
			name: "render metrics-server values.yaml",
			fields: fields{
				base: &clients.Clients{
					Cluster: &ccev1.Cluster{
						Spec: ccetypes.ClusterSpec{
							ClusterID: "test",
						},
					},
				},
				config: &MetricsServerConfigV2{
					MetricsServerReplicas:          1,
					MetricsServerScraperReplicas:   1,
					MetricsServerV2ImageID:         "registry.baidubce.com/cce-plugin-pro/metrics-server:v0.5.2",
					DashboardMetricsScraperImageID: "registry.baidubce.com/cce-plugin-pro/dashboard-metrics-scraper:v1.0.8",
					OSType:                         "linux",
				},
			},
			args: args{
				ctx: context.TODO(),
				valuesTemp: `metricsServerReplicas: {{.MetricsServerReplicas}}
metricsServerScraperReplicas: {{.MetricsServerScraperReplicas}}
metricsServerImageID: {{.MetricsServerImageID}}
dashboardMetricsScraperImage: {{.DashboardMetricsScraperImageID}}
osType: {{.OSType}}`,
			},
			want: `metricsServerReplicas: 1
metricsServerScraperReplicas: 1
metricsServerImageID: registry.baidubce.com/cce-plugin-pro/metrics-server:v0.5.2
dashboardMetricsScraperImage: registry.baidubce.com/cce-plugin-pro/dashboard-metrics-scraper:v1.0.8
osType: linux`,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &metricsServerV2{
				base:   tt.fields.base,
				config: tt.fields.config,
			}
			got, err := c.RenderValuesYAML(tt.args.ctx, tt.args.valuesTemp)
			if (err != nil) != tt.wantErr {
				t.Errorf("RenderValuesYAML() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("RenderValuesYAML() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestNewMetricsServerV2(t *testing.T) {
	type args struct {
		ctx  context.Context
		base *clients.Clients
		c    Config
	}
	tests := []struct {
		name    string
		args    args
		want    Interface
		wantErr bool
	}{
		{
			name: "正常流程: amd64",
			args: args{
				ctx: context.Background(),
				base: &clients.Clients{
					Config: &clients.Config{
						MetricsServerV2ImageID:         "registry.baidubce.com/cce-plugin-pro/metrics-server:v0.5.2",
						DashboardMetricsScraperImageID: "registry.baidubce.com/cce-plugin-pro/dashboard-metrics-scraper:v1.0.8",
					},
					Cluster: &ccev1.Cluster{
						Spec: ccetypes.ClusterSpec{
							ClusterType: ccetypes.ClusterTypeNormal,
							ContainerNetworkConfig: ccetypes.ContainerNetworkConfig{
								ClusterIPServiceCIDR: "***********/16",
							},
						},
					},
				},
			},
			want: &metricsServerV2{
				base: &clients.Clients{
					Config: &clients.Config{
						MetricsServerV2ImageID:         "registry.baidubce.com/cce-plugin-pro/metrics-server:v0.5.2",
						DashboardMetricsScraperImageID: "registry.baidubce.com/cce-plugin-pro/dashboard-metrics-scraper:v1.0.8",
					},
					Cluster: &ccev1.Cluster{
						Spec: ccetypes.ClusterSpec{
							ClusterType: ccetypes.ClusterTypeNormal,
							ContainerNetworkConfig: ccetypes.ContainerNetworkConfig{
								ClusterIPServiceCIDR: "***********/16",
							},
						},
					},
				},
				config: &MetricsServerConfigV2{
					MetricsServerReplicas:          1,
					MetricsServerScraperReplicas:   1,
					MetricsServerV2ImageID:         "registry.baidubce.com/cce-plugin-pro/metrics-server:v0.5.2",
					DashboardMetricsScraperImageID: "registry.baidubce.com/cce-plugin-pro/dashboard-metrics-scraper:v1.0.8",
					OSType:                         "linux",
				},
			},
			wantErr: false,
		},
		{
			name: "正常流程: arm64",
			args: args{
				ctx: context.Background(),
				base: &clients.Clients{
					Config: &clients.Config{
						MetricsServerV2ImageID:         "registry.baidubce.com/cce-plugin-pro/metrics-server:v0.5.2",
						DashboardMetricsScraperImageID: "registry.baidubce.com/cce-plugin-pro/dashboard-metrics-scraper:v1.0.8",
					},
					Cluster: &ccev1.Cluster{
						Spec: ccetypes.ClusterSpec{
							ClusterType: ccetypes.ClusterTypeARM,
							ContainerNetworkConfig: ccetypes.ContainerNetworkConfig{
								ClusterIPServiceCIDR: "***********/16",
							},
						},
					},
				},
			},
			want: &metricsServerV2{
				base: &clients.Clients{
					Config: &clients.Config{
						MetricsServerV2ImageID:         "registry.baidubce.com/cce-plugin-pro/metrics-server:v0.5.2",
						DashboardMetricsScraperImageID: "registry.baidubce.com/cce-plugin-pro/dashboard-metrics-scraper:v1.0.8",
					},
					Cluster: &ccev1.Cluster{
						Spec: ccetypes.ClusterSpec{
							ClusterType: ccetypes.ClusterTypeARM,
							ContainerNetworkConfig: ccetypes.ContainerNetworkConfig{
								ClusterIPServiceCIDR: "***********/16",
							},
						},
					},
				},
				config: &MetricsServerConfigV2{
					MetricsServerReplicas:          1,
					MetricsServerScraperReplicas:   1,
					MetricsServerV2ImageID:         "registry.baidubce.com/cce-plugin-pro/metrics-server-arm64:v0.5.2",
					DashboardMetricsScraperImageID: "registry.baidubce.com/cce-plugin-pro/dashboard-metrics-scraper-arm64:v1.0.8",
					OSType:                         "linux",
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := NewMetricsServerV2(tt.args.ctx, tt.args.base, tt.args.c)
			if (err != nil) != tt.wantErr {
				t.Errorf("NewMetricsServerV2() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("NewMetricsServerV2() got = %v, want %v", got, tt.want)
			}
		})
	}
}
