// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2019/10/26 00:39:00, by <EMAIL>, create
*/
/*
DESCRIPTION
This file contains the
*/

package addon

import (
	"context"
	"errors"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/plugin/clients"
)

//go:generate mockgen -destination=./mock/mock_client.go -package=mock icode.baidu.com/baidu/cce-test/e2e-test/pkg/plugin/addon Interface

// Interface - CCE K8S 插件需要实现方法
type Interface interface {
	Name(ctx context.Context) Type
	Namespace(ctx context.Context) string
	HelmChartName(ctx context.Context) HelmChartName
	HelmChartVersion(ctx context.Context) string
	ModifyHelmChartValues(ctx context.Context, values string) (string, error) // in case the addon needs to modify the values.yml
	RenderValuesYAML(ctx context.Context, valuesTpl string) (string, error)
}

// Type - CCE 支持插件类型
type Type string

type HelmChartName string

// SupportAddons - CCE 插件管理支持的插件类型
// 各插件通过 init() 方法实现注册, 参考 CoreDNS 实现
var SupportAddons = map[Type]any{}

// HelmSupportedAddons Helm 插件部署所支持的插件 Key 为 ChartName
var HelmSupportedAddons = map[Type]any{}

// Config - 插件定义自己 Config 用于渲染 helm values.yaml,
// Config 包含两部分参数:
// 1. 用户可自定义: 定义为大写, 对外暴露;
// 2. 系统自动生成: 定义为小写, 不对外暴露.
// ps: 具体可参考 CoreDNSConfig 定义.
type Config interface {
	Config()
}

// NewClient - 返回 K8S 插件对应实现
func NewClient(ctx context.Context, pluginName string, config Config, base *clients.Clients) (Interface, error) {
	if base == nil {
		return nil, errors.New("base is nil")
	}

	// TODO 部署 NvidiaDevicePlugin 插件
	switch Type(pluginName) {
	case AddonCoreDNS:
		return NewCoreDNS(ctx, base, config)
	case AddonMetricsServer:
		return NewMetricsServer(ctx, base, config)
	case AddonMetricsServerV2:
		return NewMetricsServerV2(ctx, base, config)
	case AddonKubeProxy:
		return NewKubeProxy(ctx, base, config)
	case AddonKubeProxyV2:
		return NewKubeProxyV2(ctx, base, config)
	case AddonCilium:
		return NewCilium(ctx, base, config)
	case AddonCCENetworkV2:
		return NewCCENetworkPlugin(ctx, base, config)
	case AddonKubeProxyUpgrade:
		return NewKubeProxyUpgrade(ctx, base, config)
	case AddonVPCCNI:
		return NewVPCCNI(ctx, base, config)
	case AddonVPCRoute:
		return NewVPCRoute(ctx, base, config)
	case AddonVPCNative:
		return NewVPCNative(ctx, base, config)
	case AddonCVEniCni:
		return NewCVEniCni(ctx, base, config)
	case AddonNodeLocalDNS:
		return NewNodeLocalDNS(ctx, base, config)
	case AddonCalicoFelix:
		return NewCalicoFelix(ctx, base, config)
	case AddonCalicoFelixV2:
		return NewCalicoFelixV2(ctx, base, config)
	case AddonNetworkInspector:
		return NewNetworkInspector(ctx, base, config)
	case AddonCCEIngressController:
		return NewCCEIngressController(ctx, base, config)
	case AddonMetricsAdapter:
		return NewMetricsAdapter(ctx, base, config)
	case AddonCronHPA:
		return NewCronHPA(ctx, base, config)
	case AddonNvidiaDevicePlugin:
		return NewNvidiaDevicePlugin(ctx, base, config)
	case AddonKunlunNvidia:
		return NewKunlunNvidia(ctx, base, config)
	case AddonKongMingNvidiaGPU:
		return NewKongmingNvidia(ctx, base, config)
	case AddonIPMasqAgent:
		return NewIPMasqAgent(ctx, base, config)
	case AddonClusterAutoscaler:
		return NewClusterAutoscaler(ctx, base, config)
	case AddonCoreDNSForServerless:
		return NewCoreDNSForServerless(ctx, base, config)
	case AddonCCECalico:
		return NewCCECalico(ctx, base, config)
	case AddOnCCELBController:
		return NewLBController(ctx, base, config)
	case AddonCloudEdgeTunnelCoreDNS:
		return NewCloudEdgeTunnelCoreDNS(ctx, base, config)
	case AddonCloudEdgeClusterCalico:
		return NewCloudEdgeClusterCalico(ctx, base, config)
	case AddonCloudTunnel:
		return NewCloudTunnel(ctx, base, config)
	case AddonEdgeTunnel:
		return NewEdgeTunnel(ctx, base, config)
	case AddOnCCECloudEdgeClusterLBController:
		return NewCloudEdgeClusterLBController(ctx, base, config)
	case AddonCloudEdgeClusterMetricsServer:
		return NewCloudEdgeClusterMetricsServer(ctx, base, config)
	case AddonCloudEdgeClusterServiceGroupController:
		return NewCloudEdgeClusterServiceGroupController(ctx, base, config)
	case AddonCloudEdgeClusterBECLBController:
		return NewCloudEdgeClusterBECLBController(ctx, base, config)
	case AddonCloudEdgeClusterTopologyServer:
		return NewCloudEdgeClusterTopologyServer(ctx, base, config)
	case AddonCoreDNSServiceTopology:
		return NewCoreDNSServiceTopology(ctx, base, config)
	case AddonVolcano:
		return NewVolcano(ctx, base, config)
	case AddonAIBox:
		return NewAIBox(ctx, base, config)
	case NvidiaGPUPrometheusExporter:
		return NewNvidiaGPUPrometheusExporter(ctx, base, config)
	case DCGMExporter:
		return NewDCGMExporter(ctx, base, config)
	case AddOnCCECloudNodeController:
		return NewCloudNodeController(ctx, base, config)
	case CCEMonitorToolkit:
		return NewAddonCCEMonitorToolkit(ctx, base, config)
	case CCEMonitorCollector:
		return NewAddonCCEMonitorCollector(ctx, base, config)
	case AddonCCEIngressNginxController:
		return NewCCEIngressNginxController(ctx, base, config)
	case AddonCCEGPUManager:
		return NewCCEGPUManager(ctx, base, config)
	case AddonCCERDMAPlugin:
		return NewCCERDMAPlugin(ctx, base, config)
	case AddOnCCENodeFeatureDiscovery:
		return NewNodeFeatureDiscoveryController(ctx, base, config)
	case AddOnCCECSIBOSPlugin:
		return NewCSIBOSPlugin(ctx, base, config)
	case AddOnCCECSICDSPlugin:
		return NewCSICDSPlugin(ctx, base, config)
	case AddonTelegraf:
		return NewTelegraf(ctx, base, config)
	case AddonKubeStateMetrics:
		return NewKubeStateMetrics(ctx, base, config)
	case AddonHybridManager:
		return NewHybridManager(ctx, base, config)
	case AddonNodeProblemDetector:
		return NewNodeProblemDetector(ctx, base, config)
	case AddonCCEFluid:
		return NewCCEFluid(ctx, base, config)
	case AddonCCEProMaster:
		return NewCCEProMaster(ctx, base, config)
	case AddonCCEProETCD:
		return NewCCEProETCD(ctx, base, config)
	case AddonCCEProETCDEvents:
		return NewCCEProETCDEvents(ctx, base, config)
	case AddonCCEProAPIServer:
		return NewCCEProAPIServer(ctx, base, config)
	case AddonCCEProControllerManager:
		return NewCCEProControllerManager(ctx, base, config)
	case AddonCCEProKubeScheduler:
		return NewCCEProKubeScheduler(ctx, base, config)
	case AddonCCEProExternalAuditor:
		return NewCCEProExternalAuditor(ctx, base, config)
	case AddonCCEProDebuger:
		return NewCCEProDebuger(ctx, base, config)
	case AddonCCEProMasterPlugin:
		return NewCCEProMasterPlugin(ctx, base, config)
	case AddonCCELogOperator:
		return NewCCELogOperator(ctx, base, config)
	case AddonCCENPUManager:
		return NewCCENPUManager(ctx, base, config)
	case CCEP2PAccelerationPlugin:
		return NewP2PAccelerationPlugin(ctx, base, config)
	case AddOnCCECSIPFSPlugin:
		return NewCSIPFSPlugin(ctx, base, config)
	case AddOnCCECSIPFSL2Plugin:
		return NewCSIPFSL2Plugin(ctx, base, config)
	case AddonAIHCPInit:
		return NewAIHCPInit(ctx, base, config)
	case AddOnCCECCMController:
		return NewCCM(ctx, base, config)
	case AddOnEniResourceController:
		return NewEniResourceController(ctx, base, config)
	case AddonCCEAscendMindxDL:
		return NewCCEAscendMindxDL(ctx, base)
	}

	return nil, ErrUnsupportedPlugin.New(ctx, "plugin %s not supported", pluginName)
}

// IsPluginDeploySupportedByHelm - 判断插件是否支持 Helm 部署
// 显式设置 PluginHelmConfig 的插件, 并且插件本身支持 Helm 部署时, 才使用 Helm 部署
func IsPluginDeploySupportedByHelm(pluginName string) bool {
	_, ok := HelmSupportedAddons[Type(pluginName)]
	return ok
}

// IsPluginSupport - is plugin support
func IsPluginSupport(pluginName string) bool {
	_, ok := SupportAddons[Type(pluginName)]
	return ok
}
