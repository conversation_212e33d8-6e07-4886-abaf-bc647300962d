package addon

import (
	"context"
	"reflect"
	"testing"

	ccev1 "icode.baidu.com/baidu/cce-test/e2e-test/pkg/crd/api/v1"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/plugin/clients"
	ccetypes "icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
)

func TestNewNodeLocalDNS(t *testing.T) {
	type args struct {
		ctx  context.Context
		base *clients.Clients
		c    Config
	}
	tests := []struct {
		name    string
		args    args
		want    Interface
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "normal",
			args: args{
				ctx: context.TODO(),
				base: &clients.Clients{
					Config: &clients.Config{
						NodeLocalDNSImageID: "NodeLocalDNSImageID",
					},
					Cluster: &ccev1.Cluster{
						Spec: ccetypes.ClusterSpec{
							ContainerNetworkConfig: ccetypes.ContainerNetworkConfig{
								NodeLocalDNSAddr:     "***********",
								ClusterIPServiceCIDR: "***********/16",
							},
						},
					},
				},
				c: nil,
			},
			want: &NodeLocalDNS{
				base: &clients.Clients{
					Config: &clients.Config{
						NodeLocalDNSImageID: "NodeLocalDNSImageID",
					},
					Cluster: &ccev1.Cluster{
						Spec: ccetypes.ClusterSpec{
							ContainerNetworkConfig: ccetypes.ContainerNetworkConfig{
								NodeLocalDNSAddr:     "***********",
								ClusterIPServiceCIDR: "***********/16",
							},
						},
					},
				},
				config: &NodeLocalDNSConfig{
					ClusterDNSAddr: "************",
					LocalDNSImage:  "NodeLocalDNSImageID",
					LocalDNSAddr:   "***********",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := NewNodeLocalDNS(tt.args.ctx, tt.args.base, tt.args.c)
			if (err != nil) != tt.wantErr {
				t.Errorf("NewNodeLocalDNS() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("NewNodeLocalDNS() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestNodeLocalDNS_RenderValuesYAML(t *testing.T) {
	valuesTemp := `
	LocalDNSAddr: {{.LocalDNSAddr}}

	ClusterDNSAddr: {{.ClusterDNSAddr}}
	
	LocalDNSImage: {{.LocalDNSImage}}
`

	values := `
	LocalDNSAddr: ***********

	ClusterDNSAddr: ************
	
	LocalDNSImage: NodeLocalDNSImageID
`
	type fields struct {
		base   *clients.Clients
		config *NodeLocalDNSConfig
	}
	type args struct {
		ctx        context.Context
		valuesTemp string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    string
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "normal",
			fields: fields{
				base: &clients.Clients{},
				config: &NodeLocalDNSConfig{
					ClusterDNSAddr: "************",
					LocalDNSImage:  "NodeLocalDNSImageID",
					LocalDNSAddr:   "***********",
				},
			},
			args: args{
				ctx:        context.TODO(),
				valuesTemp: valuesTemp,
			},
			want: values,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &NodeLocalDNS{
				base:   tt.fields.base,
				config: tt.fields.config,
			}
			got, err := c.RenderValuesYAML(tt.args.ctx, tt.args.valuesTemp)
			if (err != nil) != tt.wantErr {
				t.Errorf("NodeLocalDNS.RenderValuesYAML() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("NodeLocalDNS.RenderValuesYAML() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestNodeLocalDNS_Name(t *testing.T) {
	type fields struct {
		base   *clients.Clients
		config *NodeLocalDNSConfig
	}
	type args struct {
		ctx context.Context
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   Type
	}{
		// TODO: Add test cases.
		{
			name: "normal",
			fields: fields{
				base:   &clients.Clients{},
				config: &NodeLocalDNSConfig{},
			},
			args: args{
				ctx: context.TODO(),
			},
			want: AddonNodeLocalDNS,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &NodeLocalDNS{
				base:   tt.fields.base,
				config: tt.fields.config,
			}
			if got := c.Name(tt.args.ctx); got != tt.want {
				t.Errorf("NodeLocalDNS.Name() = %v, want %v", got, tt.want)
			}
		})
	}
}
