// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.

package addon

import (
	"bytes"
	"context"
	"errors"
	"text/template"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/plugin/clients"
)

const (
	// AddonVolcano - Volcano 插件名
	AddonVolcano Type = "cce-volcano"

	defaultVolcanoReplicas = 1
)

// 插件注册
func init() {
	SupportAddons[AddonVolcano] = nil
	HelmSupportedAddons[AddonVolcano] = nil
}

// volcano - 实现 plugin.Interface 方法, 部署 Volcano
type volcano struct {
	base   *clients.Clients
	config *VolcanoConfig
}

// VolcanoConfig - 部署 Volcano 依赖配置
type VolcanoConfig struct {
	// 用户可选
	Replicas int

	// 默认参数, CCE 自动生成
	schedulerImageID string

	schedulerV2ImageID              string
	controllerImageID               string
	webhookImageID                  string
	queueUserValidateWebhookImageID string
}

// Config - Volcano 配置
func (c *VolcanoConfig) Config() {}

// NewVolcano - 初始化 volcano 方法
//
// PARAMS:
//   - ctx: context.Context
//   - base: *clients.Clients
//   - c: Config 用户定义 Volcano 配置
//
// RETURNS:
//
//	Interface: volcano 的 addon.Interface 实现
//	error: nil if succeed, error if fail
func NewVolcano(ctx context.Context, base *clients.Clients, c Config) (Interface, error) {
	if base == nil {
		return nil, errors.New("base is nil")
	}

	// 配置初始化
	var ok bool
	var config *VolcanoConfig

	if c != nil {
		config, ok = c.(*VolcanoConfig)
		if !ok {
			return nil, errors.New("addon config type not VolcanoConfig")
		}
	} else {
		config = &VolcanoConfig{}
	}

	// Replicas
	if config.Replicas <= 0 {
		config.Replicas = defaultVolcanoReplicas
	}

	// ImageID
	config.controllerImageID = base.Config.VolcanoControllerImageID
	config.schedulerImageID = base.Config.VolcanoSchedulerImageID
	config.schedulerV2ImageID = base.Config.VolcanoSchedulerV2ImageID
	config.webhookImageID = base.Config.VolcanoWebhookImageID
	config.queueUserValidateWebhookImageID = base.Config.VolcanoQueueUserValidateWebhookImageID

	return &volcano{
		base:   base,
		config: config,
	}, nil
}

// Name - 插件名字
func (c *volcano) Name(ctx context.Context) Type {
	return AddonVolcano
}

// Namespace - 插件部署命名空间
func (c *volcano) Namespace(ctx context.Context) string {
	return ""
}

func (c *volcano) HelmChartName(ctx context.Context) HelmChartName {
	return "cce-volcano"
}

func (c *volcano) HelmChartVersion(ctx context.Context) string {
	return c.base.Config.HelmPluginsConfig.ChartVersionCCEVolcano
}

func (c *volcano) ModifyHelmChartValues(ctx context.Context, values string) (string, error) {
	// modify nothing and just return the origin values
	return values, nil
}

// RenderValuesYAML - 通过 VolcanoConfig 渲染 values.yaml 模板
func (c *volcano) RenderValuesYAML(ctx context.Context, valuesTemp string) (string, error) {
	tmpl, err := template.New("Volcano").Parse(valuesTemp)
	if err != nil {
		logger.Errorf(ctx, "template.Parse failed: %v", err)
		return "", err
	}

	var buf bytes.Buffer
	if err := tmpl.Execute(&buf, &struct {
		Replicas                               int
		VolcanoControllerImageID               string
		VolcanoSchedulerImageID                string
		VolcanoSchedulerV2ImageID              string
		VolcanoWebhookImageID                  string
		VolcanoQueueUserValidateWebhookImageID string
	}{
		Replicas:                               c.config.Replicas,
		VolcanoControllerImageID:               c.config.controllerImageID,
		VolcanoSchedulerImageID:                c.config.schedulerImageID,
		VolcanoSchedulerV2ImageID:              c.config.schedulerV2ImageID,
		VolcanoWebhookImageID:                  c.config.webhookImageID,
		VolcanoQueueUserValidateWebhookImageID: c.config.queueUserValidateWebhookImageID,
	}); err != nil {
		logger.Errorf(ctx, "tmpl.Execute failed: %v", err)
		return "", err
	}

	logger.Infof(ctx, "Volcano YAML: %s", buf.String())

	return buf.String(), nil
}
