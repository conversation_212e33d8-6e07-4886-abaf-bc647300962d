// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.

package addon

import (
	"context"
	"testing"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/plugin/clients"
)

func Test_Volcano(t *testing.T) {
	valueTemp := `
VolcanoControllerImageID: {{ .VolcanoControllerImageID }}
VolcanoSchedulerImageIDID: {{ .VolcanoSchedulerImageID }}
VolcanoWebhookImageID: {{ .VolcanoWebhookImageID }}
Replicas: {{.Replicas}}
`

	wantValue1 := `
VolcanoControllerImageID: test
VolcanoSchedulerImageIDID: test
VolcanoWebhookImageID: test
Replicas: 3
`
	cases := []struct {
		name      string
		base      *clients.Clients
		config    *VolcanoConfig
		valueTemp string
		wantValue string
	}{
		{
			name: "render value template",
			config: &VolcanoConfig{
				Replicas:          3,
				schedulerImageID:  "test",
				controllerImageID: "test",
				webhookImageID:    "test",
			},
			valueTemp: valueTemp,
			wantValue: wantValue1,
		},
	}

	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			vc := &volcano{
				base:   c.base,
				config: c.config,
			}

			got, err := vc.RenderValuesYAML(context.TODO(), c.valueTemp)
			if err != nil {
				t.Errorf("got err: %v", err)
			}

			if got != c.wantValue {
				t.Errorf("want: %v, got: %v", c.wantValue, got)
			}
		})
	}
}
