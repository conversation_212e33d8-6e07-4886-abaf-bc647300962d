package addon

import (
	"bytes"
	"context"
	"errors"
	"text/template"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/plugin/clients"
	ccetypes "icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/utils"
)

const (
	// AddonMetricsServerV2 - MetricsServer 插件名
	AddonMetricsServerV2 Type = "metrics-server-v2"
)

// 插件注册
func init() {
	SupportAddons[AddonMetricsServerV2] = nil
	HelmSupportedAddons[AddonMetricsServerV2] = nil
}

// metricsServer - 实现 plugin.Interface 方法, 部署 MetricsServer
type metricsServerV2 struct {
	base   *clients.Clients
	config *MetricsServerConfigV2
}

// MetricsServerConfigV2 - 部署 MetricsServer 依赖配置
type MetricsServerConfigV2 struct {
	// 默认参数, CCE 自动生成
	MetricsServerReplicas int

	MetricsServerScraperReplicas   int
	MetricsServerV2ImageID         string
	DashboardMetricsScraperImageID string
	OSType                         string
}

// Config - MetricsServer 配置
func (c *MetricsServerConfigV2) Config() {}

// NewMetricsServerV2 - 初始化 metricsServer 方法
//
// PARAMS:
//   - ctx: context.Context
//   - base: *clients.Clients
//   - c: Config 用户定义 MetricsServer 配置
//
// RETURNS:
//
//	Interface: metricsServer 的 addon.Interface 实现
//	error: nil if succeed, error if fail
func NewMetricsServerV2(ctx context.Context, base *clients.Clients, c Config) (Interface, error) {
	if base == nil {
		return nil, errors.New("base is nil")
	}

	// 配置初始化
	var ok bool
	var config *MetricsServerConfigV2
	if c != nil {
		// 目前看来, 允许用户不传入该参数
		config, ok = c.(*MetricsServerConfigV2)
		if !ok {
			return nil, errors.New("addon config type not CoreDNSConfig")
		}
	} else {
		config = &MetricsServerConfigV2{}
	}

	// Replicas
	if config.MetricsServerReplicas <= 0 {
		config.MetricsServerReplicas = defaultMetricsServerReplicas
	}
	if config.MetricsServerScraperReplicas <= 0 {
		config.MetricsServerScraperReplicas = defaultMetricsServerScraperReplicas
	}

	// ImageID
	config.MetricsServerV2ImageID = base.Config.MetricsServerV2ImageID
	config.DashboardMetricsScraperImageID = base.Config.DashboardMetricsScraperImageID

	// arm 集群插件镜像替换
	if base.Cluster.Spec.ClusterType == ccetypes.ClusterTypeARM {
		config.MetricsServerV2ImageID = utils.ConvertToARM64Image(ctx, config.MetricsServerV2ImageID)
		config.DashboardMetricsScraperImageID = utils.ConvertToARM64Image(ctx, config.DashboardMetricsScraperImageID)
	}

	// osType
	config.OSType = osType

	return &metricsServerV2{
		base:   base,
		config: config,
	}, nil
}

// Name - 插件名字
func (c *metricsServerV2) Name(ctx context.Context) Type {
	return AddonMetricsServerV2
}

// Namespace - 插件部署命名空间
func (c *metricsServerV2) Namespace(ctx context.Context) string {
	return ""
}

func (c *metricsServerV2) HelmChartName(ctx context.Context) HelmChartName {
	return helmChartNameMetricsServer
}

func (c *metricsServerV2) HelmChartVersion(ctx context.Context) string {
	return c.base.Config.HelmPluginsConfig.ChartVersionCCEMetricsServer
}

func (c *metricsServerV2) ModifyHelmChartValues(ctx context.Context, values string) (string, error) {
	// modify nothing and just return the origin values
	return values, nil
}

// RenderValuesYAML - 通过 template.yaml 渲染 values.yaml 模板
// ------  2022-01-06  ------
// metrics-server 版本由 0.2.1 升级到 0.5.2
// 不再依赖 kubelet 10255 端口
func (c *metricsServerV2) RenderValuesYAML(ctx context.Context, valuesTemp string) (string, error) {
	tmpl, err := template.New("MetricsServer").Parse(valuesTemp)
	if err != nil {
		logger.Errorf(ctx, "template.Parse failed: %v", err)
		return "", err
	}

	var buf bytes.Buffer
	var cfg = &struct {
		MetricsServerReplicas          int
		MetricsServerScraperReplicas   int
		MetricsServerImageID           string
		DashboardMetricsScraperImageID string
		OSType                         string
		RequestsCpu                    string
		RequestsMemory                 string
		LimitCpu                       string
		LimitMemory                    string
		ScraperLimitCpu                string
		ScraperLimitMemory             string
	}{
		MetricsServerReplicas:          c.config.MetricsServerReplicas,
		MetricsServerScraperReplicas:   c.config.MetricsServerScraperReplicas,
		MetricsServerImageID:           c.config.MetricsServerV2ImageID,
		DashboardMetricsScraperImageID: c.config.DashboardMetricsScraperImageID,
		OSType:                         c.config.OSType,
	}

	if err := tmpl.Execute(&buf, cfg); err != nil {
		logger.Errorf(ctx, "tmpl.Execute failed: %v", err)
		return "", err
	}

	logger.Infof(ctx, "MetricsServer YAML: %s", buf.String())

	return buf.String(), nil
}
