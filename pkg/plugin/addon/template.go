// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2020/07/01 11:59:00, by <EMAIL>, create
*/
/*
DESCRIPTION
Template 插件部署, 实现 plugin.Interface
*/

package addon

import (
	"bytes"
	"context"
	"errors"
	"text/template"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/plugin/clients"
)

const (
	// AddonaTemplate - addonTemplate 插件名
	AddonaTemplate Type = "addonTemplate"

	helmChartTemplate HelmChartName = "caddonTemplateHelmCharts"
)

func init() {
	SupportAddons[AddonaTemplate] = nil
}

// addonTemplate - 实现 plugin.Interface 方法, 部署 addonTemplate
type addonTemplate struct {
	base   *clients.Clients
	config *addonTemplateConfig
}

// addonTemplateConfig - 部署 addonTemplate 依赖配置
type addonTemplateConfig struct {
	// 用户可选
	Replicas int

	// 默认参数, CCE 自动生成
	imageID string
}

// Config - addonTemplate 配置
func (c *addonTemplateConfig) Config() {}

// NewTemplate - 初始化 addonTemplate 方法
//
// PARAMS:
//   - ctx: context.Context
//   - base: *clients.Clients
//   - c: Config 用户定义 addonTemplate 配置
//
// RETURNS:
//
//	Interface: addonTemplate 的 addon.Interface 实现
//	error: nil if succeed, error if fail
func NewTemplate(ctx context.Context, base *clients.Clients, c Config) (Interface, error) {
	if base == nil {
		return nil, errors.New("base is nil")
	}

	config, ok := c.(*addonTemplateConfig)
	if !ok {
		return nil, errors.New("addon config type not addonTemplateConfig")
	}

	return &addonTemplate{
		base:   base,
		config: config,
	}, nil
}

// Name - 插件名字
func (c *addonTemplate) Name(ctx context.Context) Type {
	return AddonaTemplate
}

// Namespace - 插件部署命名空间
func (c *addonTemplate) Namespace(ctx context.Context) string {
	return ""
}

func (c *addonTemplate) HelmChartName(ctx context.Context) HelmChartName {
	return helmChartTemplate
}

func (c *addonTemplate) HelmChartVersion(ctx context.Context) string {
	return ""
}

func (c *addonTemplate) ModifyHelmChartValues(ctx context.Context, values string) (string, error) {
	// modify nothing and just return the origin values
	return values, nil
}

// renderValuesYAML - 通过 addonTemplateConfig 渲染 values.yaml 模板
func (c *addonTemplate) RenderValuesYAML(ctx context.Context, valuesTemp string) (string, error) {
	tmpl, err := template.New("addonTemplate").Parse(valuesTemp)
	if err != nil {
		logger.Errorf(ctx, "addonTemplate.Parse failed: %v", err)
		return "", err
	}

	var buf bytes.Buffer
	if err := tmpl.Execute(&buf, &struct {
		Replicas int
	}{
		Replicas: c.config.Replicas,
	}); err != nil {
		logger.Errorf(ctx, "tmpl.Execute failed: %v", err)
		return "", err
	}

	logger.Infof(ctx, "addonTemplate YAML: %s", buf.String())

	return buf.String(), nil
}
