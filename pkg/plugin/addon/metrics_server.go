package addon

import (
	"bytes"
	"context"
	"errors"
	"text/template"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/plugin/clients"
	ccetypes "icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/utils"
)

const (
	// AddonMetricsServer - MetricsServer 插件名
	AddonMetricsServer Type = "metrics-server"

	// 云端 metrics-server 的 helm chart name
	helmChartNameMetricsServer HelmChartName = "cce-metrics-server"

	defaultMetricsServerReplicas = 1

	defaultMetricsServerScraperReplicas = 1

	osType = "linux"

	metricsServerV050ImageID = "registry.baidubce.com/cce-plugin-pro/metrics-server:v0.5.0"
)

// 插件注册
func init() {
	SupportAddons[AddonMetricsServer] = nil
	HelmSupportedAddons[AddonMetricsServer] = nil
}

// metricsServer - 实现 plugin.Interface 方法, 部署 MetricsServer
type metricsServer struct {
	base   *clients.Clients
	config *MetricsServerConfig
}

// MetricsServerConfig - 部署 MetricsServer 依赖配置
type MetricsServerConfig struct {
	// 默认参数, CCE 自动生成
	MetricsServerReplicas int

	MetricsServerScraperReplicas int
	MetricsServerImageID         string
	MetricsServerScraperImageID  string
	OSType                       string
}

// Config - MetricsServer 配置
func (c *MetricsServerConfig) Config() {}

// NewMetricsServer - 初始化 metricsServer 方法
//
// PARAMS:
//   - ctx: context.Context
//   - base: *clients.Clients
//   - c: Config 用户定义 MetricsServer 配置
//
// RETURNS:
//
//	Interface: metricsServer 的 addon.Interface 实现
//	error: nil if succeed, error if fail
func NewMetricsServer(ctx context.Context, base *clients.Clients, c Config) (Interface, error) {
	if base == nil {
		return nil, errors.New("base is nil")
	}

	// 配置初始化
	var ok bool
	var config *MetricsServerConfig
	if c != nil {
		// 目前看来, 允许用户不传入该参数
		config, ok = c.(*MetricsServerConfig)
		if !ok {
			return nil, errors.New("addon config type not CoreDNSConfig")
		}
	} else {
		config = &MetricsServerConfig{}
	}

	// Replicas
	if config.MetricsServerReplicas <= 0 {
		config.MetricsServerReplicas = defaultMetricsServerReplicas
	}
	if config.MetricsServerScraperReplicas <= 0 {
		config.MetricsServerScraperReplicas = defaultMetricsServerScraperReplicas
	}

	// ImageID
	config.MetricsServerImageID = base.Config.MetricsServerImageID
	config.MetricsServerScraperImageID = base.Config.MetricsServerScraperImageID

	// arm 集群插件镜像替换
	if base.Cluster.Spec.ClusterType == ccetypes.ClusterTypeARM {
		config.MetricsServerImageID = utils.ConvertToARM64Image(ctx, config.MetricsServerImageID)
		config.MetricsServerScraperImageID = utils.ConvertToARM64Image(ctx, config.MetricsServerScraperImageID)
	}

	// osType
	config.OSType = osType

	return &metricsServer{
		base:   base,
		config: config,
	}, nil
}

// Name - 插件名字
func (c *metricsServer) Name(ctx context.Context) Type {
	return AddonMetricsServer
}

// Namespace - 插件部署命名空间
func (c *metricsServer) Namespace(ctx context.Context) string {
	return ""
}

func (c *metricsServer) HelmChartName(ctx context.Context) HelmChartName {
	return helmChartNameMetricsServer
}

func (c *metricsServer) HelmChartVersion(ctx context.Context) string {
	return c.base.Config.HelmPluginsConfig.ChartVersionCCEMetricsServer
}

func (c *metricsServer) ModifyHelmChartValues(ctx context.Context, values string) (string, error) {
	// modify nothing and just return the origin values
	return values, nil
}

// RenderValuesYAML - 通过 template.yaml 渲染 values.yaml 模板
func (c *metricsServer) RenderValuesYAML(ctx context.Context, valuesTemp string) (string, error) {
	tmpl, err := template.New("MetricsServer").Parse(valuesTemp)
	if err != nil {
		logger.Errorf(ctx, "template.Parse failed: %v", err)
		return "", err
	}

	var buf bytes.Buffer
	var cfg = &struct {
		MetricsServerReplicas        int
		MetricsServerScraperReplicas int
		MetricsServerImageID         string
		MetricsServerScraperImageID  string
		OSType                       string
		DisableKubeletReadOnlyPort   bool
	}{
		MetricsServerReplicas:        c.config.MetricsServerReplicas,
		MetricsServerScraperReplicas: c.config.MetricsServerScraperReplicas,
		// 兼容老版本集群，默认走老版本的逻辑，传入的 metrics-server 镜像为 0.2.1
		MetricsServerImageID:        c.config.MetricsServerImageID,
		MetricsServerScraperImageID: c.config.MetricsServerScraperImageID,
		OSType:                      c.config.OSType,
		DisableKubeletReadOnlyPort:  c.base.Cluster.Spec.K8SCustomConfig.DisableKubeletReadOnlyPort,
	}

	// 新版本的逻辑也就是 cluster 启用 DisableKubeletReadOnlyPort=true，kubelet 10255 关闭了
	// 镜像必须设置为 v0.5.0 的版本
	if cfg.DisableKubeletReadOnlyPort {
		cfg.MetricsServerImageID = metricsServerV050ImageID
	}

	if err := tmpl.Execute(&buf, cfg); err != nil {
		logger.Errorf(ctx, "tmpl.Execute failed: %v", err)
		return "", err
	}

	logger.Infof(ctx, "MetricsServer YAML: %s", buf.String())

	return buf.String(), nil
}
