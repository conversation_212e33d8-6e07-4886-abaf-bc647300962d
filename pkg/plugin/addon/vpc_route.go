package addon

import (
	"bytes"
	"context"
	"errors"
	"text/template"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/plugin/clients"
)

const (
	// AddonVPCRoute - VPCRoute 插件名
	AddonVPCRoute        Type          = "cce-vpc-route-cni"
	helmChartVPCRouteCNI HelmChartName = "cce-vpc-route-cni"
)

// 插件注册
func init() {
	SupportAddons[AddonVPCRoute] = nil
	HelmSupportedAddons[AddonVPCRoute] = nil
}

// VPCRoute - 实现 plugin.Interface 方法, 部署 VPCRoute
type VPCRoute struct {
	base   *clients.Clients
	config *VPCRouteConfig
}

// VPCRouteConfig - 部署 VPCRoute 依赖配置
type VPCRouteConfig struct {
	// 默认参数, CCE 自动生成
	cniRouteImage string

	cniRouteAgentImage      string
	cniRouteControllerImage string
	cniMasqAgentImage       string

	CNIMode      string
	ServiceCIDR  string
	LocalDNSAddr string
	VPCEndpoint  string
}

// Config - VPCRoute 配置
func (c *VPCRouteConfig) Config() {}

// NewVPCRoute - 初始化 VPCRoute 方法
//
// PARAMS:
//   - ctx: context.Context
//   - base: *clients.Clients
//   - c: Config 用户定义 VPCRoute 配置
//
// RETURNS:
//
//	Interface: VPCRoute 的 addon.Interface 实现
//	error: nil if succeed, error if fail
func NewVPCRoute(ctx context.Context, base *clients.Clients, c Config) (Interface, error) {
	if base == nil {
		return nil, errors.New("base is nil")
	}

	// 配置初始化
	var ok bool
	var config *VPCRouteConfig
	if c != nil {
		// 目前看来, 允许用户不传入该参数
		config, ok = c.(*VPCRouteConfig)
		if !ok {
			return nil, errors.New("addon config type not VPCRouteConfig")
		}
	} else {
		config = &VPCRouteConfig{}
	}

	// ImageID
	config.cniRouteImage = base.Config.VPCRouteCNIImageID
	config.cniRouteAgentImage = base.Config.VPCRouteCNIAgentImageID
	config.cniRouteControllerImage = base.Config.VPCRouteControllerImageID
	config.cniMasqAgentImage = base.Config.VPCIPMasqAgentImageID

	// Params
	if config.CNIMode == "" {
		config.CNIMode = string(base.Cluster.Spec.ContainerNetworkConfig.Mode)
	}
	if config.ServiceCIDR == "" {
		config.ServiceCIDR = base.Cluster.Spec.ContainerNetworkConfig.ClusterIPServiceCIDR
	}

	if base.Cluster.Spec.ContainerNetworkConfig.EnableNodeLocalDNS {
		config.LocalDNSAddr = base.Cluster.Spec.ContainerNetworkConfig.NodeLocalDNSAddr
	}

	if config.VPCEndpoint == "" {
		config.VPCEndpoint = base.Config.BCESDKEndpoints.VPCEndpoint
	}

	return &VPCRoute{
		base:   base,
		config: config,
	}, nil
}

// Name - 插件名字
func (c *VPCRoute) Name(ctx context.Context) Type {
	return AddonVPCRoute
}

// Namespace - 插件部署命名空间
func (c *VPCRoute) Namespace(ctx context.Context) string {
	return ""
}

func (c *VPCRoute) HelmChartName(ctx context.Context) HelmChartName {
	return helmChartVPCRouteCNI
}

func (c *VPCRoute) HelmChartVersion(ctx context.Context) string {
	return c.base.Config.HelmPluginsConfig.ChartVersionCCEVPCRouteCNI
}

func (c *VPCRoute) ModifyHelmChartValues(ctx context.Context, values string) (string, error) {
	// modify nothing and just return the origin values
	return values, nil
}

// RenderValuesYAML - 通过 VPCRouteConfig 渲染 values.yaml 模板
func (c *VPCRoute) RenderValuesYAML(ctx context.Context, valuesTemp string) (string, error) {
	tmpl, err := template.New("VPCRoute").Parse(valuesTemp)
	if err != nil {
		logger.Errorf(ctx, "template.Parse failed: %v", err)
		return "", err
	}

	var buf bytes.Buffer
	if err := tmpl.Execute(&buf, &struct {
		CNIRouteImage           string
		CNIRouteAgentImage      string
		CNIRouteControllerImage string
		CNIMasqAgentImage       string
		CNIMode                 string
		ServiceCIDR             string
		LocalDNSAddr            string
		VPCEndpoint             string
	}{
		CNIRouteImage:           c.config.cniRouteImage,
		CNIRouteAgentImage:      c.config.cniRouteAgentImage,
		CNIRouteControllerImage: c.config.cniRouteControllerImage,
		CNIMasqAgentImage:       c.config.cniMasqAgentImage,
		CNIMode:                 c.config.CNIMode,
		ServiceCIDR:             c.config.ServiceCIDR,
		LocalDNSAddr:            c.config.LocalDNSAddr,
		VPCEndpoint:             c.config.VPCEndpoint,
	}); err != nil {
		logger.Errorf(ctx, "tmpl.Execute failed: %v", err)
		return "", err
	}

	logger.Infof(ctx, "VPCRoute YAML: %s", buf.String())

	return buf.String(), nil
}
