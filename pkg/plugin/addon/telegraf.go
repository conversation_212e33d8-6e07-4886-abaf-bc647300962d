// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.

package addon

import (
	"bytes"
	"context"
	"errors"
	"text/template"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/plugin/clients"
	ccetypes "icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/utils"
)

const (
	// AddonTelegraf - Telegraf 插件名
	AddonTelegraf Type = "cce-telegraf"
)

// 插件注册
func init() {
	SupportAddons[AddonTelegraf] = nil
	HelmSupportedAddons[AddonTelegraf] = nil
}

// Telegraf - 实现 plugin.Interface 方法, 部署 Telegraf
type Telegraf struct {
	base   *clients.Clients
	config *TelegrafConfig
}

// TelegrafConfig - 部署 Telegraf 依赖配置
type TelegrafConfig struct {
	// 默认参数, CCE 自动生成
	CCETelegrafConfRenderImageID string

	CCETelegrafImageID string

	EnableOnGPUNode bool
}

// Config - Telegraf 配置
func (c *TelegrafConfig) Config() {}

// NewTelegraf - 初始化 Telegraf 方法
//
// PARAMS:
//   - ctx: context.Context
//   - base: *clients.Clients
//   - c: Config 用户定义 Telegraf 配置
//
// RETURNS:
//
//	Interface: Telegraf 的 addon.Interface 实现
//	error: nil if succeed, error if fail
func NewTelegraf(ctx context.Context, base *clients.Clients, c Config) (Interface, error) {
	if base == nil {
		return nil, errors.New("base is nil")
	}

	// 配置初始化
	var ok bool
	var config *TelegrafConfig

	if c != nil {
		config, ok = c.(*TelegrafConfig)
		if !ok {
			return nil, errors.New("addon config type not TelegrafConfig")
		}
	} else {
		config = &TelegrafConfig{}
	}

	if config.CCETelegrafImageID == "" {
		config.CCETelegrafImageID = base.Config.CCETelegrafImageID
	}

	if config.CCETelegrafConfRenderImageID == "" {
		config.CCETelegrafConfRenderImageID = base.Config.CCETelegrafConfRenderImageID
	}

	// arm 集群插件镜像替换
	if base.Cluster.Spec.ClusterType == ccetypes.ClusterTypeARM {
		config.CCETelegrafConfRenderImageID = utils.ConvertToARM64Image(ctx, config.CCETelegrafConfRenderImageID)
		config.CCETelegrafImageID = utils.ConvertToARM64Image(ctx, config.CCETelegrafImageID)
	}

	return &Telegraf{
		base:   base,
		config: config,
	}, nil
}

// Name - 插件名字
func (c *Telegraf) Name(ctx context.Context) Type {
	return AddonTelegraf
}

// Namespace - 插件部署命名空间
func (c *Telegraf) Namespace(ctx context.Context) string {
	return ""
}

func (c *Telegraf) HelmChartName(ctx context.Context) HelmChartName {
	return HelmChartName(AddonTelegraf)
}

func (c *Telegraf) HelmChartVersion(ctx context.Context) string {
	return "1.0.22"
}

func (c *Telegraf) ModifyHelmChartValues(ctx context.Context, values string) (string, error) {
	// modify nothing and just return the origin values
	return values, nil
}

// RenderValuesYAML - 通过 TelegrafConfig 渲染 values.yaml 模板
func (c *Telegraf) RenderValuesYAML(ctx context.Context, valuesTemp string) (string, error) {
	tmpl, err := template.New("Telegraf").Parse(valuesTemp)
	if err != nil {
		logger.Errorf(ctx, "template.Parse failed: %v", err)
		return "", err
	}

	var buf bytes.Buffer
	if err := tmpl.Execute(&buf, &struct {
		EnableOnGPUNode              bool
		CCETelegrafConfRenderImageID string
		CCETelegrafImageID           string
		CCEKubernetesVersion         string
	}{
		EnableOnGPUNode:              c.config.EnableOnGPUNode,
		CCETelegrafConfRenderImageID: c.config.CCETelegrafConfRenderImageID,
		CCETelegrafImageID:           c.config.CCETelegrafImageID,
		CCEKubernetesVersion:         string(c.base.Cluster.Spec.K8SVersion),
	}); err != nil {
		logger.Errorf(ctx, "tmpl.Execute failed: %v", err)
		return "", err
	}

	logger.Infof(ctx, "Telegraf YAML: %s", buf.String())

	return buf.String(), nil
}
