package addon

import (
	"bytes"
	"context"
	"errors"
	"text/template"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/plugin/clients"
)

const (
	// AddonVPCCNI - VPCCNI 插件名
	AddonVPCCNI     Type          = "vpc-cni"
	helmChartVPCCNI HelmChartName = "cce-vpc-cni"
)

// 插件注册
func init() {
	SupportAddons[AddonVPCCNI] = nil
	HelmSupportedAddons[AddonVPCCNI] = nil
}

// VPCCNI - 实现 plugin.Interface 方法, 部署 VPCCNI
type VPCCNI struct {
	base   *clients.Clients
	config *VPCCNIConfig
}

// VPCCNIConfig - 部署 VPCCNI 依赖配置
type VPCCNIConfig struct {
	// 默认参数, CCE 自动生成
	vpcCNIImage string

	vpcCNIBackendImage string

	ServiceCIDR string
	ClusterCIDR string
}

// Config - VPCCNI 配置
func (c *VPCCNIConfig) Config() {}

// NewVPCCNI - 初始化 VPCCNI 方法
//
// PARAMS:
//   - ctx: context.Context
//   - base: *clients.Clients
//   - c: Config 用户定义 VPCCNI 配置
//
// RETURNS:
//
//	Interface: VPCCNI 的 addon.Interface 实现
//	error: nil if succeed, error if fail
func NewVPCCNI(ctx context.Context, base *clients.Clients, c Config) (Interface, error) {
	if base == nil {
		return nil, errors.New("base is nil")
	}

	// 配置初始化
	var ok bool
	var config *VPCCNIConfig
	if c != nil {
		// 目前看来, 允许用户不传入该参数
		config, ok = c.(*VPCCNIConfig)
		if !ok {
			return nil, errors.New("addon config type not CoreDNSConfig")
		}
	} else {
		config = &VPCCNIConfig{}
	}

	// ImageID
	config.vpcCNIImage = base.Config.VPCCNIImageID
	config.vpcCNIBackendImage = base.Config.VPCCNIBackendImageID

	// Params
	if config.ServiceCIDR == "" {
		config.ServiceCIDR = base.Cluster.Spec.ContainerNetworkConfig.ClusterIPServiceCIDR
	}
	if config.ClusterCIDR == "" {
		config.ClusterCIDR = base.Cluster.Spec.ContainerNetworkConfig.ClusterPodCIDR
	}

	return &VPCCNI{
		base:   base,
		config: config,
	}, nil
}

// Name - 插件名字
func (c *VPCCNI) Name(ctx context.Context) Type {
	return AddonVPCCNI
}

// Namespace - 插件部署命名空间
func (c *VPCCNI) Namespace(ctx context.Context) string {
	return ""
}

func (c *VPCCNI) HelmChartName(ctx context.Context) HelmChartName {
	return helmChartVPCCNI
}

func (c *VPCCNI) HelmChartVersion(ctx context.Context) string {
	return c.base.Config.HelmPluginsConfig.ChartVersionVPCCNI
}

func (c *VPCCNI) ModifyHelmChartValues(ctx context.Context, values string) (string, error) {
	// modify nothing and just return the origin values
	return values, nil
}

// RenderValuesYAML - 通过 VPCCNIConfig 渲染 values.yaml 模板
func (c *VPCCNI) RenderValuesYAML(ctx context.Context, valuesTemp string) (string, error) {
	tmpl, err := template.New("VPCCNI").Parse(valuesTemp)
	if err != nil {
		logger.Errorf(ctx, "template.Parse failed: %v", err)
		return "", err
	}

	var buf bytes.Buffer
	if err := tmpl.Execute(&buf, &struct {
		VPCCNIImage        string
		VPCCNIBackendImage string
		ServiceCIDR        string
		ClusterCIDR        string
	}{
		VPCCNIImage:        c.config.vpcCNIImage,
		VPCCNIBackendImage: c.config.vpcCNIBackendImage,
		ServiceCIDR:        c.config.ServiceCIDR,
		ClusterCIDR:        c.config.ClusterCIDR,
	}); err != nil {
		logger.Errorf(ctx, "tmpl.Execute failed: %v", err)
		return "", err
	}

	logger.Infof(ctx, "VPCCNI YAML: %s", buf.String())

	return buf.String(), nil
}
