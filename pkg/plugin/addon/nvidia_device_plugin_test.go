package addon

import (
	"context"
	"reflect"
	"testing"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/plugin/clients"
)

func TestNewNvidiaDevicePlugin(t *testing.T) {
	type args struct {
		ctx  context.Context
		base *clients.Clients
		c    Config
	}
	tests := []struct {
		name    string
		args    args
		want    Interface
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "normal",
			args: args{
				ctx: context.TODO(),
				base: &clients.Clients{
					Config: &clients.Config{
						NvidiaDevicePluginImageID: "NvidiaDevicePluginImageID",
					},
				},
				c: nil,
			},
			want: &NvidiaDevicePlugin{
				base: &clients.Clients{
					Config: &clients.Config{
						NvidiaDevicePluginImageID: "NvidiaDevicePluginImageID",
					},
				},
				config: &NvidiaDevicePluginConfig{
					NvidiaDevicePluginImageID: "NvidiaDevicePluginImageID",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := NewNvidiaDevicePlugin(tt.args.ctx, tt.args.base, tt.args.c)
			if (err != nil) != tt.wantErr {
				t.Errorf("NewNvidiaDevicePlugin() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("NewNvidiaDevicePlugin() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestNvidiaDevicePlugin_RenderValuesYAML(t *testing.T) {
	valuesTemp := `
	image: {{.NvidiaDevicePluginImageID}}
`

	values := `
	image: NvidiaDevicePluginImageID
`
	type fields struct {
		base   *clients.Clients
		config *NvidiaDevicePluginConfig
	}
	type args struct {
		ctx        context.Context
		valuesTemp string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    string
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "normal",
			fields: fields{
				base: &clients.Clients{},
				config: &NvidiaDevicePluginConfig{
					NvidiaDevicePluginImageID: "NvidiaDevicePluginImageID",
				},
			},
			args: args{
				ctx:        context.TODO(),
				valuesTemp: valuesTemp,
			},
			want: values,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &NvidiaDevicePlugin{
				base:   tt.fields.base,
				config: tt.fields.config,
			}
			got, err := c.RenderValuesYAML(tt.args.ctx, tt.args.valuesTemp)
			if (err != nil) != tt.wantErr {
				t.Errorf("NvidiaDevicePlugin.RenderValuesYAML() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("NvidiaDevicePlugin.RenderValuesYAML() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestNvidiaDevicePlugin_Name(t *testing.T) {
	type fields struct {
		base   *clients.Clients
		config *NvidiaDevicePluginConfig
	}
	type args struct {
		ctx context.Context
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   Type
	}{
		// TODO: Add test cases.
		{
			name: "normal",
			fields: fields{
				base:   &clients.Clients{},
				config: &NvidiaDevicePluginConfig{},
			},
			args: args{
				ctx: context.TODO(),
			},
			want: AddonNvidiaDevicePlugin,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &NvidiaDevicePlugin{
				base:   tt.fields.base,
				config: tt.fields.config,
			}
			if got := c.Name(tt.args.ctx); got != tt.want {
				t.Errorf("NvidiaDevicePlugin.Name() = %v, want %v", got, tt.want)
			}
		})
	}
}
