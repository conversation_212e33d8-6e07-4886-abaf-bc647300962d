// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2021/05/06 14:59:00, by <EMAIL>, create
*/
/*
DESCRIPTION
Template 插件部署, 实现 plugin.Interface
*/

package addon

import (
	"bytes"
	"context"
	"errors"
	"text/template"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/plugin/clients"
)

const (
	// NvidiaGPUPrometheusExporter - nvidiaGPUPrometheusExporter 插件名
	NvidiaGPUPrometheusExporter Type = "nvidia-gpu-prometheus-exporter"

	NvidiaGPUPrometheusExporterhelmChartTemplate HelmChartName = "nvidia-gpu-prometheus-exporter"
)

func init() {
	SupportAddons[NvidiaGPUPrometheusExporter] = nil
}

// nvidiaGPUPrometheusExporter - 实现 plugin.Interface 方法, 部署 nvidiaGPUPrometheusExporter
type nvidiaGPUPrometheusExporter struct {
	base   *clients.Clients
	config *nvidiaGPUPrometheusExporterConfig
}

// nvidiaGPUPrometheusExporterConfig - 部署 nvidiaGPUPrometheusExporter 依赖配置
type nvidiaGPUPrometheusExporterConfig struct {
	// 默认参数, CCE 自动生成
	imageID string
}

// Config - nvidiaGPUPrometheusExporter 配置
func (c *nvidiaGPUPrometheusExporterConfig) Config() {}

// NewNvidiaGPUPrometheusExporter - 初始化 nvidiaGPUPrometheusExporter 方法
//
// PARAMS:
//   - ctx: context.Context
//   - base: *clients.Clients
//   - c: Config 用户定义 nvidiaGPUPrometheusExporter 配置
//
// RETURNS:
//
//	Interface: nvidiaGPUPrometheusExporter 的 addon.Interface 实现
//	error: nil if succeed, error if fail
func NewNvidiaGPUPrometheusExporter(ctx context.Context, base *clients.Clients, c Config) (Interface, error) {
	if base == nil {
		return nil, errors.New("base is nil")
	}

	var ok bool
	var config *nvidiaGPUPrometheusExporterConfig
	if c != nil {
		config, ok = c.(*nvidiaGPUPrometheusExporterConfig)
		if !ok {
			return nil, errors.New("addon config type not nvidiaGPUPrometheusExporterConfig")
		}
	} else {
		config = &nvidiaGPUPrometheusExporterConfig{}
	}

	return &nvidiaGPUPrometheusExporter{
		base:   base,
		config: config,
	}, nil
}

// Name - 插件名字
func (c *nvidiaGPUPrometheusExporter) Name(ctx context.Context) Type {
	return NvidiaGPUPrometheusExporter
}

// Namespace - 插件部署命名空间
func (c *nvidiaGPUPrometheusExporter) Namespace(ctx context.Context) string {
	return ""
}

func (c *nvidiaGPUPrometheusExporter) HelmChartName(ctx context.Context) HelmChartName {
	return NvidiaGPUPrometheusExporterhelmChartTemplate
}

func (c *nvidiaGPUPrometheusExporter) HelmChartVersion(ctx context.Context) string {
	return ""
}

func (c *nvidiaGPUPrometheusExporter) ModifyHelmChartValues(ctx context.Context, values string) (string, error) {
	// modify nothing and just return the origin values
	return values, nil
}

// renderValuesYAML - 通过 nvidiaGPUPrometheusExporterConfig 渲染 values.yaml 模板
func (c *nvidiaGPUPrometheusExporter) RenderValuesYAML(ctx context.Context, valuesTemp string) (string, error) {
	tmpl, err := template.New("nvidiaGPUPrometheusExporter").Parse(valuesTemp)
	if err != nil {
		logger.Errorf(ctx, "nvidiaGPUPrometheusExporter.Parse failed: %v", err)
		return "", err
	}

	var buf bytes.Buffer
	if err := tmpl.Execute(&buf, &struct {
	}{}); err != nil {
		logger.Errorf(ctx, "tmpl.Execute failed: %v", err)
		return "", err
	}

	logger.Infof(ctx, "nvidiaGPUPrometheusExporter YAML: %s", buf.String())

	return buf.String(), nil
}
