// Copyright 2022 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.

package addon

import (
	"context"
	"reflect"
	"testing"

	ccev1 "icode.baidu.com/baidu/cce-test/e2e-test/pkg/crd/api/v1"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/plugin/clients"
	ccetypes "icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
)

func TestNewVPCNative(t *testing.T) {
	type args struct {
		ctx  context.Context
		base *clients.Clients
		c    Config
	}
	tests := []struct {
		name    string
		args    args
		want    Interface
		wantErr bool
	}{
		{
			name: "enable cni chaining",
			args: args{
				ctx: context.Background(),
				base: &clients.Clients{
					Cluster: &ccev1.Cluster{
						Spec: ccetypes.ClusterSpec{
							ContainerNetworkConfig: ccetypes.ContainerNetworkConfig{
								NodePortRangeMin:     8000,
								NodePortRangeMax:     9000,
								ClusterPodCIDR:       "********/16",
								ClusterIPServiceCIDR: "***********/16",
								EBPFConfig: ccetypes.EBPFConfiuration{
									Enabled:                  true,
									KubeProxyReplacementMode: "strict",
									ServiceLBMode:            "socket",
									CNIChainingMode:          "generic-veth",
								},
								Mode: "mode",
							},
							ClusterID: "clusterid",
							VPCID:     "vpcid",
						},
					},
					Config: &clients.Config{
						Region:              "region",
						VPCNativeCNIImageID: "image",
					},
				},
			},
			want: &VPCNative{
				base: &clients.Clients{
					Cluster: &ccev1.Cluster{
						Spec: ccetypes.ClusterSpec{
							ContainerNetworkConfig: ccetypes.ContainerNetworkConfig{
								NodePortRangeMin:     8000,
								NodePortRangeMax:     9000,
								ClusterPodCIDR:       "********/16",
								ClusterIPServiceCIDR: "***********/16",
								EBPFConfig: ccetypes.EBPFConfiuration{
									Enabled:                  true,
									KubeProxyReplacementMode: "strict",
									ServiceLBMode:            "socket",
									CNIChainingMode:          "generic-veth",
								},
								Mode: "mode",
							},
							ClusterID: "clusterid",
							VPCID:     "vpcid",
						},
					},
					Config: &clients.Config{
						Region:              "region",
						VPCNativeCNIImageID: "image",
					},
				},
				config: &VPCNativeConfig{
					CNIMode:            "mode",
					Region:             "region",
					ClusterID:          "clusterid",
					VPCID:              "vpcid",
					ENISubnetList:      nil,
					SecurityGroupList:  []string{""},
					PodSubnetList:      nil,
					CCEGatewayEndpoint: "",
					BCCEndpoint:        "",
					BBCEndpoint:        "",
					ServiceCIDR:        "***********/16",

					CCECNIImage: "image",

					EnableVPCRoute:    false,
					EnableStaticRoute: false,

					ContainerNetworkCIDRIPv4: "********/16",
					ContainerNetworkCIDRIPv6: "",

					IPAMScheduledToMaster: false,

					CiliumCNIChainingMode: "generic-veth",
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := NewVPCNative(tt.args.ctx, tt.args.base, tt.args.c)
			if err != nil {
				if tt.wantErr {
					return
				}

				t.Errorf("NewVPCNative() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("NewVPCNative() got = %v, want %v", got, tt.want)
			}
		})
	}
}
