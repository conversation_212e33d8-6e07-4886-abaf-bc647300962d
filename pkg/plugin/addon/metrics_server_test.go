package addon

import (
	"context"
	"testing"

	"github.com/google/go-cmp/cmp"

	ccev1 "icode.baidu.com/baidu/cce-test/e2e-test/pkg/crd/api/v1"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/plugin/clients"
	ccetypes "icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/utils"
)

func Test_MetricsServer_RenderValuesYAML(t *testing.T) {
	type fields struct {
		base   *clients.Clients
		config *MetricsServerConfig
	}

	type args struct {
		ctx        context.Context
		valuesTemp string
	}

	tests := []struct {
		name    string
		fields  fields
		args    args
		want    string
		wantErr bool
	}{
		{
			name: "render metrics-server values.yaml",
			fields: fields{
				base: &clients.Clients{
					Cluster: &ccev1.Cluster{
						Spec: ccetypes.ClusterSpec{
							ClusterID: "test",
							K8SCustomConfig: ccetypes.K8SCustomConfig{
								DisableKubeletReadOnlyPort: false,
							},
						},
					},
				},
				config: &MetricsServerConfig{
					MetricsServerReplicas:        1,
					MetricsServerScraperReplicas: 1,
					MetricsServerImageID:         "hub.baidubce.com/jpaas-public/metrics-server:0.2.1",
					MetricsServerScraperImageID:  "hub.baidubce.com/jpaas-public/metrics-scraper:0.1.0",
					OSType:                       "linux",
				},
			},
			args: args{
				ctx: context.TODO(),
				valuesTemp: `metricsServerReplicas: {{.MetricsServerReplicas}}
metricsServerScraperReplicas: {{.MetricsServerScraperReplicas}}
metricsServerImageID: {{.MetricsServerImageID}}
metricsServerScraperImageID: {{.MetricsServerScraperImageID}}
osType: {{.OSType}}`,
			},
			want: `metricsServerReplicas: 1
metricsServerScraperReplicas: 1
metricsServerImageID: hub.baidubce.com/jpaas-public/metrics-server:0.2.1
metricsServerScraperImageID: hub.baidubce.com/jpaas-public/metrics-scraper:0.1.0
osType: linux`,
			wantErr: false,
		},
		{
			name: "render metrics-server values.yaml DisableKubeletReadOnlyPort=false",
			fields: fields{
				base: &clients.Clients{
					Cluster: &ccev1.Cluster{
						Spec: ccetypes.ClusterSpec{
							ClusterID: "test",
							K8SCustomConfig: ccetypes.K8SCustomConfig{
								DisableKubeletReadOnlyPort: false,
							},
						},
					},
				},
				config: &MetricsServerConfig{
					MetricsServerReplicas:        1,
					MetricsServerScraperReplicas: 1,
					MetricsServerImageID:         "hub.baidubce.com/jpaas-public/metrics-server:0.2.1",
					MetricsServerScraperImageID:  "hub.baidubce.com/jpaas-public/metrics-scraper:0.1.0",
					OSType:                       "linux",
				},
			},
			args: args{
				ctx: context.TODO(),
				valuesTemp: `metricsServerReplicas: {{.MetricsServerReplicas}}
metricsServerScraperReplicas: {{.MetricsServerScraperReplicas}}
metricsServerImageID: {{.MetricsServerImageID}}
metricsServerScraperImageID: {{.MetricsServerScraperImageID}}
osType: {{.OSType}}
disableKubeletReadOnlyPort: {{.DisableKubeletReadOnlyPort}}`,
			},
			want: `metricsServerReplicas: 1
metricsServerScraperReplicas: 1
metricsServerImageID: hub.baidubce.com/jpaas-public/metrics-server:0.2.1
metricsServerScraperImageID: hub.baidubce.com/jpaas-public/metrics-scraper:0.1.0
osType: linux
disableKubeletReadOnlyPort: false`,
			wantErr: false,
		},
		{
			name: "render metrics-server values.yaml DisableKubeletReadOnlyPort=true",
			fields: fields{
				base: &clients.Clients{
					Cluster: &ccev1.Cluster{
						Spec: ccetypes.ClusterSpec{
							ClusterID: "test",
							K8SCustomConfig: ccetypes.K8SCustomConfig{
								DisableKubeletReadOnlyPort: true,
							},
						},
					},
				},
				config: &MetricsServerConfig{
					MetricsServerReplicas:        1,
					MetricsServerScraperReplicas: 1,
					MetricsServerImageID:         "hub.baidubce.com/jpaas-public/metrics-server:0.2.1",
					MetricsServerScraperImageID:  "hub.baidubce.com/jpaas-public/metrics-scraper:0.1.0",
					OSType:                       "linux",
				},
			},
			args: args{
				ctx: context.TODO(),
				valuesTemp: `metricsServerReplicas: {{.MetricsServerReplicas}}
metricsServerScraperReplicas: {{.MetricsServerScraperReplicas}}
metricsServerImageID: {{.MetricsServerImageID}}
metricsServerScraperImageID: {{.MetricsServerScraperImageID}}
osType: {{.OSType}}
disableKubeletReadOnlyPort: {{.DisableKubeletReadOnlyPort}}`,
			},
			want: `metricsServerReplicas: 1
metricsServerScraperReplicas: 1
metricsServerImageID: registry.baidubce.com/cce-plugin-pro/metrics-server:v0.5.0
metricsServerScraperImageID: hub.baidubce.com/jpaas-public/metrics-scraper:0.1.0
osType: linux
disableKubeletReadOnlyPort: true`,
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &metricsServer{
				base:   tt.fields.base,
				config: tt.fields.config,
			}

			got, err := c.RenderValuesYAML(tt.args.ctx, tt.args.valuesTemp)
			if err != nil {
				if tt.wantErr {
					return
				}

				t.Errorf("metricsServer.RenderValuesYAML %s failed: %v", tt.name, err)
				return
			}

			if tt.wantErr {
				t.Errorf("metricsServer.RenderValuesYAML %s want err, but err is nil", tt.name)
				return
			}

			if got != tt.want {
				t.Errorf("got: %s", utils.ToJSON(got))
				t.Errorf("got: %s", utils.ToJSON(tt.want))
				t.Errorf("metricsServer.RenderValuesYAML %s failed: cmp=%v", tt.name, cmp.Diff(got, tt.want))
			}
		})
	}
}
