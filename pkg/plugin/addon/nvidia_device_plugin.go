package addon

import (
	"bytes"
	"context"
	"errors"
	"text/template"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/plugin/clients"
)

const (
	// AddonNvidiaGPU named nvidia-gpu
	AddonNvidiaDevicePlugin Type = "cce-nvidia-device-plugin"

	// 云端 cce-nvidia-device-plugin 的 helm chart name
	helmChartNvidiaDevicePlugin HelmChartName = "cce-nvidia-device-plugin"
)

// 插件注册
func init() {
	SupportAddons[AddonNvidiaDevicePlugin] = nil
	HelmSupportedAddons[AddonNvidiaDevicePlugin] = nil
}

// NvidiaDevicePlugin - 实现 plugin.Interface 方法, 部署 NvidiaDevicePlugin
type NvidiaDevicePlugin struct {
	base   *clients.Clients
	config *NvidiaDevicePluginConfig
}

// NvidiaDevicePluginConfig - 部署 NvidiaDevicePlugin 依赖配置
type NvidiaDevicePluginConfig struct {
	// 默认参数, CCE 自动生成
	NvidiaDevicePluginImageID string
}

// Config - NvidiaDevicePlugin 配置
func (c *NvidiaDevicePluginConfig) Config() {}

// NewNvidiaDevicePlugin - 初始化 NvidiaDevicePlugin 方法
//
// PARAMS:
//   - ctx: context.Context
//   - base: *clients.Clients
//   - c: Config 用户定义 NvidiaDevicePlugin 配置
//
// RETURNS:
//
//	Interface: NvidiaDevicePlugin 的 addon.Interface 实现
//	error: nil if succeed, error if fail
func NewNvidiaDevicePlugin(ctx context.Context, base *clients.Clients, c Config) (Interface, error) {
	if base == nil {
		return nil, errors.New("base is nil")
	}

	// 配置初始化
	var ok bool
	var config *NvidiaDevicePluginConfig
	if c != nil {
		// 目前看来, 允许用户不传入该参数
		config, ok = c.(*NvidiaDevicePluginConfig)
		if !ok {
			return nil, errors.New("addon config type not NvidiaDevicePluginConfig")
		}
	} else {
		config = &NvidiaDevicePluginConfig{}
	}

	// ImageID
	config.NvidiaDevicePluginImageID = base.Config.NvidiaDevicePluginImageID

	return &NvidiaDevicePlugin{
		base:   base,
		config: config,
	}, nil
}

// Name - 插件名字
func (c *NvidiaDevicePlugin) Name(ctx context.Context) Type {
	return AddonNvidiaDevicePlugin
}

// Namespace - 插件部署命名空间
func (c *NvidiaDevicePlugin) Namespace(ctx context.Context) string {
	return ""
}

func (c *NvidiaDevicePlugin) HelmChartName(ctx context.Context) HelmChartName {
	return helmChartNvidiaDevicePlugin
}

func (c *NvidiaDevicePlugin) HelmChartVersion(ctx context.Context) string {
	return c.base.Config.HelmPluginsConfig.ChartVersionCCENvidiaDevicePlugin
}

func (c *NvidiaDevicePlugin) ModifyHelmChartValues(ctx context.Context, values string) (string, error) {
	// modify nothing and just return the origin values
	return values, nil
}

// RenderValuesYAML - 通过 NvidiaDevicePluginConfig 渲染 values.yaml 模板
func (c *NvidiaDevicePlugin) RenderValuesYAML(ctx context.Context, valuesTemp string) (string, error) {
	tmpl, err := template.New("NvidiaDevicePlugin").Parse(valuesTemp)
	if err != nil {
		logger.Errorf(ctx, "template.Parse failed: %v", err)
		return "", err
	}

	var buf bytes.Buffer
	if err := tmpl.Execute(&buf, &struct {
		NvidiaDevicePluginImageID string
	}{
		NvidiaDevicePluginImageID: c.config.NvidiaDevicePluginImageID,
	}); err != nil {
		logger.Errorf(ctx, "tmpl.Execute failed: %v", err)
		return "", err
	}

	logger.Infof(ctx, "NvidiaDevicePlugin YAML: %s", buf.String())

	return buf.String(), nil
}
