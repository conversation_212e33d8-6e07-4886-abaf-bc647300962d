package addon

import (
	"bytes"
	"context"
	"errors"
	"text/template"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/plugin/clients"
	ccetypes "icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/utils"
)

const (
	// AddonVPCNative - VPCNative 插件名
	AddonVPCNative        Type          = "cce-vpc-native-cni"
	helmChartVPCNativeCNI HelmChartName = "cce-vpc-native-cni"
)

// 插件注册
func init() {
	SupportAddons[AddonVPCNative] = nil
	HelmSupportedAddons[AddonVPCNative] = nil
}

// VPCNative - 实现 plugin.Interface 方法, 部署 VPCNative
type VPCNative struct {
	base   *clients.Clients
	config *VPCNativeConfig
}

// VPCNativeConfig - 部署 VPCNative 依赖配置
type VPCNativeConfig struct {
	// 默认参数, CCE 自动生成
	CNIMode string

	Region             string
	ClusterID          string
	VPCID              string
	ENISubnetList      []string
	SecurityGroupList  []string
	PodSubnetList      []string
	CCEGatewayEndpoint string
	BCCEndpoint        string
	BBCEndpoint        string
	ServiceCIDR        string

	CCECNIImage string

	EnableVPCRoute    bool
	EnableStaticRoute bool

	ContainerNetworkCIDRIPv4 string
	ContainerNetworkCIDRIPv6 string

	IPAMScheduledToMaster bool

	CiliumCNIChainingMode string
}

// Config - VPCNative 配置
func (c *VPCNativeConfig) Config() {}

// NewVPCNative - 初始化 VPCNative 方法
//
// PARAMS:
//   - ctx: context.Context
//   - base: *clients.Clients
//   - c: Config 用户定义 VPCNative 配置
//
// RETURNS:
//
//	Interface: VPCNative 的 addon.Interface 实现
//	error: nil if succeed, error if fail
func NewVPCNative(ctx context.Context, base *clients.Clients, c Config) (Interface, error) {
	if base == nil {
		return nil, errors.New("base is nil")
	}

	// 配置初始化
	var ok bool
	var config *VPCNativeConfig
	if c != nil {
		// 目前看来, 允许用户不传入该参数
		config, ok = c.(*VPCNativeConfig)
		if !ok {
			return nil, errors.New("addon config type not VPCNativeConfig")
		}
	} else {
		config = &VPCNativeConfig{}
	}

	// ImageID
	config.CCECNIImage = base.Config.VPCNativeCNIImageID
	// arm 集群插件镜像替换
	if base.Cluster.Spec.ClusterType == ccetypes.ClusterTypeARM {
		config.CCECNIImage = utils.ConvertToARM64Image(ctx, config.CCECNIImage)
	}

	// Params
	if config.CNIMode == "" {
		config.CNIMode = string(base.Cluster.Spec.ContainerNetworkConfig.Mode)
	}
	if utils.IsKubenetMode(ccetypes.ContainerNetworkMode(config.CNIMode)) || utils.IsVPCRouteCNIMode(ccetypes.ContainerNetworkMode(config.CNIMode)) {
		config.EnableVPCRoute = true
		config.EnableStaticRoute = false
	}

	if config.Region == "" {
		config.Region = base.Config.Region
	}

	if config.ClusterID == "" {
		config.ClusterID = base.Cluster.Spec.ClusterID
	}

	if config.VPCID == "" {
		config.VPCID = base.Cluster.Spec.VPCID
	}

	if len(config.ENISubnetList) == 0 {
		for _, subnets := range base.Cluster.Spec.ContainerNetworkConfig.ENIVPCSubnetIDs {
			config.ENISubnetList = append(config.ENISubnetList, subnets...)
		}
	}

	if len(config.SecurityGroupList) == 0 {
		config.SecurityGroupList = []string{base.Cluster.Spec.ContainerNetworkConfig.ENISecurityGroupID}
	}

	if config.CCEGatewayEndpoint == "" {
		config.CCEGatewayEndpoint = base.Config.OpenCCEGatewayEndpoint
	}

	if config.BCCEndpoint == "" {
		config.BCCEndpoint = base.Config.BCESDKEndpoints.BCCEndpoint
	}

	if config.BBCEndpoint == "" {
		config.BBCEndpoint = base.Config.BCESDKEndpoints.BBCEndpoint
	}

	if config.ServiceCIDR == "" {
		config.ServiceCIDR = base.Cluster.Spec.ContainerNetworkConfig.ClusterIPServiceCIDR
	}

	if config.ContainerNetworkCIDRIPv4 == "" {
		config.ContainerNetworkCIDRIPv4 = base.Cluster.Spec.ContainerNetworkConfig.ClusterPodCIDR
	}

	if config.ContainerNetworkCIDRIPv6 == "" {
		config.ContainerNetworkCIDRIPv6 = base.Cluster.Spec.ContainerNetworkConfig.ClusterPodCIDRIPv6
	}

	// 针对非托管容器化 master 集群，ipam 调度到 master
	if base.Cluster.Spec.MasterConfig.MasterType == ccetypes.MasterTypeContainerizedCustom {
		config.IPAMScheduledToMaster = true
	}

	if base.Cluster.Spec.ContainerNetworkConfig.EBPFConfig.Enabled &&
		base.Cluster.Spec.ContainerNetworkConfig.EBPFConfig.CNIChainingMode == "generic-veth" {
		config.CiliumCNIChainingMode = base.Cluster.Spec.ContainerNetworkConfig.EBPFConfig.CNIChainingMode
	} else {
		config.CiliumCNIChainingMode = "none"
	}

	return &VPCNative{
		base:   base,
		config: config,
	}, nil
}

// Name - 插件名字
func (c *VPCNative) Name(ctx context.Context) Type {
	return AddonVPCNative
}

// Namespace - 插件部署命名空间
func (c *VPCNative) Namespace(ctx context.Context) string {
	return ""
}

func (c *VPCNative) HelmChartName(ctx context.Context) HelmChartName {
	return helmChartVPCNativeCNI
}

func (c *VPCNative) HelmChartVersion(ctx context.Context) string {
	return c.base.Config.HelmPluginsConfig.ChartVersionCCEVPCNativeCNI
}

func (c *VPCNative) ModifyHelmChartValues(ctx context.Context, values string) (string, error) {
	// modify nothing and just return the origin values
	return values, nil
}

// RenderValuesYAML - 通过 VPCNativeConfig 渲染 values.yaml 模板
func (c *VPCNative) RenderValuesYAML(ctx context.Context, valuesTemp string) (string, error) {
	tmpl, err := template.New("VPCNative").Parse(valuesTemp)
	if err != nil {
		logger.Errorf(ctx, "template.Parse failed: %v", err)
		return "", err
	}

	var buf bytes.Buffer
	if err := tmpl.Execute(&buf, c.config); err != nil {
		logger.Errorf(ctx, "tmpl.Execute failed: %v", err)
		return "", err
	}

	logger.Infof(ctx, "VPCNative YAML: %s", buf.String())

	return buf.String(), nil
}
