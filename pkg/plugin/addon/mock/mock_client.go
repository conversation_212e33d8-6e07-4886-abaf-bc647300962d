// Code generated by MockGen. DO NOT EDIT.
// Source: ./pkg/plugin/addon/types.go

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	addon "icode.baidu.com/baidu/cce-test/e2e-test/pkg/plugin/addon"
)

// MockInterface is a mock of Interface interface.
type MockInterface struct {
	ctrl     *gomock.Controller
	recorder *MockInterfaceMockRecorder
}

// MockInterfaceMockRecorder is the mock recorder for MockInterface.
type MockInterfaceMockRecorder struct {
	mock *MockInterface
}

// NewMockInterface creates a new mock instance.
func NewMockInterface(ctrl *gomock.Controller) *MockInterface {
	mock := &MockInterface{ctrl: ctrl}
	mock.recorder = &MockInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockInterface) EXPECT() *MockInterfaceMockRecorder {
	return m.recorder
}

// HelmChartName mocks base method.
func (m *MockInterface) HelmChartName(ctx context.Context) addon.HelmChartName {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HelmChartName", ctx)
	ret0, _ := ret[0].(addon.HelmChartName)
	return ret0
}

// HelmChartName indicates an expected call of HelmChartName.
func (mr *MockInterfaceMockRecorder) HelmChartName(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HelmChartName", reflect.TypeOf((*MockInterface)(nil).HelmChartName), ctx)
}

// HelmChartVersion mocks base method.
func (m *MockInterface) HelmChartVersion(ctx context.Context) string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HelmChartVersion", ctx)
	ret0, _ := ret[0].(string)
	return ret0
}

// HelmChartVersion indicates an expected call of HelmChartVersion.
func (mr *MockInterfaceMockRecorder) HelmChartVersion(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HelmChartVersion", reflect.TypeOf((*MockInterface)(nil).HelmChartVersion), ctx)
}

// ModifyHelmChartValues mocks base method.
func (m *MockInterface) ModifyHelmChartValues(ctx context.Context, values string) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ModifyHelmChartValues", ctx, values)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ModifyHelmChartValues indicates an expected call of ModifyHelmChartValues.
func (mr *MockInterfaceMockRecorder) ModifyHelmChartValues(ctx, values interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ModifyHelmChartValues", reflect.TypeOf((*MockInterface)(nil).ModifyHelmChartValues), ctx, values)
}

// Name mocks base method.
func (m *MockInterface) Name(ctx context.Context) addon.Type {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Name", ctx)
	ret0, _ := ret[0].(addon.Type)
	return ret0
}

// Name indicates an expected call of Name.
func (mr *MockInterfaceMockRecorder) Name(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Name", reflect.TypeOf((*MockInterface)(nil).Name), ctx)
}

// Namespace mocks base method.
func (m *MockInterface) Namespace(ctx context.Context) string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Namespace", ctx)
	ret0, _ := ret[0].(string)
	return ret0
}

// Namespace indicates an expected call of Namespace.
func (mr *MockInterfaceMockRecorder) Namespace(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Namespace", reflect.TypeOf((*MockInterface)(nil).Namespace), ctx)
}

// RenderValuesYAML mocks base method.
func (m *MockInterface) RenderValuesYAML(ctx context.Context, valuesTpl string) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RenderValuesYAML", ctx, valuesTpl)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RenderValuesYAML indicates an expected call of RenderValuesYAML.
func (mr *MockInterfaceMockRecorder) RenderValuesYAML(ctx, valuesTpl interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RenderValuesYAML", reflect.TypeOf((*MockInterface)(nil).RenderValuesYAML), ctx, valuesTpl)
}

// MockConfig is a mock of Config interface.
type MockConfig struct {
	ctrl     *gomock.Controller
	recorder *MockConfigMockRecorder
}

// MockConfigMockRecorder is the mock recorder for MockConfig.
type MockConfigMockRecorder struct {
	mock *MockConfig
}

// NewMockConfig creates a new mock instance.
func NewMockConfig(ctrl *gomock.Controller) *MockConfig {
	mock := &MockConfig{ctrl: ctrl}
	mock.recorder = &MockConfigMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockConfig) EXPECT() *MockConfigMockRecorder {
	return m.recorder
}

// Config mocks base method.
func (m *MockConfig) Config() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Config")
}

// Config indicates an expected call of Config.
func (mr *MockConfigMockRecorder) Config() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Config", reflect.TypeOf((*MockConfig)(nil).Config))
}
