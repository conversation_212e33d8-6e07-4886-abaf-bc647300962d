// Code generated by MockGen. DO NOT EDIT.
// Source: icode.baidu.com/baidu/cce-test/e2e-test/pkg/plugin/addon (interfaces: FactoryInterface)

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	gomock "github.com/golang/mock/gomock"
	addon "icode.baidu.com/baidu/cce-test/e2e-test/pkg/plugin/addon"
	clients "icode.baidu.com/baidu/cce-test/e2e-test/pkg/plugin/clients"
	reflect "reflect"
)

// MockFactoryInterface is a mock of FactoryInterface interface
type MockFactoryInterface struct {
	ctrl     *gomock.Controller
	recorder *MockFactoryInterfaceMockRecorder
}

// MockFactoryInterfaceMockRecorder is the mock recorder for MockFactoryInterface
type MockFactoryInterfaceMockRecorder struct {
	mock *MockFactoryInterface
}

// NewMockFactoryInterface creates a new mock instance
func NewMockFactoryInterface(ctrl *gomock.Controller) *MockFactoryInterface {
	mock := &MockFactoryInterface{ctrl: ctrl}
	mock.recorder = &MockFactoryInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use
func (m *MockFactoryInterface) EXPECT() *MockFactoryInterfaceMockRecorder {
	return m.recorder
}

// NewAddonClient mocks base method
func (m *MockFactoryInterface) NewAddonClient(arg0 context.Context, arg1 string, arg2 addon.Config, arg3 *clients.Clients) (addon.Interface, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NewAddonClient", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(addon.Interface)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// NewAddonClient indicates an expected call of NewAddonClient
func (mr *MockFactoryInterfaceMockRecorder) NewAddonClient(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NewAddonClient", reflect.TypeOf((*MockFactoryInterface)(nil).NewAddonClient), arg0, arg1, arg2, arg3)
}
