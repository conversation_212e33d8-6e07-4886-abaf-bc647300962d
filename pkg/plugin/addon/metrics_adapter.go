package addon

import (
	"bytes"
	"context"
	"errors"
	"text/template"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/plugin/clients"
	ccetypes "icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/utils"
)

const (
	// AddonMetricsAdapter named metrics-adapter
	AddonMetricsAdapter Type = "metrics-adapter"

	// 云端 metrics-adapter 的 helm chart name
	helmChartNameMetricsAdapter HelmChartName = "cce-metrics-adapter"

	defaultMetricsAdapterReplicas = 1
)

// 插件注册
func init() {
	SupportAddons[AddonMetricsAdapter] = nil
	HelmSupportedAddons[AddonMetricsAdapter] = nil
}

// metricsAdapter - 实现 plugin.Interface 方法, 部署 metricsAdapter
type metricsAdapter struct {
	base   *clients.Clients
	config *MetricsAdapterConfig
}

// MetricsAdapterConfig - 部署 MetricsAdapter 依赖配置
type MetricsAdapterConfig struct {
	// 默认参数, CCE 自动生成
	MetricsAdapterReplicas int

	MetricsAdapterImageID string
}

// Config - MetricsAdapter 配置
func (c *MetricsAdapterConfig) Config() {}

// NewMetricsAdapter - 初始化 metricsAdapter 方法
//
// PARAMS:
//   - ctx: context.Context
//   - base: *clients.Clients
//   - c: Config 用户定义 MetricsAdapter 配置
//
// RETURNS:
//
//	Interface: metricsAdapter 的 addon.Interface 实现
//	error: nil if succeed, error if fail
func NewMetricsAdapter(ctx context.Context, base *clients.Clients, c Config) (Interface, error) {
	if base == nil {
		return nil, errors.New("base is nil")
	}

	// 配置初始化
	var ok bool
	var config *MetricsAdapterConfig
	if c != nil {
		// 目前看来, 允许用户不传入该参数
		config, ok = c.(*MetricsAdapterConfig)
		if !ok {
			return nil, errors.New("addon config type not MetricsAdapterConfig")
		}
	} else {
		config = &MetricsAdapterConfig{}
	}

	// Replicas
	if config.MetricsAdapterReplicas <= 0 {
		config.MetricsAdapterReplicas = defaultMetricsAdapterReplicas
	}
	// ImageID
	config.MetricsAdapterImageID = base.Config.MetricsAdapterImageID
	if base.Cluster.Spec.ClusterType == ccetypes.ClusterTypeARM {
		config.MetricsAdapterImageID = utils.ConvertToARM64Image(ctx, base.Config.MetricsAdapterImageID)
	}
	return &metricsAdapter{
		base:   base,
		config: config,
	}, nil
}

// Name - 插件名字
func (c *metricsAdapter) Name(ctx context.Context) Type {
	return AddonMetricsAdapter
}

// Namespace - 插件部署命名空间
func (c *metricsAdapter) Namespace(ctx context.Context) string {
	return ""
}

func (c *metricsAdapter) HelmChartName(ctx context.Context) HelmChartName {
	return helmChartNameMetricsAdapter
}

func (c *metricsAdapter) HelmChartVersion(ctx context.Context) string {
	return ""
}

func (c *metricsAdapter) ModifyHelmChartValues(ctx context.Context, values string) (string, error) {
	// modify nothing and just return the origin values
	return values, nil
}

// RenderValuesYAML - 通过 MetricsAdapterConfig 渲染 values.yaml 模板
func (c *metricsAdapter) RenderValuesYAML(ctx context.Context, valuesTemp string) (string, error) {
	tmpl, err := template.New("MetricsAdapter").Parse(valuesTemp)
	if err != nil {
		logger.Errorf(ctx, "template.Parse failed: %v", err)
		return "", err
	}

	var buf bytes.Buffer
	if err := tmpl.Execute(&buf, &struct {
		MetricsAdapterReplicas int
		MetricsAdapterImageID  string
	}{
		MetricsAdapterReplicas: c.config.MetricsAdapterReplicas,
		MetricsAdapterImageID:  c.config.MetricsAdapterImageID,
	}); err != nil {
		logger.Errorf(ctx, "tmpl.Execute failed: %v", err)
		return "", err
	}

	logger.Infof(ctx, "MetricsAdapter YAML: %s", buf.String())

	return buf.String(), nil
}
