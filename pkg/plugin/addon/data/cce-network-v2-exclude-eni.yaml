# Default values for helm.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

# cce守护进程配置
ccedConfig:
  # ipam 模式. privatecloudbase: 私有云底座;vpc-eni: BCE VPC ENI
  ipam: vpc-eni
  # 启动ipv4
  enable-ipv4: true
  enable-ipv6: true

  # cni 配置，从指定文件读取，写入到另一个路径
  read-cni-conf: /etc/cce/cni-config-template/cce-ptp.template
  write-cni-conf-when-ready: "/etc/cni/net.d/00-cce-cni.conflist"

  # cce专用配置
  cce-cluster-id: cce-abc

  # operator BCE VPC 配置
  # vpc id
  bce-cloud-vpc-id: vpc-xxxx
  # 云的可用区，用于选择Cloud API的地址
  bce-cloud-region: region
  bce-cloud-host: https://vpc-eni-gateway.default.svc.cluster.local

  net-device-driver: "veth-pair"

  # agent vpc-eni 配置
  # ENI使用模式：Secondary：辅助IP；Primary：主IP（独占ENI）
  eni-use-mode: "Secondary"
  eni-install-source-based-routing: true
  eni-subnet-ids:
    - subnet-id-1
    - subnet-id-2

  # vpc 路由配置
  # ipv4
  cluster-pool-ipv4-cidr:
  - ***********/16
  cluster-pool-ipv4-mask-size: 23
  # ipv6
  cluster-pool-ipv6-cidr:
  - fe80:2a5c::1/64
  cluster-pool-ipv6-mask-size: 112

  # 通用配置
  annotate-k8s-node: true

  # 外部服务配置
  clusterip-ipv4-cidrs:
  - ***********/16
  clusterip-ipv6-cidrs:
  - fe80:db8::1/112
  vpc-cidrs:
  - ********/12
  vpc-ipv6-cidrs:
  - 2001:db8::1/54

network:
  operator:
    webhook:
      enable: true

# 扩展插件配置
ext-plugins:
  cilium-cni:
    type: cilium-cni
  sbr-eip:
    type: sbr-eip