package addon

import (
	"bytes"
	"context"
	"errors"
	"text/template"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/plugin/clients"
)

const (
	// AddonNodeLocalDNS - NodeLocalDNS 插件名
	AddonNodeLocalDNS     Type          = "node-local-dns"
	HelmChartNodeLocalDNS HelmChartName = "cce-node-local-dns"
)

// 插件注册
func init() {
	SupportAddons[AddonNodeLocalDNS] = nil
	HelmSupportedAddons[AddonNodeLocalDNS] = nil
}

// NodeLocalDNS - 实现 plugin.Interface 方法, 部署 NodeLocalDNS
type NodeLocalDNS struct {
	base   *clients.Clients
	config *NodeLocalDNSConfig
}

// NodeLocalDNSConfig - 部署 NodeLocalDNS 依赖配置
type NodeLocalDNSConfig struct {
	LocalDNSImage  string
	ClusterDNSAddr string
	LocalDNSAddr   string
}

// Config - NodeLocalDNS 配置
func (c *NodeLocalDNSConfig) Config() {}

// NewNodeLocalDNS - 初始化 NodeLocalDNS 方法
//
// PARAMS:
//   - ctx: context.Context
//   - base: *clients.Clients
//   - c: Config 用户定义 NodeLocalDNS 配置
//
// RETURNS:
//
//	Interface: NodeLocalDNS 的 addon.Interface 实现
//	error: nil if succeed, error if fail
func NewNodeLocalDNS(ctx context.Context, base *clients.Clients, c Config) (Interface, error) {
	if base == nil {
		return nil, errors.New("base is nil")
	}

	// 配置初始化
	var ok bool
	var config *NodeLocalDNSConfig
	if c != nil {
		// 目前看来, 允许用户不传入该参数
		config, ok = c.(*NodeLocalDNSConfig)
		if !ok {
			return nil, errors.New("addon config type not NodeLocalDNSConfig")
		}
	} else {
		config = &NodeLocalDNSConfig{}
	}

	config.LocalDNSAddr = base.Cluster.Spec.ContainerNetworkConfig.NodeLocalDNSAddr
	config.LocalDNSImage = base.Config.NodeLocalDNSImageID

	serviceClusterIPCIDR := base.Cluster.Spec.ContainerNetworkConfig.ClusterIPServiceCIDR
	clusterIP, err := GetCoreDNSClusterIP(ctx, serviceClusterIPCIDR)
	if err != nil {
		logger.Errorf(ctx, "getCoreDNSClusterIP failed: %s", err)
		return nil, err
	}
	config.ClusterDNSAddr = clusterIP

	return &NodeLocalDNS{
		base:   base,
		config: config,
	}, nil
}

// Name - 插件名字
func (c *NodeLocalDNS) Name(ctx context.Context) Type {
	return AddonNodeLocalDNS
}

// Namespace - 插件部署命名空间
func (c *NodeLocalDNS) Namespace(ctx context.Context) string {
	return ""
}

// HelmChartName - 插件在云端的 HelmChart 名称
func (c *NodeLocalDNS) HelmChartName(ctx context.Context) HelmChartName {
	return HelmChartNodeLocalDNS
}

func (c *NodeLocalDNS) HelmChartVersion(ctx context.Context) string {
	return c.base.Config.HelmPluginsConfig.ChartVersionNodeLocalDNS
}

func (c *NodeLocalDNS) ModifyHelmChartValues(ctx context.Context, values string) (string, error) {
	// modify nothing and just return the origin values
	return values, nil
}

// RenderValuesYAML - 通过 NodeLocalDNSConfig 渲染 values.yaml 模板
func (c *NodeLocalDNS) RenderValuesYAML(ctx context.Context, valuesTemp string) (string, error) {
	tmpl, err := template.New("NodeLocalDNS").Parse(valuesTemp)
	if err != nil {
		logger.Errorf(ctx, "template.Parse failed: %v", err)
		return "", err
	}

	var buf bytes.Buffer
	if err := tmpl.Execute(&buf, c.config); err != nil {
		logger.Errorf(ctx, "tmpl.Execute failed: %v", err)
		return "", err
	}

	logger.Infof(ctx, "NodeLocalDNS YAML: %s", buf.String())

	return buf.String(), nil
}
