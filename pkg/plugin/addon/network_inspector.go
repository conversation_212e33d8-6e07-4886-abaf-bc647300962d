// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2020/07/01 11:59:00, by <EMAIL>, create
*/
/*
DESCRIPTION
Template 插件部署, 实现 plugin.Interface
*/

package addon

import (
	"bytes"
	"context"
	"errors"
	"text/template"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/plugin/clients"
)

const (
	// AddonNetworkInspector - networkInspector 插件名
	AddonNetworkInspector Type = "cce-network-inspector"
)

func init() {
	SupportAddons[AddonNetworkInspector] = nil
}

// networkInspector - 实现 plugin.Interface 方法, 部署 networkInspector
type networkInspector struct {
	base   *clients.Clients
	config *networkInspectorConfig
}

// networkInspectorConfig - 部署 networkInspector 依赖配置
type networkInspectorConfig struct {
	inspectorImageID string
	resultImageID    string
}

// Config - networkInspector 配置
func (c *networkInspectorConfig) Config() {}

// NewNetworkInspector - 初始化 networkInspector 方法
//
// PARAMS:
//   - ctx: context.Context
//   - base: *clients.Clients
//   - c: Config 用户定义 networkInspector 配置
//
// RETURNS:
//
//	Interface: networkInspector 的 addon.Interface 实现
//	error: nil if succeed, error if fail
func NewNetworkInspector(ctx context.Context, base *clients.Clients, c Config) (Interface, error) {
	if base == nil {
		return nil, errors.New("base is nil")
	}

	return &networkInspector{
		base: base,
		config: &networkInspectorConfig{
			inspectorImageID: base.Config.NetworkInspectorImageID,
			resultImageID:    base.Config.NetworkInspectorResultImageID,
		},
	}, nil
}

// Name - 插件名字
func (c *networkInspector) Name(ctx context.Context) Type {
	return AddonNetworkInspector
}

// Namespace - 插件部署命名空间
func (c *networkInspector) Namespace(ctx context.Context) string {
	return ""
}

func (c *networkInspector) HelmChartName(ctx context.Context) HelmChartName {
	return ""
}

func (c *networkInspector) HelmChartVersion(ctx context.Context) string {
	return ""
}

func (c *networkInspector) ModifyHelmChartValues(ctx context.Context, values string) (string, error) {
	// modify nothing and just return the origin values
	return values, nil
}

// renderValuesYAML - 通过 networkInspectorConfig 渲染 values.yaml 模板
func (c *networkInspector) RenderValuesYAML(ctx context.Context, valuesTemp string) (string, error) {
	tmpl, err := template.New("networkInspector").Parse(valuesTemp)
	if err != nil {
		logger.Errorf(ctx, "networkInspector.Parse failed: %v", err)
		return "", err
	}

	var buf bytes.Buffer
	if err := tmpl.Execute(&buf, &struct {
		InspectorImageID string
		ResultImageID    string
	}{
		InspectorImageID: c.config.inspectorImageID,
		ResultImageID:    c.config.resultImageID,
	}); err != nil {
		logger.Errorf(ctx, "tmpl.Execute failed: %v", err)
		return "", err
	}

	logger.Infof(ctx, "networkInspector YAML: %s", buf.String())

	return buf.String(), nil
}
