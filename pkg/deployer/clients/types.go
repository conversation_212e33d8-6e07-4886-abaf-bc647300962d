// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2020/04/28 13:42:00, by <EMAIL>, create
*/
/*
Clients.Interface 定义 Deployer 依赖三方 Clients
*/

package clients

import (
	"context"
	"fmt"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/deployer/exec"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/deployer/types"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/deployer/workflow"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/sts"
)

// Interface 定义 Deployer 依赖三方 clients
type Interface interface {
	NewExecClient(ctx context.Context, instance *types.Instance) (exec.Interface, error)
	NewWorkflow(ctx context.Context, config *types.Config, cluster *types.Cluster, instance *types.Instance, stsClient sts.Interface) (workflow.Interface, error)
	NewUpgradeWorkflow(ctx context.Context, config *types.Config, cluster *types.Cluster, instance *types.Instance, roleType workflow.WorkflowRoleType) (workflow.Interface, error)
}

// Clients 实现 Interface 方法
type Clients struct {
}

// NewClients - 初始化 clients.Interface
func NewClients(ctx context.Context, config *types.Config) (Interface, error) {
	return &Clients{}, nil
}

// NewExecClient - 初始化实现 exec.Interface 的 Client
func (c *Clients) NewExecClient(ctx context.Context, instance *types.Instance) (exec.Interface, error) {
	return nil, nil
}

// NewWorkflow - 初始化实现 workflow.Interface 的 Client
func (c *Clients) NewWorkflow(ctx context.Context, config *types.Config, cluster *types.Cluster, instance *types.Instance, stsClient sts.Interface) (workflow.Interface, error) {
	return nil, fmt.Errorf("NewWorkflow not implemented")
}

// NewUpgradeWorkflow - 初始化实现 workflow.Interface 的 Client
func (c *Clients) NewUpgradeWorkflow(ctx context.Context, config *types.Config, cluster *types.Cluster, instance *types.Instance, roleType workflow.WorkflowRoleType) (workflow.Interface, error) {
	return nil, fmt.Errorf("NewUpgradeWorkflow not implemented")
}

// NewClusterRoleClient - 初始化实现 workflow.ClusterRoleInterface 的 Client
func (c *Clients) NewClusterRoleClient(ctx context.Context, config *types.Config, cluster *types.Cluster, instance *types.Instance) (workflow.ClusterRoleInterface, error) {
	return nil, nil
}
