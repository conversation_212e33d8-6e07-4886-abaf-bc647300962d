// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2020/04/21 15:50:00, by ch<PERSON><EMAIL>, create
*/
/*
DESCRIPTION
Workflow.Interface 定义部署一个 K8S Instance 核心步骤
*/

package workflow

import (
	"context"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/deployer/types"
	ccetypes "icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
)

// Interface 定义 Deployer 流程
type Interface interface {
	// 最终装机方式
	DecideDeployType(ctx context.Context) ccetypes.InstanceDeployType

	// 磁盘挂载
	EnsureDiskMounted(ctx context.Context) error

	// 安装包分发
	DeliverPackage(ctx context.Context) error

	// 系统配置初始化
	EnsureSystemInited(ctx context.Context) error

	// K8S 服务部署
	EnsureK8SServiceRunning(ctx context.Context) error

	// EnsurePostUserScript 执行后置脚本
	EnsurePostUserScript(ctx context.Context) error

	// 清理部署环境
	Clean(ctx context.Context) error
}

//go:generate mockgen -destination=./mock/mock_client.go -package=mock icode.baidu.com/baidu/cce-test/e2e-test/pkg/deployer/workflow ClusterRoleInterface

// ClusterRoleInterface 定义 Master 或 Node 返回不同 TaskList
// k8s_node.go: K8S Node 实现
// k8s_master.go: K8S Master 实现
type ClusterRoleInterface interface {
	DeliverPackage(ctx context.Context) ([]types.TaskInterface, error)
	SystemInitTaskList(ctx context.Context) ([]types.TaskInterface, error)
	K8SServiceTaskList(ctx context.Context) ([]types.TaskInterface, error)
	PostUserScriptList(ctx context.Context) ([]types.TaskInterface, error)
	Clean(ctx context.Context) ([]types.TaskInterface, error)
}

// WorkflowRoleType - 不同 ClusterRoleInterface 实现的 Type, 主要用于升级变更时指定
type WorkflowRoleType string

const (
	// 部署

	// WorkflowRoleTypeK8SContainerizedEdgeMaster -
	WorkflowRoleTypeK8SContainerizedEdgeMaster WorkflowRoleType = "K8SContainerizedEdgeMaster"

	// WorkflowRoleTypeK8SContainerizedManagedMaster -
	WorkflowRoleTypeK8SContainerizedManagedMaster WorkflowRoleType = "K8SContainerizedManagedMaster"

	// WorkflowRoleTypeK8SContainerizedMaster -
	WorkflowRoleTypeK8SContainerizedMaster WorkflowRoleType = "K8SContainerizedMaster"

	// WorkflowRoleTypeK8SEdgeNode -
	WorkflowRoleTypeK8SEdgeNode WorkflowRoleType = "K8SEdgeNode"

	// WorkflowRoleTypeK8SMaster -
	WorkflowRoleTypeK8SMaster WorkflowRoleType = "K8SMaster"

	// WorkflowRoleTypeK8SNode -
	WorkflowRoleTypeK8SNode WorkflowRoleType = "K8SNode"

	// 升级相关 WorkflowType:

	// WorkflowRoleTypeUpgradeMasterToContainerized -
	WorkflowRoleTypeUpgradeMasterToContainerized WorkflowRoleType = "UpgradeMasterToContainerized"

	// WorkflowRoleTypeUpgradeContainerizedMasterK8SVersion -
	WorkflowRoleTypeUpgradeContainerizedMasterK8SVersion WorkflowRoleType = "UpgradeContainerizedMasterK8SVersion"

	// WorkflowRoleTypeUpgradeBinaryMasterK8SVersion -
	WorkflowRoleTypeUpgradeBinaryMasterK8SVersion WorkflowRoleType = "UpgradeBinaryMasterK8SVersion"

	// WorkflowRoleTypeUpgradeBinaryTelegraf -
	WorkflowRoleTypeUpgradeBinaryTelegraf WorkflowRoleType = "UpgradeBinaryTelegraf"

	// WorkflowRoleTypeUpgradeBinaryLogbeat - bls logbeat agent
	WorkflowRoleTypeUpgradeBinaryLogbeat WorkflowRoleType = "UpgradeBinaryLogbeat"

	// WorkflowRoleTypeUpgradeBinaryKubeStateMetrics -
	WorkflowRoleTypeUpgradeBinaryKubeStateMetrics WorkflowRoleType = "UpgradeMonitorCollector"

	// WorkflowRoleTypeCheckBinaryMasterStatus -
	WorkflowRoleTypeCheckBinaryMasterStatus WorkflowRoleType = "CheckBinaryMasterStatus"

	// WorkflowRoleTypeCheckContainerizedMasterManifest -
	WorkflowRoleTypeCheckContainerizedMasterManifest WorkflowRoleType = "CheckContainerizedMasterManifest"
)

var (
	commandListNVME = `for dev in /sys/block/nvme*; do if [ -e "$dev" ]; then echo /dev/$(basename "$dev"); fi; done 2>/dev/null`

	mountDiskTmpl = `function ensureDiskConfig() {
  local config_Path="$1"
  local config_Device="$2"
  local config_VolumeID="$3"
  local config_ID="$4"
  local config_Local="$5"

  if [ -z "$config_Path" ]; then
    echo "check disk config failed, config.Path is empty" >&2
    return 1
  fi

  if [ "$config_Local" = "true" ]; then
    return 0
  fi

  if [ -z "$config_VolumeID" ]; then
    echo "check disk config failed, config.VolumeID is empty" >&2
    return 1
  fi

  echo "disk config: Path=$config_Path, Device=$config_Device, VolumeID=$config_VolumeID"

  # Resolve disk drift issue:
  # When a virtual machine has multiple disks and the system is reinstalled,
  # the device obtained from the CDS API may not be accurate.
  # For example, the API may return /dev/vdc, but the actual device in the VM is /dev/vdb.
  # We can use /sys/block/vd*/serial to identify the correct device (short ID or first 8 characters of the UUID).
  # eg: grep . /sys/block/vd*/serial
  # /sys/block/vda/serial:v-UjMB1yp1
  # /sys/block/vdb/serial:v-dU6rZcWz

  local stdout
  stdout=$(grep . /sys/block/vd*/serial 2>/dev/null)

  if [ $? -ne 0 ]; then
    echo "get disk serial failed, stderr: stdout" >&2
    return 1
  fi

  local newDevice=""
  IFS=$'\n' read -r -d '' -a devices <<<"$stdout"
  for dev in "${devices[@]}"; do
    if [ -z "$dev" ]; then
      continue
    fi

    IFS=':' read -ra kv <<<"$dev"

    if [ ${#kv[@]} -ne 2 ]; then
      echo "unexpected device serial: $dev" >&2
      continue
    fi

    if [ "${kv[1]}" = "$config_VolumeID" ] || [[ "${kv[1]}" == "$config_ID"* ]]; then
      IFS='/' read -ra strs <<<"${kv[0]}"

      if [ ${#strs[@]} -ne 5 ]; then
        echo "unexpected device serial: $dev, use default device" >&2
        continue
      fi

      newDevice="/dev/${strs[3]}"
      break
    fi
  done

  # If the CDS is not found on the virtual machine, raise an error
  if [ -z "$newDevice" ]; then
    echo "not found cds: $config_VolumeID in instance" >&2
    return 1
  fi

  # Use the discovered device instead of the one obtained from the API to avoid disk drift
  echo "get device from interface: $config_Device, real device is: $newDevice"
  config_Device="$newDevice"
  echo "newDevice: $newDevice"
  device=$newDevice
  return 0
}

function checkDiskAttached() {
  local config_Device="$1"

  local stdout
  stdout=$(ls "$config_Device" 2>/dev/null | wc -l)

  if [ $? -ne 0 ]; then
    echo "checkDiskAttached failed" >&2
    return 1
  fi

  if [ "$stdout" = "1" ]; then
    echo "Disk $config_Device is attached"
    return 0
  fi

  return 1
}

function getMountSrcOfDev() {
  local config_Device="$1"

  echo "try to find first partition of dev $config_Device"

  IFS='/' read -ra devName <<< "$config_Device"
  if [ ${#devName[@]} -ne 3 ]; then
    echo "invalid dev $config_Device" >&2
    return 1
  fi

  allDevs=()
  local devFiles
  devFiles=$(ls /dev)
  if [ $? -ne 0 ]; then
    echo "error occurs when ls /dev" >&2
    return 1
  fi

  for devFile in $devFiles; do
    if [ ! -d "/dev/$devFile" ] && [[ $devFile == *"${devName[2]}"* ]]; then
      echo $devFile
      allDevs+=("$devFile")
    fi
  done

  echo "all dev files found for dev $config_Device: ${allDevs[@]}"

  if [ ${#allDevs[@]} -eq 1 ]; then
    echo "no partition found for $config_Device, use device name as mount src"
    devSrc="$config_Device"
    return 0
  fi

  # todo 需要测试这个场景
  sort <<<"${allDevs[*]}" | read -a sortedDevs

  if [ ${#sortedDevs[@]} -ge 2 ] && [[ ${sortedDevs[1]} == ${sortedDevs[0]}* ]]; then
    echo "first partition ${sortedDevs[1]} found for $config_Device, use it as mount src"
    devSrc="/dev/${sortedDevs[1]}"
    return 0
  fi

  echo "cannot find first partition nor device file for dev $config_Device" >&2
  return 1
}

function  checkDiskMounted() {
  local config_Path="$1"
  local devSrc="$2"

  local stdout=$(df -h | awk '{print $1}' | grep "$devSrc" | wc -l)
  if [ $? -ne 0 ]; then
    echo "checkDiskMounted failed" >&2
    return 1
  fi

  if [ "$stdout" = "1" ]; then
    stdout=$(df -h | grep "$devSrc" | grep "$config_Path" | awk '{print $6}')
    if [ $? -ne 0 ]; then
      echo "checkDiskMounted failed" >&2
      return 1
    fi

    if [ "$stdout" != "$config_Path" ]; then
      echo "disk $devSrc already mounted and mount path is not as expected: $config_Path" >&2
      mounted=1
      return 1
    fi
    mounted=1
    return 0
  fi

  echo "Disk $devSrc not mounted, start mount"
  return 0
}

function getBlkidInfo() {

  local devSrc="$1"
  local blkidOutput=$2
  local blkidInfo
  
  declare -g -A "blkidOutput"  # 在全局范围内声明关联数组

  blkidInfo=$(blkid -o udev "$devSrc" 2>&1)
  if [ $? -ne 0 ]; then
    echo "exec blkid error, stdout: $blkidInfo" >&2
    return 1
  fi

  IFS=$'\n' read -r -d '' -a outputs <<<"$blkidInfo"
  for m in "${outputs[@]}"; do
     IFS='=' read -r key value <<< "$m"
     eval "$blkidOutput[$key]=$value"
  done
}

function getFilesystemType() {
    local devSrc="$1"

    declare -A blkidInfoArr

    if ! getBlkidInfo "$devSrc" "blkidInfoArr"; then  # 修复此处传递关联数组的方式
      echo "getBlkidInfo err" >&2
      return 0
    fi

    ID_FS_TYPE=${blkidInfoArr["ID_FS_TYPE"]}

    if [ -z "ID_FS_TYPE" ]; then  # 修复此处的变量引用，添加双引号以避免空格问题
      echo "ID_FS_TYPE not exist in blkid" >&2
      return 1
    fi
    fsType=$ID_FS_TYPE


    echo "dev: $devSrc file system: $ID_FS_TYPE"
    return 0
}

# 判断文件中是否存在指定内容
checkFileContent() {
  local filename="$1"
  local content="$2"
  if grep -q "$content" "$filename"; then
    return 0  # 存在指定内容，返回 0
  else
    return 1  # 不存在指定内容，返回非 0
  fi
}

function writeFstab() {
  local devSrc="$1"
  local mountPath="$2"

  # 声明空的关联数组
  declare -A blkidInfoArr

  if ! getBlkidInfo "$devSrc" "blkidInfoArr"; then
    echo "getBlkidInfo err" >&2
    return 1
  fi

  ID_FS_UUID=${blkidInfoArr["ID_FS_UUID"]}

  if [ -z "$ID_FS_UUID" ]; then
    echo "ID_FS_UUID not exist in blkid" >&2
    return 1
  fi

  echo "ID_FS_UUID: $ID_FS_UUID"

  # checkfile
  if [ -f "/etc/fstab" ]; then
    if checkFileContent "/etc/fstab" "$ID_FS_UUID" || checkFileContent "/etc/fstab" "$mountPath "; then
      echo "Disk $ID_FS_UUID already mount, /etc/fstab skip" 
      cat /etc/fstab
      return 0
    fi
  else
    echo "File /etc/fstab not exist" >&2
    return 1
  fi

  echo "# CCE UUID=$ID_FS_UUID init Start " >> /etc/fstab
  echo "UUID=$ID_FS_UUID $mountPath auto defaults,nofail 0 0" >> /etc/fstab
  echo "# CCE UUID=$ID_FS_UUID init End " >> /etc/fstab

  echo "Write fstab success, fsUUID:$ID_FS_UUID"
  return 0
}

# Main function
function main() {
  {{- if .DiskInfoLength }}
  length={{.DiskInfoLength}}
  {{- end }}
  {{range $key, $value := .DiskInfo}}
  {{$key}}={{$value}}{{end}}

  # length=2
  # arr_0=("2d0719a6-bfbe-4d60-937b-66bae9463ab3" "v-2O3VBe4h" "/dev/vdb" "/home/<USER>")
  # arr_1=("10c6e75c-41bb-4cbc-ab77-c1753ec1c7e7" "v-vRFgHaT9" "/dev/vdc" "/tmp")
  for ((i=0; i<length; i++)); do
    current_arr="arr_$i[@]"
    current_arr_values=("${!current_arr}")

    id=${current_arr_values[0]}
    volumeID=${current_arr_values[1]}
    device=${current_arr_values[2]}
    path=${current_arr_values[3]}
    needFormat=${current_arr_values[4]}
    isLocal=${current_arr_values[5]}

    echo "ID: $id Volume ID: $volumeID Device: $device Path: $path NeedFormat: $needFormat IsLocal: $isLocal"

    # Ensure disk configuration
    if ! ensureDiskConfig "$path" "$device" "$volumeID" "$id" "$isLocal"; then
      echo "checkDiskMountConfig failed, volumeID: $volumeID" >&2
      return 1
    fi

     # Check if disk is attached
    if ! checkDiskAttached "$device"; then
      echo "disk $mountConfig is not attached" >&2
      return 1
    fi

    echo "MountDisk begin:: Path=$path, Device=$device, VolumeID=$volumeID, NeedFormat: $needFormat, IsLocal: $isLocal"


    devSrc=""
      # Get mount source of device
    if ! getMountSrcOfDev "$device"; then
      echo "getMountSrcOfDev failed, $device is $device" >&2
      return 1
    fi
    echo "getMountSrcOfDev,devSrc is: $devSrc"

    # Check if disk is already mounted
    mounted=0
    if ! checkDiskMounted "$path" "$devSrc"; then
      echo "checkDiskMounted failed: $device" >&2 
      return 1
    fi

    if [ $mounted == 1 ]; then
      echo "disk $devSrc already mounted and mount path is not as expected: $config_Path" >&2
      continue
    fi

    # Get filesystem type
    fsType=""
    if ! getFilesystemType "$devSrc"; then
      echo "getFilesystemType failed: $devSrc"
      return 0
    fi
    echo fsType $fsType
    if [ -z $fsType ] || [ "$needFormat" = "true" ]; then
      {{.MkfsCmd}} $devSrc
      if [ $? -ne 0 ]; then
        echo "mkfs.ext4 $devSrc failed" >&2
        return 1
      fi
      echo "ensureDiskExt4 succeeded"
    fi

    # 创建挂载目录
    [ -d "$path" ] || mkdir -p "$path"
    # 更新 /etc/fstab 文件
    if ! writeFstab $devSrc $path ; then
      echo "writeFstab err" >&2
      return 1
    fi

  done
  # 统一挂载磁盘
  mount -a
  if [ $? -ne 0 ]; then
      echo "mount failed" >&2
      return 1
  fi
  return 0
}

# Call the main function
main
`
)
