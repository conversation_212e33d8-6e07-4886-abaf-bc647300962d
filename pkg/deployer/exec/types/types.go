package types

import "time"

const (
	// 默认 SSH 端口
	DefaultSSHPort = 22

	// SSH 操作默认重试次数
	DefaultRetry = 1

	// SSH 操作默认重试间隔
	DefaultInterval = 2

	// SSH 操作默认总超时时间, 超时不再重试
	DefaultTimeout = 30

	// 初始化 SSH Client 重试次数
	MaxRetryCount = 5
)

// File - Write 文件
type File struct {
	WriteType WriteType `json:"writeType"`
	Config    Config    `json:"config"`
}

// WriteType 写入类型
type WriteType string

const (
	// WriteTypeCreate - write
	WriteTypeCreate WriteType = "write"

	// WriteTypeCopy - copy
	WriteTypeCopy WriteType = "copy"

	// WriteTypeAppend - echo >>
	WriteTypeAppend WriteType = "append"

	// WriteTypeReplaceContent - replace
	WriteTypeReplaceContent WriteType = "replaceContent"
)

// Config - 定义写文件依赖配置
type Config interface {
	config()
}

func (*WriteFileConfig) config() {}

func (*CopyFileConfig) config() {}

func (*AppendFileConfig) config() {}

func (*ReplaceFileContentConfig) config() {}

// WriteFileConfig - 新建文件配置
type WriteFileConfig struct {
	// 源端
	Content *string `json:"content,omitempty"` // 文件内容

	// 目的
	Name string `json:"name"` // 文件名字

	Dir   *Dir   `json:"dir"`   // 文件目录
	Mod   string `json:"mod"`   // 文件权限
	Owner string `json:"owner"` // 文件属主
}

// CopyFileConfig - 复制文件配置
type CopyFileConfig struct {
	SourceFilePath string `json:"sourceFilePath"` // 原文件路径

	// 目的
	Name string `json:"name"` // 文件名字

	Dir   *Dir   `json:"dir"`   // 文件目录
	Mod   string `json:"mod"`   // 文件权限
	Owner string `json:"owner"` // 文件属主
}

// AppendFileConfig - 追加写文件配置
type AppendFileConfig struct {
	Contents []string `json:"contents,omitempty"` // 追加条目
	Name     string   `json:"name"`               // 文件名字
	Marker   string   `json:"marker"`             // 追加内容tag
	Dir      *Dir     `json:"dir"`                // 文件目录
}

// ReplaceFileContentConfig - 替换文件内容配置
type ReplaceFileContentConfig struct {
	Contents map[string]string `json:"contents,omitempty"` // 替换配置
	Name     string            `json:"name"`               // 文件名字
	Dir      string            `json:"dir"`                // 文件目录
}

// Command - Exec 命令
type Command struct {
	Command      string        `json:"command"`       // 命令
	Retry        int           `json:"retry"`         // 重试次数
	Interval     time.Duration `json:"interval"`      // 重试间隔
	Timeout      time.Duration `json:"timeout"`       // 整体超时时间
	IgnoreErrors bool          `json:"ignore_errors"` // 忽略错误
}

// Dir - 定义远程操作目录操作
type Dir struct {
	Path  string `json:"path"`  // 目录路径
	Mod   string `json:"mod"`   // 目录权限
	Owner string `json:"owner"` // 目录属主
}
