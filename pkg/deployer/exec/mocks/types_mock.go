// Code generated by MockGen. DO NOT EDIT.
// Source: ./pkg/deployer/exec/types.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	_ "fmt"
	os "os"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"

	exectypes "icode.baidu.com/baidu/cce-test/e2e-test/pkg/deployer/exec/types"
)

// MockInterface is a mock of Interface interface
type MockInterface struct {
	ctrl     *gomock.Controller
	recorder *MockInterfaceMockRecorder
}

func (m *MockInterface) CheckOOSClientOnline(ctx context.Context) (online bool, err error) {
	//TODO implement me
	return false, nil
}

func (m *MockInterface) ExecOOSTask(ctx context.Context, scripts, OOSTaskName string) (stdout, stderr string, err error) {
	//TODO implement me
	return "", "", nil
}

func (m *MockInterface) GeneratorScript(ctx context.Context, command *exectypes.Command) (string, string, error) {
	//TODO implement me
	return "", "", nil
}

// MockInterfaceMockRecorder is the mock recorder for MockInterface
type MockInterfaceMockRecorder struct {
	mock *MockInterface
}

// NewMockInterface creates a new mock instance
func NewMockInterface(ctrl *gomock.Controller) *MockInterface {
	mock := &MockInterface{ctrl: ctrl}
	mock.recorder = &MockInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use
func (m *MockInterface) EXPECT() *MockInterfaceMockRecorder {
	return m.recorder
}

func (m *MockInterface) GetWriteContents(ctx context.Context, file *exectypes.File) ([]string, error) {
	return nil, nil
}

// Read mocks base method
func (m *MockInterface) Read(ctx context.Context, path string) ([]byte, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Read", ctx, path)
	ret0, _ := ret[0].([]byte)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Read indicates an expected call of Read
func (mr *MockInterfaceMockRecorder) Read(ctx, path interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Read", reflect.TypeOf((*MockInterface)(nil).Read), ctx, path)
}

// ReadDir mocks base method
func (m *MockInterface) ReadDir(ctx context.Context, path string) ([]os.FileInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ReadDir", ctx, path)
	ret0, _ := ret[0].([]os.FileInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ReadDir indicates an expected call of ReadDir
func (mr *MockInterfaceMockRecorder) ReadDir(ctx, path interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReadDir", reflect.TypeOf((*MockInterface)(nil).ReadDir), ctx, path)
}

// Write mocks base method
func (m *MockInterface) Write(ctx context.Context, file *exectypes.File) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Write", ctx, file)
	ret0, _ := ret[0].(error)
	return ret0
}

// Write indicates an expected call of Write
func (mr *MockInterfaceMockRecorder) Write(ctx, file interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Write", reflect.TypeOf((*MockInterface)(nil).Write), ctx, file)
}

// Mkdir mocks base method
func (m *MockInterface) Mkdir(ctx context.Context, dir *exectypes.Dir) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Mkdir", ctx, dir)
	ret0, _ := ret[0].(error)
	return ret0
}

// Mkdir indicates an expected call of Mkdir
func (mr *MockInterfaceMockRecorder) Mkdir(ctx, dir interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Mkdir", reflect.TypeOf((*MockInterface)(nil).Mkdir), ctx, dir)
}

// Chmod mocks base method
func (m *MockInterface) Chmod(ctx context.Context, mod, path string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Chmod", ctx, mod, path)
	ret0, _ := ret[0].(error)
	return ret0
}

// Chmod indicates an expected call of Chmod
func (mr *MockInterfaceMockRecorder) Chmod(ctx, mod, path interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Chmod", reflect.TypeOf((*MockInterface)(nil).Chmod), ctx, mod, path)
}

// Chown mocks base method
func (m *MockInterface) Chown(ctx context.Context, owner, path string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Chown", ctx, owner, path)
	ret0, _ := ret[0].(error)
	return ret0
}

// Chown indicates an expected call of Chown
func (mr *MockInterfaceMockRecorder) Chown(ctx, owner, path interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Chown", reflect.TypeOf((*MockInterface)(nil).Chown), ctx, owner, path)
}

// Exists mocks base method
func (m *MockInterface) Exists(ctx context.Context, path string) bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Exists", ctx, path)
	ret0, _ := ret[0].(bool)
	return ret0
}

// Exists indicates an expected call of Exists
func (mr *MockInterfaceMockRecorder) Exists(ctx, path interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Exists", reflect.TypeOf((*MockInterface)(nil).Exists), ctx, path)
}

// Exec mocks base method
func (m *MockInterface) Exec(ctx context.Context, command *exectypes.Command) (string, string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Exec", ctx, command)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(string)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// Exec indicates an expected call of Exec
func (mr *MockInterfaceMockRecorder) Exec(ctx, command interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Exec", reflect.TypeOf((*MockInterface)(nil).Exec), ctx, command)
}

// MD5 mocks base method
func (m *MockInterface) MD5(ctx context.Context, path string) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MD5", ctx, path)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MD5 indicates an expected call of MD5
func (mr *MockInterfaceMockRecorder) MD5(ctx, path interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MD5", reflect.TypeOf((*MockInterface)(nil).MD5), ctx, path)
}

// MD5Equal mocks base method
func (m *MockInterface) MD5Equal(ctx context.Context, srcPath, dstPath string) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MD5Equal", ctx, srcPath, dstPath)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MD5Equal indicates an expected call of MD5Equal
func (mr *MockInterfaceMockRecorder) MD5Equal(ctx, srcPath, dstPath interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MD5Equal", reflect.TypeOf((*MockInterface)(nil).MD5Equal), ctx, srcPath, dstPath)
}

// MockConfig is a mock of Config interface
type MockConfig struct {
	ctrl     *gomock.Controller
	recorder *MockConfigMockRecorder
}

// MockConfigMockRecorder is the mock recorder for MockConfig
type MockConfigMockRecorder struct {
	mock *MockConfig
}

// NewMockConfig creates a new mock instance
func NewMockConfig(ctrl *gomock.Controller) *MockConfig {
	mock := &MockConfig{ctrl: ctrl}
	mock.recorder = &MockConfigMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use
func (m *MockConfig) EXPECT() *MockConfigMockRecorder {
	return m.recorder
}

// config mocks base method
func (m *MockConfig) config() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "config")
}

// config indicates an expected call of config
func (mr *MockConfigMockRecorder) config() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "config", reflect.TypeOf((*MockConfig)(nil).config))
}
