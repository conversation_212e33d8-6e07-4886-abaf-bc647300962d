// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2020/04/21 18:40:00, by <EMAIL>, create
*/
/*
DESCRIPTION
使用 ssh user + password 实现 exec.Interface
*/

package exec

import (
	"bytes"
	"context"
	"crypto/md5"
	"encoding/hex"
	"errors"
	"fmt"
	"io"
	"math/rand"
	"net"
	"os"
	"path"
	"strconv"
	"strings"
	"time"

	"github.com/pkg/sftp"
	"golang.org/x/crypto/ssh"

	exectypes "icode.baidu.com/baidu/cce-test/e2e-test/pkg/deployer/exec/types"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/deployer/types"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	ccetypes "icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
)

// 自定义 KexAlgos
// 解决 ARM 自定义镜像 ssh 报错: ssh: handshake failed: ssh: no common algorithm for key exchange; client offered: [<EMAIL> ecdh-sha2-nistp256 ecdh-sha2-nistp384 ecdh-sha2-nistp521 diffie-hellman-group14-sha1], server offered: [diffie-hellman-group1-sha1]
// Copy from crypto/ssh/common.go supportedKexAlgos
var supportedKexAlgos = []string{
	"<EMAIL>",
	"ecdh-sha2-nistp256",
	"ecdh-sha2-nistp384",
	"ecdh-sha2-nistp521",
	"diffie-hellman-group14-sha1",
	"diffie-hellman-group1-sha1",
}

// SSHClient - 使用 SSH 实现 exec.Interface
type SSHClient struct {
	// TODO: 是否可复用, 如果不行可以在每次方法调用中初始化
	client *ssh.Client
}

// NewSSHClient - 初始化 SSHClient
func NewSSHClient(ctx context.Context, instance *types.Instance) (Interface, ccetypes.InstanceDeployType, error) {
	if instance == nil {
		logger.Errorf(ctx, "instanceDeployConfig is nil")
		return nil, "", errors.New("instanceDeployConfig is nil")
	}

	if instance.SSHIP == "" {
		return nil, "", errors.New("ip is empty")
	}

	if instance.SSHPort <= 0 {
		logger.Infof(ctx, "SSHPort is set to %s", exectypes.DefaultSSHPort)
		instance.SSHPort = exectypes.DefaultSSHPort
	}

	if instance.RunUser == "" {
		return nil, "", errors.New("user is empty")
	}

	if instance.Password == "" {
		return nil, "", errors.New("password is empty")
	}

	// 初始化 SSH 配置
	config := &ssh.ClientConfig{
		Config: ssh.Config{
			KeyExchanges: supportedKexAlgos,
		},
		User: instance.RunUser,
		Auth: []ssh.AuthMethod{
			ssh.Password(instance.Password),
		},
		HostKeyCallback: func(hostname string, remote net.Addr, key ssh.PublicKey) error {
			return nil
		},
		Timeout: time.Second * time.Duration(exectypes.DefaultTimeout),
	}

	// 初始化 ssh.Client
	addr := instance.SSHIP + ":" + strconv.Itoa(instance.SSHPort)
	logger.Infof(ctx, "SSH Addr: %s", addr)

	var err error
	var client *ssh.Client

	// 初始化可能失败, 重试多次
	for i := 0; i < exectypes.MaxRetryCount; i++ {
		logger.Infof(ctx, "SSH Dial: %s", addr)
		client, err = sshDial("tcp", addr, config)
		// 密码错误导致登录失败的情况，直接退出，不重试，避免被拉黑
		if err != nil && strings.Contains(err.Error(), "ssh: unable to authenticate") {
			logger.Errorf(ctx, "ssh.Dial failed, password not correct, err: %s", err)
			return nil, "", err
		}
		if err != nil {
			time.Sleep(time.Second * 5)
			logger.Errorf(ctx, "ssh.Dial failed: %s", err)
		}

		if err == nil {
			break
		}
	}

	if err != nil {
		logger.Errorf(ctx, "Init ssh.Client failed: %s", err)
		return nil, "", err
	}

	return &SSHClient{
		client: client,
	}, ccetypes.DeployInstanceBySSH, nil
}

// Note that half of the function is just a copy of
// golang.org/x/crypto/ssh/client.go:func Dial()
func sshDial(network, addr string, config *ssh.ClientConfig) (*ssh.Client, error) {
	conn, err := net.DialTimeout(network, addr, config.Timeout)
	if err != nil {
		return nil, err
	}
	// start of the new code
	// Ensure that if the connection goes away during e.g. a hang node
	// the code does not hang forever.
	if config.Timeout > 0 {
		if err := conn.SetDeadline(time.Now().Add(config.Timeout)); err != nil {
			return nil, err
		}
		defer func() {
			conn.SetDeadline(time.Time{})
		}()
	}
	// end of the new code
	c, chans, reqs, err := ssh.NewClientConn(conn, addr, config)
	if err != nil {
		return nil, err
	}
	return ssh.NewClient(c, chans, reqs), nil
}

// Exec - 远程执行命令
//
// PARAMS:
//   - command: *Command, 参数含义如下
//     Command: 执行命令
//     Retry: 重试次数
//     Interval: 每次重试间隔
//     Timeout: Exec 整体超时时间
//     IgnoreErrors: 是否忽略报错
//
// RETURNS:
//
//	string: stdout 输出
//	string: stderr 输出
//	error: nil if succeed, error if fail
func (c *SSHClient) Exec(ctx context.Context, command *exectypes.Command) (string, string, error) {
	if command == nil {
		return "", "", errors.New("comamnd is nil")
	}

	if command.Command == "" {
		return "", "", errors.New("comamnd.Command is nil")
	}

	if command.Timeout <= 0 {
		command.Timeout = exectypes.DefaultTimeout
	}

	if command.Interval == 0 {
		command.Interval = exectypes.DefaultInterval
	}

	if command.Interval < 0 {
		command.Interval = 0
	}

	if command.Retry <= 0 {
		command.Retry = exectypes.DefaultRetry
	}

	errChan := make(chan error, 1)
	timeout := time.NewTimer(command.Timeout * time.Second)

	// 执行命令
	var cmdError error
	var stdout bytes.Buffer
	var stderr bytes.Buffer
	for i := 0; i < command.Retry; i++ {
		go func(ctx context.Context) {
			// 建立连接
			session, err := c.client.NewSession()
			if err != nil {
				logger.Errorf(ctx, "NewSession failed: %s", err)
				errChan <- err
				return
			}
			defer session.Close()

			// reset buffer
			stdout.Reset()
			stderr.Reset()

			session.Stdout = &stdout
			session.Stderr = &stderr

			logger.Infof(ctx, "Exec '%s' begin", command.Command)

			// 执行命令
			if err := session.Run(command.Command); err != nil {
				logger.Errorf(ctx, "Run '%s' failed, stderr: %s, err: %s", command.Command, Trim(stderr.String()), err)
				errChan <- err
				return
			}

			errChan <- nil
		}(ctx)

		// 等待结果
		select {
		case cmdError = <-errChan:
			if cmdError == nil {
				logger.Infof(ctx, "Exec [%s] success: stdout=[%s] stderr=[%s]",
					command.Command, Trim(stdout.String()), Trim(stderr.String()))
				return Trim(stdout.String()), Trim(stderr.String()), nil
			}

			if command.IgnoreErrors {
				logger.Infof(ctx, "Ignore errors: Exec '%s' failed, stderr: %s",
					command.Command, Trim(stderr.String()))
				return Trim(stdout.String()), Trim(stderr.String()), nil
			}
		case <-timeout.C:
			logger.Errorf(ctx, "Exec '%s' timeout", command.Command)
			errMsg := fmt.Sprintf("command: %s execution timeout", command.Command)
			return "", Trim(errMsg), errors.New("ExecutionTimeout")
		}

		time.Sleep(command.Interval * time.Second)
	}

	// TODO: 可能出现 err == nil 但 stderr != "" 情况
	var errMsg string
	if stderr.String() == "" {
		errMsg = fmt.Sprintf("command: %s execution failed", command.Command)
	} else {
		errMsg = fmt.Sprintf("command: %s execution failed: %s", command.Command, Trim(stderr.String()))
	}
	return Trim(stdout.String()), Trim(errMsg), cmdError
}

func (c *SSHClient) GetWriteContents(ctx context.Context, file *exectypes.File) ([]string, error) {
	return nil, nil
}

// Write - 将文件写到远程
//
// PARAMS:
//   - file: *File
//
// RETURNS:
//
//	error: nil if succeed, error if fail
func (c *SSHClient) Write(ctx context.Context, file *exectypes.File) error {
	if file == nil {
		return errors.New("file is nil")
	}

	// 写文件
	if file.WriteType == exectypes.WriteTypeCreate {
		config, ok := file.Config.(*exectypes.WriteFileConfig)
		if !ok {
			msg := fmt.Sprintf("WriteType %s but Config is not WriteFileConfig", exectypes.WriteTypeCreate)
			logger.Errorf(ctx, msg)
			return fmt.Errorf(msg)
		}

		if err := c.writeFile(ctx, config); err != nil {
			logger.Errorf(ctx, "CopyFile failed: %s", err)
			return err
		}

		return nil
	}

	// 复制文件
	if file.WriteType == exectypes.WriteTypeCopy {
		config, ok := file.Config.(*exectypes.CopyFileConfig)
		if !ok {
			msg := fmt.Sprintf("WriteType %s but Config is not CopyFileConfig", exectypes.WriteTypeCopy)
			logger.Errorf(ctx, msg)
			return fmt.Errorf(msg)
		}

		if err := c.copyFile(ctx, config); err != nil {
			logger.Errorf(ctx, "CopyFile failed: %s", err)
			return err
		}

		return nil
	}

	// 追加文件
	if file.WriteType == exectypes.WriteTypeAppend {
		config, ok := file.Config.(*exectypes.AppendFileConfig)
		if !ok {
			msg := fmt.Sprintf("WriteType %s but Config is not AppendFileConfig", exectypes.WriteTypeAppend)
			logger.Errorf(ctx, msg)
			return fmt.Errorf(msg)
		}

		if err := c.appendFile(ctx, config); err != nil {
			logger.Errorf(ctx, "appendFile failed: %s", err)
			return err
		}

		return nil
	}

	// 替换配置
	if file.WriteType == exectypes.WriteTypeReplaceContent {
		config, ok := file.Config.(*exectypes.ReplaceFileContentConfig)
		if !ok {
			msg := fmt.Sprintf("WriteType %s but Config is not WriteTypeReplace", exectypes.WriteTypeReplaceContent)
			logger.Errorf(ctx, msg)
			return fmt.Errorf(msg)
		}

		if err := c.replaceFileContent(ctx, config); err != nil {
			logger.Errorf(ctx, "replaceFile failed: %s", err)
			return err
		}

		return nil
	}

	return fmt.Errorf("unknown type %s", file.WriteType)
}

// Read - 从远程读取文件
//
// PARAMS:
//   - path: 远程文件路径
//
// RETURNS:
//
//	[]byte: 文件内容
//	error: nil if succeed, error if fail
func (c *SSHClient) Read(ctx context.Context, path string) ([]byte, error) {
	if path == "" {
		return nil, errors.New("path is empty")
	}

	var content []byte

	// 检查是否存在
	if exist := c.Exists(ctx, path); !exist {
		logger.Errorf(ctx, "File %s not exist in remote", path)
		return content, fmt.Errorf("File %s not exist in remote", path)
	}

	// 初始化 sftp client
	client, err := sftp.NewClient(c.client)
	if err != nil {
		logger.Errorf(ctx, "sftp.NewClient failed: %s", err)
		return content, err
	}
	defer client.Close()

	// 打开远程文件
	file, err := client.Open(path)
	if err != nil {
		logger.Errorf(ctx, "Open failed: %s", err)
		return content, err
	}
	defer file.Close()

	// 创建临时目录
	tempFileName := strings.ReplaceAll(path[1:], "/", "_")
	now := time.Now().Unix()
	rand := rand.New(rand.NewSource(now))
	prefix := tempFileName + "_" + strconv.FormatInt(now, 10) + "_" + strconv.Itoa(rand.Int())
	tempDir, err := os.MkdirTemp("/tmp/", prefix)
	if err != nil {
		return content, err
	}
	defer os.Remove(tempDir)

	// 创建临时文件
	tempFile, err := os.CreateTemp(tempDir, tempFileName)
	if err != nil {
		return content, err
	}
	defer os.Remove(tempFile.Name())

	// 远程写临时文件
	_, err = file.WriteTo(tempFile)
	if err != nil {
		return content, err
	}

	// 返回文件内容
	return os.ReadFile(tempFile.Name())
}

// ReadDir - 从远程读取文件夹内容
//
// PARAMS:
//   - path: 远程文件夹路径
//
// RETURNS:
//
//	[]os.FileInfo: 文件信息
//	error: nil if succeed, error if fail
func (c *SSHClient) ReadDir(ctx context.Context, path string) ([]os.FileInfo, error) {
	if path == "" {
		return nil, errors.New("path is empty")
	}

	// 检查是否存在
	if exist := c.Exists(ctx, path); !exist {
		logger.Errorf(ctx, "File %s not exist in remote", path)
		return nil, fmt.Errorf("File %s not exist in remote", path)
	}

	// 初始化 sftp client
	client, err := sftp.NewClient(c.client)
	if err != nil {
		logger.Errorf(ctx, "sftp.NewClient failed: %s", err)
		return nil, err
	}
	defer client.Close()

	files, err := client.ReadDir(path)
	if err != nil {
		logger.Errorf(ctx, "sftp.NewClient failed: %s", err)
		return nil, err
	}

	// 返回文件夹内容
	return files, nil
}

// Mkdir - 远程创建目录
//
// PARAMS:
//   - dir: string
//
// RETURNS:
//
//	error: nil if succeed, error if fail
func (c *SSHClient) Mkdir(ctx context.Context, dir *exectypes.Dir) error {
	if dir == nil {
		return errors.New("dir is nil")
	}

	if dir.Path == "" {
		return errors.New("dir.Path is nil")
	}

	// 检查目录是否存在
	// 这里的检查可以跟 mkdir 放在一起执行，但是不知道有没有其他地方依赖，暂时不做改动
	if c.Exists(ctx, dir.Path) {
		logger.Infof(ctx, "Dir %s exists, skip mkdir", dir.Path)
		return nil
	}

	// 创建目录
	command := fmt.Sprintf("mkdir -p %s", dir.Path)
	logger.Infof(ctx, "Mkdir command: %s", command)

	if _, stderr, err := c.Exec(ctx, &exectypes.Command{
		Command: command,

		Retry:    exectypes.DefaultRetry,
		Interval: exectypes.DefaultInterval,
		Timeout:  exectypes.DefaultTimeout,
	}); err != nil {
		logger.Errorf(ctx, "Exec '%s' failed: stderr=%s err=%s", command, stderr, err)
		return err
	}

	// 设置属主
	if dir.Owner != "" {
		if err := c.Chown(ctx, dir.Owner, dir.Path); err != nil {
			logger.Errorf(ctx, "Chown failed: %s", err)
			return err
		}
	}

	// 设置权限
	if dir.Mod != "" {
		if err := c.Chmod(ctx, dir.Mod, dir.Path); err != nil {
			logger.Errorf(ctx, "Chmod failed: %s", err)
			return err
		}
	}

	return nil
}

// Exists - 检查路径是否存在
//
// PARAMS:
//   - path: string
//
// RETURNS:
//
//	bool: true = 路径存在
func (c *SSHClient) Exists(ctx context.Context, path string) bool {
	if _, _, err := c.Exec(ctx, &exectypes.Command{
		Interval: -1,
		Command:  fmt.Sprintf("ls %s", path),
	}); err != nil {
		return false
	}

	return true
}

// Chmod - 设置权限
//
// PARAMS:
//   - mod: 权限
//   - path: string
//
// RETURNS:
//
//	error: nil if succeed, error if fail
func (c *SSHClient) Chmod(ctx context.Context, mod, path string) error {
	if path == "" {
		return errors.New("path is empty")
	}

	if mod == "" {
		return errors.New("mod is empty")
	}

	command := fmt.Sprintf("chmod %s %s", mod, path)
	logger.Infof(ctx, "Chmod command: %s", command)

	if _, stderr, err := c.Exec(ctx, &exectypes.Command{
		Command: command,

		Retry:    exectypes.DefaultRetry,
		Interval: exectypes.DefaultInterval,
		Timeout:  exectypes.DefaultTimeout,
	}); err != nil {
		logger.Errorf(ctx, "Exec '%s' failed: stderr=%s err=%s", command, stderr, err)
		return err
	}

	return nil
}

// Chown - 设置属主
//
// PARAMS:
//   - mod: 权限
//   - path: string
//
// RETURNS:
//
//	error: nil if succeed, error if fail
func (c *SSHClient) Chown(ctx context.Context, owner, path string) error {
	if owner == "" {
		return errors.New("owner is empty")
	}

	if path == "" {
		return errors.New("path is empty")
	}

	command := fmt.Sprintf("chown %s:%s %s", owner, owner, path)
	logger.Infof(ctx, "Chown command: %s", command)

	if _, stderr, err := c.Exec(ctx, &exectypes.Command{
		Command: command,

		Retry:    exectypes.DefaultRetry,
		Interval: exectypes.DefaultInterval,
		Timeout:  exectypes.DefaultTimeout,
	}); err != nil {
		logger.Errorf(ctx, "Exec '%s' failed: stderr=%s err=%s", command, stderr, err)
		return err
	}

	return nil
}

// Copy - 复制文件
func (c *SSHClient) copyFile(ctx context.Context, config *exectypes.CopyFileConfig) error {
	if config == nil {
		return errors.New("file is nil")
	}

	if config.Dir == nil {
		return errors.New("file.Dir is nil")
	}

	if config.SourceFilePath == "" {
		return errors.New("file.SourceFilePath is nil")
	}

	if config.Dir.Path == "" {
		return errors.New("file.Dir.Path is empty")
	}

	// Name 不允许带路径
	if config.Name == "" {
		config.Name = path.Base(config.SourceFilePath)
	} else {
		config.Name = path.Base(config.Name)
	}

	// 远程目录存在
	if err := c.Mkdir(ctx, config.Dir); err != nil {
		logger.Errorf(ctx, "Mkdir %s failed: %s", config.Dir.Path, err)
		return err
	}

	// 目的端 Path
	dstPath := path.Join(config.Dir.Path, config.Name)

	// 对比 MD5 是否相等
	equal, err := c.MD5Equal(ctx, config.SourceFilePath, dstPath)
	if err != nil {
		logger.Errorf(ctx, "MD5Equal failed: %s", err)
		return err
	}

	if equal {
		logger.Infof(ctx, "Source and Destination MD5 euqal, skip scp")
		return nil
	}

	// 源文件
	src, err := os.Open(config.SourceFilePath)
	if err != nil {
		logger.Errorf(ctx, "Open sourceFile %s failed: %s", config.SourceFilePath, err)
		return err
	}
	defer src.Close()

	// 目的文件
	sftpclient, err := sftp.NewClient(c.client)
	if err != nil {
		logger.Errorf(ctx, "sftp.NewClient failed: %s", err)
		return err
	}
	defer sftpclient.Close()

	dst, err := sftpclient.Create(dstPath)
	if err != nil {
		logger.Errorf(ctx, "Create DstFile %s failed: %s", dstPath, err)
		return err
	}
	defer dst.Close()

	// 复制内容
	start := time.Now()
	logger.Infof(ctx, "Delivering '%s'", config.Name)

	bufsize := 1024 * 1024 * 5
	buf := make([]byte, bufsize)
	for {
		n, err := src.Read(buf)
		if err != nil {
			if err == io.EOF {
				break
			}

			logger.Errorf(ctx, "Read SourceFilePath failed: %s", err)
			return err
		}

		dst.Write(buf[0:n])
	}

	logger.Infof(ctx, "Delivering '%s' success, costs in seconds: %.2f", config.Name, time.Since(start).Seconds())

	return nil
}

// writeFile - 写文件, 并保证前置目录存在, 如果文件已经存在会覆盖原有内容
func (c *SSHClient) writeFile(ctx context.Context, config *exectypes.WriteFileConfig) error {
	if config == nil {
		return errors.New("config is nil")
	}

	if config.Content == nil {
		return errors.New("config.Content is nil")
	}

	if config.Name == "" {
		return errors.New("config.Name is empty")
	}

	if config.Dir == nil {
		return errors.New("config.Dir is empty")
	}

	// 文件内容
	content := *(config.Content)
	if content == "" {
		return errors.New("config.Content is empty")
	}

	// 保证目录存在
	if err := c.Mkdir(ctx, config.Dir); err != nil {
		logger.Errorf(ctx, "Mkdir %s failed: %s", config.Dir.Path, err)
		return err
	}

	// 写文件
	session, err := c.client.NewSession()
	if err != nil {
		logger.Errorf(ctx, "NewSession failed: %s", err)
		return err
	}
	defer session.Close()

	go func() {
		stdinpipe, err := session.StdinPipe()
		if err != nil {
			logger.Errorf(ctx, "Session stdin pipe failed: %s", err)
			return
		}

		defer stdinpipe.Close()

		fmt.Fprintln(stdinpipe, "C0644", len(content), config.Name)
		fmt.Fprint(stdinpipe, content)
		fmt.Fprint(stdinpipe, "\x00")
	}()

	if err := session.Run(fmt.Sprintf("/usr/bin/scp -tr %s", config.Dir.Path)); err != nil {
		logger.Errorf(ctx, "scp failed: %s", err)
		return nil
	}

	filePath := fmt.Sprintf("%s/%s", config.Dir.Path, config.Name)

	// 设置属主
	if config.Owner != "" {
		if err := c.Chown(ctx, config.Owner, filePath); err != nil {
			logger.Errorf(ctx, "Chown failed: %s", err)
			return err
		}
	}

	// 设置权限
	if config.Mod != "" {
		if err := c.Chown(ctx, config.Mod, filePath); err != nil {
			logger.Errorf(ctx, "Chown failed: %s", err)
			return err
		}
	}

	return nil
}

func (c *SSHClient) appendFile(ctx context.Context, config *exectypes.AppendFileConfig) error {
	if config == nil {
		return errors.New("config is nil")
	}

	if len(config.Contents) == 0 {
		return errors.New("config.Contents len is 0")
	}

	if config.Name == "" {
		return errors.New("config.Name is empty")
	}

	if config.Dir == nil {
		return errors.New("config.Dir is empty")
	}

	// 文件 Path
	srcPath := path.Join(config.Dir.Path, config.Name)

	// read File
	fileContent, err := c.Read(ctx, srcPath)
	if err != nil {
		return fmt.Errorf("read file failed, path: %s, err: %s", srcPath, err)
	}

	// check market
	fileStr := string(fileContent[:])
	if strings.Contains(fileStr, config.Marker) {
		logger.Infof(ctx, "marker exists, do not need to append")
		return nil
	}

	// append content
	appendContent := config.Marker + " Start" + "\n"
	for _, content := range config.Contents {
		appendContent = appendContent + content + "\n"
	}
	appendContent = appendContent + config.Marker + " End"

	// 追加文件
	session, err := c.client.NewSession()
	if err != nil {
		logger.Errorf(ctx, "NewSession failed: %s", err)
		return err
	}
	defer session.Close()

	if err := session.Run(fmt.Sprintf("echo '%s' >> %s", appendContent, srcPath)); err != nil {
		logger.Errorf(ctx, "append file failed: %s", err)
		return nil
	}

	return nil
}

func (c *SSHClient) replaceFileContent(ctx context.Context, config *exectypes.ReplaceFileContentConfig) error {
	return nil
}

// MD5Equal - 检查本地和远程文件 md5 是否一致
//
// PARAMS:
//   - sourcePath: 本地文件路径
//   - dstPath: 远程文件路径
//
// RETURNS:
//
//	bool: 是否相等
//	error: nil if succeed, error if fail
func (c *SSHClient) MD5Equal(ctx context.Context, sourcePath, dstPath string) (bool, error) {
	if sourcePath == "" {
		return false, errors.New("sourcePath is empty")
	}

	if dstPath == "" {
		return false, errors.New("dstPath is empty")
	}

	// 本地 MD5
	sourceFile, err := os.Open(sourcePath)
	if err != nil {
		logger.Errorf(ctx, "Open %s failed: %s", sourceFile, err)
		return false, err
	}
	defer sourceFile.Close()

	md5 := md5.New()
	_, err = io.Copy(md5, sourceFile)
	if err != nil {
		logger.Errorf(ctx, "io.Copy failed: %s", err)
		return false, err
	}

	sourceMD5 := hex.EncodeToString(md5.Sum(nil))
	logger.Infof(ctx, "Source %s MD5: %s", sourcePath, sourceMD5)

	// 远程 MD5
	dstMD5, err := c.MD5(ctx, dstPath)
	if err != nil {
		logger.Errorf(ctx, "MD5 failed: %s", err)
		return false, err
	}
	logger.Infof(ctx, "Dst %s MD5: %s", dstPath, dstMD5)

	// 比较 MD5
	return sourceMD5 == dstMD5, nil
}

// MD5 - 获取 reader 的 MD5 值
func (c *SSHClient) MD5(ctx context.Context, path string) (string, error) {
	if path == "" {
		return "", errors.New("path is empty")
	}

	command := fmt.Sprintf("md5sum %s | awk '{print $1}'", path)
	logger.Infof(ctx, "md5sum command: %s", command)

	stdout, stderr, err := c.Exec(ctx, &exectypes.Command{
		Command: command,
	})
	if err != nil {
		logger.Errorf(ctx, "Exec '%s' failed: stderr=%s err=%s", command, stderr, err)
		return "", err
	}

	return stdout, nil
}

// Trim - 去除空格和换行符
func Trim(str string) string {
	str = strings.TrimLeft(str, "\n")
	str = strings.TrimLeft(str, " ")

	str = strings.TrimRight(str, "\n")
	str = strings.TrimRight(str, " ")

	return str
}

func (c *SSHClient) CheckOOSClientOnline(ctx context.Context) (online bool, err error) {
	return false, errors.New("sshClient not support CheckOOSClientOnline")
}

func (c *SSHClient) ExecOOSTask(ctx context.Context, scripts string, OOSTaskName string) (stdout, stderr string, err error) {
	return "", "", errors.New("sshClient not support ExecOOSTask")
}

func (c *SSHClient) GeneratorScript(ctx context.Context, command *exectypes.Command) (string, string, error) {
	logger.Errorf(ctx, "sshClient not support GeneratorScript")
	return "", "", errors.New("sshClient not support GeneratorScript")
}
