// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2020/04/28 18:40:00, by <EMAIL>, create
*/
/*
DESCRIPTION
本地 local run 实现 exec.Interface
*/

package exec

import (
	"context"
	"errors"
	"fmt"
	"os"
	"strings"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/oos"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/retrypolicy"
	exectypes "icode.baidu.com/baidu/cce-test/e2e-test/pkg/deployer/exec/types"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/deployer/types"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/sts"
	ccetypes "icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/utils"
)

var _ Interface = &OOSClient{}

type OOSClient struct {
	instanceDeployConfig *types.Instance
	oosClient            *oos.Client
	stsClient            sts.Interface
}

func NewOOSClient(ctx context.Context, instance *types.Instance, config *types.Config) (Interface, ccetypes.InstanceDeployType, error) {
	if instance == nil {
		logger.Errorf(ctx, "instanceDeployConfig is nil")
		return nil, "", errors.New("instanceDeployConfig is nil")
	}
	if config == nil {
		logger.Errorf(ctx, "deployConfig is nil")
		return nil, "", errors.New("deployConfig is nil")
	}

	logger.Infof(ctx, "instanceDeployConfig: %s", utils.ToJSON(instance))
	oosClient := oos.NewClient(oos.BCEConfigWithRetry(ctx, oos.Endpoint[instance.Region], 30, instance.Region, 2))
	if oosClient == nil {
		return nil, "", errors.New("create oos client failed")
	}

	retryPolicy := retrypolicy.NewIntervalRetryPolicy(ctx, 3, 4)
	if retryPolicy == nil {
		return nil, "", errors.New("create retry policy failed")
	}

	if config.STSEndpoint == "" {
		return nil, "", errors.New("config STSEndpoint is empty")
	}
	if config.IAMEndpoint == "" {
		return nil, "", errors.New("config IAMEndpoint is empty")
	}
	if config.ServiceRoleName == "" {
		return nil, "", errors.New("config ServiceRoleName is empty")
	}
	if config.ServiceName == "" {
		return nil, "", errors.New("config ServiceName is empty")
	}
	if config.ServicePassword == "" {
		return nil, "", errors.New("config ServicePassword is empty")
	}

	if instance.InstanceUUID == "" {
		return nil, "", errors.New("instanceDeploConfig InstanceUUID is empty")
	}
	if instance.MachineID == "" {
		return nil, "", errors.New("instanceDeploConfig MachineID is empty")
	}
	if instance.CCEInstanceID == "" {
		return nil, "", errors.New("instanceDeploConfig CCEInstanceID is empty")
	}
	if instance.Region == "" {
		return nil, "", errors.New("instanceDeploConfig Region is empty")
	}
	if instance.AccountID == "" {
		return nil, "", errors.New("instanceDeploConfig AccountID is empty")
	}

	stsClient := sts.NewClient(ctx,
		&bce.Config{
			Endpoint:    config.STSEndpoint,
			Checksum:    true,
			Timeout:     config.Timeout,
			Region:      instance.Region,
			RetryPolicy: retryPolicy,
		}, &bce.Config{
			Endpoint:    config.IAMEndpoint,
			Checksum:    true,
			Timeout:     config.Timeout,
			Region:      instance.Region,
			RetryPolicy: retryPolicy,
		}, config.ServiceRoleName, config.ServiceName, config.ServicePassword)
	if stsClient == nil {
		return nil, "", errors.New("create sts client failed")
	}

	return &OOSClient{
		instanceDeployConfig: instance,
		oosClient:            oosClient,
		stsClient:            stsClient,
	}, ccetypes.DeployInstanceByOOS, nil
}

func (c *OOSClient) Read(ctx context.Context, path string) ([]byte, error) {
	return nil, errors.New("function not implemented")
}

func (c *OOSClient) ReadDir(ctx context.Context, path string) ([]os.FileInfo, error) {
	return nil, errors.New("function not implemented")
}

func (c *OOSClient) Write(ctx context.Context, file *exectypes.File) error {
	return errors.New("function not implemented")
}

func (c *OOSClient) GetWriteContents(ctx context.Context, file *exectypes.File) ([]string, error) {
	if file == nil {
		return nil, errors.New("file is nil")
	}

	if file.WriteType != exectypes.WriteTypeCreate {
		logger.Errorf(ctx, "only support type:WriteTypeCreate")
		return nil, errors.New("only support type:WriteTypeCreate")
	}

	config, ok := file.Config.(*exectypes.WriteFileConfig)
	if !ok {
		msg := fmt.Sprintf("WriteType %s but Config is not WriteFileConfig", exectypes.WriteTypeCreate)
		logger.Errorf(ctx, msg)
		return nil, fmt.Errorf(msg)
	}

	writeFileContents, err := c.writeFile(ctx, config)
	if err != nil {
		logger.Errorf(ctx, "Write file failed: %s", err)
		return nil, err
	}
	return writeFileContents, nil
}

func (c *OOSClient) Mkdir(ctx context.Context, dir *exectypes.Dir) error {
	return errors.New("function not implemented")
}

func (c *OOSClient) Chmod(ctx context.Context, mod, path string) error {
	return errors.New("function not implemented")
}

func (c *OOSClient) Chown(ctx context.Context, owner, path string) error {
	return errors.New("function not implemented")
}

// 通过执行结果 stderr 判断是否执行成功
func (c *OOSClient) Exists(ctx context.Context, path string) bool {
	var stderr string
	var err error
	if _, stderr, err = c.Exec(ctx, &exectypes.Command{
		Interval: -1,
		Command:  fmt.Sprintf("ls %s", path),
	}); err != nil {
		logger.Errorf(ctx, "exec ls %s failed: %s, stderr: %s", path, err, stderr)
	}
	if stderr != "" {
		return false
	}

	return true
}

func (c *OOSClient) Exec(ctx context.Context, command *exectypes.Command) (stdout, stderr string, err error) {
	script, _, err := c.GeneratorScript(ctx, command)
	if err != nil {
		logger.Errorf(ctx, "generate script failed: %s", err)
		return "", "", err
	}
	if !strings.HasPrefix(script, "#!/bin/bash") {
		logger.Infof(ctx, "script: %s", script)
		script = fmt.Sprintf("#!/bin/bash\n%s", script)
	}

	// 调用 oos 实现脚本执行
	if stdout, stderr, err = c.ExecOOSTask(ctx, script, "execClientTask"); err != nil {
		logger.Errorf(ctx, "execClientTask failed: %s", err)
		return stdout, stderr, err
	}
	return stdout, stderr, nil
}

func (c *OOSClient) GeneratorScript(ctx context.Context, command *exectypes.Command) (string, string, error) {
	if command == nil {
		return "", "", errors.New("comamnd is nil")
	}

	if command.Command == "" {
		return "", "", errors.New("comamnd.Command is nil")
	}

	if command.Interval <= 0 {
		command.Interval = exectypes.DefaultInterval
	}

	if command.Retry <= 0 {
		command.Retry = exectypes.DefaultRetry
	}
	returnTpl := ""
	if command.Retry == 1 {
		if command.IgnoreErrors {
			returnTpl = command.Command
		} else {
			returnTpl = fmt.Sprintf(`%s
if [ $? -ne 0 ]; then
  echo "exec %s failed" >&2
  exit 1
fi
`, command.Command, command.Command)
		}
	} else {
		if command.IgnoreErrors {
			returnTpl = fmt.Sprintf(`maxRetryCount=%d
for ((i=0; i<maxRetryCount; i++)); do
  %s
  if [ $? -ne 0 ]; then
    echo "exec %s failed" >&2
    if [ $i -ne $((maxRetryCount - 1)) ]; then
      sleep %d
    fi
  else
    break
  fi
done
`, command.Retry, command.Command, command.Command, command.Interval)
		} else {
			returnTpl = fmt.Sprintf(`maxRetryCount=%d
tempFile=$(mktemp)
for ((i=0; i<maxRetryCount; i++)); do
  %s 2>$tempFile
  if [ $? -ne 0 ]; then
    if [ $i -ne $((maxRetryCount - 1)) ]; then
	  sleep %d
    else
      echo "exec %s failed" >&2
	  cat $tempFile >&2
	  rm $tempFile
	  exit 1
    fi
  else
    rm $tempFile
    break
  fi
done
`, command.Retry, command.Command, command.Interval, command.Command)
		}
	}

	return returnTpl, "", nil
}

// MD5 - 获取 reader 的 MD5 值
func (c *OOSClient) MD5(ctx context.Context, path string) (string, error) {
	return "", errors.New("function not implemented")
}

func (c *OOSClient) MD5Equal(ctx context.Context, srcPath, dstPath string) (bool, error) {
	return false, errors.New("function not implemented")
}

// writeFile - 写文件, 并保证前置目录存在, 如果文件已经存在会覆盖原有内容
func (c *OOSClient) writeFile(ctx context.Context, config *exectypes.WriteFileConfig) ([]string, error) {
	if config == nil {
		return nil, errors.New("config is nil")
	}

	if config.Content == nil {
		return nil, errors.New("config.Content is nil")
	}

	if config.Name == "" {
		return nil, errors.New("config.Name is empty")
	}

	if config.Dir == nil {
		return nil, errors.New("config.Dir is empty")
	}

	scripts := []string{}

	// 文件内容
	content := *(config.Content)
	if content == "" {
		return nil, errors.New("config.Content is empty")
	}

	// 保证目录存在
	// scripts = append(scripts, "set -e")
	ensureDir := fmt.Sprintf("[ -d %s ] || mkdir -p %s", config.Dir.Path, config.Dir.Path)
	scripts = append(scripts, ensureDir)
	// 写入文件
	filePath := config.Dir.Path
	if strings.HasSuffix(config.Dir.Path, "/") {
		filePath = fmt.Sprintf("%s%s", filePath, config.Name)
	} else {
		filePath = fmt.Sprintf("%s/%s", filePath, config.Name)
	}
	// writeFile := fmt.Sprintf("echo %s > %s", *config.Content, filePath)
	writeFile := fmt.Sprintf("cat>%s<<'INNER_EOF'", filePath)
	scripts = append(scripts, writeFile)
	scripts = append(scripts, *config.Content)
	scripts = append(scripts, "INNER_EOF")

	// 设置属主
	if config.Owner != "" {
		chown := fmt.Sprintf("chown %s:%s %s", config.Owner, config.Owner, filePath)
		scripts = append(scripts, chown)
	}

	// 设置权限
	if config.Mod != "" {
		chown := fmt.Sprintf("chown %s:%s %s", config.Mod, config.Mod, filePath)
		scripts = append(scripts, chown)
	}

	return scripts, nil
}

func (c *OOSClient) CheckOOSClientOnline(ctx context.Context) (online bool, err error) {
	if c.instanceDeployConfig == nil {
		return false, errors.New("instanceDeployConfig is nil")
	}
	online, err = c.oosClient.CheckOOSClientOnline(ctx,
		c.instanceDeployConfig.InstanceUUID,
		c.instanceDeployConfig.MachineID,
		c.stsClient.NewSignOption(ctx, c.instanceDeployConfig.AccountID))
	if err != nil {
		logger.Errorf(ctx, "check oos client online failed, instanceUUID: %s, MachineID: %s,err: %v", c.instanceDeployConfig.MachineID, c.instanceDeployConfig.InstanceUUID, err)
		return
	}
	return
}

func (c *OOSClient) ExecOOSTask(ctx context.Context, scripts string, OOSTaskName string) (stdout, stderr string, err error) {
	if c.instanceDeployConfig == nil {
		return "", "", errors.New("instanceDeployConfig is nil")
	}
	stdout, stderr, err = c.oosClient.ExecOOSTask(ctx, scripts, OOSTaskName, c.instanceDeployConfig, c.stsClient.NewSignOption(ctx, c.instanceDeployConfig.AccountID))
	if err != nil {
		logger.Errorf(ctx, "ExecOOSTask failed, err: %v", err)
		return stdout, stderr, err
	}
	return stdout, stderr, nil
}
