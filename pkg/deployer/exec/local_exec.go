// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2020/04/28 18:40:00, by <EMAIL>, create
*/
/*
DESCRIPTION
本地 local run 实现 exec.Interface
*/

package exec

import (
	"bytes"
	"context"
	"crypto/md5"
	"encoding/hex"
	"errors"
	"fmt"
	"io"
	"io/ioutil"
	"os"
	osexec "os/exec"
	"path"
	"strings"
	"time"

	exectypes "icode.baidu.com/baidu/cce-test/e2e-test/pkg/deployer/exec/types"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	ccetypes "icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
)

var _ Interface = &LocalClient{}

type LocalClient struct {
}

func NewLocalClient(ctx context.Context) (Interface, ccetypes.InstanceDeployType, error) {
	return &LocalClient{}, ccetypes.DeployInstanceByLocal, nil
}

// Read - 本地读取文件
//
// PARAMS:
//   - path: 文件路径
//
// RETURNS:
//
//	[]byte: 文件内容
//	error: nil if succeed, error if fail
func (c *LocalClient) Read(ctx context.Context, path string) ([]byte, error) {
	if path == "" {
		return nil, errors.New("path is empty")
	}

	var content []byte

	// 检查是否存在
	if exist := c.Exists(ctx, path); !exist {
		logger.Errorf(ctx, "File %s not exist", path)
		return content, fmt.Errorf("file %s not exist in remote", path)
	}

	// 读取文件
	file, err := os.Open(path)
	if err != nil {
		return content, err
	}
	defer file.Close()

	content, err = io.ReadAll(file)
	if err != nil {
		return content, err
	}

	return content, nil
}

// ReadDir - 读取文件夹内容
//
// PARAMS:
//   - path: 文件夹路径
//
// RETURNS:
//
//	[]os.FileInfo: 文件信息
//	error: nil if succeed, error if fail
func (c *LocalClient) ReadDir(ctx context.Context, path string) ([]os.FileInfo, error) {
	if path == "" {
		return nil, errors.New("path is empty")
	}

	// 检查是否存在
	if exist := c.Exists(ctx, path); !exist {
		logger.Errorf(ctx, "File %s not exist in remote", path)
		return nil, fmt.Errorf("File %s not exist in remote", path)
	}

	files, err := ioutil.ReadDir(path)
	if err != nil {
		logger.Errorf(ctx, "read dir failed: %s", err)
		return nil, err
	}

	for index, file := range files {
		fmt.Printf("file:%d, name: %s\n", index, file.Name())
	}

	return files, nil
}

// Write - 写文件
//
// PARAMS:
//   - file: *File
//
// RETURNS:
//
//	error: nil if succeed, error if fail
func (c *LocalClient) Write(ctx context.Context, file *exectypes.File) error {
	if file == nil {
		return errors.New("file is nil")
	}

	// 写文件
	if file.WriteType == exectypes.WriteTypeCreate {
		config, ok := file.Config.(*exectypes.WriteFileConfig)
		if !ok {
			msg := fmt.Sprintf("WriteType %s but Config is not WriteFileConfig", exectypes.WriteTypeCreate)
			logger.Errorf(ctx, msg)
			return fmt.Errorf(msg)
		}

		if err := c.writeFile(ctx, config); err != nil {
			logger.Errorf(ctx, "Write file failed: %s", err)
			return err
		}
		return nil
	}

	// 复制文件
	if file.WriteType == exectypes.WriteTypeCopy {
		config, ok := file.Config.(*exectypes.CopyFileConfig)
		if !ok {
			msg := fmt.Sprintf("WriteType %s but Config is not CopyFileConfig", exectypes.WriteTypeCopy)
			logger.Errorf(ctx, msg)
			return fmt.Errorf(msg)
		}

		if err := c.copyFile(ctx, config); err != nil {
			logger.Errorf(ctx, "CopyFile failed: %s", err)
			return err
		}

		return nil
	}

	// 追加文件
	if file.WriteType == exectypes.WriteTypeAppend {
		config, ok := file.Config.(*exectypes.AppendFileConfig)
		if !ok {
			msg := fmt.Sprintf("WriteType %s but Config is not AppendFileConfig", exectypes.WriteTypeAppend)
			logger.Errorf(ctx, msg)
			return fmt.Errorf(msg)
		}

		if err := c.appendFile(ctx, config); err != nil {
			logger.Errorf(ctx, "appendFile failed: %s", err)
			return err
		}

		return nil
	}

	// 替换配置
	if file.WriteType == exectypes.WriteTypeReplaceContent {
		config, ok := file.Config.(*exectypes.ReplaceFileContentConfig)
		if !ok {
			msg := fmt.Sprintf("WriteType %s but Config is not WriteTypeReplace", exectypes.WriteTypeReplaceContent)
			logger.Errorf(ctx, msg)
			return fmt.Errorf(msg)
		}

		if err := c.replaceFileContent(ctx, config); err != nil {
			logger.Errorf(ctx, "replaceFile failed: %s", err)
			return err
		}

		return nil
	}

	return fmt.Errorf("unknown type %s", file.WriteType)
}

// Mkdir - 创建目录
//
// PARAMS:
//   - dir: string
//
// RETURNS:
//
//	error: nil if succeed, error if fail
func (c *LocalClient) Mkdir(ctx context.Context, dir *exectypes.Dir) error {
	if dir == nil {
		return errors.New("dir is nil")
	}

	if dir.Path == "" {
		return errors.New("dir.Path is nil")
	}

	// 检查目录是否存在
	if c.Exists(ctx, dir.Path) {
		logger.Infof(ctx, "Dir %s exists, skip mkdir", dir.Path)
		return nil
	}

	// 创建目录
	command := fmt.Sprintf("mkdir -p %s", dir.Path)
	logger.Infof(ctx, "Mkdir command: %s", command)

	if _, stderr, err := c.Exec(ctx, &exectypes.Command{
		Command: command,

		Retry:    exectypes.DefaultRetry,
		Interval: exectypes.DefaultInterval,
		Timeout:  exectypes.DefaultTimeout,
	}); err != nil {
		logger.Errorf(ctx, "Exec '%s' failed: stderr=%s err=%s", command, stderr, err)
		return err
	}

	// 设置属主
	if dir.Owner != "" {
		if err := c.Chown(ctx, dir.Owner, dir.Path); err != nil {
			logger.Errorf(ctx, "Chown failed: %s", err)
			return err
		}
	}

	// 设置权限
	if dir.Mod != "" {
		if err := c.Chmod(ctx, dir.Mod, dir.Path); err != nil {
			logger.Errorf(ctx, "Chmod failed: %s", err)
			return err
		}
	}

	return nil
}

// Chmod - 设置权限
//
// PARAMS:
//   - mod: 权限
//   - path: string
//
// RETURNS:
//
//	error: nil if succeed, error if fail
func (c *LocalClient) Chmod(ctx context.Context, mod, path string) error {
	if path == "" {
		return errors.New("path is empty")
	}

	if mod == "" {
		return errors.New("mod is empty")
	}

	command := fmt.Sprintf("chmod %s %s", mod, path)
	logger.Infof(ctx, "Chmod command: %s", command)

	if _, stderr, err := c.Exec(ctx, &exectypes.Command{
		Command: command,

		Retry:    exectypes.DefaultRetry,
		Interval: exectypes.DefaultInterval,
		Timeout:  exectypes.DefaultTimeout,
	}); err != nil {
		logger.Errorf(ctx, "Exec '%s' failed: stderr=%s err=%s", command, stderr, err)
		return err
	}

	return nil
}

// Chown - 设置属主
//
// PARAMS:
//   - mod: 权限
//   - path: string
//
// RETURNS:
//
//	error: nil if succeed, error if fail
func (c *LocalClient) Chown(ctx context.Context, owner, path string) error {
	if owner == "" {
		return errors.New("owner is empty")
	}

	if path == "" {
		return errors.New("path is empty")
	}

	command := fmt.Sprintf("chown %s:%s %s", owner, owner, path)
	logger.Infof(ctx, "Chown command: %s", command)

	if _, stderr, err := c.Exec(ctx, &exectypes.Command{
		Command: command,

		Retry:    exectypes.DefaultRetry,
		Interval: exectypes.DefaultInterval,
		Timeout:  exectypes.DefaultTimeout,
	}); err != nil {
		logger.Errorf(ctx, "Exec '%s' failed: stderr=%s err=%s", command, stderr, err)
		return err
	}

	return nil
}

// Exists - 检查路径是否存在
//
// PARAMS:
//   - path: string
//
// RETURNS:
//
//	bool: true = 路径存在
func (c *LocalClient) Exists(ctx context.Context, path string) bool {
	_, err := os.Stat(path)
	if err != nil {
		if os.IsExist(err) {
			return true
		}
		return false
	}
	return true
}

// Exec - 执行命令
//
// PARAMS:
//   - command: *Command, 参数含义如下
//     Command: 执行命令
//     Retry: 重试次数
//     Interval: 每次重试间隔
//     Timeout: Exec 整体超时时间
//     IgnoreErrors: 是否忽略报错
//
// RETURNS:
//
//	string: stdout 输出
//	string: stderr 输出
//	error: nil if succeed, error if fail
func (c *LocalClient) Exec(ctx context.Context, command *exectypes.Command) (string, string, error) {
	if command == nil {
		return "", "", errors.New("comamnd is nil")
	}

	if command.Command == "" {
		return "", "", errors.New("comamnd.Command is nil")
	}

	if command.Timeout <= 0 {
		command.Timeout = exectypes.DefaultTimeout
	}

	if command.Interval <= 0 {
		command.Interval = exectypes.DefaultInterval
	}

	if command.Retry <= 0 {
		command.Retry = exectypes.DefaultRetry
	}

	errChan := make(chan error, 1)
	timeout := time.NewTimer(command.Timeout * time.Second)

	// 执行命令
	var cmdError error
	var stdout bytes.Buffer
	var stderr bytes.Buffer
	for i := 0; i < command.Retry; i++ {
		go func(ctx context.Context) {
			cmd := osexec.CommandContext(ctx, "/bin/bash", "-c", command.Command)

			// reset buffer
			stdout.Reset()
			stderr.Reset()

			cmd.Stdout = &stdout
			cmd.Stderr = &stderr

			logger.Infof(ctx, "Exec '%s' begin", command.Command)

			// 执行命令
			if err := cmd.Run(); err != nil {
				logger.Errorf(ctx, "Run '%s' failed, stderr: %s, err: %s", command.Command, Trim(stderr.String()), err)
				errChan <- err
				return
			}

			errChan <- nil
		}(ctx)

		select {
		case cmdError = <-errChan:
			if cmdError == nil {
				logger.Infof(ctx, "Exec [%s] success: stdout=[%s] stderr=[%s]",
					command.Command, Trim(stdout.String()), Trim(stderr.String()))
				return Trim(stdout.String()), Trim(stderr.String()), nil
			}

			if command.IgnoreErrors {
				logger.Infof(ctx, "Ignore errors: Exec '%s' failed, stderr: %s",
					command.Command, Trim(stderr.String()))
				return Trim(stdout.String()), Trim(stderr.String()), nil
			}
		case <-timeout.C:
			logger.Errorf(ctx, "Exec '%s' timeout", command.Command)
			return "", "ExecutionTimeout", errors.New("ExecutionTimeout")
		}

		time.Sleep(command.Interval * time.Second)
	}

	// TODO: 可能出现 err == nil 但 stderr != "" 情况
	return Trim(stdout.String()), Trim(stderr.String()), cmdError
}

// MD5 - 获取 reader 的 MD5 值
func (c *LocalClient) MD5(ctx context.Context, path string) (string, error) {
	if path == "" {
		return "", errors.New("path is empty")
	}

	file, err := os.Open(path)
	if err != nil {
		logger.Errorf(ctx, "Open %s failed: %s", path, err)
		return "", err
	}
	defer file.Close()

	md5 := md5.New()
	_, err = io.Copy(md5, file)
	if err != nil {
		logger.Errorf(ctx, "io.Copy failed: %s", err)
		return "", err
	}

	fileMD5 := hex.EncodeToString(md5.Sum(nil))

	return fileMD5, nil
}

// MD5Equal - 检查文件 md5 是否一致
//
// PARAMS:
//   - sourcePath: 源文件路径
//   - dstPath: 目标文件路径
//
// RETURNS:
//
//	bool: 是否相等
//	error: nil if succeed, error if fail
func (c *LocalClient) MD5Equal(ctx context.Context, srcPath, dstPath string) (bool, error) {
	if srcPath == "" {
		return false, errors.New("sourcePath is empty")
	}

	if dstPath == "" {
		return false, errors.New("dstPath is empty")
	}

	// src MD5
	srcMD5, err := c.MD5(ctx, srcPath)
	if err != nil {
		logger.Errorf(ctx, "MD5 failed: %s", err)
		return false, err
	}
	logger.Infof(ctx, "srcMD5: %s", srcMD5)

	// dst MD5
	dstMD5, err := c.MD5(ctx, dstPath)
	if err != nil {
		logger.Errorf(ctx, "MD5 failed: %s", err)
		return false, err
	}
	logger.Infof(ctx, "dstMD5: %s", dstMD5)

	return srcMD5 == dstMD5, nil
}

// writeFile - 写文件, 并保证前置目录存在, 如果文件已经存在会覆盖原有内容
func (c *LocalClient) writeFile(ctx context.Context, config *exectypes.WriteFileConfig) error {
	if config == nil {
		return errors.New("config is nil")
	}

	if config.Content == nil {
		return errors.New("config.Content is nil")
	}

	if config.Name == "" {
		return errors.New("config.Name is empty")
	}

	if config.Dir == nil {
		return errors.New("config.Dir is empty")
	}

	// 文件内容
	content := *(config.Content)
	if content == "" {
		return errors.New("config.Content is empty")
	}

	// 保证目录存在
	if err := c.Mkdir(ctx, config.Dir); err != nil {
		logger.Errorf(ctx, "Mkdir %s failed: %s", config.Dir.Path, err)
		return err
	}

	filePath := fmt.Sprintf("%s/%s", config.Dir.Path, config.Name)

	// 写文件, 属主默认为当前用户(root)、权限为 644
	if err := os.WriteFile(filePath, []byte(*config.Content), 0644); err != nil {
		return err
	}

	// 设置属主
	if config.Owner != "" {
		if err := c.Chown(ctx, config.Owner, filePath); err != nil {
			logger.Errorf(ctx, "Chown failed: %s", err)
			return err
		}
	}

	// 设置权限
	if config.Mod != "" {
		if err := c.Chown(ctx, config.Mod, filePath); err != nil {
			logger.Errorf(ctx, "Chown failed: %s", err)
			return err
		}
	}

	return nil
}

// Copy - 复制文件
func (c *LocalClient) copyFile(ctx context.Context, config *exectypes.CopyFileConfig) error {
	if config == nil {
		return errors.New("file is nil")
	}

	if config.Dir == nil {
		return errors.New("file.Dir is nil")
	}

	if config.SourceFilePath == "" {
		return errors.New("file.SourceFilePath is nil")
	}

	if config.Dir.Path == "" {
		return errors.New("file.Dir.Path is empty")
	}

	// Name 不允许带路径
	if config.Name == "" {
		config.Name = path.Base(config.SourceFilePath)
	} else {
		config.Name = path.Base(config.Name)
	}

	// 目的端 目录存在
	if err := c.Mkdir(ctx, config.Dir); err != nil {
		logger.Errorf(ctx, "Mkdir %s failed: %s", config.Dir.Path, err)
		return err
	}

	// 目的端 Path
	dstPath := path.Join(config.Dir.Path, config.Name)

	// 对比 MD5 是否相等
	equal, err := c.MD5Equal(ctx, config.SourceFilePath, dstPath)
	if err != nil {
		logger.Errorf(ctx, "MD5Equal failed: %s", err)
		return err
	}

	if equal {
		logger.Infof(ctx, "Source and Destination MD5 euqal, skip cp")
		return nil
	}

	// 复制内容
	start := time.Now()
	srcContent, err := c.Read(ctx, config.SourceFilePath)
	if err != nil {
		logger.Errorf(ctx, "Read SourceFilePath failed: %s", err)
		return err
	}
	if err := os.WriteFile(dstPath, srcContent, 0644); err != nil {
		logger.Errorf(ctx, "Write dstFilePath failed: %s", err)
	}

	logger.Infof(ctx, "Copy '%s' success, costs in seconds: %.2f", config.Name, time.Since(start).Seconds())

	return nil
}

func (c *LocalClient) GetWriteContents(ctx context.Context, file *exectypes.File) ([]string, error) {
	return nil, nil
}

func (c *LocalClient) appendFile(ctx context.Context, config *exectypes.AppendFileConfig) error {
	if config == nil {
		return errors.New("config is nil")
	}

	if len(config.Contents) == 0 {
		return errors.New("config.Contents len is 0")
	}

	if config.Name == "" {
		return errors.New("config.Name is empty")
	}

	if config.Dir == nil {
		return errors.New("config.Dir is empty")
	}

	// 文件 Path
	srcPath := path.Join(config.Dir.Path, config.Name)

	// read File
	fileContent, err := c.Read(ctx, srcPath)
	if err != nil {
		return fmt.Errorf("read file failed, path: %s, err: %s", srcPath, err)
	}

	// check market
	fileStr := string(fileContent[:])
	if strings.Contains(fileStr, config.Marker) {
		logger.Infof(ctx, "marker exists, do not need to append")
		return nil
	}

	// append content
	appendContent := config.Marker + " Start" + "\n"
	for _, content := range config.Contents {
		appendContent = appendContent + content + "\n"
	}
	appendContent = appendContent + config.Marker + " End\n"

	// 检查是否存在
	if exist := c.Exists(ctx, srcPath); !exist {
		logger.Errorf(ctx, "File %s not exist", srcPath)
		return fmt.Errorf("file %s not exist in remote", srcPath)
	}

	// 读取文件
	file, err := os.OpenFile(srcPath, os.O_APPEND|os.O_RDWR, os.ModeAppend)
	if err != nil {
		return err
	}
	defer file.Close()

	// 追加内容
	_, err = file.WriteString(appendContent)
	if err != nil {
		return err
	}

	return nil
}

func (c *LocalClient) replaceFileContent(ctx context.Context, config *exectypes.ReplaceFileContentConfig) error {
	return nil
}

func (c *LocalClient) CheckOOSClientOnline(ctx context.Context) (online bool, err error) {
	return false, errors.New("localClient not support CheckOOSClientOnline")
}

func (c *LocalClient) ExecOOSTask(ctx context.Context, scripts string, OOSTaskName string) (stdout, stderr string, err error) {
	return "", "", errors.New("localClient not support ExecOOSTask")
}

func (c *LocalClient) GeneratorScript(ctx context.Context, command *exectypes.Command) (string, string, error) {
	logger.Errorf(ctx, "localClient not support GeneratorScript")
	return "", "", errors.New("localClient not support GeneratorScript")
}
