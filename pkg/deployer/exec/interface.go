// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2020/04/21 18:40:00, by <EMAIL>, create
*/
/*
DESCRIPTION
远程执行器接口定义
*/

package exec

import (
	"context"
	"errors"
	"os"

	exectypes "icode.baidu.com/baidu/cce-test/e2e-test/pkg/deployer/exec/types"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/deployer/types"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	ccetypes "icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
)

// Interface - 定义部署远程执行接口
type Interface interface {
	Read(ctx context.Context, path string) ([]byte, error)
	ReadDir(ctx context.Context, path string) ([]os.FileInfo, error)
	Write(ctx context.Context, file *exectypes.File) error
	Mkdir(ctx context.Context, dir *exectypes.Dir) error
	Chmod(ctx context.Context, mod, path string) error
	Chown(ctx context.Context, owner, path string) error
	Exists(ctx context.Context, path string) bool
	Exec(ctx context.Context, command *exectypes.Command) (string, string, error)

	MD5(ctx context.Context, path string) (string, error)
	MD5Equal(ctx context.Context, srcPath, dstPath string) (bool, error)
	GetWriteContents(ctx context.Context, file *exectypes.File) ([]string, error)

	// oos 专属方法
	CheckOOSClientOnline(ctx context.Context) (online bool, err error)

	GeneratorScript(ctx context.Context, command *exectypes.Command) (string, string, error)
	ExecOOSTask(ctx context.Context, scripts, OOSTaskName string) (stdout, stderr string, err error)
}

func NewExecClient(ctx context.Context, instance *types.Instance, config *types.Config) (Interface, ccetypes.InstanceDeployType, error) {
	// config == nil 暂时不判断，因为 ssh client 初始化不依赖 config
	if instance == nil {
		logger.Errorf(ctx, "instanceDeployConfig is nil")
		return nil, "", errors.New("instanceDeployConfig is nil, NewExecClient failed")
	}

	if instance.DeployType == ccetypes.DeployInstanceBySSH {
		return NewSSHClient(ctx, instance)
	}

	if instance.DeployType == ccetypes.DeployInstanceByOOS {
		// bbc 机器暂时不支持 bsm agent，因此该机器的部署方式为 ssh 部署
		// 集群中有 bbc 机器，部署时多次尝试检测 agent 是否 online，会增加装机时间；因此，检测到 bbc 机器，直接用 ssh 部署
		if instance.Machinetype == ccetypes.MachineTypeBBC {
			logger.Infof(ctx, "instance machineType:%s is bbc machine, fallback to ssh deploy", instance.CCEInstanceID)
			return NewSSHClient(ctx, instance)
		}
		if instance.MasterType == ccetypes.MasterTypeManaged {
			logger.Infof(ctx, "instance MasterType:%s is managedOld, fallback to ssh deploy", instance.CCEInstanceID)
			return NewSSHClient(ctx, instance)
		}

		oos, _, err := NewOOSClient(ctx, instance, config)
		if err != nil {
			logger.Errorf(ctx, "new oos client failed, %s", err)
			return nil, "", err
		}
		_, err = oos.CheckOOSClientOnline(ctx)
		if err != nil {
			logger.Errorf(ctx, "bsm agent is not online, fallback to ssh deploy, error: %s", err)
			return NewSSHClient(ctx, instance)
		}
		return oos, ccetypes.DeployInstanceByOOS, nil
	}

	if instance.Local {
		return NewLocalClient(ctx)
	}

	// 最终 ssh client 兜底
	return NewSSHClient(ctx, instance)
}
