// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2020/04/21 15:50:00, by ch<PERSON><EMAIL>, create
*/
/*
DESCRIPTION
CCE K8S Deployer 公共方法等定义, 尽可能实现三者部署统一:
1. cce-stack;
2. 天合 stack;
3. caas-manager;

deployer.Interface 定义 K8S Cluster 部署方法

设计参考: http://wiki.baidu.com/pages/viewpage.action?pageId=**********
*/

package deployer

import (
	"context"
	"errors"
	"path"

	ccev1 "icode.baidu.com/baidu/cce-test/e2e-test/pkg/crd/api/v1"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/deployer/types"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/deployer/workflow"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	ccetypes "icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/utils"
)

const (
	// SSHPort - 部署默认 SSHPort
	SSHPort = 22

	// RunUser - 部署默认 User
	RunUser = "root"

	// DefaultDataPath - 默认数据目录
	DefaultDataPath = "/home/<USER>"

	// DefaultKubeletPath - 默认 kubelet 数据目录
	DefaultKubeletPath = "/var/lib"

	// kubeProxyNameSpace - kube-proxy daemonset config
	kubeProxyNameSpace = "kube-system"

	// kubeProxyDSName - kube-proxy daemonset config
	kubeProxyDSName = "kube-proxy"
)

// Interface - 定义 Deployer 部署和删除一批节点流程
type Interface interface {
	Reconcile(ctx context.Context, cluster *types.Cluster, instance *types.Instance) (map[ccetypes.StepName]ccetypes.Step, error)
	ReconcileDelete(ctx context.Context, cluster *types.Cluster, instance *types.Instance) (map[ccetypes.StepName]ccetypes.Step, error)

	// ReconcileUpgrade - 升级变更操作指定 WorkflowRoleType
	ReconcileUpgrade(ctx context.Context, cluster *types.Cluster, instance *types.Instance, workflowRoleType workflow.WorkflowRoleType) (map[ccetypes.StepName]ccetypes.Step, error)

	// ReconcilePostUserScript - 执行用户自定义脚本
	ReconcilePostUserScript(ctx context.Context, cluster *types.Cluster, instance *types.Instance) error
}

// DecryptAdminPassword 解密: 密码明文长度范围是 8-32，对应的密文的长度范围是 44-88，所以如果 adminPassword 长度 >= 44，可以判定被加密过
func DecryptAdminPassword(password string) (string, error) {
	var err error
	if len(password) >= 44 {
		password, err = utils.AESCFBDecrypt(password)
		if err != nil {
			return "", err
		}
	}
	return password, nil
}

func DeployType(cluster *ccev1.Cluster, instance *ccev1.Instance) (instanceDeployType ccetypes.InstanceDeployType) {
	instanceDeployType = ccetypes.DeployInstanceByOOS

	if instance == nil {
		return ccetypes.DeployInstanceByOOS
	}

	// cce 后端配置的 oos 装机方式
	if instance.Spec.InstanceDeployType == ccetypes.DeployInstanceBySSH {
		instanceDeployType = ccetypes.DeployInstanceBySSH
	} else if instance.Spec.InstanceDeployType == ccetypes.DeployInstanceByOOS {
		instanceDeployType = ccetypes.DeployInstanceByOOS
	} else {
		instanceDeployType = ccetypes.DeployInstanceBySSH
	}

	if cluster != nil && cluster.Annotations != nil {
		status, ok := cluster.Annotations[ccev1.AnnotationInstanceDeployType]
		if ok && ccetypes.InstanceDeployType(status) == ccetypes.DeployInstanceBySSH {
			instanceDeployType = ccetypes.DeployInstanceBySSH
		} else if ok && ccetypes.InstanceDeployType(status) == ccetypes.DeployInstanceByOOS {
			instanceDeployType = ccetypes.DeployInstanceByOOS
		}
	}

	if instance.Spec.Annotations != nil {
		status, ok := instance.Spec.Annotations[ccev1.AnnotationInstanceDeployType]
		if ok && ccetypes.InstanceDeployType(status) == ccetypes.DeployInstanceBySSH {
			instanceDeployType = ccetypes.DeployInstanceBySSH
		} else if ok && ccetypes.InstanceDeployType(status) == ccetypes.DeployInstanceByOOS {
			instanceDeployType = ccetypes.DeployInstanceByOOS
		}
	}

	// instance.Annotations 保留，cce介入时，优先级最高
	if instance.Annotations != nil {
		status, ok := instance.Annotations[ccev1.AnnotationInstanceDeployType]
		if ok && ccetypes.InstanceDeployType(status) == ccetypes.DeployInstanceBySSH {
			instanceDeployType = ccetypes.DeployInstanceBySSH
		} else if ok && ccetypes.InstanceDeployType(status) == ccetypes.DeployInstanceByOOS {
			instanceDeployType = ccetypes.DeployInstanceByOOS
		}
	}
	return
}

// ClusterPostUserScriptConfig - 将 CRD Cluster/Instance 转成执行后置脚本依赖 Cluster 配置
func ClusterPostUserScriptConfig(ctx context.Context, cluster *ccev1.Cluster, instance *ccev1.Instance) (*types.Cluster, error) {
	if cluster == nil {
		return nil, errors.New("cluster is nil")
	}

	if instance == nil {
		return nil, errors.New("instance is nil")
	}

	clusterMasterVIP := cluster.Status.ClusterBLB.VPCIP
	if cluster.Spec.ClusterType == ccetypes.ClusterTypeCloudEdge {
		// 云边集群 master VIP 使用 BLB EIP
		if cluster.Status.ClusterBLB.EIP == "" {
			logger.Errorf(ctx, "node instance: %s machine type: %s, master type: %s, cluster lb eip is not set",
				instance.Status.Machine.InstanceID, instance.Spec.MachineType, cluster.Spec.MasterConfig.MasterType)
			return nil, errors.New("cluster lb eip is not set for BEC node")
		}
		clusterMasterVIP = cluster.Status.ClusterBLB.EIP
	}

	clusterMasterVPCIP := cluster.Status.ClusterBLB.VPCIP

	if r, ok := instance.Spec.Annotations[ccev1.AnnotationScaleControlPlane]; ok && r == ccev1.AnnotationTrue {
		cluster.Status.APIServerWhiteList = append(cluster.Status.APIServerWhiteList, instance.Status.Machine.FloatingIP)
		cluster.Status.APIServerWhiteList = append(cluster.Status.APIServerWhiteList, instance.Status.Machine.VPCIP)
	}

	return &types.Cluster{
		AccountID:                       cluster.Spec.AccountID,
		UserID:                          cluster.Spec.UserID,
		ClusterID:                       cluster.Name,
		ClusterName:                     cluster.Spec.ClusterName,
		ClusterType:                     cluster.Spec.ClusterType,
		MasterType:                      cluster.Spec.MasterConfig.MasterType,
		RuntimeType:                     cluster.Spec.RuntimeType,
		RuntimeVersion:                  cluster.Spec.RuntimeVersion,
		ClusterMasterVIP:                clusterMasterVIP,
		ClusterMasterVPCIP:              clusterMasterVPCIP,
		APIServerWhiteList:              cluster.Status.APIServerWhiteList,
		ServiceNodePortMin:              cluster.Spec.ContainerNetworkConfig.NodePortRangeMin,
		ServiceNodePortMax:              cluster.Spec.ContainerNetworkConfig.NodePortRangeMax,
		KubeProxyModel:                  cluster.Spec.ContainerNetworkConfig.KubeProxyMode,
		ContainerNetworkMode:            cluster.Spec.ContainerNetworkConfig.Mode,
		PauseImage:                      cluster.Spec.K8SCustomConfig.PauseImage,
		KubeAPIQPS:                      cluster.Spec.K8SCustomConfig.KubeAPIQPS,
		KubeAPIBurst:                    cluster.Spec.K8SCustomConfig.KubeAPIBurst,
		AdmissionPlugins:                cluster.Spec.K8SCustomConfig.AdmissionPlugins,
		MasterFeatureGates:              cluster.Spec.K8SCustomConfig.MasterFeatureGates,
		NodeFeatureGates:                cluster.Spec.K8SCustomConfig.NodeFeatureGates,
		SchedulerPredicates:             cluster.Spec.K8SCustomConfig.SchedulerPredicates,
		SchedulerPriorities:             cluster.Spec.K8SCustomConfig.SchedulerPriorities,
		EnableLBServiceController:       cluster.Spec.K8SCustomConfig.EnableLBServiceController,
		EnableCloudNodeController:       cluster.Spec.K8SCustomConfig.EnableCloudNodeController,
		EnableDefaultPluginDeployByHelm: cluster.Spec.K8SCustomConfig.EnableDefaultPluginDeployByHelm,
		EnableEdgeHub:                   cluster.Spec.K8SCustomConfig.EnableEdgeHub,
		DisableKubeletReadOnlyPort:      cluster.Spec.K8SCustomConfig.DisableKubeletReadOnlyPort,
		InsecureRegistries:              cluster.Spec.K8SCustomConfig.InsecureRegistries,
		AuthenticateMode:                cluster.Spec.AuthenticateMode,
		EnableHostname:                  cluster.Spec.K8SCustomConfig.EnableHostname,
		K8SCustomConfig:                 cluster.Spec.K8SCustomConfig,
	}, nil
}

// GetKubeletDataPath - 获取 kubelet 数据目录
//
// PARAMS:
//   - cdsDataPath: cds 数据目录
//   - customKubeletRootDir: 用户自定义数据目录
//
// RETURNS:
//
//	string: kubelet 数据目录
func GetKubeletDataPath(customKubeletRootDir string) string {
	kubeletPath := path.Join(DefaultKubeletPath, "kubelet")

	if customKubeletRootDir != "" {
		kubeletPath = customKubeletRootDir
	}

	return kubeletPath
}
