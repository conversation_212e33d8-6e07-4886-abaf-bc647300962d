// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2020/04/21 15:50:00, by <EMAIL>, create
*/
/*
DESCRIPTION
定义初始化 deployer 依赖配置
*/

package types

import (
	"context"
	"errors"
	"time"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bcc"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/eip"
	exectypes "icode.baidu.com/baidu/cce-test/e2e-test/pkg/deployer/exec/types"
	ccetypes "icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
)

// Config - 定义 Deployer 依赖基础配置
type Config struct {
	Region string `json:"Region"`

	BLSAgentToken string `json:"BLSAgentToken"`

	// TODO: 后面cce service如果可以下线了，InternalEndpoint就不需要了
	InternalEndpoint string `json:"InternalEndpoint"`

	CCEV2Endpoint string `json:"CCEV2Endpoint"`
	KMSEndpoint   string `json:"KMSEndpoint"`
	CCEOIDCIssuer string `json:"CCEOIDCIssuer"`

	// CCE Gateway A区端点 通过Deployer部署的某些组件可能会需要该参数
	OpenCCEGatewayEndpoint string `json:"OpenCCEGatewayEndpoint"`

	// upload ca / config for scaleup control plane
	JPaaSAccessKeyID string `json:"JPaaSAccessKeyID"`

	JPaaSSecretAccessKey string `json:"JPaaSSecretAccessKey"`
	BackupBucket         string `json:"BackupBucket"`

	KubeBinPath                     map[ccetypes.K8SVersion]string `json:"KubeBinPath"`
	KubeAPIServer                   map[ccetypes.K8SVersion]string `json:"KubeAPIServer"`
	KubeControllerManager           map[ccetypes.K8SVersion]string `json:"KubeControllerManager"`
	KubeScheduler                   map[ccetypes.K8SVersion]string `json:"KubeScheduler"`
	ManagedProKubeAPIServer         map[ccetypes.K8SVersion]string `json:"ManagedProKubeAPIServer"`
	ManagedProKubeControllerManager map[ccetypes.K8SVersion]string `json:"ManagedProKubeControllerManager"`
	ManagedProKubeScheduler         map[ccetypes.K8SVersion]string `json:"ManagedProKubeScheduler"`

	KubeBinPath_1_11_1                                string `json:"KubeBinPath_1_11_1"`
	KubeBinPath_1_11_5                                string `json:"KubeBinPath_1_11_5"`
	KubeBinPath_1_13_4                                string `json:"KubeBinPath_1_13_4"`
	KubeBinPath_1_13_10                               string `json:"KubeBinPath_1_13_10"`
	KubeBinPath_1_14_9                                string `json:"KubeBinPath_1_14_9"`
	KubeBinPath_1_16_3                                string `json:"KubeBinPath_1_16_3"`
	KubeBinPath_1_16_8                                string `json:"KubeBinPath_1_16_8"`
	KubeBinPath_1_17_17                               string `json:"KubeBinPath_1_17_17"`
	KubeBinPath_1_18_9                                string `json:"KubeBinPath_1_18_9"`
	KubeBinPath_1_18_9_BilibiliMixprotocols           string `json:"KubeBinPath_1_18_9_BilibiliMixprotocols"`
	KubeBinPath_1_20_8                                string `json:"KubeBinPath_1_20_8"`
	KubeBinPath_1_20_8_containerd                     string `json:"KubeBinPath_1_20_8_containerd"`
	KubeBinPath_1_22_5_containerd                     string `json:"KubeBinPath_1_22_5_containerd"`
	KubeBinPath_1_24_4_containerd                     string `json:"KubeBinPath_1_24_4_containerd"`
	KubeBinPath_1_20_8_arm64                          string `json:"KubeBinPath_1_20_8_arm64"`
	KubeBinPath_1_21_14                               string `json:"KubeBinPath_1_21_14 "`
	KubeBinPath_1_22_5                                string `json:"KubeBinPath_1_22_5"`
	KubeBinPath_1_24_4                                string `json:"KubeBinPath_1_24_4"`
	KubeBinPath_1_26_9                                string `json:"KubeBinPath_1_26_9"`
	ContainerdBinPath_1_5_4                           string `json:"ContainerdBinPath_1_5_4"`
	ContainerdBinPath_1_6_8                           string `json:"ContainerdBinPath_1_6_8"`
	ContainerdBinPath_1_6_20                          string `json:"ContainerdBinPath_1_6_20"`
	ContainerdBinPath_1_6_24                          string `json:"ContainerdBinPath_1_6_24"`
	ContainerdBinPath_1_6_28                          string `json:"ContainerdBinPath_1_6_28"`
	ContainerdBinPath_1_7_13                          string `json:"ContainerdBinPath_1_7_13"`
	DockerBinPath_20_10_5                             string `json:"DockerBinPath_20_10_5"`
	DockerBinPath_20_10_24                            string `json:"DockerBinPath_20_10_24"`
	NvidiaContainerToolkitPath                        string `json:"NvidiaContainerToolkitPath"`
	InsecureRegistry                                  string `json:"InsecureRegistry"`
	PauseImage_2_0                                    string `json:"PauseImage_2_0"`
	PauseImage_3_1                                    string `json:"PauseImage_3_1"`
	PauseImage_3_2                                    string `json:"PauseImage_3_2"`
	PauseImage_3_1_arm64                              string `json:"PauseImage_3_1_arm64"`
	KubeAPIServer_1_16_8                              string `json:"KubeAPIServer_1_16_8"`
	KubeAPIServer_1_17_17                             string `json:"KubeAPIServer_1_17_17"`
	KubeAPIServer_1_18_9                              string `json:"KubeAPIServer_1_18_9"`
	KubeAPIServer_1_18_9_BilibiliMixprotocols         string `json:"KubeAPIServer_1_18_9_BilibiliMixprotocols"`
	KubeAPIServer_1_20_8                              string `json:"KubeAPIServer_1_20_8"`
	KubeAPIServer_1_20_8_arm64                        string `json:"KubeAPIServer_1_20_8_arm64"`
	KubeAPIServer_1_21_14                             string `json:"KubeAPIServer_1_21_14"`
	KubeAPIServer_1_22_5                              string `json:"KubeAPIServer_1_22_5"`
	KubeAPIServer_1_24_4                              string `json:"KubeAPIServer_1_24_4"`
	KubeAPIServer_1_26_9                              string `json:"KubeAPIServer_1_26_9"`
	KubeControllerManager_1_16_8                      string `json:"KubeControllerManager_1_16_8"`
	KubeControllerManager_1_17_17                     string `json:"KubeControllerManager_1_17_17"`
	KubeControllerManager_1_18_9                      string `json:"KubeControllerManager_1_18_9"`
	KubeControllerManager_1_18_9_BilibiliMixprotocols string `json:"KubeControllerManager_1_18_9_BilibiliMixprotocols"`
	KubeControllerManager_1_20_8                      string `json:"KubeControllerManager_1_20_8"`
	KubeControllerManager_1_20_8_arm64                string `json:"KubeControllerManager_1_20_8_arm64"`
	KubeControllerManager_1_21_14                     string `json:"KubeControllerManager_1_21_14"`
	KubeControllerManager_1_22_5                      string `json:"KubeControllerManager_1_22_5"`
	KubeControllerManager_1_24_4                      string `json:"KubeControllerManager_1_24_4"`
	KubeControllerManager_1_26_9                      string `json:"KubeControllerManager_1_26_9"`
	KubeScheduler_1_16_8                              string `json:"KubeScheduler_1_16_8"`
	KubeScheduler_1_17_17                             string `json:"KubeScheduler_1_17_17"`
	KubeScheduler_1_18_9                              string `json:"KubeScheduler_1_18_9"`
	KubeScheduler_1_18_9_BilibiliMixprotocols         string `json:"KubeScheduler_1_18_9_BilibiliMixprotocols"`
	KubeScheduler_1_20_8                              string `json:"KubeScheduler_1_20_8"`
	KubeScheduler_1_20_8_arm64                        string `json:"KubeScheduler_1_20_8_arm64"`
	KubeScheduler_1_21_14                             string `json:"KubeScheduler_1_21_14"`
	KubeScheduler_1_22_5                              string `json:"KubeScheduler_1_22_5"`
	KubeScheduler_1_24_4                              string `json:"KubeScheduler_1_24_4"`
	KubeScheduler_1_26_9                              string `json:"KubeScheduler_1_26_9"`
	ManagedProKubeAPIServer_1_16_8                    string `json:"ManagedProKubeAPIServer_1_16_8"`
	ManagedProKubeAPIServer_1_17_17                   string `json:"ManagedProKubeAPIServer_1_17_17"`
	ManagedProKubeAPIServer_1_18_9                    string `json:"ManagedProKubeAPIServer_1_18_9"`
	ManagedProKubeAPIServer_1_20_8                    string `json:"ManagedProKubeAPIServer_1_20_8"`
	ManagedProKubeAPIServer_1_21_14                   string `json:"ManagedProKubeAPIServer_1_21_14"`
	ManagedProKubeAPIServer_1_22_5                    string `json:"ManagedProKubeAPIServer_1_22_5"`
	ManagedProKubeAPIServer_1_24_4                    string `json:"ManagedProKubeAPIServer_1_24_4"`
	ManagedProKubeAPIServer_1_26_9                    string `json:"ManagedProKubeAPIServer_1_26_9"`
	ManagedProKubeControllerManager_1_16_8            string `json:"ManagedProKubeControllerManager_1_16_8"`
	ManagedProKubeControllerManager_1_17_17           string `json:"ManagedProKubeControllerManager_1_17_17"`
	ManagedProKubeControllerManager_1_18_9            string `json:"ManagedProKubeControllerManager_1_18_9"`
	ManagedProKubeControllerManager_1_20_8            string `json:"ManagedProKubeControllerManager_1_20_8"`
	ManagedProKubeControllerManager_1_21_14           string `json:"ManagedProKubeControllerManager_1_21_14"`
	ManagedProKubeControllerManager_1_22_5            string `json:"ManagedProKubeControllerManager_1_22_5"`
	ManagedProKubeControllerManager_1_24_4            string `json:"ManagedProKubeControllerManager_1_24_4"`
	ManagedProKubeControllerManager_1_26_9            string `json:"ManagedProKubeControllerManager_1_26_9"`
	ManagedProKubeScheduler_1_16_8                    string `json:"ManagedProKubeScheduler_1_16_8"`
	ManagedProKubeScheduler_1_17_17                   string `json:"ManagedProKubeScheduler_1_17_17"`
	ManagedProKubeScheduler_1_18_9                    string `json:"ManagedProKubeScheduler_1_18_9"`
	ManagedProKubeScheduler_1_20_8                    string `json:"ManagedProKubeScheduler_1_20_8"`
	ManagedProKubeScheduler_1_21_14                   string `json:"ManagedProKubeScheduler_1_21_14"`
	ManagedProKubeScheduler_1_22_5                    string `json:"ManagedProKubeScheduler_1_22_5"`
	ManagedProKubeScheduler_1_24_4                    string `json:"ManagedProKubeScheduler_1_24_4"`
	ManagedProKubeScheduler_1_26_9                    string `json:"ManagedProKubeScheduler_1_26_9"`
	ManagedProETCDImage                               string `json:"ManagedProETCDImage"`
	ManagedProKubeInitImage                           string `json:"ManagedProKubeInitImage"`
	ManagedProDebugerImage                            string `json:"ManagedProDebugerImage"`

	CCEAgentImage            string `json:"CCEAgentImage"`            // 升级 Kubelet 特权容器镜像
	KubeExternalAuditorImage string `json:"KubeExternalAuditorImage"` // KubeExternalAuditor 容器化镜像

	EdgeHub string `json:"EdgeHub"`

	TelegrafFTPAddress               string `json:"TelegrafFTPAddress"`
	LogbeatFTPAddress                string `json:"LogbeatFTPAddress"`
	KubeStateMetricFTPAddress        string `json:"KubeStateMetricFTPAddress"`
	CloudControllerManagerFTPAddress string `json:"CloudControllerManagerFTPAddress"`
	KubeExternalAuditerFTPAddress    string `json:"KubeExternalAuditerFTPAddress"`
	K8sCloudKMSPluginAddress         string `json:"K8sCloudKMSPluginAddress"`
	KubeExtenderSchedulerFTPAddress  string `json:"KubeExtenderSchedulerFTPAddress"`

	CCEOIDCProviderCA string `json:"CCEOIDCProviderCA"`

	BLBEndpoint string `json:"BLBEndpoint"`
	VPCEndpoint string `json:"VPCEndpoint"`
	EIPEndpoint string `json:"EIPEndpoint"`
	BOSEndpoint string `json:"BOSEndpoint"`

	EIPPurchaseType eip.PurchaseType `json:"EIPPurchaseType"`

	// 关闭二进制 CCM 并使用容器化组件代替其功能
	DisableCCM bool `json:"DisableCCM"`

	IsUnionPayEnv bool `json:"IsUnionPayEnv,omitempty"` // 是否是银联商务私有化环境

	// 中控机网段 和安全组相关
	RegionNetworkSegment string `json:"RegionNetworkSegment,omitempty"`

	// sts 配置
	STSEndpoint string `json:"STSEndpoint"`

	IAMEndpoint     string        `json:"IAMEndpoint"`
	ServiceRoleName string        `json:"ServiceRoleName"` // CCE 服务号角色名, 固定唯一
	ServiceName     string        `json:"ServiceName"`     // CCE 服务号名字
	ServicePassword string        `json:"ServicePassword"` // CCE 服务号密码
	Timeout         time.Duration `json:"Timeout"`         // 请求超时时间
	AccountID       string        `json:"AccountID"`
}

// Cluster - 集群部署依赖 Cluster 配置
type Cluster struct {
	AccountID string
	UserID    string

	ClusterID   string
	ClusterName string
	ClusterType ccetypes.ClusterType // 集群类型
	MasterType  ccetypes.MasterType  // Master 类型

	CAPem        string
	CAKeyPem     string
	CAConfigJSON string

	EnableIPV6 bool

	K8SVersion     ccetypes.K8SVersion `json:"K8SVersion"` // K8S 版本
	RuntimeType    ccetypes.RuntimeType
	RuntimeVersion string

	MasterList         []Master `json:"MasterList"`
	ClusterMasterVIP   string
	ClusterMasterVPCIP string

	APIServerWhiteList []string

	ContainerNetworkMode ccetypes.ContainerNetworkMode
	ClusterIPCIDR        string `json:"ClusterIPCIDR"`
	ClusterPodIPCIDR     string
	NodeCIDRMaskSize     int
	NodeCIDRMaskSizeIPv4 int
	NodeCIDRMaskSizeIPv6 int
	ServiceNodePortMin   int
	ServiceNodePortMax   int

	ClusterDNSList      []string
	APIServerPodDNSList []string
	KubeProxyModel      ccetypes.KubeProxyMode
	EnableMasquerade    bool
	PauseImage          string

	KubeAPIQPS   int // default 100
	KubeAPIBurst int // default 100

	AdmissionPlugins                []string        // 自定义 AdmissionPlugins
	MasterFeatureGates              map[string]bool // 自定义 FeatureGates
	NodeFeatureGates                map[string]bool // 自定义 FeatureGates
	SchedulerPredicates             []string        // 自定义 Scheduler Predicates 配置
	SchedulerPriorities             map[string]int  // 自定义 Scheduler Priorities 配置
	EnableLBServiceController       bool            // 部署 lb-controller 开启此开关将会部署 cce-lb-controller 并关闭 ccm 的 service-controller
	EnableCloudNodeController       bool            // 部署 cloud-node-controller
	EnableDefaultPluginDeployByHelm bool            // 开启默认插件部署使用 Helm
	EnableEdgeHub                   bool            // 开启边缘自治组件
	DisableKubeletReadOnlyPort      bool            // 是否禁用 kubelet 10255 端口
	InsecureRegistries              []string        // 自定义 集群维度 InsecureRegistries

	AuthenticateMode ccetypes.AuthenticateMode // APIServer 认证方式

	EnableKMSProvider bool   // 开启 KMS Provider
	EnableHostname    bool   // 开启 nodename 使用 BCC Instance Name
	KMSKeyID          string // KMS KeyID

	SkipInstallRuntime   bool // 跳过 docker 安装
	SkipInstallNFS       bool // 跳过 NFS 安装
	SkipInstallCNI       bool // 跳过 CNI 安装, 仅在容器网络模式为 kubenet 的情况下安装，其他默认由 cni 插件负责安装
	DisableKubeProxyMasq bool // kube-proxy masquerade: false, 关闭 conntrack 配置

	// K8SCustomConfig
	K8SCustomConfig ccetypes.K8SCustomConfig
}

// Master - 集群 Master 信息
type Master struct {
	VPCIP string

	ETCDIndex    int
	ETCDDataPath string
}

// Instance - 集群部署依赖 Instance 配置
type Instance struct {
	Region     string `json:"Region"`    // 区域
	AccountID  string `json:"AccountID"` // AccountID
	HostIP     string `json:"IP"`        // IP
	HostIPIPv6 string `json:"IPv6"`
	NodeName   string `json:"NodeName"`

	Local      bool                        `json:"Local"`      // 是否本地部署
	SSHIP      string                      `json:"SSHIP"`      // SSH 登录 IP
	SSHPort    int                         `json:"SSHPort"`    // SSH 登录 Port
	DeployType ccetypes.InstanceDeployType `json:"DeployType"` // 机器部署方式

	RunUser  string `json:"RunUser"`  // 服务运行账户
	Password string `json:"Password"` // 运行账户密码

	ClusterRole ccetypes.ClusterRole `json:"ClusterRole"` // 集群角色

	MasterType ccetypes.MasterType `json:"MasterType"` // Master 类型, 仅对 CCE 有效

	ETCDConfig `json:"ETCDConfig"` // ETCD 配置

	InstanceType bcc.InstanceType    `json:"InstanceType"` // InstanceType, 区分 GPU 机器
	InstanceOS   ccetypes.InstanceOS `json:"InstanceOS"`   // InstanceOS, OS 信息

	EnableImageAccelerate bool `json:"imageAccelerate"` // image 信息

	DiskMountConfigs []*DiskMountConfig `json:"DiskMountConfigs"` // 磁盘挂载信息

	DockerConfig            ccetypes.DockerConfig     `json:"DockerConfig"`            // docker 配置
	ContainerdConfig        ccetypes.ContainerdConfig `json:"ContainerdConfig"`        // containerd 配置
	RuntimeType             ccetypes.RuntimeType      `json:"runtimeType"`             // 运行时类型docker/containerd
	RuntimeVersion          string                    `json:"runtimeVersion"`          // 运行时版本
	ContainerUnlimitMemlock bool                      `json:"ContainerUnlimitMemlock"` // container unlimit memlock 配置
	KubeletRootDir          string                    `json:"KubeletRootDir"`          // kubelet 数据目录

	MaxPodNumber int    `json:"MaxPodNumber"` // 节点最大 pod数目
	PodPidsLimit *int64 `json:"PodPidsLimit"` // 节点 pod pid 限制

	EnableBinaryKubeProxy  bool              `json:"EnableBinaryKubeProxy"`  // kube-proxy是否二进制部署
	EnableCleanCNI         bool              `json:"EnableCleanCNI"`         // 是否需要在部署前清除残留的CNI配置
	EnableResourceReserved bool              `json:"EnableResourceReserved"` //  开启资源预留, 废弃
	KubeReserved           map[string]string `json:"KubeReserved"`           //  k8s进程资源预留
	SystemReserved         map[string]string `json:"SystemReserved"`         //  系统进程资源预留

	CPU         int                  `json:"cpu"`         //  cpu 值，单位m
	MEM         int                  `json:"mem"`         //  内存 值，单位Mi
	GPUType     bcc.GPUType          `json:"gpuType"`     // GPU类型
	GPUCount    int                  `json:"gpuCount"`    // GPU数量
	MachineSpec string               `json:"machineSpec"` // 机器规格套餐
	Machinetype ccetypes.MachineType `json:"machineType"` // 机器类型

	// registry config
	RegistryPullQPS *int32 `json:"RegistryPullQPS"`

	RegistryBurst *int32 `json:"RegistryBurst"`

	EnableCordon    bool // 是否封锁节点
	EnableGPUDriver bool

	PreUserScriptContent  string // 安装前执行脚本，前端 base64编码后传参
	PostUserScriptContent string // 安装后执行脚本，前端 base64编码后传参

	MachineID     string // vm实例的ID
	InstanceUUID  string // vm实例的UUID
	CCEInstanceID string

	KubeletBindAddressType ccetypes.KubeletBindAddressType `json:"KubeletBindAddressType"` // kubelet 绑定端口类型

	KubeAPIQPS            *int32 // default 500
	KubeAPIBurst          *int32 // default 500
	EventRecordQPS        *int32
	EventBurst            *int32
	CPUManagerPolicy      ccetypes.K8SCPUManagerPolicy
	TopologyManagerScope  ccetypes.K8STopologyManagerScope
	TopologyManagerPolicy ccetypes.K8STopologyManagerPolicy
	CPUCFSQuota           *bool

	ResolvConf *string `json:"ResolvConf,omitempty"` //  resolv.conf 文件路径

	// 设置允许使用的非安全的sysctl或sysctl通配符
	AllowedUnsafeSysctls []string `json:"allowedUnsafeSysctls,omitempty"`

	// 串行拉取镜像 默认值：true
	SerializeImagePulls bool `json:"serializeImagePulls,omitempty"`

	// 硬性门限
	EvictionHard map[string]string `json:"evictionHard,omitempty"`

	// 驱逐阈值
	EvictionSoft map[string]string `json:"evictionSoft,omitempty"`

	// 驱逐宽限期，需已设置evictionSoft
	EvictionSoftGracePeriod map[string]string `json:"evictionSoftGracePeriod,omitempty"`

	// 容器的日志文件个数上限。此值必须大于等于2，且容器运行时需为containerd， 默认值：5
	ContainerLogMaxFiles *int32 `json:"containerLogMaxFiles,omitempty"`

	// 容器日志文件轮换生成新文件的最大阈值。容器运行时需为containerd。
	ContainerLogMaxSize string `json:"containerLogMaxSize,omitempty"`

	// 实验性特性的特性开关组
	FeatureGates map[string]bool `json:"featureGates,omitempty"`

	// kubelet无鉴权只读端口
	ReadOnlyPort *int32 `json:"readOnlyPort,omitempty"`

	// 设置CPU CFS配额周期值。需确保CustomCPUCFSQuotaPeriod特性门控已被启用 默认值：100ms
	CpuCFSQuotaPeriod *int32 `json:"cpuCFSQuotaPeriod,omitempty"`

	// 内存管理器需要使用的策略。默认值：none
	MemoryManagerPolicy ccetypes.K8SMemoryManagerPolicy `json:"memoryManagerPolicy,omitempty"`

	// 配置镜像的磁盘用量百分比阈值，一旦镜像用量超过此阈值，镜像垃圾收集会一直运行。 配置时，此值需大于imageGCLowThresholdPercent取值， 默认值：85
	ImageGCHighThresholdPercent *int32 `json:"imageGCHighThresholdPercent,omitempty"`

	// 配置镜像的磁盘用量百分比阈值，镜像用量低于此阈值时不会执行镜像垃圾收集操作。 配置时，此值需小于imageGCHighThresholdPercent取值。默认值：80
	ImageGCLowThresholdPercent *int32 `json:"imageGCLowThresholdPercent,omitempty"`

	// NUMA节点内存预留列表
	ReservedMemory []ccetypes.MemoryReservation `json:"reservedMemory,omitempty"`

	// 用来设置除长期运行的请求（pull、 logs、exec 和 attach）之外所有运行时请求的超时时长。 默认值：120s
	Runtimerequesttimeout *int32 `json:"runtimerequesttimeout,omitempty"`

	ScaleUpControlPlaneConf ScaleUpControlPlaneConf

	PostUserScriptFailedAutoCordon bool // 部署后执行脚本失败自动封锁节点
}

// DiskMountConfig 定义磁盘挂载配置
type DiskMountConfig struct {
	ID         string `json:"id"`       // e.g. id: f8b1d7b2-c023-4043-8d4e-0c8b9ed44fdc
	VolumeID   string `json:"volumeID"` // e.g. 磁盘短ID: v-dU6rZcWz
	Device     string `json:"device"`   // e.g. 设备名 /dev/vdb
	Path       string `json:"path"`     // e.g. 挂载目录 /home/<USER>
	NeedFormat bool   `json:"needFormat"`

	Local bool `json:"local"`
}

// ETCDConfig ETCD 配置
type ETCDConfig struct {
	ETCDIndex    int    `json:"ETCDIndex"`    // ETCD Index
	ETCDDataPath string `json:"ETCDDataPath"` // ETCD 数据盘路径
}

// ScaleUpControlPlaneConf 集群控制面扩容配置
type ScaleUpControlPlaneConf struct {
	ScaleUpControlPlane          bool
	ScaleUpKubeApiserver         bool
	ScaleUpKubeControllerManager bool
	ScaleUpKubeScheduler         bool
	ScaleUpETCD                  bool
	UseNewConfig                 bool           // use new config means that will deploy with latest config rather than copying old conf
	SourceInstance               SourceInstance // 配置源
}

type SourceInstance struct {
	HostIP       string `json:"IP"`       // IP
	SSHIP        string `json:"SSHIP"`    // SSH 登录 IP
	SSHPort      int    `json:"SSHPort"`  // SSH 登录 Port
	Password     string `json:"Password"` // 运行账户密码
	InstacenID   string `json:"InstacenID"`
	InstanceUUID string `json:"InstanceUUID"`
}

// TaskInterface - 定义一个操作, 它可由两部分组成:
// 1. 分发配置文件;
// 2. 执行命令行.
//
// 比如: 部署 apiserver = 下发 bin/config + 生成配置/启动服务
type TaskInterface interface {
	Name(ctx context.Context) string
	Configs(ctx context.Context, config *Config, cluster *Cluster, instance *Instance) ([]*exectypes.File, error)
	Commands(ctx context.Context, config *Config, cluster *Cluster, instance *Instance) ([]*exectypes.Command, error)
}

// ParamCheck - 参数校验
func ParamCheck(ctx context.Context, config *Config, cluster *Cluster, instance *Instance) error {
	if config == nil {
		return errors.New("config is nil")
	}

	if cluster == nil {
		return errors.New("cluster is nil")
	}

	if instance == nil {
		return errors.New("instance is nil")
	}

	return nil
}

// CheckConfig - 检查配置
//
// PARAMS:
//   - ctx: The context to trace request
//   - config: *Config
//
// RETURNS:
//
//	error: nil if succeed, error if fai
func CheckConfig(ctx context.Context, config *Config) error {
	if config == nil {
		return errors.New("config is nil")
	}

	if config.Region == "" {
		return errors.New("config.Region is empty")
	}

	if config.InternalEndpoint == "" {
		return errors.New("config.InternalEndpoint is empty")
	}

	if config.KubeBinPath_1_11_1 == "" {
		return errors.New("config.KubeBinPath_1_11_1 is empty")
	}

	if config.KubeBinPath_1_11_5 == "" {
		return errors.New("config.KubeBinPath_1_11_5 is empty")
	}

	if config.KubeBinPath_1_13_4 == "" {
		return errors.New("config.KubeBinPath_1_13_4 is empty")
	}

	if config.KubeBinPath_1_13_10 == "" {
		return errors.New("config.KubeBinPath_1_13_10 is empty")
	}

	if config.KubeBinPath_1_16_3 == "" {
		return errors.New("config.KubeBinPath_1_16_3 is empty")
	}

	if config.KubeBinPath_1_16_8 == "" {
		return errors.New("config.KubeBinPath_1_16_8 is empty")
	}

	if config.CloudControllerManagerFTPAddress == "" {
		return errors.New("config.CloudControllerManagerFTPAddress is empty")
	}

	return nil
}
