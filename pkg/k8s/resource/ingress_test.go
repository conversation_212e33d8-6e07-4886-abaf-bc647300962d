// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2019/12/06 13:52:00, by <EMAIL>, create
*/
/*
DESCRIPTION
本文件定义 CCE K8S 集群中 Ingress/Service 和 IaaS 相关 Annotation 的方法
引用 cce-ingress-controller 存在依赖冲突
*/

package resource

import (
	"context"
	"encoding/json"
	"errors"
	"reflect"
	"sort"
	"testing"

	"github.com/golang/mock/gomock"
	v1 "k8s.io/api/core/v1"
	v1beta1 "k8s.io/api/extensions/v1beta1"
	networkingv1 "k8s.io/api/networking/v1"
	kerrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	runtime "k8s.io/apimachinery/pkg/runtime"
	discoveryVersion "k8s.io/apimachinery/pkg/version"
	discoveryfake "k8s.io/client-go/discovery/fake"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/kubernetes/fake"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/appblb"
	appblbmock "icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/appblb/mock"
	eipsdk "icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/eip"
	eipmock "icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/eip/mock"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/sts"
	stsmock "icode.baidu.com/baidu/cce-test/e2e-test/pkg/sts/mock"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/utils"
)

func TestAnnotation_HandledByCCEIngress(t *testing.T) {
	ctx := context.TODO()

	cases := []struct {
		name    string
		ingress []byte
		target  bool
	}{
		{
			name: "正常情况",
			ingress: []byte(`apiVersion: extensions/v1beta1
kind: Ingress
metadata:
  annotations:
    kubernetes.io/ingress.class: "cce"
  name: chenhuan
  namespace: default
spec:
  rules:
  - host: cce-default-host
    http:
      paths:
      - backend:
          serviceName: non-service
          servicePort: 65535
        path: /`),
			target: true,
		},
		{
			name:    "Ingress 为空",
			ingress: []byte(``),
			target:  false,
		},
		{
			name: "未设置 Annotation",
			ingress: []byte(`apiVersion: extensions/v1beta1
kind: Ingress
metadata:
  name: chenhuan
  namespace: default
spec:
  rules:
  - host: cce-default-host
    http:
      paths:
      - backend:
          serviceName: non-service
          servicePort: 65535
        path: /`),
			target: false,
		}, {
			name: "Annotation 不是 CCE",
			ingress: []byte(`apiVersion: extensions/v1beta1
kind: Ingress
metadata:
  annotations:
    kubernetes.io/ingress.class: "aliyun"
  name: chenhuan
  namespace: default
spec:
  rules:
  - host: cce-default-host
    http:
      paths:
      - backend:
          serviceName: non-service
          servicePort: 65535
        path: /`),
			target: false,
		}, {
			name: "Annotation 为空",
			ingress: []byte(`apiVersion: extensions/v1beta1
kind: Ingress
metadata:
  annotations:
    kubernetes.io/ingress.class: ""
  name: chenhuan
  namespace: default
spec:
  rules:
  - host: cce-default-host
    http:
      paths:
      - backend:
          serviceName: non-service
          servicePort: 65535
        path: /`),
			target: false,
		},
	}

	for _, c := range cases {
		ingress, err := utils.YAMLToIngress(c.ingress)
		if err != nil {
			t.Errorf("YAMLToIngress name='%s' failed: %v", c.name, err)
			return
		}

		got := handledByCCEIngress(ctx, ingress)
		if got != c.target {
			t.Errorf("HandledByCCEIngress %s failed: got %v, want %v", c.name, got, c.target)
			return
		}
	}
}

func TestAnnotation_GetIngressBLBID(t *testing.T) {
	ctx := context.Background()

	cases := []struct {
		name    string
		ingress []byte
		isSet   bool
		blbID   string
	}{
		{
			name: "正常情况",
			ingress: []byte(`apiVersion: extensions/v1beta1
kind: Ingress
metadata:
  annotations:
    kubernetes.io/ingress.class: "cce"
    kubernetes.io/cce.ingress.blb-id: "lb-aaaaaaaa"
  name: chenhuan
  namespace: default
spec:
  rules:
  - host: cce-default-host
    http:
      paths:
      - backend:
          serviceName: non-service
          servicePort: 65535
        path: /`),
			isSet: true,
			blbID: "lb-aaaaaaaa",
		},
		{
			name:    "Ingress 为空",
			ingress: []byte(``),
			isSet:   false,
		},
		{
			name: "Annotation 未设置",
			ingress: []byte(`apiVersion: extensions/v1beta1
kind: Ingress
metadata:
  name: chenhuan
  namespace: default
spec:
  rules:
  - host: cce-default-host
    http:
      paths:
      - backend:
          serviceName: non-service
          servicePort: 65535
        path: /`),
			isSet: false,
		}, {
			name: "Annotation 设置格式错误",
			ingress: []byte(`apiVersion: extensions/v1beta1
kind: Ingress
metadata:
  annotations:
    kubernetes.io/cce.ingress.blb-id: "l-aaaaaaa"
  name: chenhuan
  namespace: default
spec:
  rules:
  - host: cce-default-host
    http:
      paths:
      - backend:
          serviceName: non-service
          servicePort: 65535
        path: /`),
			isSet: false,
		}, {
			name: "Annotation 为空字符串",
			ingress: []byte(`apiVersion: extensions/v1beta1
kind: Ingress
metadata:
  annotations:
    kubernetes.io/cce.ingress.blb-id: ""
  name: chenhuan
  namespace: default
spec:
  rules:
  - host: cce-default-host
    http:
      paths:
      - backend:
          serviceName: non-service
          servicePort: 65535
        path: /`),
			isSet: false,
		},
	}

	for _, c := range cases {
		ingress, err := utils.YAMLToIngress(c.ingress)
		if err != nil {
			t.Errorf("YAMLToIngress name='%s' failed: %v", c.name, err)
			return
		}

		blbID, isSet := getIngressBLBID(ctx, ingress)
		if isSet != c.isSet || blbID != c.blbID {
			t.Errorf("isAppBLBIDSetted %s failed: got %v %v, want %v %v", c.name, isSet, blbID, c.isSet, c.blbID)
			return
		}
	}
}

func TestAnnotation_GetIngressEIP(t *testing.T) {
	ctx := context.Background()

	cases := []struct {
		name    string
		ingress []byte
		isSet   bool
		eip     string
	}{
		{
			name: "set EIP",
			ingress: []byte(`apiVersion: extensions/v1beta1
kind: Ingress
metadata:
  annotations:
    kubernetes.io/ingress.class: cce
    kubernetes.io/cce.ingress.eip: "************"
  name: chenhuan
  namespace: default
spec:
  rules:
  - host: cce-default-host
    http:
      paths:
      - backend:
          serviceName: non-service
          servicePort: 65535
        path: /`),
			isSet: true,
			eip:   "************",
		},
		{
			name:    "Ingress 为空",
			ingress: []byte(``),
			isSet:   false,
		},
		{
			name: "not set EIP",
			ingress: []byte(`apiVersion: extensions/v1beta1
kind: Ingress
metadata:
  annotations:
    kubernetes.io/ingress.class: cce
  name: chenhuan
  namespace: default
spec:
  rules:
  - host: cce-default-host
    http:
      paths:
      - backend:
          serviceName: non-service
          servicePort: 65535
        path: /`),
			isSet: false,
		},
		{
			name: "set EIP nil",
			ingress: []byte(`apiVersion: extensions/v1beta1
kind: Ingress
metadata:
  annotations:
    kubernetes.io/ingress.class: cce
    kubernetes.io/cce.ingress.eip: ""
  name: chenhuan
  namespace: default
spec:
  rules:
  - host: cce-default-host
    http:
      paths:
      - backend:
          serviceName: non-service
          servicePort: 65535
        path: /`),
			isSet: false,
		},
		{
			name: "set invalid EIP",
			ingress: []byte(`apiVersion: extensions/v1beta1
kind: Ingress
metadata:
  annotations:
    kubernetes.io/ingress.class: cce
    kubernetes.io/cce.ingress.eip: "***********"
  name: chenhuan
  namespace: default
spec:
  rules:
  - host: cce-default-host
    http:
      paths:
      - backend:
          serviceName: non-service
          servicePort: 65535
        path: /`),
			isSet: false,
		},
		{
			name: "set invalid EIP",
			ingress: []byte(`apiVersion: extensions/v1beta1
kind: Ingress
metadata:
  annotations:
    kubernetes.io/ingress.class: cce
    kubernetes.io/cce.ingress.eip: "xxxxxxx"
  name: chenhuan
  namespace: default
spec:
  rules:
  - host: cce-default-host
    http:
      paths:
      - backend:
          serviceName: non-service
          servicePort: 65535
        path: /`),
			isSet: false,
		},
		{
			name: "set right EIP in Status",
			ingress: []byte(`apiVersion: extensions/v1beta1
kind: Ingress
metadata:
  annotations:
    kubernetes.io/ingress.class: cce
  name: chenhuan
  namespace: default
spec:
  rules:
  - host: cce-default-host
    http:
      paths:
      - backend:
          serviceName: non-service
          servicePort: 65535
        path: /
status:
  loadBalancer:
    ingress:
    - ip: *************
    - ip: *************`),
			isSet: true,
			eip:   "*************",
		},
		{
			name: "set invalid EIP in Status",
			ingress: []byte(`apiVersion: extensions/v1beta1
kind: Ingress
metadata:
  annotations:
    kubernetes.io/ingress.class: cce
  name: chenhuan
  namespace: default
spec:
  rules:
  - host: cce-default-host
    http:
      paths:
      - backend:
          serviceName: non-service
          servicePort: 65535
        path: /
status:
  loadBalancer:
    ingress:
    - ip: *************
    - ip: ************`),
			isSet: false,
		},
	}

	for _, c := range cases {
		ingress, err := utils.YAMLToIngress(c.ingress)
		if err != nil {
			t.Errorf("YAMLToIngress name='%s' failed: %v", c.name, err)
			return
		}

		eip, isSet := getIngressEIP(ctx, ingress)
		if isSet != c.isSet || eip != c.eip {
			t.Errorf("getIngressEIP %s failed: got %v %v, want %v %v",
				c.name, isSet, eip, c.isSet, c.eip)
			return
		}
	}
}

func TestIngress_ListResources(t *testing.T) {
	type fields struct {
		clientset    kubernetes.Interface
		appblbClient appblb.Interface
		eipClient    eipsdk.Interface
	}

	type args struct {
		ctx       context.Context
		namespace string
	}

	// 测试 ingress
	ingress1 := &v1beta1.Ingress{
		ObjectMeta: metav1.ObjectMeta{
			Namespace: "default",
			Name:      "chenhuan",
		},
	}

	ingress2 := &v1beta1.Ingress{
		ObjectMeta: metav1.ObjectMeta{
			Namespace: "default",
			Name:      "chen",
		},
	}

	ingress3 := &v1beta1.Ingress{
		ObjectMeta: metav1.ObjectMeta{
			Namespace: "kube-system",
			Name:      "huan",
		},
	}

	tests := []struct {
		name    string
		fields  fields
		args    args
		want    []runtime.Object
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "存在多个 Ingress, 正常返回",
			fields: fields{
				clientset: fake.NewSimpleClientset(ingress1, ingress2, ingress3),
			},
			args: args{
				ctx:       context.TODO(),
				namespace: "default",
			},
			want: []runtime.Object{
				ingress1,
				ingress2,
			},
			wantErr: false,
		},
		{
			name: "存在 1 个 Ingress, 正常返回",
			fields: fields{
				clientset: fake.NewSimpleClientset(ingress1),
			},
			args: args{
				ctx:       context.TODO(),
				namespace: "default",
			},
			want: []runtime.Object{
				ingress1,
			},
			wantErr: false,
		},
		{
			name: "存在 0 个 Ingress, 正常返回",
			fields: fields{
				clientset: fake.NewSimpleClientset(ingress1),
			},
			args: args{
				ctx:       context.TODO(),
				namespace: "other-namespace",
			},
			want:    []runtime.Object{},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.fields.clientset.Discovery().(*discoveryfake.FakeDiscovery).FakedServerVersion = &discoveryVersion.Info{
				GitVersion: "v1.18.0",
			}
			converter, err := NewIngressStore(tt.fields.clientset)
			if err != nil {
				t.Errorf("NewIngressStore() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			r := &Ingress{
				clientset:        tt.fields.clientset,
				appblbClient:     tt.fields.appblbClient,
				eipClient:        tt.fields.eipClient,
				ingressConverter: converter,
			}

			got, err := r.ListResources(tt.args.ctx, tt.args.namespace)
			if err != nil {
				if tt.wantErr {
					return
				}

				t.Errorf("ListResources() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if tt.wantErr {
				t.Errorf("ListResources() failed, wantErr=true got=false")
				return
			}

			// 稳定排序，避免判断相等时出错
			sort.SliceStable(got, func(i, j int) bool {
				a := got[i].(*v1beta1.Ingress)
				b := got[j].(*v1beta1.Ingress)
				return a.Name < b.Name
			})

			sort.SliceStable(tt.want, func(i, j int) bool {
				a := tt.want[i].(*v1beta1.Ingress)
				b := tt.want[j].(*v1beta1.Ingress)

				return a.Name < b.Name
			})

			if !reflect.DeepEqual(got, tt.want) {
				if str, err := json.Marshal(got); err == nil {
					t.Errorf("got: %v", string(str))
				}

				if str, err := json.Marshal(tt.want); err == nil {
					t.Errorf("want: %v", string(str))
				}

				t.Errorf("Ingress.ListResources() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestIngress_GetResourceMeta(t *testing.T) {
	type fields struct {
		clientset    kubernetes.Interface
		appblbClient appblb.Interface
		eipClient    eipsdk.Interface
	}

	type args struct {
		ctx context.Context
		obj runtime.Object
	}

	// 测试 ingress
	ingress1 := &v1beta1.Ingress{
		TypeMeta: metav1.TypeMeta{
			Kind:       "Ingress",
			APIVersion: "extensions/v1beta1",
		},
		ObjectMeta: metav1.ObjectMeta{
			Namespace: "default",
			Name:      "chenhuan",
		},
	}

	tests := []struct {
		name          string
		fields        fields
		args          args
		wantNamespace string
		wantName      string
		wantErr       bool
	}{
		// TODO: Add test cases.
		{
			name: "Ingress 存在, 正常返回",
			fields: fields{
				clientset: fake.NewSimpleClientset(ingress1),
			},
			args: args{
				ctx: context.TODO(),
				obj: ingress1,
			},
			wantNamespace: "default",
			wantName:      "chenhuan",
			wantErr:       false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			converter, err := NewIngressStore(tt.fields.clientset)
			if err != nil {
				t.Errorf("NewIngressStore() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			r := &Ingress{
				clientset:        tt.fields.clientset,
				appblbClient:     tt.fields.appblbClient,
				eipClient:        tt.fields.eipClient,
				ingressConverter: converter,
			}

			ns, name, err := r.GetResourceMeta(tt.args.ctx, tt.args.obj)
			if err != nil {
				if tt.wantErr {
					return
				}

				t.Errorf("GetResourceMeta() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if tt.wantErr {
				t.Errorf("GetResourceMeta() failed, wantErr=true got=false")
				return
			}

			if ns != tt.wantNamespace {
				t.Errorf("Ingress.GetResourceMeta() got = %v, want %v", ns, tt.wantNamespace)
			}

			if name != tt.wantName {
				t.Errorf("Ingress.GetResourceMeta() got1 = %v, want %v", name, tt.wantName)
			}
		})
	}
}

func TestIngress_DeleteK8SResource(t *testing.T) {
	type fields struct {
		clientset    kubernetes.Interface
		appblbClient appblb.Interface
		eipClient    eipsdk.Interface
	}

	type args struct {
		ctx  context.Context
		ns   string
		name string
	}

	// 测试用 ingress
	ingress1 := &v1beta1.Ingress{
		TypeMeta: metav1.TypeMeta{
			Kind:       "Ingress",
			APIVersion: "extensions/v1beta1",
		},
		ObjectMeta: metav1.ObjectMeta{
			Namespace: "default",
			Name:      "chenhuan",
			Annotations: map[string]string{
				ingressAnnotationClass: ingressAnnotationHandler,
			},
		},
	}

	ingress2 := &v1beta1.Ingress{
		TypeMeta: metav1.TypeMeta{
			Kind:       "Ingress",
			APIVersion: "extensions/v1beta1",
		},
		ObjectMeta: metav1.ObjectMeta{
			Namespace: "default",
			Name:      "chenhuan",
		},
	}

	tests := []struct {
		name       string
		fields     fields
		args       args
		wantDelete bool
		wantErr    bool
	}{
		// TODO: Add test cases.
		{
			name: "Ingress 存在, 删除成功",
			fields: fields{
				clientset: fake.NewSimpleClientset(ingress1),
			},
			args: args{
				ctx:  context.TODO(),
				ns:   "default",
				name: "chenhuan",
			},
			wantDelete: true,
			wantErr:    false,
		},
		{
			name: "Ingress 不由 CCE 托管, 不删除",
			fields: fields{
				clientset: fake.NewSimpleClientset(ingress2),
			},
			args: args{
				ctx:  context.TODO(),
				ns:   "default",
				name: "chenhuan",
			},
			wantDelete: false,
			wantErr:    false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			converter, err := NewIngressStore(tt.fields.clientset)
			if err != nil {
				t.Errorf("NewIngressStore() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			r := &Ingress{
				clientset:        tt.fields.clientset,
				appblbClient:     tt.fields.appblbClient,
				eipClient:        tt.fields.eipClient,
				ingressConverter: converter,
			}

			err = r.DeleteK8SResource(tt.args.ctx, tt.args.ns, tt.args.name)
			if err != nil {
				if tt.wantErr {
					return
				}

				t.Errorf("DeleteK8SResource() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if tt.wantErr {
				t.Errorf("DeleteK8SResource() failed, wantErr=true got=false")
				return
			}

			_, err = tt.fields.clientset.ExtensionsV1beta1().Ingresses(tt.args.ns).Get(context.TODO(), tt.args.name, metav1.GetOptions{})
			if err != nil {
				if kerrors.IsNotFound(err) {
					if tt.wantDelete {
						return
					}

					t.Errorf("DeleteK8SResource() failed: wantDelete=false but ingress has been deleted")
					return
				}

				t.Errorf("DeleteK8SResource() failed: %v", err)
				return
			}

			if err == nil {
				if !tt.wantDelete {
					return
				}

				t.Errorf("DeleteK8SResource() failed: wantDelete=true but ingress exist")
				return
			}
		})
	}
}

func TestIngress_IaasReleased(t *testing.T) {
	type fields struct {
		ctl          *gomock.Controller
		clientset    kubernetes.Interface
		appblbClient appblb.Interface
		eipClient    eipsdk.Interface
		stsClient    sts.Interface
	}

	type args struct {
		ctx       context.Context
		obj       runtime.Object
		accountID string
	}

	tests := []struct {
		name    string
		fields  fields
		args    args
		want    bool
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "非 CCE Ingress 跳过检查",
			fields: func() fields {
				return fields{}
			}(),
			args: args{
				ctx: context.TODO(),
				obj: &v1beta1.Ingress{
					TypeMeta: metav1.TypeMeta{
						Kind:       "Ingress",
						APIVersion: "extensions/v1beta1",
					},
					ObjectMeta: metav1.ObjectMeta{
						Namespace: "default",
						Name:      "chenhuan",
						Annotations: map[string]string{
							"kubernetes.io/ingress.class": "not-cce",
						},
					},
				},
			},
			want:    true,
			wantErr: false,
		},
		{
			name: "Ingress Annotation 不包含 BLB/EIP, 正常释放",
			fields: func() fields {
				return fields{}
			}(),
			args: args{
				ctx: context.TODO(),
				obj: &v1beta1.Ingress{
					TypeMeta: metav1.TypeMeta{
						Kind:       "Ingress",
						APIVersion: "extensions/v1beta1",
					},
					ObjectMeta: metav1.ObjectMeta{
						Namespace: "default",
						Name:      "chenhuan",
						Annotations: map[string]string{
							"kubernetes.io/ingress.class": "cce",
						},
					},
				},
			},
			want:    true,
			wantErr: false,
		},
		{
			name: "Ingress Annotation 包含 BLB, 正常释放",
			fields: func() fields {
				ctx := context.TODO()
				ctl := gomock.NewController(t)

				blbclient := appblbmock.NewMockInterface(ctl)
				stsclient := stsmock.NewMockInterface(ctl)

				gomock.InOrder(
					stsclient.EXPECT().NewSignOption(ctx, "accountID").Return(nil),
					blbclient.EXPECT().DescribeAppLoadBalancerByID(ctx, "blb-id", nil).Return(nil, errors.New("NoSuchObject")),
				)

				return fields{
					ctl:          ctl,
					appblbClient: blbclient,
					stsClient:    stsclient,
				}
			}(),
			args: args{
				ctx: context.TODO(),
				obj: &v1beta1.Ingress{
					TypeMeta: metav1.TypeMeta{
						Kind:       "Ingress",
						APIVersion: "extensions/v1beta1",
					},
					ObjectMeta: metav1.ObjectMeta{
						Namespace: "default",
						Name:      "chenhuan",
						Annotations: map[string]string{
							"kubernetes.io/cce.ingress.blb-id": "blb-id",
							"kubernetes.io/ingress.class":      "cce",
						},
					},
				},
				accountID: "accountID",
			},
			want:    true,
			wantErr: false,
		},
		{
			name: "Ingress Annotation 包含 BLB, 查询 BLB 失败",
			fields: func() fields {
				ctx := context.TODO()
				ctl := gomock.NewController(t)

				stsclient := stsmock.NewMockInterface(ctl)
				blbclient := appblbmock.NewMockInterface(ctl)

				gomock.InOrder(
					stsclient.EXPECT().NewSignOption(ctx, "accountID").Return(nil),
					blbclient.EXPECT().DescribeAppLoadBalancerByID(ctx, "blb-id", nil).Return(nil, errors.New("Query BLB failed")),
				)

				return fields{
					ctl:          ctl,
					appblbClient: blbclient,
					stsClient:    stsclient,
				}
			}(),
			args: args{
				ctx: context.TODO(),
				obj: &v1beta1.Ingress{
					TypeMeta: metav1.TypeMeta{
						Kind:       "Ingress",
						APIVersion: "extensions/v1beta1",
					},
					ObjectMeta: metav1.ObjectMeta{
						Namespace: "default",
						Name:      "chenhuan",
						Annotations: map[string]string{
							"kubernetes.io/cce.ingress.blb-id": "blb-id",
							"kubernetes.io/ingress.class":      "cce",
						},
					},
				},
				accountID: "accountID",
			},
			want:    true,
			wantErr: true,
		},
		{
			name: "Ingress Annotation 包含 BLB, BLB 未释放",
			fields: func() fields {
				ctx := context.TODO()
				ctl := gomock.NewController(t)

				stsclient := stsmock.NewMockInterface(ctl)
				blbclient := appblbmock.NewMockInterface(ctl)

				gomock.InOrder(
					stsclient.EXPECT().NewSignOption(ctx, "accountID").Return(nil),
					blbclient.EXPECT().DescribeAppLoadBalancerByID(ctx, "blb-id", nil).Return(&appblb.AppLoadBalancer{}, nil),
				)

				return fields{
					ctl:          ctl,
					appblbClient: blbclient,
					stsClient:    stsclient,
				}
			}(),
			args: args{
				ctx: context.TODO(),
				obj: &v1beta1.Ingress{
					TypeMeta: metav1.TypeMeta{
						Kind:       "Ingress",
						APIVersion: "extensions/v1beta1",
					},
					ObjectMeta: metav1.ObjectMeta{
						Namespace: "default",
						Name:      "chenhuan",
						Annotations: map[string]string{
							"kubernetes.io/cce.ingress.blb-id": "blb-id",
							"kubernetes.io/ingress.class":      "cce",
						},
					},
				},
				accountID: "accountID",
			},
			want:    false,
			wantErr: false,
		},
		{
			name: "Ingress Annotation 包含 EIP, 正常释放",
			fields: func() fields {
				ctx := context.TODO()
				ctl := gomock.NewController(t)

				stsclient := stsmock.NewMockInterface(ctl)
				eipclient := eipmock.NewMockInterface(ctl)

				gomock.InOrder(
					stsclient.EXPECT().NewSignOption(ctx, "accountID").Return(nil),
					eipclient.EXPECT().GetEIPs(ctx, &eipsdk.GetEIPsArgs{EIP: "***********"}, nil).Return([]*eipsdk.EIP{}, nil),
				)

				return fields{
					ctl:       ctl,
					eipClient: eipclient,
					stsClient: stsclient,
				}
			}(),
			args: args{
				ctx: context.TODO(),
				obj: &v1beta1.Ingress{
					TypeMeta: metav1.TypeMeta{
						Kind:       "Ingress",
						APIVersion: "extensions/v1beta1",
					},
					ObjectMeta: metav1.ObjectMeta{
						Namespace: "default",
						Name:      "chenhuan",
						Annotations: map[string]string{
							"kubernetes.io/cce.ingress.eip": "***********",
							"kubernetes.io/ingress.class":   "cce",
						},
					},
				},
				accountID: "accountID",
			},
			want:    true,
			wantErr: false,
		},
		{
			name: "Ingress Annotation 包含 EIP, 未释放",
			fields: func() fields {
				ctx := context.TODO()
				ctl := gomock.NewController(t)

				stsclient := stsmock.NewMockInterface(ctl)
				eipclient := eipmock.NewMockInterface(ctl)

				gomock.InOrder(
					stsclient.EXPECT().NewSignOption(ctx, "accountID").Return(nil),
					eipclient.EXPECT().GetEIPs(ctx, &eipsdk.GetEIPsArgs{EIP: "***********"}, nil).Return([]*eipsdk.EIP{
						{},
					}, nil),
				)

				return fields{
					ctl:       ctl,
					eipClient: eipclient,
					stsClient: stsclient,
				}
			}(),
			args: args{
				ctx: context.TODO(),
				obj: &v1beta1.Ingress{
					TypeMeta: metav1.TypeMeta{
						Kind:       "Ingress",
						APIVersion: "extensions/v1beta1",
					},
					ObjectMeta: metav1.ObjectMeta{
						Namespace: "default",
						Name:      "chenhuan",
						Annotations: map[string]string{
							"kubernetes.io/cce.ingress.eip": "***********",
							"kubernetes.io/ingress.class":   "cce",
						},
					},
				},
				accountID: "accountID",
			},
			want:    false,
			wantErr: false,
		},
		{
			name: "Ingress Annotation 包含 EIP, 查询 EIP 失败",
			fields: func() fields {
				ctx := context.TODO()
				ctl := gomock.NewController(t)

				stsclient := stsmock.NewMockInterface(ctl)
				eipclient := eipmock.NewMockInterface(ctl)

				gomock.InOrder(
					stsclient.EXPECT().NewSignOption(ctx, "accountID").Return(nil),
					eipclient.EXPECT().GetEIPs(ctx, &eipsdk.GetEIPsArgs{EIP: "***********"}, nil).Return([]*eipsdk.EIP{
						{},
					}, errors.New("error")),
				)

				return fields{
					ctl:       ctl,
					eipClient: eipclient,
					stsClient: stsclient,
				}
			}(),
			args: args{
				ctx: context.TODO(),
				obj: &v1beta1.Ingress{
					TypeMeta: metav1.TypeMeta{
						Kind:       "Ingress",
						APIVersion: "extensions/v1beta1",
					},
					ObjectMeta: metav1.ObjectMeta{
						Namespace: "default",
						Name:      "chenhuan",
						Annotations: map[string]string{
							"kubernetes.io/cce.ingress.eip": "***********",
							"kubernetes.io/ingress.class":   "cce",
						},
					},
				},
				accountID: "accountID",
			},
			want:    false,
			wantErr: true,
		},
		{
			name: "Ingress Annotation 包含 BLB/EIP, 都释放成功",
			fields: func() fields {
				ctx := context.TODO()
				ctl := gomock.NewController(t)

				stsclient := stsmock.NewMockInterface(ctl)
				blbclient := appblbmock.NewMockInterface(ctl)
				eipclient := eipmock.NewMockInterface(ctl)

				gomock.InOrder(
					stsclient.EXPECT().NewSignOption(ctx, "accountID").Return(nil),
					blbclient.EXPECT().DescribeAppLoadBalancerByID(ctx, "blb-id", nil).Return(nil, errors.New("NoSuchObject")),
					stsclient.EXPECT().NewSignOption(ctx, "accountID").Return(nil),
					eipclient.EXPECT().GetEIPs(ctx, &eipsdk.GetEIPsArgs{EIP: "***********"}, nil).Return([]*eipsdk.EIP{}, nil),
				)

				return fields{
					ctl:          ctl,
					appblbClient: blbclient,
					eipClient:    eipclient,
					stsClient:    stsclient,
				}
			}(),
			args: args{
				ctx: context.TODO(),
				obj: &v1beta1.Ingress{
					TypeMeta: metav1.TypeMeta{
						Kind:       "Ingress",
						APIVersion: "extensions/v1beta1",
					},
					ObjectMeta: metav1.ObjectMeta{
						Namespace: "default",
						Name:      "chenhuan",
						Annotations: map[string]string{
							"kubernetes.io/cce.ingress.blb-id": "blb-id",
							"kubernetes.io/cce.ingress.eip":    "***********",
							"kubernetes.io/ingress.class":      "cce",
						},
					},
				},
				accountID: "accountID",
			},
			want:    true,
			wantErr: false,
		},
		{
			name: "Ingress Annotation 包含 BLB/EIP, 都释放成功",
			fields: func() fields {
				ctx := context.TODO()
				ctl := gomock.NewController(t)

				stsclient := stsmock.NewMockInterface(ctl)
				blbclient := appblbmock.NewMockInterface(ctl)
				eipclient := eipmock.NewMockInterface(ctl)

				gomock.InOrder(
					stsclient.EXPECT().NewSignOption(ctx, "accountID").Return(nil),
					blbclient.EXPECT().DescribeAppLoadBalancerByID(ctx, "blb-id", nil).Return(nil, errors.New("NoSuchObject")),
					stsclient.EXPECT().NewSignOption(ctx, "accountID").Return(nil),
					eipclient.EXPECT().GetEIPs(ctx, &eipsdk.GetEIPsArgs{EIP: "***********"}, nil).Return([]*eipsdk.EIP{}, nil),
				)

				return fields{
					ctl:          ctl,
					appblbClient: blbclient,
					eipClient:    eipclient,
					stsClient:    stsclient,
				}
			}(),
			args: args{
				ctx: context.TODO(),
				obj: &networkingv1.Ingress{
					TypeMeta: metav1.TypeMeta{
						Kind:       "Ingress",
						APIVersion: "networking.k8s.io/v1",
					},
					ObjectMeta: metav1.ObjectMeta{
						Namespace: "default",
						Name:      "chenhuan",
						Annotations: map[string]string{
							"kubernetes.io/cce.ingress.blb-id": "blb-id",
							"kubernetes.io/cce.ingress.eip":    "***********",
							"kubernetes.io/ingress.class":      "cce",
						},
					},
				},
				accountID: "accountID",
			},
			want:    true,
			wantErr: false,
		},
		{
			name: "illeaal object",
			fields: func() fields {
				// ctx := context.TODO()
				ctl := gomock.NewController(t)

				stsclient := stsmock.NewMockInterface(ctl)
				blbclient := appblbmock.NewMockInterface(ctl)
				eipclient := eipmock.NewMockInterface(ctl)

				gomock.InOrder(
				// stsclient.EXPECT().NewSignOption(ctx, "accountID").Return(nil),
				// blbclient.EXPECT().DescribeAppLoadBalancerByID(ctx, "blb-id", nil).Return(nil, fmt.Errorf("NoSuchObject")),
				// stsclient.EXPECT().NewSignOption(ctx, "accountID").Return(nil),
				// eipclient.EXPECT().GetEIPs(ctx, &eipsdk.GetEIPsArgs{EIP: "***********"}, nil).Return([]*eipsdk.EIP{}, nil),
				)

				return fields{
					ctl:          ctl,
					appblbClient: blbclient,
					eipClient:    eipclient,
					stsClient:    stsclient,
				}
			}(),
			args: args{
				ctx:       context.TODO(),
				obj:       &v1.Service{},
				accountID: "accountID",
			},
			want:    false,
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.fields.ctl != nil {
				defer tt.fields.ctl.Finish()
			}

			r := &Ingress{
				clientset:    tt.fields.clientset,
				appblbClient: tt.fields.appblbClient,
				eipClient:    tt.fields.eipClient,
				stsClient:    tt.fields.stsClient,
			}

			got, err := r.IaasReleased(tt.args.ctx, tt.args.obj, tt.args.accountID)
			if err != nil {
				if tt.wantErr {
					return
				}

				t.Errorf("Ingress.IaasReleased() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if tt.wantErr {
				t.Errorf("Ingress.IaasReleased() failed, wantErr=true got=false")
				return
			}

			if got != tt.want {
				t.Errorf("Ingress.IaasReleased() = %v, want %v", got, tt.want)
			}
		})
	}
}
