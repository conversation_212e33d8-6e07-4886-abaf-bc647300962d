// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2018/12/25 19:30:00, by <EMAIL>, create
*/
/*
DESCRIPTION
本文件定义了 pv 对 Interface 接口的实现
*/

package resource

import (
	"context"

	runtime "k8s.io/apimachinery/pkg/runtime"
	"k8s.io/client-go/kubernetes"
)

var _ Interface = &PV{}

// PV 实现 resource.Interface
type PV struct{}

// NewPVClient 初始化 PV
func NewPVClient(ctx context.Context, clientset kubernetes.Interface) (Interface, error) {
	return &PV{}, nil
}

// ListResources - 返回 namespace 下所有 资源对象
//
// PARAMS:
//   - ctx: The context to trace request
//   - namespace: string
//
// RETURNS:
//
//	[]runtime.Object: []*v1.PersistVolume
//	error: nil if succeed, error if fail
func (r *PV) ListResources(ctx context.Context, ns string) ([]runtime.Object, error) {
	return []runtime.Object{}, nil
}

// GetResourceMeta - 返回对象的 namespace, name
//
// PARAMS:
//   - ctx: The context to trace request
//
// RETURNS:
//
//	string: namespace
//	string: name
//	error: nil if succeed, error if fail
func (r *PV) GetResourceMeta(ctx context.Context, obj runtime.Object) (string, string, error) {
	return "", "", nil
}

// DeleteK8SResource - 删除对象
//
// PARAMS:
//   - ctx: The context to trace request
//   - ns: namespace
//   - name: name
//
// RETURNS:
//
//	error: nil if succeed, error if fail
func (r *PV) DeleteK8SResource(ctx context.Context, ns, name string) error {
	return nil
}

// IaasReleased - 检查资源对应 IaaS 是否释放成功
//
// PARAMS:
//   - ctx: The context to trace request
//
// RETURNS:
//
//	bool: true = 释放成功
//	error: nil if succeed, error if fail
func (r *PV) IaasReleased(ctx context.Context, obj runtime.Object, accountID string) (bool, error) {
	return true, nil
}
