// Copyright 2021 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2021/11/06 21:00:00, by yezi<PERSON><EMAIL>, create
*/
/*
DESCRIPTION
本文件定义了 volumeattachment 对 Interface 接口的实现
*/

package resource

import (
	"bytes"
	"context"
	"errors"
	"time"

	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	runtime "k8s.io/apimachinery/pkg/runtime"
	"k8s.io/client-go/kubernetes"
	"k8s.io/kubectl/pkg/drain"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	"icode.baidu.com/baidu/cce-test/e2e-test/services/cluster/cluster-controller/provider"
)

// NodeVolumeAttachment 实现 resource.Interface
type NodeVolumeAttachment struct {
	clientset kubernetes.Interface
}

// NewNodeVolumeAttachmentClient 初始化 NodeVolumeAttachmentClient
func NewNodeVolumeAttachmentClient(ctx context.Context, clientset kubernetes.Interface, config *provider.Config) (Interface, error) {
	return &NodeVolumeAttachment{
		clientset: clientset,
	}, nil
}

func (nva *NodeVolumeAttachment) ListResources(ctx context.Context, ns string) ([]runtime.Object, error) {
	return nil, errors.New("not implemented")
}

func (nva *NodeVolumeAttachment) GetResourceMeta(ctx context.Context, obj runtime.Object) (string, string, error) {
	return "", "", errors.New("not implemented")
}

func (nva *NodeVolumeAttachment) DeleteK8SResource(ctx context.Context, ns, name string) error {
	node, err := nva.clientset.CoreV1().Nodes().Get(ctx, name, metav1.GetOptions{})
	if err != nil {
		logger.Errorf(ctx, "fail to get nodes %s: %v", name)
		return err
	}

	stdout, stderr := new(bytes.Buffer), new(bytes.Buffer)
	drainer := &drain.Helper{
		Ctx:                 ctx,
		Client:              nva.clientset,
		GracePeriodSeconds:  -1,
		Out:                 stdout,
		ErrOut:              stderr,
		IgnoreAllDaemonSets: true,
		DeleteLocalData:     true,
		Force:               true,
		Timeout:             60 * time.Second,
	}

	if err := drain.RunCordonOrUncordon(drainer, node, true); err != nil {
		logger.Errorf(ctx, "RunCordonOrUncordon on %s failed: %v, stdout=%s, stderr=%s", name, err, stdout.String(), stderr.String())
		return err
	}

	if err := drain.RunNodeDrain(drainer, name); err != nil {
		logger.Errorf(ctx, "RunNodeDrain on %s err: %v, stdout=%s, stderr=%s", name, err, stdout.String(), stderr.String())
		return err
	}

	logger.Infof(ctx, "Cordon and drain node %s successfully, stdout=%s, stderr=%s", name, stdout.String(), stderr.String())

	return nil
}

func (nva *NodeVolumeAttachment) IaasReleased(ctx context.Context, obj runtime.Object, accountID string) (bool, error) {
	if obj == nil {
		return false, errors.New("IaasReleased failed: obj is nil")
	}

	node, ok := obj.(*corev1.Node)
	if !ok {
		return false, errors.New("IaasReleased failed: obj is not *corev1.Node")
	}

	// NOTE: node.Status.VolumesAttached is not reliable to determin if
	// all volumes have been detached, and even not the fast path.
	// That is, items may be left in node.Status.VolumesAttached after volume detachment,
	// or node.Status.VolumesAttached comes empty while detach action is still running.
	// TODO: find a fast path.
	volumeattachments, err := nva.clientset.StorageV1().VolumeAttachments().List(ctx, metav1.ListOptions{})
	if err != nil {
		logger.Errorf(ctx, "fail to get volumeattachments: %v", err)
		return false, err
	}
	for _, va := range volumeattachments.Items {
		if va.Spec.NodeName == node.GetName() {
			logger.Warnf(ctx, "volumeattachment %s: spec=%v attachment=%v is still present for node %s", va.GetName(), va.Spec, va.Status.AttachmentMetadata, node.GetName())
			return false, nil
		}
	}

	return true, nil
}
