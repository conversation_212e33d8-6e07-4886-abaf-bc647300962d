// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2019/12/04 15:15:00, by <EMAIL>, create
*/
/*
该包定义了针对用户 K8S 集群的 Resource 操作
*/

package resource

import (
	"context"
	"fmt"

	runtime "k8s.io/apimachinery/pkg/runtime"
	"k8s.io/client-go/kubernetes"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	"icode.baidu.com/baidu/cce-test/e2e-test/services/cluster/cluster-controller/provider"
)

// K8SResourceType CCE 集群回收资源类型
type K8SResourceType string

const (
	// K8SResourceTypeIngress 回收 ingress
	K8SResourceTypeIngress K8SResourceType = "ingress"

	// K8SResourceTypeService 回收 service
	K8SResourceTypeService K8SResourceType = "service"

	// K8SResourceTypePV 回收 PV
	K8SResourceTypePV K8SResourceType = "pv"

	// K8SResourceTypeNode 回收 Node
	K8SResourceTypeNode K8SResourceType = "node"

	// K8SResourceTypeNode 回收 NodeVolumeAttachment
	K8SResourceTypeNodeVolumeAttachment K8SResourceType = "nodeVolumeattachment"

	// K8SResourceTypeDeployment 删除deployment从而回收serverless集群的pod
	K8SResourceTypeDeployment K8SResourceType = "deployment"
)

//go:generate mockgen -destination=./mock/mock_client.go -package=mock icode.baidu.com/baidu/cce-test/e2e-test/pkg/k8s/resource Interface

// Interface 定义用户集群 K8S 资源删除依赖方法
// 新增一种资源需要实现对应的 Interface
type Interface interface {
	ListResources(ctx context.Context, ns string) ([]runtime.Object, error)
	GetResourceMeta(ctx context.Context, obj runtime.Object) (string, string, error)
	DeleteK8SResource(ctx context.Context, ns, name string) error
	IaasReleased(ctx context.Context, obj runtime.Object, accountID string) (bool, error)
}

// NewClient 根据资源类似返回对应 resource.Interface 实现
func NewClient(ctx context.Context, clientset kubernetes.Interface, resourceType K8SResourceType, config *provider.Config) (Interface, error) {
	switch resourceType {
	case K8SResourceTypeIngress:
		return NewIngressClient(ctx, clientset, config)
	case K8SResourceTypeService:
		return NewServiceClient(ctx, clientset, config)
	case K8SResourceTypePV:
		return NewPVClient(ctx, clientset)
	case K8SResourceTypeNode:
		return NewNodeClient(ctx, clientset)
	case K8SResourceTypeDeployment:
		return NewDeploymentClient(ctx, clientset)
	case K8SResourceTypeNodeVolumeAttachment:
		return NewNodeVolumeAttachmentClient(ctx, clientset, config)
	default:
		msg := fmt.Sprintf("uknown resource type: %v", resourceType)
		logger.Warnf(ctx, msg)
		return nil, fmt.Errorf(msg)
	}
}

// K8SResourceTypes - 返回需要清理的 K8S Resource 类型
//
// PARAMS:
//   - ctx: The context to trace request
//
// RETURNS:
//
//	[]K8SResourceType
func K8SResourceTypes(ctx context.Context) []K8SResourceType {
	// 注意 Node 不要放到里面 !!!
	return []K8SResourceType{
		K8SResourceTypeIngress,
		K8SResourceTypeService,
		K8SResourceTypeDeployment,

		// 暂时不删除 PV
		// K8SResourceTypePV
	}
}
