// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2018/12/25 19:30:00, by <EMAIL>, create
*/
/*
DESCRIPTION
Node 实现 resource.Interface
*/

package resource

import (
	"context"
	"fmt"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	runtime "k8s.io/apimachinery/pkg/runtime"
	"k8s.io/client-go/kubernetes"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
)

var _ Interface = &Node{}

// Node 实现 resource.Interface
type Node struct {
	clientset kubernetes.Interface
}

// NewNodeClient 初始化 Node
func NewNodeClient(ctx context.Context, clientset kubernetes.Interface) (Interface, error) {
	return &Node{
		clientset: clientset,
	}, nil
}

// ListResources - 返回 namespace 下所有 资源对象
//
// PARAMS:
//   - ctx: The context to trace request
//   - namespace: string
//
// RETURNS:
//
//	[]runtime.Object: []*v1.PersistVolume
//	error: nil if succeed, error if fail
func (r *Node) ListResources(ctx context.Context, ns string) ([]runtime.Object, error) {
	return []runtime.Object{}, nil
}

// GetResourceMeta - 返回对象的 namespace, name
//
// PARAMS:
//   - ctx: The context to trace request
//
// RETURNS:
//
//	string: namespace
//	string: name
//	error: nil if succeed, error if fail
func (r *Node) GetResourceMeta(ctx context.Context, obj runtime.Object) (string, string, error) {
	return "", "", nil
}

// DeleteK8SResource - 删除对象
//
// PARAMS:
//   - ctx: The context to trace request
//   - ns: namespace
//   - name: name
//
// RETURNS:
//
//	error: nil if succeed, error if fail
func (r *Node) DeleteK8SResource(ctx context.Context, ns, name string) error {
	if err := r.clientset.CoreV1().Nodes().Delete(ctx, name, metav1.DeleteOptions{}); err != nil {
		logger.Errorf(ctx, "Delete node %v failed: %v", name, err)
		return err
	}

	// 删除node之后,直接删除该节点上的cni-node-agent pod
	// 否则，在短时间内节点重新加入集群，cni-node-agent pod还没有来得及删除，会导致找不到cni目录节点notready
	// 不阻塞节点删除
	if err := r.deleteCNINodeAgentPod(ctx, ns, name); err != nil {
		logger.Errorf(ctx, "DeleteCNINodeAgent failed: %v", err)
	}

	// 删除 kubelet 动态配置
	configmap := fmt.Sprintf("node-config-%s", name)
	if err := r.clientset.CoreV1().ConfigMaps("kube-system").Delete(ctx, configmap, metav1.DeleteOptions{}); err != nil {
		logger.Errorf(ctx, "Delete node configmap kube-system/%v failed: %v", configmap, err)
		return err
	}

	return nil
}

// IaasReleased - 检查资源对应 IaaS 是否释放成功
//
// PARAMS:
//   - ctx: The context to trace request
//
// RETURNS:
//
//	bool: true = 释放成功
//	error: nil if succeed, error if fail
func (r *Node) IaasReleased(ctx context.Context, obj runtime.Object, accountID string) (bool, error) {
	return true, nil
}

// 删除节点上的CNINodeAgent pod
func (r *Node) deleteCNINodeAgentPod(ctx context.Context, ns, name string) error {
	// 删除node上的cce-cni-node-agent Pod
	labelSelector := metav1.LabelSelector{
		MatchLabels: map[string]string{"name": "cce-cni-node-agent"},
	}
	fieldSelector := fmt.Sprintf("spec.nodeName=%s", name)
	pods, err := r.clientset.CoreV1().Pods("kube-system").List(ctx, metav1.ListOptions{
		FieldSelector: fieldSelector,
		LabelSelector: metav1.FormatLabelSelector(&labelSelector),
	})
	if err != nil {
		logger.Errorf(ctx, "get spec.nodeName=%s pods fail : %v", name, err)
		return err
	}

	// ccm的集群没有cce-cni-node-agent，或者移出时如果删除较快，此时容器网络的agent也可能已经删掉了
	// 没有找到cce-cni-node-agent 就直接跳过
	if len(pods.Items) == 0 {
		return nil
	}

	for _, pod := range pods.Items {
		if pod.Spec.NodeName == name {
			logger.Infof(ctx, "pod.Spec.NodeName = %s ,deleting Pod: %s", name, pod.Name)
			err := r.clientset.CoreV1().Pods("kube-system").Delete(ctx, pod.Name, metav1.DeleteOptions{})
			if err != nil {
				logger.Errorf(ctx, "delete pod.name = %s fail : %v", pod.Name, err)
				return err
			}
		}
	}
	return nil
}
