// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2018/12/25 19:30:00, by <EMAIL>, create
*/
/*
DESCRIPTION
本文件定义了 ingress 对 Interface 接口的实现
*/

package resource

import (
	"context"
	"errors"
	"fmt"
	"net"
	"regexp"
	"strings"
	"time"

	"k8s.io/api/extensions/v1beta1"
	networkingv1 "k8s.io/api/networking/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/client-go/kubernetes"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/appblb"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/eip"
	eipsdk "icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/eip"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/retrypolicy"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/sts"
	"icode.baidu.com/baidu/cce-test/e2e-test/services/cluster/cluster-controller/provider"
	"icode.baidu.com/baidu/cce-test/e2e-test/services/network/ingress-controller/controller"
)

// Ingress Annotation
const (
	// ingressAnnotationClass Ingress Class
	ingressAnnotationClass = "kubernetes.io/ingress.class"

	// ingressAnnotationHandler only handler kubernetes.io/ingress.class=cce
	ingressAnnotationHandler = "cce"

	// ingressAnnotationBLBShortID Ingress's associated app blbShortID
	ingressAnnotationBLBShortID = "kubernetes.io/cce.ingress.blb-id"

	// ingressAnnotationEIP Ingress's associated EIP
	ingressAnnotationEIP = "kubernetes.io/cce.ingress.eip"
)

var _ Interface = &Ingress{}

// Ingress 实现 resource.Interface
type Ingress struct {
	clientset        kubernetes.Interface
	appblbClient     appblb.Interface
	eipClient        eipsdk.Interface
	stsClient        sts.Interface
	ingressConverter IngressStoreConverter
}

// NewIngressClient 返回 &Ingress
func NewIngressClient(ctx context.Context, clientset kubernetes.Interface, config *provider.Config) (Interface, error) {
	if clientset == nil {
		return nil, errors.New("NewIngressClient failed: clientset is nil")
	}

	if config == nil {
		return nil, errors.New("config is nil")
	}

	retryPolicy := retrypolicy.NewIntervalRetryPolicy(ctx, config.MaxRetryTime, config.RetryIntervalSeconds)

	// 初始化 AppBLB Client
	appblbClient := appblb.NewClient(&bce.Config{
		Checksum:    true,
		Timeout:     config.Timeout * time.Second,
		Region:      config.Region,
		Endpoint:    config.AppBLBOpenAPIEndpoint,
		RetryPolicy: retryPolicy,
	})

	// 初始化 EIP Client
	eipClient := eip.NewClient(&bce.Config{
		Checksum:    true,
		Timeout:     config.Timeout * time.Second,
		Region:      config.Region,
		Endpoint:    config.EIPOpenAPIEndpoint,
		RetryPolicy: retryPolicy,
	})

	// 初始化 STS Client
	stsClient := sts.NewClient(ctx, &bce.Config{
		Endpoint:    config.STSEndpoint,
		Checksum:    true,
		Timeout:     config.Timeout * time.Second,
		Region:      config.Region,
		RetryPolicy: retryPolicy,
	}, &bce.Config{
		Endpoint:    config.IAMEndpoint,
		Checksum:    true,
		Timeout:     config.Timeout * time.Second,
		Region:      config.Region,
		RetryPolicy: retryPolicy,
	}, config.ServiceRoleName, config.ServiceName, config.ServicePassword)

	ingressConverter, err := NewIngressStore(clientset)
	if err != nil {
		logger.Errorf(ctx, "Fail to to new ingress converter: %v", err)
		return nil, err
	}

	return &Ingress{
		clientset:        clientset,
		stsClient:        stsClient,
		appblbClient:     appblbClient,
		eipClient:        eipClient,
		ingressConverter: ingressConverter,
	}, nil
}

// ListResources - 返回 namespace 下所有 资源对象
//
// PARAMS:
//   - ctx: The context to trace request
//   - namespace: string
//
// RETURNS:
//
//	[]runtime.Object: []*v1beta1.Ingress
//	error: nil if succeed, error if fail
func (r *Ingress) ListResources(ctx context.Context, namespace string) ([]runtime.Object, error) {
	ingresses := []runtime.Object{}

	ingressList, err := r.ingressConverter.ListIngressesObjs(ctx, namespace)
	if err != nil {
		logger.Errorf(ctx, "listClusterResources ingress in %v failed: %v", namespace, err)
		return ingresses, err
	}
	if ingressList == nil {
		logger.Errorf(ctx, "listClusterResources ingress in %v is nil", namespace)
		return ingresses, nil
	}

	for _, ingress := range ingressList {
		tmp := ingress
		ingresses = append(ingresses, tmp)
	}

	return ingresses, nil
}

// GetResourceMeta - 返回对象的 namespace, name
//
// PARAMS:
//   - ctx: The context to trace request
//
// RETURNS:
//
//	string: namespace
//	string: name
//	error: nil if succeed, error if fail
func (r *Ingress) GetResourceMeta(ctx context.Context, obj runtime.Object) (string, string, error) {
	var ns string
	var name string

	if obj == nil {
		return ns, name, errors.New("GetResourceMeta failed: obj is nil")
	}

	ingress, err := r.ingressConverter.ConvertToIngress(ctx, obj)
	if err != nil {
		return "", "", fmt.Errorf("ingress is not Ingress: %v", err)
	}

	ns = ingress.GetNamespace()
	name = ingress.GetName()

	return ns, name, nil
}

// DeleteK8SResource - 删除对象
//
// PARAMS:
//   - ctx: The context to trace request
//   - ns: namespace
//   - name: name
//
// RETURNS:
//
//	error: nil if succeed, error if fail
func (r *Ingress) DeleteK8SResource(ctx context.Context, ns, name string) error {
	// 检查 Ingress 是否存在
	ingress, err := r.ingressConverter.GetIngress(ctx, ns, name)
	if err != nil {
		logger.Errorf(ctx, "Get ingress %v/%v failed: %v", ns, name, err)
		return err
	}

	if ingress == nil {
		logger.Infof(ctx, "Ingress %v/%v is nil, skip deleted", ns, name)
		return nil
	}

	// 检查 Ingress 是否由 CCE 托管
	if !handledByCCEIngress(ctx, ingress) {
		logger.Infof(ctx, "Ingress %v/%v not handle by CCE, skip delete resource", ns, name)
		return nil
	}

	// 删除 ingress
	if err := r.ingressConverter.DeleteIngress(ctx, ns, name); err != nil {
		logger.Errorf(ctx, "Delete ingress %v/%v failed: %v", ns, name, err)
		return err
	}

	return nil
}

// IaasReleased - 检查资源对应 IaaS 是否释放成功
//
// PARAMS:
//   - ctx: The context to trace request
//
// RETURNS:
//
//	bool: true = 释放成功
//	error: nil if succeed, error if fail
func (r *Ingress) IaasReleased(ctx context.Context, obj runtime.Object, accountID string) (bool, error) {
	if obj == nil {
		return false, errors.New("IaasReleased failed: obj is nil")
	}

	var err error
	ingress, ok := obj.(*v1beta1.Ingress)
	if !ok {
		logger.Infof(ctx, "object is not v1beta1 ingress, will try to convert to networking.v1 ingress")
		networkingV1Ingress, ok := obj.(*networkingv1.Ingress)
		if !ok {
			logger.Errorf(ctx, "fail to convert object to networking.v1 ingress :%v", obj)
			return false, errors.New("fail to convert object to networking.v1 ingress")
		}
		ingress, err = controller.ConvertNetworkingV1IngressToExtensionsV1beta1(networkingV1Ingress)
		if err != nil {
			logger.Errorf(ctx, "fail to ConvertNetworkingV1IngressToExtensionsV1beta1 %v: %+v", ingress, err)
			return false, err
		}
	}
	ns := ingress.GetNamespace()
	name := ingress.GetName()

	// 如果 Ingress 不是由 CCE 负责就跳过 IaaS 资源释放检查
	if !handledByCCEIngress(ctx, ingress) {
		logger.Infof(ctx, "Ingress %v/%v not handle by CCE, skip check iaas released", ns, name)
		return true, nil
	}

	// 检查对应 BLB 是否释放
	blbExists := true
	if blbID, set := getIngressBLBID(ctx, ingress); set {
		logger.Infof(ctx, "Ingress %v/%v BLBID: %v", ns, name, blbID)

		blb, err := r.appblbClient.DescribeAppLoadBalancerByID(ctx, blbID, r.stsClient.NewSignOption(ctx, accountID))

		// BLB 不存在
		if err != nil && strings.Contains(err.Error(), "NoSuchObject") {
			logger.Infof(ctx, "Ingress %v/%v associated AppBLB %v deleted success", ns, name, blbID)
			blbExists = false
		}

		//  报错
		if err != nil && !strings.Contains(err.Error(), "NoSuchObject") {
			logger.Errorf(ctx, "Get Ingress %v/%v associated AppBLB %v failed: %v", ns, name, blbID, err)
			return false, err
		}

		// BLB 还存在
		if err == nil && blb != nil {
			logger.Infof(ctx, "Get Ingress %v/%v associated AppBLB %v still exist", ns, name, blbID)
		}

		// BLB 不存在, 实际上本情况不会发生
		if err == nil && blb == nil {
			logger.Infof(ctx, "Ingress %v/%v associated AppBLB %v deleted success", ns, name, blbID)
			blbExists = false
		}
	} else {
		blbExists = false
	}

	// 检查对应 EIP 是否释放
	eipExists := true
	if eip, set := getIngressEIP(ctx, ingress); set {
		eips, err := r.eipClient.GetEIPs(ctx, &eipsdk.GetEIPsArgs{EIP: eip}, r.stsClient.NewSignOption(ctx, accountID))
		// 报错
		if err != nil {
			logger.Errorf(ctx, "Get Ingress %v/%v associated EIP %v failed: %v", ns, name, eip, err)
			return false, err
		}

		// EIP 不存在
		if len(eips) == 0 {
			logger.Infof(ctx, "Ingress %v/%v associated EIP %v deleted success", ns, name, eip)
			eipExists = false
		}

		// EIP 还存在
		if len(eips) >= 1 {
			logger.Infof(ctx, "Ingress %v/%v associated EIP %v still exist ", ns, name, eip)
		}
	} else {
		eipExists = false
	}

	if !blbExists && !eipExists {
		return true, nil
	}

	return false, nil
}

// HandledByCCEIngress check annotation "kubernetes.io/ingress.class" == "cce"
func handledByCCEIngress(ctx context.Context, ingress *v1beta1.Ingress) bool {
	if ingress == nil {
		logger.Errorf(ctx, "HandledByCCEIngress failed: ingress is nil")
		return false
	}

	if class, ok := ingress.Annotations[ingressAnnotationClass]; ok {
		if class == ingressAnnotationHandler {
			return true
		}
	}

	return false
}

// GetIngressBLBID - Is BLBID setted: kubernetes.io/cce.ingress.blb-id
func getIngressBLBID(ctx context.Context, ingress *v1beta1.Ingress) (string, bool) {
	if ingress == nil {
		logger.Errorf(ctx, "GetIngressBLBID failed: ingress is nil")
		return "", false
	}

	blbID, ok := ingress.ObjectMeta.Annotations[ingressAnnotationBLBShortID]
	if ok {
		reg := regexp.MustCompile(`lb-.+`)
		if !reg.MatchString(blbID) {
			logger.Errorf(ctx, "GetIngressBLBID failed: %s format not match", blbID)
			return "", false
		}

		return blbID, true
	}

	return "", false
}

// GetIngressEIP - If EIP not exist return ("", nil)
func getIngressEIP(ctx context.Context, ingress *v1beta1.Ingress) (string, bool) {
	if ingress == nil {
		logger.Errorf(ctx, "GetIngressEIP failed: ingress is nil")
		return "", false
	}

	// Get EIP From ingress.ObjectMeta.Annotations[AnnotationEIP]
	eip, ok := ingress.ObjectMeta.Annotations[ingressAnnotationEIP]

	if ok && eip != "" {
		if isExternalIP(eip) {
			return eip, true
		}

		// TODO: In sandbox EIP in 10.0.0.0/8
		// return eip, true
	}

	// Get EIP From ingress.Status.LoadBalancer.Ingress
	for _, i := range ingress.Status.LoadBalancer.Ingress {
		ip := i.IP
		if isExternalIP(ip) {
			return ip, true
		}
	}

	return "", false
}

// IsExternalIP to check if IP is a External IP
// When IP not in following Net:
// 10.0.0.0/8
// **********/12
// ***********/16
func isExternalIP(ip string) bool {
	// check if ip match format
	isMatch, err := regexp.MatchString("[\\d]+\\.[\\d]+\\.[\\d]+\\.[\\d]+", ip)
	if err != nil || !isMatch {
		return false
	}

	// check if ip is external
	privateNetList := []net.IPNet{
		{
			IP:   net.IP([]byte{10, 0, 0, 0}),
			Mask: net.IPMask([]byte{255, 0, 0, 0}),
		},
		{
			IP:   net.IP([]byte{172, 16, 0, 0}),
			Mask: net.IPMask([]byte{255, 240, 0, 0}),
		},
		{
			IP:   net.IP([]byte{192, 168, 0, 0}),
			Mask: net.IPMask([]byte{255, 255, 0, 0}),
		},
	}

	for _, privateNet := range privateNetList {
		if privateNet.Contains(net.ParseIP(ip)) {
			return false
		}
	}

	return true
}
