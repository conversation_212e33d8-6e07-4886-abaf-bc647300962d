// Code generated by MockGen. DO NOT EDIT.
// Source: icode.baidu.com/baidu/cce-test/e2e-test/pkg/k8s/resource (interfaces: Interface)

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	gomock "github.com/golang/mock/gomock"
	runtime "k8s.io/apimachinery/pkg/runtime"
	reflect "reflect"
)

// MockInterface is a mock of Interface interface
type MockInterface struct {
	ctrl     *gomock.Controller
	recorder *MockInterfaceMockRecorder
}

// MockInterfaceMockRecorder is the mock recorder for MockInterface
type MockInterfaceMockRecorder struct {
	mock *MockInterface
}

// NewMockInterface creates a new mock instance
func NewMockInterface(ctrl *gomock.Controller) *MockInterface {
	mock := &MockInterface{ctrl: ctrl}
	mock.recorder = &MockInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use
func (m *MockInterface) EXPECT() *MockInterfaceMockRecorder {
	return m.recorder
}

// DeleteK8SResource mocks base method
func (m *MockInterface) DeleteK8SResource(arg0 context.Context, arg1, arg2 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteK8SResource", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteK8SResource indicates an expected call of DeleteK8SResource
func (mr *MockInterfaceMockRecorder) DeleteK8SResource(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteK8SResource", reflect.TypeOf((*MockInterface)(nil).DeleteK8SResource), arg0, arg1, arg2)
}

// GetResourceMeta mocks base method
func (m *MockInterface) GetResourceMeta(arg0 context.Context, arg1 runtime.Object) (string, string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetResourceMeta", arg0, arg1)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(string)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetResourceMeta indicates an expected call of GetResourceMeta
func (mr *MockInterfaceMockRecorder) GetResourceMeta(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetResourceMeta", reflect.TypeOf((*MockInterface)(nil).GetResourceMeta), arg0, arg1)
}

// IaasReleased mocks base method
func (m *MockInterface) IaasReleased(arg0 context.Context, arg1 runtime.Object, arg2 string) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IaasReleased", arg0, arg1, arg2)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IaasReleased indicates an expected call of IaasReleased
func (mr *MockInterfaceMockRecorder) IaasReleased(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IaasReleased", reflect.TypeOf((*MockInterface)(nil).IaasReleased), arg0, arg1, arg2)
}

// ListResources mocks base method
func (m *MockInterface) ListResources(arg0 context.Context, arg1 string) ([]runtime.Object, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListResources", arg0, arg1)
	ret0, _ := ret[0].([]runtime.Object)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListResources indicates an expected call of ListResources
func (mr *MockInterfaceMockRecorder) ListResources(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListResources", reflect.TypeOf((*MockInterface)(nil).ListResources), arg0, arg1)
}
