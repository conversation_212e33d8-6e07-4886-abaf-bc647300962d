package resource

import (
	"context"
	"strconv"
	"testing"

	"k8s.io/api/extensions/v1beta1"
	networkingv1 "k8s.io/api/networking/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	discoveryVersion "k8s.io/apimachinery/pkg/version"
	discoveryfake "k8s.io/client-go/discovery/fake"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/kubernetes/fake"
)

func Test_NewIngressStore(t *testing.T) {
	cases := []struct {
		name      string
		k8sClient kubernetes.Interface
		expectErr bool
	}{
		{
			name: "illegal k8s client",
			k8sClient: func() kubernetes.Interface {
				client := fake.NewSimpleClientset()
				client.Discovery().(*discoveryfake.FakeDiscovery).FakedServerVersion = &discoveryVersion.Info{
					GitVersion: "vasdasdasd0",
				}
				return client
			}(),
			expectErr: true,
		},
		{
			name: "normal v1",
			k8sClient: func() kubernetes.Interface {
				client := fake.NewSimpleClientset()
				client.Discovery().(*discoveryfake.FakeDiscovery).FakedServerVersion = &discoveryVersion.Info{
					GitVersion: "v1.22.0",
				}
				return client
			}(),
			expectErr: false,
		},
		{
			name: "normal v1beta1",
			k8sClient: func() kubernetes.Interface {
				client := fake.NewSimpleClientset()
				client.Discovery().(*discoveryfake.FakeDiscovery).FakedServerVersion = &discoveryVersion.Info{
					GitVersion: "v1.18.0",
				}
				return client
			}(),
			expectErr: false,
		},
	}
	for _, tt := range cases {
		t.Run(tt.name, func(t *testing.T) {
			_, err := NewIngressStore(tt.k8sClient)
			if (err != nil) != tt.expectErr {
				t.Errorf("Case %s NewIngressStore should expect err %s but actual not", tt.name, strconv.FormatBool(tt.expectErr))
			}
		})
	}
}

func Test_ConvertToIngress(t *testing.T) {
	cases := []struct {
		name      string
		store     *IngressStore
		obj       runtime.Object
		expectErr bool
	}{
		{
			name: "normal extensions v1",
			store: &IngressStore{
				groupType: IngressGroupTypeExtensionsV1beta1,
			},
			obj:       &v1beta1.Ingress{},
			expectErr: false,
		},
		{
			name: "error extensions v1",
			store: &IngressStore{
				groupType: IngressGroupTypeExtensionsV1beta1,
			},
			obj:       &networkingv1.Ingress{},
			expectErr: true,
		},
		{
			name: "normal networking v1",
			store: &IngressStore{
				groupType: IngressGroupTypeNetworkingV1,
			},
			obj: &networkingv1.Ingress{},

			expectErr: false,
		},
		{
			name: "error networking v1",
			store: &IngressStore{
				groupType: IngressGroupTypeNetworkingV1,
			},
			obj:       &v1beta1.Ingress{},
			expectErr: true,
		},
	}
	for _, tt := range cases {
		t.Run(tt.name, func(t *testing.T) {
			_, err := tt.store.ConvertToIngress(context.TODO(), tt.obj)
			if (err != nil) != tt.expectErr {
				t.Errorf("Case %s ConvertToIngress should expect err %s but actual not", tt.name, strconv.FormatBool(tt.expectErr))
			}
		})
	}
}

func Test_GetIngress(t *testing.T) {
	cases := []struct {
		name      string
		store     *IngressStore
		expectErr bool
	}{
		{
			name: "normal extensions v1",
			store: &IngressStore{
				groupType: IngressGroupTypeExtensionsV1beta1,
				kubeClient: func() kubernetes.Interface {
					client := fake.NewSimpleClientset()
					client.ExtensionsV1beta1().Ingresses("default").Create(context.TODO(), &v1beta1.Ingress{
						ObjectMeta: metav1.ObjectMeta{
							Name: "cce",
						},
					}, metav1.CreateOptions{})
					client.ExtensionsV1beta1().Ingresses("default").Create(context.TODO(), &v1beta1.Ingress{
						ObjectMeta: metav1.ObjectMeta{
							Name: "cce2",
						},
					}, metav1.CreateOptions{})
					return client
				}(),
			},
			expectErr: false,
		},
		{
			name: "error extensions v1",
			store: &IngressStore{
				groupType: IngressGroupTypeExtensionsV1beta1,
				kubeClient: func() kubernetes.Interface {
					client := fake.NewSimpleClientset()
					return client
				}(),
			},
			expectErr: true,
		},
		{
			name: "normal networking v1",
			store: &IngressStore{
				groupType: IngressGroupTypeNetworkingV1,
				kubeClient: func() kubernetes.Interface {
					client := fake.NewSimpleClientset()
					client.NetworkingV1().Ingresses("default").Create(context.TODO(), &networkingv1.Ingress{
						ObjectMeta: metav1.ObjectMeta{
							Name: "cce",
						},
					}, metav1.CreateOptions{})
					client.NetworkingV1().Ingresses("default").Create(context.TODO(), &networkingv1.Ingress{
						ObjectMeta: metav1.ObjectMeta{
							Name: "cce2",
						},
					}, metav1.CreateOptions{})
					return client
				}(),
			},
			expectErr: false,
		},
		{
			name: "error networking v1",
			store: &IngressStore{
				groupType: IngressGroupTypeNetworkingV1,
				kubeClient: func() kubernetes.Interface {
					client := fake.NewSimpleClientset()
					return client
				}(),
			},
			expectErr: true,
		},
	}
	for _, tt := range cases {
		t.Run(tt.name, func(t *testing.T) {
			_, err := tt.store.GetIngress(context.TODO(), "default", "cce2")
			if (err != nil) != tt.expectErr {
				t.Errorf("Case %s GetIngress should expect err %s but actual not", tt.name, strconv.FormatBool(tt.expectErr))
			}
		})
	}
}

func Test_ListIngressesObjs(t *testing.T) {
	cases := []struct {
		name      string
		store     *IngressStore
		expectErr bool
	}{
		{
			name: "normal extensions v1",
			store: &IngressStore{
				groupType: IngressGroupTypeExtensionsV1beta1,
				kubeClient: func() kubernetes.Interface {
					client := fake.NewSimpleClientset()
					client.ExtensionsV1beta1().Ingresses("default").Create(context.TODO(), &v1beta1.Ingress{
						ObjectMeta: metav1.ObjectMeta{
							Name: "cce",
						},
					}, metav1.CreateOptions{})
					client.ExtensionsV1beta1().Ingresses("default").Create(context.TODO(), &v1beta1.Ingress{
						ObjectMeta: metav1.ObjectMeta{
							Name: "cce2",
						},
					}, metav1.CreateOptions{})
					return client
				}(),
			},
			expectErr: false,
		},
		{
			name: "normal networking v1",
			store: &IngressStore{
				groupType: IngressGroupTypeNetworkingV1,
				kubeClient: func() kubernetes.Interface {
					client := fake.NewSimpleClientset()
					client.NetworkingV1().Ingresses("default").Create(context.TODO(), &networkingv1.Ingress{
						ObjectMeta: metav1.ObjectMeta{
							Name: "cce",
						},
					}, metav1.CreateOptions{})
					client.NetworkingV1().Ingresses("default").Create(context.TODO(), &networkingv1.Ingress{
						ObjectMeta: metav1.ObjectMeta{
							Name: "cce2",
						},
					}, metav1.CreateOptions{})
					return client
				}(),
			},
			expectErr: false,
		},
	}
	for _, tt := range cases {
		t.Run(tt.name, func(t *testing.T) {
			_, err := tt.store.ListIngressesObjs(context.TODO(), "")
			if (err != nil) != tt.expectErr {
				t.Errorf("Case %s ListIngressesObjs should expect err %s but actual not", tt.name, strconv.FormatBool(tt.expectErr))
			}
		})
	}
}

func Test_DeleteIngress(t *testing.T) {
	cases := []struct {
		name      string
		store     *IngressStore
		expectErr bool
	}{
		{
			name: "normal extensions v1",
			store: &IngressStore{
				groupType: IngressGroupTypeExtensionsV1beta1,
				kubeClient: func() kubernetes.Interface {
					client := fake.NewSimpleClientset()
					client.ExtensionsV1beta1().Ingresses("default").Create(context.TODO(), &v1beta1.Ingress{
						ObjectMeta: metav1.ObjectMeta{
							Name: "cce",
						},
					}, metav1.CreateOptions{})
					return client
				}(),
			},
			expectErr: false,
		},
		{
			name: "normal networking v1",
			store: &IngressStore{
				groupType: IngressGroupTypeNetworkingV1,
				kubeClient: func() kubernetes.Interface {
					client := fake.NewSimpleClientset()
					client.NetworkingV1().Ingresses("default").Create(context.TODO(), &networkingv1.Ingress{
						ObjectMeta: metav1.ObjectMeta{
							Name: "cce",
						},
					}, metav1.CreateOptions{})
					return client
				}(),
			},
			expectErr: false,
		},
	}
	for _, tt := range cases {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.store.DeleteIngress(context.TODO(), "default", "cce")
			if (err != nil) != tt.expectErr {
				t.Errorf("Case %s DeleteIngress should expect err %s but actual not", tt.name, strconv.FormatBool(tt.expectErr))
			}
		})
	}
}

func Test_ConvertNetworkingV1IngressToExtensionsV1beta1(t *testing.T) {
	cases := []struct {
		name                string
		networkingv1Ingress *networkingv1.Ingress
		expectErr           bool
	}{
		{
			name:                "normal",
			networkingv1Ingress: &networkingv1.Ingress{},
			expectErr:           false,
		},
	}
	for _, tt := range cases {
		t.Run(tt.name, func(t *testing.T) {
			_, err := ConvertNetworkingV1IngressToExtensionsV1beta1(tt.networkingv1Ingress)
			if (err != nil) != tt.expectErr {
				t.Errorf("Case %s ConvertNetworkingV1IngressToExtensionsV1beta1 should expect err %s but actual not", tt.name, strconv.FormatBool(tt.expectErr))
			}
		})
	}
}
