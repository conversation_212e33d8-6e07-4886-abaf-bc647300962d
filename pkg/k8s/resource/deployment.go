/**
 * @Author: pansiyuan02
 * @Description:
 * @File:  deployment
 * @Version: 1.0.0
 * @Date: 2020/9/1 11:14 上午
 */
package resource

import (
	"context"
	"errors"

	appsv1 "k8s.io/api/apps/v1"
	k8sErrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/client-go/kubernetes"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
)

// 主要的目的是删除serverless集群的coreDNS pod，从而释放对应的bci pod。
type deployment struct {
	clientset kubernetes.Interface
}

func NewDeploymentClient(ctx context.Context, clientset kubernetes.Interface) (Interface, error) {
	if clientset == nil {
		return nil, errors.New("clientset is nil")
	}
	return &deployment{clientset: clientset}, nil
}

func (d *deployment) ListResources(ctx context.Context, ns string) ([]runtime.Object, error) {
	deployments := make([]runtime.Object, 0)

	list, err := d.clientset.AppsV1().Deployments(ns).List(ctx, metav1.ListOptions{})
	if err != nil {
		logger.WithValues("namespace", ns).Errorf(ctx, "listClusterResources deployment failed: %v", err)
		return deployments, err
	}

	for _, item := range list.Items {
		deployments = append(deployments, item.DeepCopy())
	}

	return deployments, nil
}

func (d *deployment) GetResourceMeta(ctx context.Context, obj runtime.Object) (string, string, error) {
	var ns string
	var name string

	if obj == nil {
		return ns, name, errors.New("GetResourceMeta failed: obj is nil")
	}

	deployment, ok := obj.(*appsv1.Deployment)
	if !ok {
		return "", "", errors.New("obj is not appsv1.Deployment")
	}

	ns = deployment.GetNamespace()
	name = deployment.GetName()

	return ns, name, nil
}

func (d *deployment) DeleteK8SResource(ctx context.Context, ns, name string) error {
	logger := logger.WithValues("deployment", ns+"/"+name)

	deleteFroeground := metav1.DeletePropagationForeground
	err := d.clientset.AppsV1().Deployments(ns).Delete(ctx, name, metav1.DeleteOptions{PropagationPolicy: &deleteFroeground})
	if err != nil {
		if k8sErrors.IsNotFound(err) {
			logger.Infof(ctx, "deployment already deleted")
			return nil
		}
		logger.Errorf(ctx, "delete deployment failed: %v", err)
		return err
	}
	return nil
}

func (d *deployment) IaasReleased(ctx context.Context, obj runtime.Object, accountID string) (bool, error) {
	// 对于deployment，检查它所关联的pod是否已经删除
	deployment, ok := obj.(*appsv1.Deployment)
	if !ok {
		return false, errors.New("obj is not appsv1.Deployment")
	}
	logger := logger.WithValues("deployment", deployment.Namespace+"/"+deployment.Name)

	selectorMap, err := metav1.LabelSelectorAsMap(deployment.Spec.Selector)
	if err != nil {
		logger.Errorf(ctx, "failed to convert label selector, err: %v", err)
		return false, err
	}
	list, err := d.clientset.CoreV1().Pods(deployment.GetNamespace()).List(ctx, metav1.ListOptions{
		LabelSelector: labels.SelectorFromSet(selectorMap).String(),
	})
	if err != nil {
		if k8sErrors.IsNotFound(err) {
			return true, nil
		}
		logger.Errorf(ctx, "failed to get pod belong to deployment, err: %v", err)
		return true, nil
	}

	if len(list.Items) > 0 {
		return false, nil
	}

	return true, nil
}
