// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2018/12/25 19:30:00, by <EMAIL>, create
*/
/*
DESCRIPTION
本文件定义了 service 对 Interface 接口的实现
*/

package resource

import (
	"context"
	"errors"
	"regexp"
	"strconv"
	"strings"
	"time"

	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	runtime "k8s.io/apimachinery/pkg/runtime"
	"k8s.io/client-go/kubernetes"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/appblb"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
	blbsdk "icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/blb"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/eip"
	eipsdk "icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/eip"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/retrypolicy"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/sts"
	"icode.baidu.com/baidu/cce-test/e2e-test/services/cluster/cluster-controller/provider"
	"icode.baidu.com/baidu/cce-test/e2e-test/services/network/lb-controller/common"
	appblb2 "icode.baidu.com/baidu/cce-test/e2e-test/services/network/service-controller/pkg/providers/appblb"
)

var _ Interface = &Service{}

// Service 实现 resource.Interface
type Service struct {
	clientset kubernetes.Interface
	blbClient blbsdk.Interface
	eipClient eipsdk.Interface
	stsClient sts.Interface

	appblbClient appblb.Interface
}

// NewServiceClient 初始化 Service
func NewServiceClient(ctx context.Context, clientset kubernetes.Interface, config *provider.Config) (Interface, error) {
	if clientset == nil {
		return nil, errors.New("clientset is nil")
	}

	if config == nil {
		return nil, errors.New("config is nil")
	}

	retryPolicy := retrypolicy.NewIntervalRetryPolicy(ctx, config.MaxRetryTime, config.RetryIntervalSeconds)

	// 初始化 BLB Client
	blbClient := blbsdk.NewClient(&bce.Config{
		Checksum:    true,
		Timeout:     config.Timeout * time.Second,
		Region:      config.Region,
		Endpoint:    config.BLBOpenAPIEndpoint,
		RetryPolicy: retryPolicy,
	})

	// 初始化 AppBLB Client
	appblbClient := appblb.NewClient(&bce.Config{
		Checksum:    true,
		Timeout:     config.Timeout * time.Second,
		Region:      config.Region,
		Endpoint:    config.AppBLBOpenAPIEndpoint,
		RetryPolicy: retryPolicy,
	})

	// 初始化 EIP Client
	eipClient := eip.NewClient(&bce.Config{
		Checksum:    true,
		Timeout:     config.Timeout * time.Second,
		Region:      config.Region,
		Endpoint:    config.EIPOpenAPIEndpoint,
		RetryPolicy: retryPolicy,
	})

	// 初始化 STS Client
	stsClient := sts.NewClient(ctx, &bce.Config{
		Endpoint:    config.STSEndpoint,
		Checksum:    true,
		Timeout:     config.Timeout * time.Second,
		Region:      config.Region,
		RetryPolicy: retryPolicy,
	}, &bce.Config{
		Endpoint:    config.IAMEndpoint,
		Checksum:    true,
		Timeout:     config.Timeout * time.Second,
		Region:      config.Region,
		RetryPolicy: retryPolicy,
	}, config.ServiceRoleName, config.ServiceName, config.ServicePassword)

	return &Service{
		clientset: clientset,
		blbClient: blbClient,
		eipClient: eipClient,
		stsClient: stsClient,

		appblbClient: appblbClient,
	}, nil
}

// ListResources - 返回 namespace 下所有 资源对象
//
// PARAMS:
//   - ctx: The context to trace request
//   - namespace: string
//
// RETURNS:
//
//	[]runtime.Object: []*v1.Service
//	error: nil if succeed, error if fail
func (r *Service) ListResources(ctx context.Context, ns string) ([]runtime.Object, error) {
	services := []runtime.Object{}

	serviceList, err := r.clientset.CoreV1().Services(ns).List(ctx, metav1.ListOptions{})
	if err != nil {
		logger.Errorf(ctx, "listClusterResources service in %v failed: %v", ns, err)
		return services, err
	}

	if serviceList == nil {
		logger.Errorf(ctx, "listClusterResources service in %v is nil", ns)
		return services, nil
	}

	for _, service := range serviceList.Items {
		if service.Name != "kubernetes" || service.Namespace != "default" {
			// 避免 kubernetes service 被删除
			tmp := service
			services = append(services, &tmp)
		}
	}

	return services, nil
}

// GetResourceMeta - 返回对象的 namespace, name
//
// PARAMS:
//   - ctx: The context to trace request
//
// RETURNS:
//
//	string: namespace
//	string: name
//	error: nil if succeed, error if fail
func (r *Service) GetResourceMeta(ctx context.Context, obj runtime.Object) (string, string, error) {
	var ns string
	var name string

	if obj == nil {
		return ns, name, errors.New("GetResourceMeta failed: obj is nil")
	}

	service, ok := obj.(*v1.Service)
	if !ok {
		return "", "", errors.New("obj is not v1.Service")
	}

	ns = service.GetNamespace()
	name = service.GetName()

	return ns, name, nil
}

// DeleteK8SResource - 删除对象
//
// PARAMS:
//   - ctx: The context to trace request
//   - ns: namespace
//   - name: name
//
// RETURNS:
//
//	error: nil if succeed, error if fail
func (r *Service) DeleteK8SResource(ctx context.Context, ns, name string) error {
	// 检查 Service 是否存在
	service, err := r.clientset.CoreV1().Services(ns).Get(ctx, name, metav1.GetOptions{})
	if err != nil {
		logger.Errorf(ctx, "Get service %v/%v failed: %v", ns, name, err)
		return err
	}

	if service == nil {
		logger.Infof(ctx, "Service %v/%v is nil, skip deleted", ns, name)
		return nil
	}

	// 删除 service
	if err := r.clientset.CoreV1().Services(ns).Delete(ctx, name, metav1.DeleteOptions{}); err != nil {
		logger.Errorf(ctx, "Delete service %v/%v failed: %v", ns, name, err)
		return err
	}

	return nil
}

// IaasReleased - 检查资源对应 IaaS 是否释放成功
//
// PARAMS:
//   - ctx: The context to trace request
//
// RETURNS:
//
//	bool: true = 释放成功
//	error: nil if succeed, error if fail
func (r *Service) IaasReleased(ctx context.Context, obj runtime.Object, accountID string) (bool, error) {
	if obj == nil {
		return false, errors.New("IaasReleased failed: obj is nil")
	}

	service := obj.(*v1.Service)
	ns := service.GetNamespace()
	name := service.GetName()

	blbExists := true
	if blbID, set := getServiceBLBID(ctx, service); set && !shouldReserveBLB(ctx, service) {
		// BLB 可能是 AppBLB 或是普通 BLB, 两种情况都要检查一下
		isAppBLBExist, err := r.isAppBLBExist(ctx, blbID, accountID)
		if err != nil {
			logger.Errorf(ctx, "Fail to check is App BLB exists: %s", err)
			return false, err
		}
		isNormalBLBExist, err := r.isNormalBLBExist(ctx, blbID, accountID)
		if err != nil {
			logger.Errorf(ctx, "Fail to check is Normal BLB exists: %s", err)
			return false, err
		}

		logger.Infof(ctx, "AppBLB Existence: %s  NormalBLB Existence: %s", strconv.FormatBool(isAppBLBExist), strconv.FormatBool(isNormalBLBExist))
		if !isAppBLBExist && !isNormalBLBExist {
			blbExists = false
		}
	} else {
		blbExists = false
	}

	// 检查 EIP 是否删除
	eipExists := true
	if eip, set := getServiceEIP(ctx, service); set && !shouldReserveEIP(ctx, service) {
		eips, err := r.eipClient.GetEIPs(ctx, &eipsdk.GetEIPsArgs{EIP: eip}, r.stsClient.NewSignOption(ctx, accountID))
		// 报错
		if err != nil {
			logger.Errorf(ctx, "Get Service %v/%v associated EIP %v failed: %v", ns, name, eip, err)
			return false, err
		}

		// EIP 不存在
		if len(eips) == 0 {
			logger.Infof(ctx, "Service %v/%v associated EIP %v deleted success", ns, name, eip)
			eipExists = false
		}

		// EIP 还存在
		if len(eips) >= 1 {
			logger.Infof(ctx, "Service %v/%v associated EIP %v still exist ", ns, name, eip)
		}
	} else {
		eipExists = false
	}

	if !blbExists && !eipExists {
		return true, nil
	}

	return false, nil
}

// isAppBLBExist 根据 BLB ID 检查 App BLB 是否存在
func (r *Service) isAppBLBExist(ctx context.Context, blbID string, accountID string) (bool, error) {
	blb, err := r.appblbClient.DescribeAppLoadBalancerByID(ctx, blbID, r.stsClient.NewSignOption(ctx, accountID))
	if err != nil {
		if !bce.IsNotFound(err) {
			return false, err
		}
		return false, nil
	}

	if blb != nil {
		return true, nil
	}
	return false, nil
}

// isAppBLBExist 根据 BLB ID 检查普通型 BLB 是否存在
func (r *Service) isNormalBLBExist(ctx context.Context, blbID string, accountID string) (bool, error) {
	blbList, err := r.blbClient.DescribeLoadBalancers(ctx, &blbsdk.DescribeLoadBalancersArgs{
		BLBID:        blbID,
		ExactlyMatch: true,
	}, r.stsClient.NewSignOption(ctx, accountID))

	if err != nil {
		if !strings.Contains(err.Error(), "NoSuchObject") {
			return false, err
		}
		return false, nil
	}

	if len(blbList) != 0 {
		return true, nil
	}
	return false, nil
}

// getServiceBLBID Get BLB ID
func getServiceBLBID(ctx context.Context, service *v1.Service) (string, bool) {
	if service == nil {
		logger.Errorf(ctx, "GetServiceBLBID failed: service is nil")
		return "", false
	}

	blbID, ok := service.Annotations[appblb2.AnnotationAPPBLBID]
	if ok {
		reg := regexp.MustCompile(`lb-.+`)
		if !reg.MatchString(blbID) {
			logger.Errorf(ctx, "GetServiceBLBID failed: %s format not match", blbID)
			return "", false
		}

		return blbID, true
	}

	return common.IsAppBLBIDSettedInAnnotation(ctx, service)
}

// shouldReserveBLB - return whether reserve blb
func shouldReserveBLB(ctx context.Context, service *v1.Service) bool {
	reserve, found := service.Annotations[appblb2.AnnotationBLBReserve]
	if found {
		return reserve == "true"
	}

	return common.ShouldKeepLoadBalancer(ctx, service)
}

// GetServiceEIP - If EIP not exist return ("", false)
func getServiceEIP(ctx context.Context, service *v1.Service) (string, bool) {
	if service == nil {
		logger.Errorf(ctx, "GetServiceEIP failed: service is nil")
		return "", false
	}

	ingresses := service.Status.LoadBalancer.Ingress
	if len(ingresses) == 0 {
		return "", false
	}

	eip := service.Status.LoadBalancer.Ingress[0].IP
	if eip == "" {
		return "", false
	}

	return eip, true
}

// shouldReserveEIP - return whether reserve eip
func shouldReserveEIP(ctx context.Context, service *v1.Service) bool {
	return service.Spec.LoadBalancerIP != ""
}
