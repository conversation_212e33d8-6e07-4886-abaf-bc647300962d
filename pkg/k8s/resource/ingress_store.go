package resource

import (
	"context"
	"errors"
	"fmt"

	"k8s.io/api/extensions/v1beta1"
	networkingv1 "k8s.io/api/networking/v1"
	metaV1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/client-go/kubernetes"
	apisextensionsv1beta1 "k8s.io/kubernetes/pkg/apis/extensions/v1beta1"
	"k8s.io/kubernetes/pkg/apis/networking"
	networkingv1api "k8s.io/kubernetes/pkg/apis/networking/v1"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/utils"
)

type IngressGroupType string

const IngressGroupTypeNetworkingV1 IngressGroupType = "NetworkingV1"
const IngressGroupTypeExtensionsV1beta1 IngressGroupType = "ExtensionsV1beta1"

type IngressStoreConverter interface {
	ConvertToIngress(ctx context.Context, obj runtime.Object) (*v1beta1.Ingress, error)
	GetIngress(ctx context.Context, namespace string, name string) (*v1beta1.Ingress, error)
	ListIngressesObjs(ctx context.Context, namespace string) ([]runtime.Object, error)
	DeleteIngress(ctx context.Context, namespace string, name string) error
}

type IngressStore struct {
	groupType  IngressGroupType
	kubeClient kubernetes.Interface
}

func NewIngressStore(k8sClient kubernetes.Interface) (*IngressStore, error) {
	isSupportNetworkingV1, err := utils.IsCCESupportNetworkingV1(k8sClient)
	if err != nil {
		return nil, err
	}

	if isSupportNetworkingV1 {
		return &IngressStore{
			groupType:  IngressGroupTypeNetworkingV1,
			kubeClient: k8sClient,
		}, nil
	}
	return &IngressStore{
		groupType:  IngressGroupTypeExtensionsV1beta1,
		kubeClient: k8sClient,
	}, nil
}

func (store *IngressStore) ConvertToIngress(ctx context.Context, obj runtime.Object) (*v1beta1.Ingress, error) {
	if store.groupType == IngressGroupTypeExtensionsV1beta1 {
		ingress, ok := obj.(*v1beta1.Ingress)
		if !ok {
			return nil, errors.New("obj not extensions v1 ingress")
		}
		return ingress, nil
	}
	ingressNetworkingV1, ok := obj.(*networkingv1.Ingress)
	if !ok {
		return nil, fmt.Errorf("obj not networking v1 ingress")
	}
	return ConvertNetworkingV1IngressToExtensionsV1beta1(ingressNetworkingV1)
}

func (store *IngressStore) GetIngress(ctx context.Context, namespace string, name string) (*v1beta1.Ingress, error) {
	if store.groupType == IngressGroupTypeExtensionsV1beta1 {
		// 直接获取 ExtensionsV1beta1 Ingress
		return store.kubeClient.ExtensionsV1beta1().Ingresses(namespace).Get(ctx, name, metaV1.GetOptions{})
	}
	// 获取 NetworkingV1 Ingress 并转换成 ExtensionsV1beta1 Ingress
	networkingV1Ingress, err := store.kubeClient.NetworkingV1().Ingresses(namespace).Get(ctx, name, metaV1.GetOptions{})
	if err != nil {
		return nil, err
	}
	return ConvertNetworkingV1IngressToExtensionsV1beta1(networkingV1Ingress)
}

func (store *IngressStore) ListIngressesObjs(ctx context.Context, namespace string) ([]runtime.Object, error) {
	if store.groupType == IngressGroupTypeExtensionsV1beta1 {
		// 直接获取 ExtensionsV1beta1 Ingress
		ingressList, err := store.kubeClient.ExtensionsV1beta1().Ingresses(namespace).List(ctx, metaV1.ListOptions{})
		if err != nil {
			return nil, err
		}
		result := make([]runtime.Object, 0)
		for _, ingress := range ingressList.Items {
			newIngress := ingress.DeepCopy()
			result = append(result, newIngress)
		}
		return result, nil
	}
	// 获取 NetworkingV1 Ingress 并转换成 ExtensionsV1beta1 Ingress
	networkingV1Ingresses, err := store.kubeClient.NetworkingV1().Ingresses(namespace).List(ctx, metaV1.ListOptions{})
	if err != nil {
		return nil, err
	}
	result := make([]runtime.Object, 0)
	for _, networkingV1Ingress := range networkingV1Ingresses.Items {
		if err != nil {
			return nil, err
		}
		result = append(result, &networkingV1Ingress)
	}
	return result, nil
}

func (store *IngressStore) DeleteIngress(ctx context.Context, namespace string, name string) error {
	if store.groupType == IngressGroupTypeExtensionsV1beta1 {
		return store.kubeClient.ExtensionsV1beta1().Ingresses(namespace).Delete(ctx, name, metaV1.DeleteOptions{})
	}
	return store.kubeClient.NetworkingV1().Ingresses(namespace).Delete(ctx, name, metaV1.DeleteOptions{})
}

func ConvertNetworkingV1IngressToExtensionsV1beta1(ingress *networkingv1.Ingress) (*v1beta1.Ingress, error) {
	networkingIngress := &networking.Ingress{}
	if err := networkingv1api.Convert_v1_Ingress_To_networking_Ingress(ingress, networkingIngress, nil); err != nil {
		return nil, err
	}

	extensionsV1beta1Ingress := &v1beta1.Ingress{}
	if err := apisextensionsv1beta1.Convert_networking_Ingress_To_v1beta1_Ingress(networkingIngress, extensionsV1beta1Ingress, nil); err != nil {
		return nil, err
	}

	return extensionsV1beta1Ingress, nil
}
