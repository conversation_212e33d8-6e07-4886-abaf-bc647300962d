package finalizer

type Finalizer string

const (
	FinalizerCceMasterLbController Finalizer = "service.kubernetes.io/master-lb"
	FinalizerCceLbController       Finalizer = "service.kubernetes.io/load-balancer-cleanup"
	FinalizerCceIngressController  Finalizer = "ingress.kubernetes.io/load-balancer-cleanup"
)

func HasFinalizer(finalizers []string, target Finalizer) bool {
	for _, finalizer := range finalizers {
		if finalizer == string(target) {
			return true
		}
	}
	return false
}

func AddFinalizer(finalizers []string, toAddFinalizer Finalizer) []string {
	excludedFinalizer := ExcludeFinalizer(finalizers, toAddFinalizer)

	excludedFinalizer = append(excludedFinalizer, string(toAddFinalizer))
	return excludedFinalizer
}

func ExcludeFinalizer(finalizers []string, toRemoveFinalizer Finalizer) []string {
	targetFinalizers := make([]string, 0)
	for _, finalizer := range finalizers {
		if finalizer != string(toRemoveFinalizer) {
			targetFinalizers = append(targetFinalizers, finalizer)
		}
	}
	return targetFinalizers
}
