package finalizer

import (
	"reflect"
	"testing"
)

func Test_HasFinalizer(t *testing.T) {
	cases := []struct {
		name       string
		finalizers []string
		target     Finalizer
		expect     bool
	}{
		{
			name: "normal exist",
			finalizers: []string{
				"a", "b", "c",
			},
			target: Finalizer("a"),
			expect: true,
		},
		{
			name: "normal not exist",
			finalizers: []string{
				"a", "b", "c",
			},
			target: Finalizer("d"),
			expect: false,
		},
	}
	for _, c := range cases {
		got := HasFinalizer(c.finalizers, c.target)
		if got != c.expect {
			t.Errorf("HasFinalizer %s failed: target %v actual %v", c.name, c.expect, got)
			return
		}
	}
}

func Test_AddFinalizer(t *testing.T) {
	cases := []struct {
		name       string
		finalizers []string
		target     Finalizer
		expect     []string
	}{
		{
			name: "normal add",
			finalizers: []string{
				"a", "b", "c",
			},
			target: Finalizer("d"),
			expect: []string{
				"a", "b", "c", "d",
			},
		},
		{
			name: "normal add duplicate",
			finalizers: []string{
				"a", "b", "c",
			},
			target: Finalizer("a"),
			expect: []string{
				"b", "c", "a",
			},
		},
	}
	for _, c := range cases {
		got := AddFinalizer(c.finalizers, c.target)
		if !reflect.DeepEqual(got, c.expect) {
			t.Errorf("AddFinalizer %s failed: target %v actual %v", c.name, c.expect, got)
			return
		}
	}
}

func Test_ExcludeFinalizer(t *testing.T) {
	cases := []struct {
		name       string
		finalizers []string
		target     Finalizer
		expect     []string
	}{
		{
			name: "normal exclude",
			finalizers: []string{
				"a", "b", "c",
			},
			target: Finalizer("c"),
			expect: []string{
				"a", "b",
			},
		},
		{
			name: "normal exclude not exist",
			finalizers: []string{
				"a", "b", "c",
			},
			target: Finalizer("d"),
			expect: []string{
				"a", "b", "c",
			},
		},
	}
	for _, c := range cases {
		got := ExcludeFinalizer(c.finalizers, c.target)
		if !reflect.DeepEqual(got, c.expect) {
			t.Errorf("ExcludeFinalizer %s failed: target %v actual %v", c.name, c.expect, got)
			return
		}
	}
}
