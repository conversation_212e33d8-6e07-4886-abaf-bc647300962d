/**
 * @Author: pansiyuan02
 * @Description:
 * @File:  types
 * @Version: 1.0.0
 * @Date: 2021/1/8 2:43 下午
 */
package patcher

import (
	"k8s.io/apimachinery/pkg/runtime"
)

// 目前还不支持subresource
type Patcher interface {
	Patch(obj runtime.Object) runtime.Object
	Add(patchFunc func(obj runtime.Object))
}

type patcher struct {
	patchs []func(obj runtime.Object)
}

func NewPatcher() Patcher {
	return &patcher{}
}

func (patcher *patcher) Patch(obj runtime.Object) runtime.Object {
	newObj := obj.DeepCopyObject()
	for _, patch := range patcher.patchs {
		patch(newObj)
	}
	return newObj
}

func (patcher *patcher) Add(patchFunc func(obj runtime.Object)) {
	patcher.patchs = append(patcher.patchs, patchFunc)
}
