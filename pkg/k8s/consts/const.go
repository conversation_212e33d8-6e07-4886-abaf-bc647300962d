// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2019/12/10 19:50:00, by <EMAIL>, create
*/
/*
DESCRIPTION
定义 controller 使用常量
*/

package consts

import (
	"time"
)

const (
	Region = "sandbox"

	BCCLogicEndpoint = ""
	VPCEndpoint      = ""

	MetaClusterKubeConfig = ""

	DefaultTimeout = 10
)

const (
	CRDGroup   = "cce.baidubce.com"
	CRDVersion = "v1"

	MetaClusterDefaultNamespace     = "default"
	MetaClusterHealthCheckNamespace = "health-check"

	MetaClusterBCINamespace = "bci"

	MetaClusterKubeConfigConfigMapKey = "kube-config"

	K8SClientTimeout = 10 * time.Second

	TestMetaKubeConfigPath = "/Users/<USER>/.kube/config.chenhuan"
)

// MetricServer 相关配置
const (
	MetricServerServiceName  = "metrics-server"
	MetricServerImage        = "hub.baidubce.com/jpaas-public/metrics-server:0.2.1"
	MetricServerScraperImage = "hub.baidubce.com/jpaas-public/metrics-scraper:0.1.0"
)

// NvidiaDevicePlugin 相关配置
const (
	NvidiaDevicePluginName  = "nvidia-device-plugin-daemonset"
	NvidiaDevicePluginImage = "hub.baidubce.com/public/nvidia-k8s-device-plugin:1.11"
)

// 测试用服务账号
const (
	CCEServiceName     = "cce"
	CCEServicePassword = "nHrpnEDTIOonHC1QTOLGDOSjKbqfwKKy"
	STSEndpoint        = "nmg02-bce-test6.nmg02.baidu.com:8586/v1"
	STSRoleName        = "BceServiceRole_SERVICE_CCE"
)

const (
	BBCEndpoint = ""
)
