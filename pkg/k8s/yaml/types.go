// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2019/12/10 17:00:00, by ch<PERSON><PERSON><PERSON>@baidu.com, create
*/
/*
DESCRIPTION
k8s client-go 直接提交 YAML 文件接口定义
*/

package yaml

import (
	"context"
)

//go:generate mockgen -destination=./mock/mock_client.go -package=mock icode.baidu.com/baidu/cce-test/e2e-test/pkg/k8s/yaml Interface

// Interface 定义提交 YAML 定义
type Interface interface {
	DeployYAML(ctx context.Context, spec string) error
}
