// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2019/12/10 17:00:00, by <EMAIL>, create
*/
/*
DESCRIPTION
调用 yaml.Interface 中 DeployYAML 测试
*/

package yaml

import (
	"context"
	"testing"

	appsv1 "k8s.io/api/apps/v1"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/api/extensions/v1beta1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/client-go/discovery"
	fakedsidcovery "k8s.io/client-go/discovery/fake"
	"k8s.io/client-go/dynamic"
	fakeddynamic "k8s.io/client-go/dynamic/fake"
	"k8s.io/client-go/kubernetes/fake"
)

// 实际调用 K8S 进行测试, 提交代码时需要去掉
//
//	func testClient_Call_DeployYAML(t *testing.T) {
//		ctx := context.TODO()
//
//		// 初始化 metaClient
//		metaKubeConfigPath := "/Users/<USER>/.kube/config.chenhuan"
//		model, err := models.NewClient(ctx, metaKubeConfigPath)
//		if err != nil {
//			t.Errorf("meta.NewClient failed: %v", err)
//			return
//		}
//
//		clusterID := "c-chenhuan"
//
//		// 通过集群ID 获取 kubeconfig
//		config, err := k8s.RESTClientConfig(ctx, model, clusterID)
//		if err != nil {
//			logger.Errorf(ctx, "RESTClientConfig failed: %v", err)
//			return
//		}
//
//		// 初始化 yaml.Client
//		client, err := NewClient(ctx, config)
//		if err != nil {
//			t.Errorf("NewClient failed: %v", err)
//			return
//		}
//
//		yaml := `apiVersion: batch/v1
//
// kind: Job
// metadata:
//
//	name: job-example-1
//
// spec:
//
//	completions: 1
//	parallelism: 1
//	backoffLimit: 6
//	template:
//	  metadata:
//	    name: pi
//	  spec:
//	    containers:
//	    - name: pi
//	      image: hub.baidubce.com/cce/perl:latest
//	      command: ["perl",  "-Mbignum=bpi", "-wle", "print bpi(2000)"]
//	    restartPolicy: Never
//
// ---
// apiVersion: batch/v1
// kind: Job
// metadata:
//
//	name: job-example-2
//
// spec:
//
//	completions: 1
//	parallelism: 1
//	backoffLimit: 6
//	template:
//	  metadata:
//	    name: pi
//	  spec:
//	    containers:
//	    - name: pi
//	      image: hub.baidubce.com/cce/perl:latest
//	      command: ["perl",  "-Mbignum=bpi", "-wle", "print bpi(2000)"]
//	    restartPolicy: Never
//
// `
//
//		if err := client.DeployYAML(ctx, yaml); err != nil {
//			t.Errorf("DeployYAML Failed: %v", err)
//		}
//	}
func TestClient_DeployYAML(t *testing.T) {
	type fields struct {
		discoveryClient discovery.ServerResourcesInterface
		dynamicClient   dynamic.Interface
	}

	type args struct {
		ctx       context.Context
		namespace string
		spec      string
	}

	// 初始化 discovery client
	client := fake.NewSimpleClientset()
	client.Resources = []*metav1.APIResourceList{
		{
			GroupVersion: v1beta1.SchemeGroupVersion.String(),
			APIResources: []metav1.APIResource{
				{Name: "ingressses", Namespaced: true, Kind: "Ingress"},
			},
		},
		{
			GroupVersion: corev1.SchemeGroupVersion.String(),
			APIResources: []metav1.APIResource{
				{Name: "services", Namespaced: true, Kind: "Service"},
			},
		},
		{
			GroupVersion: appsv1.SchemeGroupVersion.String(),
			APIResources: []metav1.APIResource{
				{Name: "deployments", Namespaced: true, Kind: "Deployment"},
			},
		},
	}

	fakeDiscovery, ok := client.Discovery().(*fakedsidcovery.FakeDiscovery)
	if !ok {
		t.Fatalf("couldn't convert Discovery() to *FakeDiscovery")
	}

	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "部署 Deployment 正常",
			fields: func() fields {
				// 初始化 dynamic client
				fakeDynamic := fakeddynamic.NewSimpleDynamicClient(runtime.NewScheme())

				return fields{
					discoveryClient: fakeDiscovery,
					dynamicClient:   fakeDynamic,
				}
			}(),
			args: args{
				ctx:       context.TODO(),
				namespace: "default",
				spec: `apiVersion: apps/v1
kind: Deployment
metadata:
  name: deployment-example
  labels:
    app: nginx
spec:
  replicas: 2
  selector:
    matchLabels:
      app: nginx
  template:
    metadata:
      labels:
        app: nginx
    spec:
      restartPolicy: Always
      containers:
        - name: nginx
          image: hub.baidubce.com/cce/nginx-alpine-go:latest
          imagePullPolicy: Always`,
			},
			wantErr: false,
		},
		{
			name: "部署 Deployment,Service 正常",
			fields: func() fields {
				// 初始化 dynamic client
				fakeDynamic := fakeddynamic.NewSimpleDynamicClient(runtime.NewScheme())

				return fields{
					discoveryClient: fakeDiscovery,
					dynamicClient:   fakeDynamic,
				}
			}(),
			args: args{
				ctx:       context.TODO(),
				namespace: "default",
				spec: `apiVersion: apps/v1
kind: Deployment
metadata:
  name: deployment-example
  labels:
    app: nginx
spec:
  replicas: 2
  selector:
    matchLabels:
      app: nginx
  template:
    metadata:
      labels:
        app: nginx
    spec:
      restartPolicy: Always
      containers:
        - name: nginx
          image: hub.baidubce.com/cce/nginx-alpine-go:latest
          imagePullPolicy: Always
---
kind: Service
apiVersion: v1
metadata:
  name: nginx-service
  annotations:
    service.beta.kubernetes.io/cce-load-balancer-cce-add-id: lb-aaaaaaaa 
spec:
  selector:
    app: nginx
  type: LoadBalancer
  ports:
  - name: nginx-port
    port: 80
    targetPort: 80
    protocol: TC`,
			},
			wantErr: false,
		},
		{
			name: "部署 CRD, GroupVersion 不存在, 异常",
			fields: func() fields {
				// 初始化 dynamic client
				fakeDynamic := fakeddynamic.NewSimpleDynamicClient(runtime.NewScheme())

				return fields{
					discoveryClient: fakeDiscovery,
					dynamicClient:   fakeDynamic,
				}
			}(),
			args: args{
				ctx:       context.TODO(),
				namespace: "default",
				spec: `apiVersion: group/version
kind: Deployment
metadata:
  name: deployment-example
  labels:
    app: nginx
spec:
  replicas: 2
  selector:
    matchLabels:
      app: nginx
  template:
    metadata:
      labels:
        app: nginx
    spec:
      restartPolicy: Always
      containers:
        - name: nginx
          image: hub.baidubce.com/cce/nginx-alpine-go:latest
          imagePullPolicy: Always`,
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Client{
				discoveryClient: tt.fields.discoveryClient,
				dynamicClient:   tt.fields.dynamicClient,
			}

			err := c.DeployYAML(tt.args.ctx, tt.args.spec)
			if err != nil {
				if tt.wantErr {
					return
				}

				t.Errorf("Client.DeployYAML() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if tt.wantErr {
				t.Errorf("Client.DeployYAML() failed, wantErr=true got=false")
				return
			}

			// TODO: dynamic 是否需要补充测试
		})
	}
}
