// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2019/12/10 17:00:00, by <EMAIL>, create
*/
/*
DESCRIPTION
Client 是 k8syaml.Interface 的实现
*/

package yaml

import (
	"context"
	"errors"
	"fmt"
	"io"
	"strings"
	"time"

	kerrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/apimachinery/pkg/runtime/schema"
	"k8s.io/apimachinery/pkg/util/yaml"
	"k8s.io/client-go/discovery"
	"k8s.io/client-go/dynamic"
	"k8s.io/client-go/rest"
	"k8s.io/client-go/tools/clientcmd"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
)

// 检查 Client 是否实现 resource.Interface
var _ Interface = &Client{}

// Client 是 k8syaml.Interface 的实现
type Client struct {
	discoveryClient discovery.ServerResourcesInterface // 查询 K8S APIVersion 下 Resources
	dynamicClient   dynamic.Interface                  // 操作 K8S Resources
}

// NewClient 初始化 Client
func NewClient(ctx context.Context, config *rest.Config) (Interface, error) {
	if config == nil {
		return nil, errors.New("NewClient failed: config is nil")
	}

	// 初始化 DiscoveryClient
	discoveryClient, err := discovery.NewDiscoveryClientForConfig(config)
	if err != nil {
		logger.Errorf(ctx, "NewDiscoveryClientForConfig failed: %v", err)
		return nil, err
	}

	// 初始化 DynamicClient
	dynamicClient, err := dynamic.NewForConfig(config)
	if err != nil {
		logger.Errorf(ctx, "dynamic.NewForConfig failed: %v", err)
		return nil, err
	}

	return &Client{
		discoveryClient: discoveryClient,
		dynamicClient:   dynamicClient,
	}, nil
}

// NewClientByConfig - 初始化 Client
func NewClientByConfig(ctx context.Context, kubeconfig string) (Interface, error) {
	if kubeconfig == "" {
		return nil, errors.New("kubeconfig is empty")
	}

	config, err := clientcmd.RESTConfigFromKubeConfig([]byte(kubeconfig))
	if err != nil {
		logger.Errorf(ctx, "RESTConfigFromKubeConfig failed: %v", err)
		return nil, err
	}

	config.Timeout = 30 * time.Second
	config.ContentType = "application/vnd.kubernetes.protobuf"

	config.QPS = 400
	config.Burst = 400

	return NewClient(ctx, config)
}

// DeployYAML - 使用 client-go 部署 Kubernetes YAML
// 支持一个 YAML 中多个 Resource 部署
//
// PARAMS:
//   - ctx:  The context to trace request
//   - spec: YAML 的 string
//
// RETURNS:
//
//	error: nil if succeed, error if fail
func (c *Client) DeployYAML(ctx context.Context, spec string) error {
	reader := strings.NewReader(spec)
	decoder := yaml.NewYAMLOrJSONDecoder(reader, 4096)

	for {
		// 将 string 转成 unstructured.Unstructured
		data := unstructured.Unstructured{}
		if err := decoder.Decode(&data); err != nil {
			if err == io.EOF {
				logger.Infof(ctx, "Plugin deploy by YAML success")
				return nil
			}

			logger.Errorf(ctx, "Decode failed: %v", err)
			return err
		}

		// 获取 APIVersion && Kind
		version := data.GetAPIVersion()
		kind := data.GetKind()

		logger.Infof(ctx, "deployFromYAML: version=%v kind=%v", version, kind)

		// 检查 kind 是否在 APIVersion 对应 Resource 中
		// e.g. 比如 /apis/vi 下面是否有 deployments
		apiResourceList, err := c.discoveryClient.ServerResourcesForGroupVersion(version)
		if err != nil {
			logger.Errorf(ctx, "ServerResourcesForGroupVersion failed: %v", err)
			return err
		}

		if apiResourceList == nil {
			return errors.New("ServerResourcesForGroupVersion failed: apiResourceList is nil")
		}

		var resource *metav1.APIResource
		for _, apiResource := range apiResourceList.APIResources {
			if apiResource.Kind == kind && !strings.Contains(apiResource.Name, "/") {
				resource = &apiResource
				break
			}
		}

		if resource == nil {
			msg := fmt.Sprintf("unknown resource kind: %s", kind)
			logger.Errorf(ctx, msg)

			return fmt.Errorf(msg)
		}

		// 部署 YAML
		groupVersion, err := schema.ParseGroupVersion(version)
		if err != nil {
			groupVersion = schema.GroupVersion{Version: version}
		}

		gvResource := schema.GroupVersionResource{
			Group:    groupVersion.Group,
			Version:  groupVersion.Version,
			Resource: resource.Name,
		}

		namespace := data.GetNamespace()
		name := data.GetName()

		logger.Infof(ctx, "K8S Resource: group=%s version=%s resource=%s namespace=%s, name=%s",
			groupVersion.Group, groupVersion.Version, resource.Name, namespace, name)

		if _, err := c.dynamicClient.Resource(gvResource).Namespace(namespace).Get(ctx, name, metav1.GetOptions{}); err != nil {
			// Resource 不存在, 则新建
			if kerrors.IsNotFound(err) {
				logger.Infof(ctx, "Resource=%s Namespace=%s Name=%s not exist, create one", resource.Name, namespace, name)

				if _, err := c.dynamicClient.Resource(gvResource).Namespace(namespace).Create(ctx, &data, metav1.CreateOptions{}); err != nil {
					logger.Errorf(ctx, "create Resource=%s Namespace=%s Name=%s failed: %v", resource.Name, namespace, name, err)
					return err
				}

				continue
			}

			logger.Errorf(ctx, "Get Resource=%s Namespace=%s Name=%s failed: %v", resource.Name, namespace, name, err)
			return err
		}

		// 存在则跳过
		logger.Infof(ctx, "Resource=%s Namespace=%s Name=%s exist, skip create", resource.Name, namespace, name)
	}
}
