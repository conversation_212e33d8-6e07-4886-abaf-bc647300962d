package meta

import (
	"context"

	kerrors "k8s.io/apimachinery/pkg/api/errors"

	ccerrors "icode.baidu.com/baidu/cce-test/e2e-test/pkg/errors"
	errs "icode.baidu.com/baidu/cce-test/e2e-test/services/cluster/cluster-controller/errors"
)

// ErrTypeName - Error 类型名
type ErrTypeName string

// model errors
var (
	ClusterNotExist  ErrTypeName = "ClusterNotExist"
	ScraperNotExist  ErrTypeName = "ScraperNotExist"
	MonitorNotExist  ErrTypeName = "MonitorNotExist"
	WorkflowNotExist ErrTypeName = "WorkflowNotExist"
)

var (
	ErrClusterNotExist  = ccerrors.NewErrType(ClusterNotExist)
	ErrScraperNotExist  = ccerrors.NewErrType(ScraperNotExist)
	ErrMonitorNotExist  = ccerrors.NewErrType(MonitorNotExist)
	ErrWorkflowNotExist = ccerrors.NewErrType(WorkflowNotExist)
)

func wrapError(ctx context.Context, err error, msg ...string) error {
	if err == nil {
		return nil
	}
	if kerrors.IsNotFound(err) {
		return errs.ErrNotExist.Wrap(ctx, err, msg...)
	}
	// TODO: wrap more k8s errors with our errors
	return err
}

// IsClusterNotExist - MetaCluster 不存在
func IsClusterNotExist(err error) bool {
	return kerrors.IsNotFound(err) || ErrClusterNotExist.Is(err)
}

// IsScraperNotExist - MetaCluster 不存在
func IsScraperNotExist(err error) bool {
	return kerrors.IsNotFound(err) || ErrScraperNotExist.Is(err)
}

// IsMonitorNotExist - MetaCluster 不存在
func IsMonitorNotExist(err error) bool {
	return kerrors.IsNotFound(err) || ErrMonitorNotExist.Is(err)
}

// IsWorkflowNotExist - MetaCluster 不存在
func IsWorkflowNotExist(err error) bool {
	return kerrors.IsNotFound(err) || ErrWorkflowNotExist.Is(err)
}
