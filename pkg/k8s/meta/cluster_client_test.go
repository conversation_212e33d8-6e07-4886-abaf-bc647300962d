// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2018/12/25 19:30:00, by <EMAIL>, create
*/
/*
DESCRIPTION
包含 meta.Interface 中 Cluster CRD 相关接口单测
*/
package meta

import (
	"context"
	"encoding/json"
	"reflect"
	"testing"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/kubernetes/fake"
	"k8s.io/client-go/rest"

	ccev1 "icode.baidu.com/baidu/cce-test/e2e-test/pkg/crd/api/v1"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/gotag/validate"
	ccetypes "icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
)

func defaultTestCluster() *ccev1.Cluster {
	return &ccev1.Cluster{
		// TypeMeta: metav1.TypeMeta{
		// 	APIVersion: "cce.baidubce.com/v1",
		// 	Kind:       "Cluster",
		// },
		ObjectMeta: metav1.ObjectMeta{
			Namespace: "default",
			Name:      "chenhuan",
		},
		Spec:   ccetypes.ClusterSpec{},
		Status: ccetypes.ClusterStatus{},
	}
}

func updateClusterSpecTest() *ccev1.Cluster {
	return &ccev1.Cluster{
		ObjectMeta: metav1.ObjectMeta{
			Namespace: "default",
			Name:      "lql",
		},
		Spec:   ccetypes.ClusterSpec{},
		Status: ccetypes.ClusterStatus{},
	}
}

func TestClient_GetCluster(t *testing.T) {
	type fields struct {
		restclient rest.Interface
	}

	type args struct {
		ctx  context.Context
		ns   string
		name string
		opts *metav1.GetOptions
	}

	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *ccev1.Cluster
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "GetCluster 正常",
			fields: fields{
				restclient: newFakeRESTClient(map[string]runtime.Object{
					"/apis/cce.baidubce.com/v1/namespaces/default/clusters/c-erdsijdh": defaultTestCluster(),
				}),
			},
			args: args{
				ctx:  context.TODO(),
				ns:   "default",
				name: "c-erdsijdh",
				opts: &metav1.GetOptions{},
			},
			want:    defaultTestCluster(),
			wantErr: false,
		},
		{
			name: "GetCluster 正常",
			fields: fields{
				restclient: newFakeRESTClient(map[string]runtime.Object{
					"/apis/cce.baidubce.com/v1/namespaces/default/clusters/c-asfeadsf": defaultTestCluster(),
				}),
			},
			args: args{
				ctx:  context.TODO(),
				ns:   "default",
				name: "c-asfeadsf",
				opts: &metav1.GetOptions{},
			},
			want:    defaultTestCluster(),
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Client{
				restclient: tt.fields.restclient,
			}

			got, err := c.GetCluster(tt.args.ctx, tt.args.ns, tt.args.name, tt.args.opts)
			if err != nil {
				if tt.wantErr {
					return
				}

				t.Errorf("Client.GetCluster() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if tt.wantErr {
				t.Errorf("Client.GetCluster() wantErr == true, but got false")
				return
			}

			if !reflect.DeepEqual(got, tt.want) {
				gots, _ := json.Marshal(got)
				t.Errorf("Got: %v", string(gots))
				wants, _ := json.Marshal(tt.want)
				t.Errorf("Want: %v", string(wants))
				t.Errorf("Client.GetCluster() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestClient_CreateCluster(t *testing.T) {
	type fields struct {
		restclient rest.Interface
	}

	type args struct {
		ctx     context.Context
		ns      string
		cluster *ccev1.Cluster
	}

	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *ccev1.Cluster
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "CreateCluster 正常",
			fields: fields{
				restclient: newFakeRESTClient(map[string]runtime.Object{
					"/apis/cce.baidubce.com/v1/namespaces/default/clusters": defaultTestCluster(),
				}),
			},
			args: args{
				ctx:     context.TODO(),
				ns:      "default",
				cluster: defaultTestCluster(),
			},
			want:    defaultTestCluster(),
			wantErr: false,
		},
		{
			name: "CreateCluster 同名失败",
			fields: fields{
				restclient: newFakeRESTClient(map[string]runtime.Object{
					"/apis/cce.baidubce.com/v1/namespaces/default/clusters": defaultTestCluster(),
				}),
			},
			args: args{
				ctx:     context.TODO(),
				ns:      "default",
				cluster: defaultTestCluster(),
			},
			want:    defaultTestCluster(),
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Client{
				restclient: tt.fields.restclient,
			}

			got, err := c.CreateCluster(tt.args.ctx, tt.args.ns, tt.args.cluster)
			if err != nil {
				if tt.wantErr {
					return
				}
				t.Errorf("Client.CreateCluster() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if tt.wantErr {
				t.Errorf("Client.CreateCluster() wantErr == true, but got false")
				return
			}

			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Client.CreateCluster() = %v, want %v", got, tt.want)
			}
		})
	}
}

func testGetCluster(t *testing.T) {
	ctx := context.TODO()

	client, err := NewClient(ctx, "")
	if err != nil {
		t.Errorf("NewClient failed: %v", err)
		return
	}

	cluster, err := client.GetCluster(ctx, "default", "c-chenhuan", &metav1.GetOptions{})
	if err != nil {
		t.Errorf("GetCluster failed: %v", err)
		return
	}

	str, err := json.Marshal(cluster)
	if err == nil {
		t.Errorf("GetCluster success: %v", string(str))
	}

	cluster, err = client.GetCluster(ctx, "default", "c-notexist", &metav1.GetOptions{})
	if err != nil {
		t.Errorf("GetCluster failed: %v", err)
	}

	str, err = json.Marshal(cluster)
	if err == nil {
		t.Errorf("GetCluster success: %v", string(str))
	}
}

func testDeleteCluster(t *testing.T) {
	ctx := context.TODO()

	client, err := NewClient(ctx, "")
	if err != nil {
		t.Errorf("NewClient failed: %v", err)
		return
	}

	if err := client.DeleteCluster(ctx, "default", "c-chenhuan", &metav1.DeleteOptions{}); err != nil {
		t.Errorf("GetCluster failed: %v", err)
		return
	}
}

func TestClient_UpdateClusterSpec(t *testing.T) {
	type fields struct {
		restclient rest.Interface
		clientset  kubernetes.Interface
	}
	type args struct {
		ctx          context.Context
		ns           string
		name         string
		spec         *ccetypes.ClusterSpec
		clusterMeta  *metav1.ObjectMeta
		conditions   map[validate.ValidateCondition]any
		skipValidate bool
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "normal",
			fields: func() fields {
				// ctl := gomock.NewController(t)
				// ctx := context.TODO()

				k8sclient := fake.NewSimpleClientset()
				restclient := newFakeRESTClient(map[string]runtime.Object{
					"/apis/cce.baidubce.com/v1/namespaces/default/clusters/lql": updateClusterSpecTest(),
				})

				return fields{
					restclient: restclient,
					clientset:  k8sclient,
				}
			}(),
			args: args{
				ctx:  context.TODO(),
				ns:   "default",
				name: "lql",
				spec: &ccetypes.ClusterSpec{
					ClusterName: "test",
				},
				clusterMeta: nil,
				conditions:  nil,
			},
			wantErr: false,
		},
		{
			name: "k8sversion",
			fields: func() fields {
				// ctl := gomock.NewController(t)
				// ctx := context.TODO()

				k8sclient := fake.NewSimpleClientset()
				restclient := newFakeRESTClient(map[string]runtime.Object{
					"/apis/cce.baidubce.com/v1/namespaces/default/clusters/lql": updateClusterSpecTest(),
				})

				return fields{
					restclient: restclient,
					clientset:  k8sclient,
				}
			}(),
			args: args{
				ctx:  context.TODO(),
				ns:   "default",
				name: "lql",
				spec: &ccetypes.ClusterSpec{
					ClusterName: "test",
					K8SVersion:  "1.20",
				},
				clusterMeta:  nil,
				conditions:   nil,
				skipValidate: true,
			},
			wantErr: false,
		},
		{
			name: "k8sversion error",
			fields: func() fields {
				// ctl := gomock.NewController(t)
				// ctx := context.TODO()

				k8sclient := fake.NewSimpleClientset()
				restclient := newFakeRESTClient(map[string]runtime.Object{
					"/apis/cce.baidubce.com/v1/namespaces/default/clusters/lql": updateClusterSpecTest(),
				})

				return fields{
					restclient: restclient,
					clientset:  k8sclient,
				}
			}(),
			args: args{
				ctx:  context.TODO(),
				ns:   "default",
				name: "lql",
				spec: &ccetypes.ClusterSpec{
					ClusterName: "test",
					K8SVersion:  "1.20",
				},
				clusterMeta:  nil,
				conditions:   nil,
				skipValidate: false,
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Client{
				restclient: tt.fields.restclient,
				clientset:  tt.fields.clientset,
			}
			if err := c.UpdateClusterSpec(tt.args.ctx, tt.args.ns, tt.args.name, tt.args.spec, tt.args.clusterMeta, tt.args.conditions, tt.args.skipValidate); (err != nil) != tt.wantErr {
				t.Errorf("Client.UpdateClusterSpec() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
