// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2019/11/29 14:19:00, by <EMAIL>, create
*/
/*
instance_client.go 中方法的调研测试
*/

package meta

import (
	"context"
	"encoding/json"
	"testing"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/kubernetes/fake"
	"k8s.io/client-go/rest"

	ccev1 "icode.baidu.com/baidu/cce-test/e2e-test/pkg/crd/api/v1"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/gotag/validate"
	ccetypes "icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/utils"
)

func updateInstanceSpecTest() *ccev1.Instance {
	return &ccev1.Instance{
		ObjectMeta: metav1.ObjectMeta{
			Namespace: "default",
			Name:      "lql",
		},
		Spec:   ccetypes.InstanceSpec{},
		Status: ccetypes.InstanceStatus{},
	}
}

func testListInstance(t *testing.T) {
	ctx := context.TODO()

	client, err := NewClient(ctx, "")
	if err != nil {
		t.Errorf("NewClient failed: %v", err)
		return
	}

	instanceList, err := client.ListInstances(ctx, "default", &metav1.ListOptions{})
	if err != nil {
		t.Errorf("ListInstances failed: %v", err)
		return
	}

	str, err := json.Marshal(instanceList)
	if err == nil {
		t.Errorf("ListInstances success: %v", string(str))
		return
	}
}

func testUpdateInstance(t *testing.T) {
	ctx := context.TODO()

	client, err := NewClient(ctx, "")
	if err != nil {
		t.Errorf("NewClient failed: %v", err)
		return
	}

	instance, err := client.GetInstance(ctx, "default", "cce-7ko69j2w-znav10nv", &metav1.GetOptions{})
	if err != nil {
		t.Errorf("GetInstance failed: %v", err)
		return
	}

	t.Errorf("ListInstances success: %v", utils.ToJSON(instance))

	instance.Spec.AdminPassword = "jianghong"
	instance.Status.InfrastructureReady = true

	if err := client.UpdateInstance(ctx, "default", "cce-7ko69j2w-znav10nv", instance); err != nil {
		t.Errorf("UpdateInstanceSpec failed: %v", err)
		return
	}
}

func TestClient_UpdateInstanceSpec(t *testing.T) {
	type fields struct {
		restclient rest.Interface
		clientset  kubernetes.Interface
	}
	type args struct {
		ctx          context.Context
		ns           string
		name         string
		spec         *ccetypes.InstanceSpec
		instanceMeta *metav1.ObjectMeta
		conditions   map[validate.ValidateCondition]any
		skipValidate bool
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "normal",
			fields: func() fields {
				// ctl := gomock.NewController(t)
				// ctx := context.TODO()

				k8sclient := fake.NewSimpleClientset()
				restclient := newFakeRESTClient(map[string]runtime.Object{
					"/apis/cce.baidubce.com/v1/namespaces/default/instances/lql": updateClusterSpecTest(),
				})

				return fields{
					restclient: restclient,
					clientset:  k8sclient,
				}
			}(),
			args: args{
				ctx:  context.TODO(),
				ns:   "default",
				name: "lql",
				spec: &ccetypes.InstanceSpec{
					AdminPassword: "xxx",
				},
				instanceMeta: nil,
				conditions:   nil,
			},
			wantErr: false,
		},
		{
			name: "instance tags",
			fields: func() fields {
				// ctl := gomock.NewController(t)
				// ctx := context.TODO()

				k8sclient := fake.NewSimpleClientset()
				restclient := newFakeRESTClient(map[string]runtime.Object{
					"/apis/cce.baidubce.com/v1/namespaces/default/instances/lql": updateClusterSpecTest(),
				})

				return fields{
					restclient: restclient,
					clientset:  k8sclient,
				}
			}(),
			args: args{
				ctx:  context.TODO(),
				ns:   "default",
				name: "lql",
				spec: &ccetypes.InstanceSpec{
					Tags: ccetypes.TagList{
						ccetypes.Tag{
							TagKey:   "name",
							TagValue: "lql",
						},
					},
				},
				instanceMeta: nil,
				conditions:   nil,
				skipValidate: true,
			},
			wantErr: false,
		},
		{
			name: "instance tags error",
			fields: func() fields {
				// ctl := gomock.NewController(t)
				// ctx := context.TODO()

				k8sclient := fake.NewSimpleClientset()
				restclient := newFakeRESTClient(map[string]runtime.Object{
					"/apis/cce.baidubce.com/v1/namespaces/default/instances/lql": updateClusterSpecTest(),
				})

				return fields{
					restclient: restclient,
					clientset:  k8sclient,
				}
			}(),
			args: args{
				ctx:  context.TODO(),
				ns:   "default",
				name: "lql",
				spec: &ccetypes.InstanceSpec{
					Tags: ccetypes.TagList{
						ccetypes.Tag{
							TagKey:   "name",
							TagValue: "lql",
						},
					},
				},
				instanceMeta: nil,
				conditions:   nil,
				skipValidate: false,
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Client{
				restclient: tt.fields.restclient,
				clientset:  tt.fields.clientset,
			}
			if err := c.UpdateInstanceSpec(tt.args.ctx, tt.args.ns, tt.args.name, tt.args.spec, tt.args.instanceMeta, tt.args.conditions, tt.args.skipValidate); (err != nil) != tt.wantErr {
				t.Errorf("Client.UpdateInstanceSpec() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
