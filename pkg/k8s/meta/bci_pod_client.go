// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2020/05/12 16:50:00, by <EMAIL>, create
*/
/*
定义对meta集群中BCI pod的增删改查
*/
package meta

import (
	"context"
	"errors"

	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
)

var (
	TolerationForVKNode = v1.Toleration{
		Key:      "virtual-kubelet.io/provider",
		Operator: v1.TolerationOpEqual,
		Value:    "baidu",
		Effect:   v1.TaintEffectNoSchedule,
	}

	TolerationForNodeNotReady = v1.Toleration{
		Key:      v1.TaintNodeNotReady,
		Operator: v1.TolerationOpExists,
		Effect:   v1.TaintEffectNoExecute,
	}

	TolerationForNodeUnreachable = v1.Toleration{
		Key:      v1.TaintNodeUnreachable,
		Operator: v1.TolerationOpExists,
		Effect:   v1.TaintEffectNoExecute,
	}
)

func (c *Client) CreateBCIPod(ctx context.Context, pod *v1.Pod) error {
	if pod == nil {
		return errors.New("bci pod to create is nil")
	}

	pod.Spec.Tolerations = append(pod.Spec.Tolerations,
		TolerationForVKNode,
		TolerationForNodeNotReady,
		TolerationForNodeUnreachable)
	if len(pod.Spec.NodeSelector) == 0 {
		pod.Spec.NodeSelector = map[string]string{"type": "virtual-kubelet"}
	} else {
		pod.Spec.NodeSelector["type"] = "virtual-kubelet"
	}

	if _, err := c.clientset.CoreV1().Pods(pod.GetNamespace()).Create(ctx, pod, metav1.CreateOptions{}); err != nil {
		logger.Errorf(ctx, "fail to create bci pod %v/%v: %v",
			pod.GetNamespace(), pod.GetName(), err)
		return wrapError(ctx, err)
	}
	return nil
}

func (c *Client) GetBCIPod(ctx context.Context, ns, name string, opts *metav1.GetOptions) (
	*v1.Pod, error) {
	if opts == nil {
		opts = &metav1.GetOptions{}
	}
	pod, err := c.clientset.CoreV1().Pods(ns).Get(ctx, name, *opts)
	return pod, wrapError(ctx, err)
}

func (c *Client) DeleteBCIPod(ctx context.Context, ns, name string, opts *metav1.DeleteOptions) error {
	if opts == nil {
		opts = &metav1.DeleteOptions{}
	}
	return wrapError(ctx, c.clientset.CoreV1().Pods(ns).Delete(ctx, name, *opts))
}
