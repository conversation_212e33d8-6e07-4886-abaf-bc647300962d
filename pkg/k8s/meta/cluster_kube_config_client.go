// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2019/12/05 19:40:00, by <EMAIL>, create
*/
/*
定义用户 K8S kube-config 在 MetaCluster 的增删查改
*/

package meta

import (
	"context"
	"strings"

	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	"icode.baidu.com/baidu/cce-test/e2e-test/services/cluster/cluster-controller/consts"
	errs "icode.baidu.com/baidu/cce-test/e2e-test/services/cluster/cluster-controller/errors"
)

// CreateClusterKubeConfig - 创建 Cluster KubeConfig
//
// PARAMS:
//   - ctx: The context to trace request
//   - ns:  namespace
//   - name: clusterID
//
// RETURNS:
//
//	instance: *ccev1.Instance
//	error: nil if succeed, error if fail
//	重要: 如果集群不存在, 返回 (nil, nil)
func (c *Client) CreateClusterKubeConfig(ctx context.Context, ns, name, config string) error {
	name = strings.ToLower(name)
	configMap := &v1.ConfigMap{
		ObjectMeta: metav1.ObjectMeta{
			Namespace: ns,
			Name:      name,
		},
		Data: map[string]string{
			consts.MetaClusterKubeConfigConfigMapKey: config,
		},
	}

	if _, err := c.clientset.CoreV1().ConfigMaps(ns).Create(ctx, configMap, metav1.CreateOptions{}); err != nil {
		logger.Errorf(ctx, "Create configmap %v/%v failed: %v", ns, name, err)
		return err
	}

	return nil
}

// GetClusterKubeConfig - MetaCluster 获取用户 K8S 证书
//
// PARAMS:
//   - ctx: The context to trace request
//   - ns: namespace
//   - name: name
//
// RETURNS:
//
//	string: The URL's HTML string content
//	error: nil if succeed, error if fail
func (c *Client) CheckClusterKubeConfig(ctx context.Context, ns, name string) (string, error) {
	// 这个name参数一般会是集群ID，老集群ID有大写字母，这里cover下
	name = strings.ToLower(name)
	configmap, err := c.clientset.CoreV1().ConfigMaps(ns).Get(ctx, name, metav1.GetOptions{})
	if err != nil {
		logger.Errorf(ctx, "Get configmap %v/%v failed: %v", ns, name, err)
		return "", wrapError(ctx, err)
	}

	if configmap == nil {
		return "", errs.ErrObjectCorrupts.New(ctx, "GetClusterKubeConfig failed: configmap is nil")
	}

	kubeconfig, ok := configmap.Data[consts.MetaClusterKubeConfigConfigMapKey]
	if !ok {
		return "", errs.ErrObjectCorrupts.New(ctx, "Get kubeConfig failed: key %s not exist", consts.MetaClusterKubeConfigConfigMapKey)
	}

	return kubeconfig, nil
}

// UpdateClusterKubeConfig - 更新 KubeConfig
func (c *Client) UpdateClusterKubeConfig(ctx context.Context, ns, name, config string) error {
	name = strings.ToLower(name)
	configMap := &v1.ConfigMap{
		ObjectMeta: metav1.ObjectMeta{
			Namespace: ns,
			Name:      name,
		},
		Data: map[string]string{
			consts.MetaClusterKubeConfigConfigMapKey: config,
		},
	}

	if _, err := c.clientset.CoreV1().ConfigMaps(ns).Update(ctx, configMap, metav1.UpdateOptions{}); err != nil {
		logger.Errorf(ctx, "Create configmap %v/%v failed: %v", ns, name, err)
		return err
	}
	return nil
}

// DeleteClusterKubeConfig - 删除 KubeConfig
func (c *Client) DeleteClusterKubeConfig(ctx context.Context, ns, name string) error {
	name = strings.ToLower(name)
	return c.clientset.CoreV1().ConfigMaps(ns).Delete(ctx, name, metav1.DeleteOptions{})
}

func (c *Client) DeleteClusterNamespace(ctx context.Context, namespace string) error {
	return c.clientset.CoreV1().Namespaces().Delete(ctx, namespace, metav1.DeleteOptions{})
}
