// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
by <PERSON><PERSON><PERSON><PERSON>@baidu.com, create
*/
/*
DESCRIPTION
包含 k8sclient.Interface 中 InstanceHealthCheck CRD 相关接口实现
*/

package meta

import (
	"context"
	"time"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes/scheme"

	ccev1 "icode.baidu.com/baidu/cce-test/e2e-test/pkg/crd/api/v1"
)

func (c *Client) CreateInstanceHealthCheck(ctx context.Context, ns string, ig *ccev1.InstanceHealthCheck) (*ccev1.InstanceHealthCheck, error) {
	result := ccev1.InstanceHealthCheck{}
	err := c.restclient.
		Post().
		Namespace(ns).
		Resource(ccev1.InstanceHealthCheckResource).
		Body(ig).
		Do(ctx).
		Into(&result)

	return &result, err
}

func (c *Client) GetInstanceHealthCheck(ctx context.Context, ns, name string, opts *metav1.GetOptions) (*ccev1.InstanceHealthCheck, error) {
	result := ccev1.InstanceHealthCheck{}
	err := c.restclient.
		Get().
		Namespace(ns).
		Resource(ccev1.InstanceHealthCheckResource).
		Name(name).
		VersionedParams(opts, scheme.ParameterCodec).
		Do(ctx).
		Into(&result)

	return &result, err
}

func (c *Client) ListInstanceHealthCheck(ctx context.Context, ns string, opts *metav1.ListOptions) (*ccev1.InstanceHealthCheckList, error) {
	if opts == nil {
		opts = &metav1.ListOptions{}
	}

	var timeout time.Duration
	if opts.TimeoutSeconds != nil {
		timeout = time.Duration(*opts.TimeoutSeconds) * time.Second
	}
	// TODO: remove after update client-go version to release-1.18
	if deadline, ok := ctx.Deadline(); ok {
		timeout = time.Until(deadline)
	}

	result := ccev1.InstanceHealthCheckList{}
	err := c.restclient.
		Get().
		Namespace(ns).
		Resource(ccev1.InstanceHealthCheckResource).
		VersionedParams(opts, scheme.ParameterCodec).
		Timeout(timeout).
		Do(ctx).
		Into(&result)

	return &result, err
}

func (c *Client) UpdateInstanceHealthCheck(ctx context.Context, ns, name string, ig *ccev1.InstanceHealthCheck) (*ccev1.InstanceHealthCheck, error) {
	result := ccev1.InstanceHealthCheck{}
	err := c.restclient.
		Put().
		Namespace(ns).
		Resource(ccev1.InstanceHealthCheckResource).
		Name(name).
		Body(ig).
		Do(ctx).
		Into(&result)

	return &result, err
}

func (c *Client) DeleteInstanceHealthCheck(ctx context.Context, ns, name string, opts *metav1.DeleteOptions) error {
	return c.restclient.
		Delete().
		Namespace(ns).
		Resource(ccev1.InstanceHealthCheckResource).
		Name(name).
		Body(opts).
		Do(ctx).
		Error()
}

// ClusterHealthCheck
func (c *Client) CreateClusterHealthCheck(ctx context.Context, ns string, ig *ccev1.ClusterHealthCheck) (*ccev1.ClusterHealthCheck, error) {
	result := ccev1.ClusterHealthCheck{}
	err := c.restclient.
		Post().
		Namespace(ns).
		Resource(ccev1.ClusterHealthCheckResource).
		Body(ig).
		Do(ctx).
		Into(&result)

	return &result, err
}

func (c *Client) GetClusterHealthCheck(ctx context.Context, ns, name string, opts *metav1.GetOptions) (*ccev1.ClusterHealthCheck, error) {
	result := ccev1.ClusterHealthCheck{}
	err := c.restclient.
		Get().
		Namespace(ns).
		Resource(ccev1.ClusterHealthCheckResource).
		Name(name).
		VersionedParams(opts, scheme.ParameterCodec).
		Do(ctx).
		Into(&result)

	return &result, err
}

func (c *Client) ListClusterHealthCheck(ctx context.Context, ns string, opts *metav1.ListOptions) (*ccev1.ClusterHealthCheckList, error) {
	if opts == nil {
		opts = &metav1.ListOptions{}
	}

	var timeout time.Duration
	if opts.TimeoutSeconds != nil {
		timeout = time.Duration(*opts.TimeoutSeconds) * time.Second
	}
	// TODO: remove after update client-go version to release-1.18
	if deadline, ok := ctx.Deadline(); ok {
		timeout = time.Until(deadline)
	}

	result := ccev1.ClusterHealthCheckList{}
	err := c.restclient.
		Get().
		Namespace(ns).
		Resource(ccev1.ClusterHealthCheckResource).
		VersionedParams(opts, scheme.ParameterCodec).
		Timeout(timeout).
		Do(ctx).
		Into(&result)

	return &result, err
}

func (c *Client) UpdateClusterHealthCheck(ctx context.Context, ns, name string, ig *ccev1.ClusterHealthCheck) (*ccev1.ClusterHealthCheck, error) {
	result := ccev1.ClusterHealthCheck{}
	err := c.restclient.
		Put().
		Namespace(ns).
		Resource(ccev1.ClusterHealthCheckResource).
		Name(name).
		Body(ig).
		Do(ctx).
		Into(&result)

	return &result, err
}

func (c *Client) DeleteClusterHealthCheck(ctx context.Context, ns, name string, opts *metav1.DeleteOptions) error {
	return c.restclient.
		Delete().
		Namespace(ns).
		Resource(ccev1.ClusterHealthCheckResource).
		Name(name).
		Body(opts).
		Do(ctx).
		Error()
}
