// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2019/12/08 17:08:00, by <EMAIL>, create
*/
/*
DESCRIPTION
*/
package meta

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"sync"
	"testing"

	kerrors "k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/runtime/schema"
	"k8s.io/apimachinery/pkg/runtime/serializer"
	"k8s.io/client-go/kubernetes/scheme"
	"k8s.io/client-go/rest"
	"k8s.io/client-go/rest/fake"

	ccev1 "icode.baidu.com/baidu/cce-test/e2e-test/pkg/crd/api/v1"
)

func init() {
	ccev1.AddToScheme(scheme.Scheme)
}

// TODO: newFakeRESTClient 放这里不是最优, 应该是类似 FakeClient 这样写法, 让其他包能调用
func newFakeRESTClient(resourcePaths map[string]runtime.Object) rest.Interface {
	return &fake.RESTClient{
		Client:       fake.CreateHTTPClient(fakeReqHandler(resourcePaths)),
		GroupVersion: ccev1.GroupVersion,
		NegotiatedSerializer: serializer.WithoutConversionCodecFactory{
			CodecFactory: scheme.Codecs,
		},
		VersionedAPIPath: "/apis/cce.baidubce.com/v1",
	}
}

func fakeReqHandler(resourcePaths map[string]runtime.Object) func(req *http.Request) (*http.Response, error) {
	return func(req *http.Request) (*http.Response, error) {
		// URL 路由
		// TODO: Object NotFound 处理有问题
		cluster, exist := resourcePaths[req.URL.Path]
		if !exist && req.Method != "GET" {
			return nil, fmt.Errorf("unexpected request for URL %q with method %q", req.URL.String(), req.Method)
		}

		switch req.Method {
		case "GET":
			if cluster == nil {
				return &http.Response{StatusCode: 404, Header: defaultHeaders(), Body: bytesBody(nil)}, kerrors.NewNotFound(schema.GroupResource{}, "")
			}

			res, err := json.Marshal(cluster)
			if err != nil {
				return nil, err
			}

			return &http.Response{StatusCode: 200, Header: defaultHeaders(), Body: bytesBody(res)}, nil
		case "POST":
			// TODO: Map 记录 Object
			codecs := serializer.NewCodecFactory(scheme.Scheme)
			decoder := codecs.UniversalDeserializer()
			body, err := io.ReadAll(req.Body)
			if err != nil {
				return nil, err
			}

			newObj, newGVK, err := decoder.Decode(body, nil, nil)
			if err != nil {
				return nil, fmt.Errorf("unexpected request body: %v", err)
			}

			if *newGVK != newObj.GetObjectKind().GroupVersionKind() {
				return nil, fmt.Errorf("unexpected scale API version %s (expected %s)", newGVK.String(), newObj.GetObjectKind().GroupVersionKind().String())
			}

			res, err := json.Marshal(newObj)
			if err != nil {
				return nil, err
			}

			return &http.Response{StatusCode: 200, Header: defaultHeaders(), Body: bytesBody(res)}, nil
		case "PUT":
			return &http.Response{StatusCode: 200, Header: defaultHeaders(), Body: bytesBody([]byte{})}, nil
		case "DELETE":
			return &http.Response{StatusCode: 200, Header: defaultHeaders(), Body: bytesBody([]byte{})}, nil
		default:
			return nil, fmt.Errorf("unexpected request for URL %q with method %q", req.URL.String(), req.Method)
		}
	}
}

func bytesBody(bodyBytes []byte) io.ReadCloser {
	return io.NopCloser(bytes.NewReader(bodyBytes))
}

func defaultHeaders() http.Header {
	header := http.Header{}
	header.Set("Content-Type", runtime.ContentTypeJSON)
	return header
}

func test_NewClient(t *testing.T) {
	// os.Setenv("KUBECONFIG", "/tmp/config")

	tests := []string{
		os.Getenv("KUBECONFIG"),
		os.Getenv("KUBECONFIG"),
		os.Getenv("KUBECONFIG"),
		os.Getenv("KUBECONFIG"),
		os.Getenv("KUBECONFIG"),
		os.Getenv("KUBECONFIG"),
	}

	wg := sync.WaitGroup{}
	for _, f := range tests {
		wg.Add(1)
		go func(f string) {
			defer wg.Done()
			_, err := NewClient(context.TODO(), f)
			if err != nil {
				t.Errorf("got err: %v", err)
			}
		}(f)
	}

	wg.Wait()
}

func TestNewClientForConfig(t *testing.T) {
	type args struct {
		ctx     context.Context
		restCfg *rest.Config
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "test1",
			args: args{
				ctx:     context.TODO(),
				restCfg: &rest.Config{},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			_, err := NewClientForConfig(tt.args.ctx, tt.args.restCfg)
			if (err != nil) != tt.wantErr {
				t.Errorf("NewClientForConfig() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}
