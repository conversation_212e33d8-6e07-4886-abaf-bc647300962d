// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2021/12/08 11:37:00, by <EMAIL>, create
*/
/*
DESCRIPTION
包含 k8sclient.Interface 中 Workflow CRD 相关接口的实现
*/

package meta

import (
	"context"
	"encoding/json"
	"reflect"
	"testing"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/client-go/rest"

	ccev1 "icode.baidu.com/baidu/cce-test/e2e-test/pkg/crd/api/v1"
	ccetypes "icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
)

func TestClient_ListTask(t *testing.T) {
	type fields struct {
		restclient rest.Interface
	}

	type args struct {
		ctx  context.Context
		ns   string
		opts *metav1.ListOptions
	}

	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *ccev1.TaskList
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "ListTask 正常",
			fields: fields{
				restclient: newFakeRESTClient(map[string]runtime.Object{
					"/apis/cce.baidubce.com/v1/namespaces/default/tasks": defaultTestTask(),
				}),
			},
			args: args{
				ctx:  context.TODO(),
				ns:   "default",
				opts: &metav1.ListOptions{},
			},
			want:    defaultTestTask(),
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Client{
				restclient: tt.fields.restclient,
			}

			got, err := c.ListTask(tt.args.ctx, tt.args.ns, tt.args.opts)
			if err != nil {
				if tt.wantErr {
					return
				}

				t.Errorf("Client.listtask() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if tt.wantErr {
				t.Errorf("Client.listtask() wantErr == true, but got false")
				return
			}

			if !reflect.DeepEqual(got, tt.want) {
				gots, _ := json.Marshal(got)
				t.Errorf("Got: %v", string(gots))
				wants, _ := json.Marshal(tt.want)
				t.Errorf("Want: %v", string(wants))
				t.Errorf("Client.listtask() = %v, want %v", got, tt.want)
			}
		})
	}
}

func defaultTestTask() *ccev1.TaskList {
	return &ccev1.TaskList{
		ListMeta: metav1.ListMeta{},
		Items: []ccev1.Task{
			{
				TypeMeta: metav1.TypeMeta{
					APIVersion: "cce.baidubce.com/v1",
					Kind:       "Task",
				},
				ObjectMeta: metav1.ObjectMeta{
					Namespace: "default",
					Name:      "miaoyongchang",
				},
				Spec:   ccetypes.TaskSpec{},
				Status: ccetypes.TaskStatus{},
			},
		},
	}
}
