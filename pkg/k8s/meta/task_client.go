/**
 * @Author: pansiyuan02
 * @Description:
 * @File:  task_client
 * @Version: 1.0.0
 * @Date: 2021/6/23 3:59 下午
 */
package meta

import (
	"context"
	"time"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes/scheme"

	ccev1 "icode.baidu.com/baidu/cce-test/e2e-test/pkg/crd/api/v1"
)

func (c *Client) CreateTask(ctx context.Context, task *ccev1.Task) (*ccev1.Task, error) {
	result := ccev1.Task{}
	err := c.restclient.
		Post().
		Namespace(task.Namespace).
		Resource(ccev1.TaskResource).
		Body(task).
		Do(ctx).
		Into(&result)

	return &result, err
}

func (c *Client) GetTask(ctx context.Context, ns, name string, opts *metav1.GetOptions) (*ccev1.Task, error) {
	result := ccev1.Task{}
	err := c.restclient.
		Get().
		Namespace(ns).
		Resource(ccev1.TaskResource).
		Name(name).
		VersionedParams(opts, scheme.ParameterCodec).
		Do(ctx).
		Into(&result)

	return &result, err
}

func (c *Client) ListTask(ctx context.Context, ns string, opts *metav1.ListOptions) (*ccev1.TaskList, error) {
	if opts == nil {
		opts = &metav1.ListOptions{}
	}

	var timeout time.Duration
	if opts.TimeoutSeconds != nil {
		timeout = time.Duration(*opts.TimeoutSeconds) * time.Second
	}
	// TODO: remove after update client-go version to release-1.18
	if deadline, ok := ctx.Deadline(); ok {
		timeout = time.Until(deadline)
	}

	result := ccev1.TaskList{}
	err := c.restclient.
		Get().
		Namespace(ns).
		Resource(ccev1.TaskResource).
		VersionedParams(opts, scheme.ParameterCodec).
		Timeout(timeout).
		Do(ctx).
		Into(&result)

	return &result, err
}
