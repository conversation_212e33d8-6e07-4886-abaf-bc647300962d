// Code generated by MockGen. DO NOT EDIT.
// Source: icode.baidu.com/baidu/cce-test/e2e-test/pkg/k8s/meta (interfaces: Interface)

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	v1 "icode.baidu.com/baidu/cce-test/e2e-test/pkg/crd/api/v1"
	validate "icode.baidu.com/baidu/cce-test/e2e-test/pkg/gotag/validate"
	ccetypes "icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
	v10 "k8s.io/api/core/v1"
	v11 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

// MockInterface is a mock of Interface interface.
type MockInterface struct {
	ctrl     *gomock.Controller
	recorder *MockInterfaceMockRecorder
}

// MockInterfaceMockRecorder is the mock recorder for MockInterface.
type MockInterfaceMockRecorder struct {
	mock *MockInterface
}

// NewMockInterface creates a new mock instance.
func NewMockInterface(ctrl *gomock.Controller) *MockInterface {
	mock := &MockInterface{ctrl: ctrl}
	mock.recorder = &MockInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockInterface) EXPECT() *MockInterfaceMockRecorder {
	return m.recorder
}

// CheckClusterKubeConfig mocks base method.
func (m *MockInterface) CheckClusterKubeConfig(arg0 context.Context, arg1, arg2 string) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckClusterKubeConfig", arg0, arg1, arg2)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckClusterKubeConfig indicates an expected call of CheckClusterKubeConfig.
func (mr *MockInterfaceMockRecorder) CheckClusterKubeConfig(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckClusterKubeConfig", reflect.TypeOf((*MockInterface)(nil).CheckClusterKubeConfig), arg0, arg1, arg2)
}

// CreateBCIConfig mocks base method.
func (m *MockInterface) CreateBCIConfig(arg0 context.Context, arg1 *v10.ConfigMap) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateBCIConfig", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateBCIConfig indicates an expected call of CreateBCIConfig.
func (mr *MockInterfaceMockRecorder) CreateBCIConfig(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateBCIConfig", reflect.TypeOf((*MockInterface)(nil).CreateBCIConfig), arg0, arg1)
}

// CreateBCIPod mocks base method.
func (m *MockInterface) CreateBCIPod(arg0 context.Context, arg1 *v10.Pod) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateBCIPod", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateBCIPod indicates an expected call of CreateBCIPod.
func (mr *MockInterfaceMockRecorder) CreateBCIPod(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateBCIPod", reflect.TypeOf((*MockInterface)(nil).CreateBCIPod), arg0, arg1)
}

// CreateCluster mocks base method.
func (m *MockInterface) CreateCluster(arg0 context.Context, arg1 string, arg2 *v1.Cluster) (*v1.Cluster, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateCluster", arg0, arg1, arg2)
	ret0, _ := ret[0].(*v1.Cluster)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateCluster indicates an expected call of CreateCluster.
func (mr *MockInterfaceMockRecorder) CreateCluster(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateCluster", reflect.TypeOf((*MockInterface)(nil).CreateCluster), arg0, arg1, arg2)
}

// CreateClusterHealthCheck mocks base method.
func (m *MockInterface) CreateClusterHealthCheck(arg0 context.Context, arg1 string, arg2 *v1.ClusterHealthCheck) (*v1.ClusterHealthCheck, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateClusterHealthCheck", arg0, arg1, arg2)
	ret0, _ := ret[0].(*v1.ClusterHealthCheck)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateClusterHealthCheck indicates an expected call of CreateClusterHealthCheck.
func (mr *MockInterfaceMockRecorder) CreateClusterHealthCheck(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateClusterHealthCheck", reflect.TypeOf((*MockInterface)(nil).CreateClusterHealthCheck), arg0, arg1, arg2)
}

// CreateClusterKubeConfig mocks base method.
func (m *MockInterface) CreateClusterKubeConfig(arg0 context.Context, arg1, arg2, arg3 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateClusterKubeConfig", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateClusterKubeConfig indicates an expected call of CreateClusterKubeConfig.
func (mr *MockInterfaceMockRecorder) CreateClusterKubeConfig(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateClusterKubeConfig", reflect.TypeOf((*MockInterface)(nil).CreateClusterKubeConfig), arg0, arg1, arg2, arg3)
}

// CreateInstance mocks base method.
func (m *MockInterface) CreateInstance(arg0 context.Context, arg1 string, arg2 *v1.Instance) (*v1.Instance, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateInstance", arg0, arg1, arg2)
	ret0, _ := ret[0].(*v1.Instance)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateInstance indicates an expected call of CreateInstance.
func (mr *MockInterfaceMockRecorder) CreateInstance(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateInstance", reflect.TypeOf((*MockInterface)(nil).CreateInstance), arg0, arg1, arg2)
}

// CreateInstanceGroup mocks base method.
func (m *MockInterface) CreateInstanceGroup(arg0 context.Context, arg1 string, arg2 *v1.InstanceGroup) (*v1.InstanceGroup, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateInstanceGroup", arg0, arg1, arg2)
	ret0, _ := ret[0].(*v1.InstanceGroup)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateInstanceGroup indicates an expected call of CreateInstanceGroup.
func (mr *MockInterfaceMockRecorder) CreateInstanceGroup(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateInstanceGroup", reflect.TypeOf((*MockInterface)(nil).CreateInstanceGroup), arg0, arg1, arg2)
}

// CreateInstanceHealthCheck mocks base method.
func (m *MockInterface) CreateInstanceHealthCheck(arg0 context.Context, arg1 string, arg2 *v1.InstanceHealthCheck) (*v1.InstanceHealthCheck, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateInstanceHealthCheck", arg0, arg1, arg2)
	ret0, _ := ret[0].(*v1.InstanceHealthCheck)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateInstanceHealthCheck indicates an expected call of CreateInstanceHealthCheck.
func (mr *MockInterfaceMockRecorder) CreateInstanceHealthCheck(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateInstanceHealthCheck", reflect.TypeOf((*MockInterface)(nil).CreateInstanceHealthCheck), arg0, arg1, arg2)
}

// CreateTask mocks base method.
func (m *MockInterface) CreateTask(arg0 context.Context, arg1 *v1.Task) (*v1.Task, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateTask", arg0, arg1)
	ret0, _ := ret[0].(*v1.Task)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateTask indicates an expected call of CreateTask.
func (mr *MockInterfaceMockRecorder) CreateTask(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateTask", reflect.TypeOf((*MockInterface)(nil).CreateTask), arg0, arg1)
}

// CreateWorkflow mocks base method.
func (m *MockInterface) CreateWorkflow(arg0 context.Context, arg1 string, arg2 *v1.Workflow) (*v1.Workflow, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateWorkflow", arg0, arg1, arg2)
	ret0, _ := ret[0].(*v1.Workflow)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateWorkflow indicates an expected call of CreateWorkflow.
func (mr *MockInterfaceMockRecorder) CreateWorkflow(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateWorkflow", reflect.TypeOf((*MockInterface)(nil).CreateWorkflow), arg0, arg1, arg2)
}

// DeleteBCIConfig mocks base method.
func (m *MockInterface) DeleteBCIConfig(arg0 context.Context, arg1, arg2 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteBCIConfig", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteBCIConfig indicates an expected call of DeleteBCIConfig.
func (mr *MockInterfaceMockRecorder) DeleteBCIConfig(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteBCIConfig", reflect.TypeOf((*MockInterface)(nil).DeleteBCIConfig), arg0, arg1, arg2)
}

// DeleteBCIPod mocks base method.
func (m *MockInterface) DeleteBCIPod(arg0 context.Context, arg1, arg2 string, arg3 *v11.DeleteOptions) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteBCIPod", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteBCIPod indicates an expected call of DeleteBCIPod.
func (mr *MockInterfaceMockRecorder) DeleteBCIPod(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteBCIPod", reflect.TypeOf((*MockInterface)(nil).DeleteBCIPod), arg0, arg1, arg2, arg3)
}

// DeleteCluster mocks base method.
func (m *MockInterface) DeleteCluster(arg0 context.Context, arg1, arg2 string, arg3 *v11.DeleteOptions) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteCluster", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteCluster indicates an expected call of DeleteCluster.
func (mr *MockInterfaceMockRecorder) DeleteCluster(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteCluster", reflect.TypeOf((*MockInterface)(nil).DeleteCluster), arg0, arg1, arg2, arg3)
}

// DeleteClusterHealthCheck mocks base method.
func (m *MockInterface) DeleteClusterHealthCheck(arg0 context.Context, arg1, arg2 string, arg3 *v11.DeleteOptions) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteClusterHealthCheck", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteClusterHealthCheck indicates an expected call of DeleteClusterHealthCheck.
func (mr *MockInterfaceMockRecorder) DeleteClusterHealthCheck(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteClusterHealthCheck", reflect.TypeOf((*MockInterface)(nil).DeleteClusterHealthCheck), arg0, arg1, arg2, arg3)
}

// DeleteClusterKubeConfig mocks base method.
func (m *MockInterface) DeleteClusterKubeConfig(arg0 context.Context, arg1, arg2 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteClusterKubeConfig", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteClusterKubeConfig indicates an expected call of DeleteClusterKubeConfig.
func (mr *MockInterfaceMockRecorder) DeleteClusterKubeConfig(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteClusterKubeConfig", reflect.TypeOf((*MockInterface)(nil).DeleteClusterKubeConfig), arg0, arg1, arg2)
}

// DeleteClusterNamespace mocks base method.
func (m *MockInterface) DeleteClusterNamespace(arg0 context.Context, arg1 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteClusterNamespace", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteClusterNamespace indicates an expected call of DeleteClusterNamespace.
func (mr *MockInterfaceMockRecorder) DeleteClusterNamespace(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteClusterNamespace", reflect.TypeOf((*MockInterface)(nil).DeleteClusterNamespace), arg0, arg1)
}

// DeleteInstance mocks base method.
func (m *MockInterface) DeleteInstance(arg0 context.Context, arg1, arg2 string, arg3 *v11.DeleteOptions) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteInstance", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteInstance indicates an expected call of DeleteInstance.
func (mr *MockInterfaceMockRecorder) DeleteInstance(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteInstance", reflect.TypeOf((*MockInterface)(nil).DeleteInstance), arg0, arg1, arg2, arg3)
}

// DeleteInstanceGroup mocks base method.
func (m *MockInterface) DeleteInstanceGroup(arg0 context.Context, arg1, arg2 string, arg3 *v11.DeleteOptions) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteInstanceGroup", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteInstanceGroup indicates an expected call of DeleteInstanceGroup.
func (mr *MockInterfaceMockRecorder) DeleteInstanceGroup(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteInstanceGroup", reflect.TypeOf((*MockInterface)(nil).DeleteInstanceGroup), arg0, arg1, arg2, arg3)
}

// DeleteInstanceHealthCheck mocks base method.
func (m *MockInterface) DeleteInstanceHealthCheck(arg0 context.Context, arg1, arg2 string, arg3 *v11.DeleteOptions) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteInstanceHealthCheck", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteInstanceHealthCheck indicates an expected call of DeleteInstanceHealthCheck.
func (mr *MockInterfaceMockRecorder) DeleteInstanceHealthCheck(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteInstanceHealthCheck", reflect.TypeOf((*MockInterface)(nil).DeleteInstanceHealthCheck), arg0, arg1, arg2, arg3)
}

// DeleteWorkflow mocks base method.
func (m *MockInterface) DeleteWorkflow(arg0 context.Context, arg1, arg2 string, arg3 *v11.DeleteOptions) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteWorkflow", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteWorkflow indicates an expected call of DeleteWorkflow.
func (mr *MockInterfaceMockRecorder) DeleteWorkflow(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteWorkflow", reflect.TypeOf((*MockInterface)(nil).DeleteWorkflow), arg0, arg1, arg2, arg3)
}

// GetBCIConfig mocks base method.
func (m *MockInterface) GetBCIConfig(arg0 context.Context, arg1, arg2 string) (*v10.ConfigMap, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBCIConfig", arg0, arg1, arg2)
	ret0, _ := ret[0].(*v10.ConfigMap)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBCIConfig indicates an expected call of GetBCIConfig.
func (mr *MockInterfaceMockRecorder) GetBCIConfig(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBCIConfig", reflect.TypeOf((*MockInterface)(nil).GetBCIConfig), arg0, arg1, arg2)
}

// GetBCIPod mocks base method.
func (m *MockInterface) GetBCIPod(arg0 context.Context, arg1, arg2 string, arg3 *v11.GetOptions) (*v10.Pod, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBCIPod", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*v10.Pod)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBCIPod indicates an expected call of GetBCIPod.
func (mr *MockInterfaceMockRecorder) GetBCIPod(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBCIPod", reflect.TypeOf((*MockInterface)(nil).GetBCIPod), arg0, arg1, arg2, arg3)
}

// GetCluster mocks base method.
func (m *MockInterface) GetCluster(arg0 context.Context, arg1, arg2 string, arg3 *v11.GetOptions) (*v1.Cluster, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCluster", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*v1.Cluster)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCluster indicates an expected call of GetCluster.
func (mr *MockInterfaceMockRecorder) GetCluster(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCluster", reflect.TypeOf((*MockInterface)(nil).GetCluster), arg0, arg1, arg2, arg3)
}

// GetClusterHealthCheck mocks base method.
func (m *MockInterface) GetClusterHealthCheck(arg0 context.Context, arg1, arg2 string, arg3 *v11.GetOptions) (*v1.ClusterHealthCheck, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetClusterHealthCheck", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*v1.ClusterHealthCheck)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetClusterHealthCheck indicates an expected call of GetClusterHealthCheck.
func (mr *MockInterfaceMockRecorder) GetClusterHealthCheck(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetClusterHealthCheck", reflect.TypeOf((*MockInterface)(nil).GetClusterHealthCheck), arg0, arg1, arg2, arg3)
}

// GetInstance mocks base method.
func (m *MockInterface) GetInstance(arg0 context.Context, arg1, arg2 string, arg3 *v11.GetOptions) (*v1.Instance, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInstance", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*v1.Instance)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInstance indicates an expected call of GetInstance.
func (mr *MockInterfaceMockRecorder) GetInstance(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInstance", reflect.TypeOf((*MockInterface)(nil).GetInstance), arg0, arg1, arg2, arg3)
}

// GetInstanceGroup mocks base method.
func (m *MockInterface) GetInstanceGroup(arg0 context.Context, arg1, arg2 string, arg3 *v11.GetOptions) (*v1.InstanceGroup, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInstanceGroup", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*v1.InstanceGroup)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInstanceGroup indicates an expected call of GetInstanceGroup.
func (mr *MockInterfaceMockRecorder) GetInstanceGroup(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInstanceGroup", reflect.TypeOf((*MockInterface)(nil).GetInstanceGroup), arg0, arg1, arg2, arg3)
}

// GetInstanceHealthCheck mocks base method.
func (m *MockInterface) GetInstanceHealthCheck(arg0 context.Context, arg1, arg2 string, arg3 *v11.GetOptions) (*v1.InstanceHealthCheck, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInstanceHealthCheck", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*v1.InstanceHealthCheck)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInstanceHealthCheck indicates an expected call of GetInstanceHealthCheck.
func (mr *MockInterfaceMockRecorder) GetInstanceHealthCheck(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInstanceHealthCheck", reflect.TypeOf((*MockInterface)(nil).GetInstanceHealthCheck), arg0, arg1, arg2, arg3)
}

// GetTask mocks base method.
func (m *MockInterface) GetTask(arg0 context.Context, arg1, arg2 string, arg3 *v11.GetOptions) (*v1.Task, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTask", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*v1.Task)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTask indicates an expected call of GetTask.
func (mr *MockInterfaceMockRecorder) GetTask(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTask", reflect.TypeOf((*MockInterface)(nil).GetTask), arg0, arg1, arg2, arg3)
}

// GetWorkflow mocks base method.
func (m *MockInterface) GetWorkflow(arg0 context.Context, arg1, arg2 string, arg3 *v11.GetOptions) (*v1.Workflow, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWorkflow", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*v1.Workflow)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWorkflow indicates an expected call of GetWorkflow.
func (mr *MockInterfaceMockRecorder) GetWorkflow(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWorkflow", reflect.TypeOf((*MockInterface)(nil).GetWorkflow), arg0, arg1, arg2, arg3)
}

// ListBCIConfigsByLabel mocks base method.
func (m *MockInterface) ListBCIConfigsByLabel(arg0 context.Context, arg1, arg2 string) (*v10.ConfigMapList, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListBCIConfigsByLabel", arg0, arg1, arg2)
	ret0, _ := ret[0].(*v10.ConfigMapList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListBCIConfigsByLabel indicates an expected call of ListBCIConfigsByLabel.
func (mr *MockInterfaceMockRecorder) ListBCIConfigsByLabel(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListBCIConfigsByLabel", reflect.TypeOf((*MockInterface)(nil).ListBCIConfigsByLabel), arg0, arg1, arg2)
}

// ListClusterHealthCheck mocks base method.
func (m *MockInterface) ListClusterHealthCheck(arg0 context.Context, arg1 string, arg2 *v11.ListOptions) (*v1.ClusterHealthCheckList, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListClusterHealthCheck", arg0, arg1, arg2)
	ret0, _ := ret[0].(*v1.ClusterHealthCheckList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListClusterHealthCheck indicates an expected call of ListClusterHealthCheck.
func (mr *MockInterfaceMockRecorder) ListClusterHealthCheck(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListClusterHealthCheck", reflect.TypeOf((*MockInterface)(nil).ListClusterHealthCheck), arg0, arg1, arg2)
}

// ListClusterMasters mocks base method.
func (m *MockInterface) ListClusterMasters(arg0 context.Context, arg1, arg2 string) (*v1.InstanceList, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListClusterMasters", arg0, arg1, arg2)
	ret0, _ := ret[0].(*v1.InstanceList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListClusterMasters indicates an expected call of ListClusterMasters.
func (mr *MockInterfaceMockRecorder) ListClusterMasters(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListClusterMasters", reflect.TypeOf((*MockInterface)(nil).ListClusterMasters), arg0, arg1, arg2)
}

// ListClusterNodes mocks base method.
func (m *MockInterface) ListClusterNodes(arg0 context.Context, arg1, arg2 string) (*v1.InstanceList, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListClusterNodes", arg0, arg1, arg2)
	ret0, _ := ret[0].(*v1.InstanceList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListClusterNodes indicates an expected call of ListClusterNodes.
func (mr *MockInterfaceMockRecorder) ListClusterNodes(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListClusterNodes", reflect.TypeOf((*MockInterface)(nil).ListClusterNodes), arg0, arg1, arg2)
}

// ListClusters mocks base method.
func (m *MockInterface) ListClusters(arg0 context.Context, arg1 string, arg2 *v11.ListOptions) (*v1.ClusterList, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListClusters", arg0, arg1, arg2)
	ret0, _ := ret[0].(*v1.ClusterList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListClusters indicates an expected call of ListClusters.
func (mr *MockInterfaceMockRecorder) ListClusters(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListClusters", reflect.TypeOf((*MockInterface)(nil).ListClusters), arg0, arg1, arg2)
}

// ListExclusiveWorkflowsByClusterID mocks base method.
func (m *MockInterface) ListExclusiveWorkflowsByClusterID(arg0 context.Context, arg1, arg2 string) (*v1.WorkflowList, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListExclusiveWorkflowsByClusterID", arg0, arg1, arg2)
	ret0, _ := ret[0].(*v1.WorkflowList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListExclusiveWorkflowsByClusterID indicates an expected call of ListExclusiveWorkflowsByClusterID.
func (mr *MockInterfaceMockRecorder) ListExclusiveWorkflowsByClusterID(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListExclusiveWorkflowsByClusterID", reflect.TypeOf((*MockInterface)(nil).ListExclusiveWorkflowsByClusterID), arg0, arg1, arg2)
}

// ListInstanceGroup mocks base method.
func (m *MockInterface) ListInstanceGroup(arg0 context.Context, arg1 string, arg2 *v11.ListOptions) (*v1.InstanceGroupList, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListInstanceGroup", arg0, arg1, arg2)
	ret0, _ := ret[0].(*v1.InstanceGroupList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListInstanceGroup indicates an expected call of ListInstanceGroup.
func (mr *MockInterfaceMockRecorder) ListInstanceGroup(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListInstanceGroup", reflect.TypeOf((*MockInterface)(nil).ListInstanceGroup), arg0, arg1, arg2)
}

// ListInstanceHealthCheck mocks base method.
func (m *MockInterface) ListInstanceHealthCheck(arg0 context.Context, arg1 string, arg2 *v11.ListOptions) (*v1.InstanceHealthCheckList, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListInstanceHealthCheck", arg0, arg1, arg2)
	ret0, _ := ret[0].(*v1.InstanceHealthCheckList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListInstanceHealthCheck indicates an expected call of ListInstanceHealthCheck.
func (mr *MockInterfaceMockRecorder) ListInstanceHealthCheck(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListInstanceHealthCheck", reflect.TypeOf((*MockInterface)(nil).ListInstanceHealthCheck), arg0, arg1, arg2)
}

// ListInstances mocks base method.
func (m *MockInterface) ListInstances(arg0 context.Context, arg1 string, arg2 *v11.ListOptions) (*v1.InstanceList, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListInstances", arg0, arg1, arg2)
	ret0, _ := ret[0].(*v1.InstanceList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListInstances indicates an expected call of ListInstances.
func (mr *MockInterfaceMockRecorder) ListInstances(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListInstances", reflect.TypeOf((*MockInterface)(nil).ListInstances), arg0, arg1, arg2)
}

// ListTask mocks base method.
func (m *MockInterface) ListTask(arg0 context.Context, arg1 string, arg2 *v11.ListOptions) (*v1.TaskList, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListTask", arg0, arg1, arg2)
	ret0, _ := ret[0].(*v1.TaskList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListTask indicates an expected call of ListTask.
func (mr *MockInterfaceMockRecorder) ListTask(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListTask", reflect.TypeOf((*MockInterface)(nil).ListTask), arg0, arg1, arg2)
}

// ListWorkflows mocks base method.
func (m *MockInterface) ListWorkflows(arg0 context.Context, arg1 string, arg2 *v11.ListOptions) (*v1.WorkflowList, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListWorkflows", arg0, arg1, arg2)
	ret0, _ := ret[0].(*v1.WorkflowList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListWorkflows indicates an expected call of ListWorkflows.
func (mr *MockInterfaceMockRecorder) ListWorkflows(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListWorkflows", reflect.TypeOf((*MockInterface)(nil).ListWorkflows), arg0, arg1, arg2)
}

// ListWorkflowsByClusterID mocks base method.
func (m *MockInterface) ListWorkflowsByClusterID(arg0 context.Context, arg1, arg2 string) (*v1.WorkflowList, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListWorkflowsByClusterID", arg0, arg1, arg2)
	ret0, _ := ret[0].(*v1.WorkflowList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListWorkflowsByClusterID indicates an expected call of ListWorkflowsByClusterID.
func (mr *MockInterfaceMockRecorder) ListWorkflowsByClusterID(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListWorkflowsByClusterID", reflect.TypeOf((*MockInterface)(nil).ListWorkflowsByClusterID), arg0, arg1, arg2)
}

// UpdateCluster mocks base method.
func (m *MockInterface) UpdateCluster(arg0 context.Context, arg1, arg2 string, arg3 *v1.Cluster) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateCluster", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateCluster indicates an expected call of UpdateCluster.
func (mr *MockInterfaceMockRecorder) UpdateCluster(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateCluster", reflect.TypeOf((*MockInterface)(nil).UpdateCluster), arg0, arg1, arg2, arg3)
}

// UpdateClusterHealthCheck mocks base method.
func (m *MockInterface) UpdateClusterHealthCheck(arg0 context.Context, arg1, arg2 string, arg3 *v1.ClusterHealthCheck) (*v1.ClusterHealthCheck, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateClusterHealthCheck", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*v1.ClusterHealthCheck)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateClusterHealthCheck indicates an expected call of UpdateClusterHealthCheck.
func (mr *MockInterfaceMockRecorder) UpdateClusterHealthCheck(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateClusterHealthCheck", reflect.TypeOf((*MockInterface)(nil).UpdateClusterHealthCheck), arg0, arg1, arg2, arg3)
}

// UpdateClusterKubeConfig mocks base method.
func (m *MockInterface) UpdateClusterKubeConfig(arg0 context.Context, arg1, arg2, arg3 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateClusterKubeConfig", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateClusterKubeConfig indicates an expected call of UpdateClusterKubeConfig.
func (mr *MockInterfaceMockRecorder) UpdateClusterKubeConfig(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateClusterKubeConfig", reflect.TypeOf((*MockInterface)(nil).UpdateClusterKubeConfig), arg0, arg1, arg2, arg3)
}

// UpdateClusterLabels mocks base method.
func (m *MockInterface) UpdateClusterLabels(arg0 context.Context, arg1, arg2 string, arg3 map[string]string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateClusterLabels", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateClusterLabels indicates an expected call of UpdateClusterLabels.
func (mr *MockInterfaceMockRecorder) UpdateClusterLabels(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateClusterLabels", reflect.TypeOf((*MockInterface)(nil).UpdateClusterLabels), arg0, arg1, arg2, arg3)
}

// UpdateClusterSpec mocks base method.
func (m *MockInterface) UpdateClusterSpec(arg0 context.Context, arg1, arg2 string, arg3 *ccetypes.ClusterSpec, arg4 *v11.ObjectMeta, arg5 map[validate.ValidateCondition]interface{}, arg6 bool) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateClusterSpec", arg0, arg1, arg2, arg3, arg4, arg5, arg6)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateClusterSpec indicates an expected call of UpdateClusterSpec.
func (mr *MockInterfaceMockRecorder) UpdateClusterSpec(arg0, arg1, arg2, arg3, arg4, arg5, arg6 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateClusterSpec", reflect.TypeOf((*MockInterface)(nil).UpdateClusterSpec), arg0, arg1, arg2, arg3, arg4, arg5, arg6)
}

// UpdateInstance mocks base method.
func (m *MockInterface) UpdateInstance(arg0 context.Context, arg1, arg2 string, arg3 *v1.Instance) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateInstance", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateInstance indicates an expected call of UpdateInstance.
func (mr *MockInterfaceMockRecorder) UpdateInstance(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateInstance", reflect.TypeOf((*MockInterface)(nil).UpdateInstance), arg0, arg1, arg2, arg3)
}

// UpdateInstanceGroup mocks base method.
func (m *MockInterface) UpdateInstanceGroup(arg0 context.Context, arg1, arg2 string, arg3 *v1.InstanceGroup) (*v1.InstanceGroup, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateInstanceGroup", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*v1.InstanceGroup)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateInstanceGroup indicates an expected call of UpdateInstanceGroup.
func (mr *MockInterfaceMockRecorder) UpdateInstanceGroup(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateInstanceGroup", reflect.TypeOf((*MockInterface)(nil).UpdateInstanceGroup), arg0, arg1, arg2, arg3)
}

// UpdateInstanceHealthCheck mocks base method.
func (m *MockInterface) UpdateInstanceHealthCheck(arg0 context.Context, arg1, arg2 string, arg3 *v1.InstanceHealthCheck) (*v1.InstanceHealthCheck, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateInstanceHealthCheck", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*v1.InstanceHealthCheck)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateInstanceHealthCheck indicates an expected call of UpdateInstanceHealthCheck.
func (mr *MockInterfaceMockRecorder) UpdateInstanceHealthCheck(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateInstanceHealthCheck", reflect.TypeOf((*MockInterface)(nil).UpdateInstanceHealthCheck), arg0, arg1, arg2, arg3)
}

// UpdateInstanceSpec mocks base method.
func (m *MockInterface) UpdateInstanceSpec(arg0 context.Context, arg1, arg2 string, arg3 *ccetypes.InstanceSpec, arg4 *v11.ObjectMeta, arg5 map[validate.ValidateCondition]interface{}, arg6 bool) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateInstanceSpec", arg0, arg1, arg2, arg3, arg4, arg5, arg6)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateInstanceSpec indicates an expected call of UpdateInstanceSpec.
func (mr *MockInterfaceMockRecorder) UpdateInstanceSpec(arg0, arg1, arg2, arg3, arg4, arg5, arg6 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateInstanceSpec", reflect.TypeOf((*MockInterface)(nil).UpdateInstanceSpec), arg0, arg1, arg2, arg3, arg4, arg5, arg6)
}

// UpdateInstanceStatus mocks base method.
func (m *MockInterface) UpdateInstanceStatus(arg0 context.Context, arg1, arg2 string, arg3 *ccetypes.InstanceStatus) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateInstanceStatus", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateInstanceStatus indicates an expected call of UpdateInstanceStatus.
func (mr *MockInterfaceMockRecorder) UpdateInstanceStatus(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateInstanceStatus", reflect.TypeOf((*MockInterface)(nil).UpdateInstanceStatus), arg0, arg1, arg2, arg3)
}

// UpdateWatchDogStatus mocks base method.
func (m *MockInterface) UpdateWatchDogStatus(arg0 context.Context, arg1, arg2 string, arg3 *v1.Workflow) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateWatchDogStatus", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateWatchDogStatus indicates an expected call of UpdateWatchDogStatus.
func (mr *MockInterfaceMockRecorder) UpdateWatchDogStatus(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateWatchDogStatus", reflect.TypeOf((*MockInterface)(nil).UpdateWatchDogStatus), arg0, arg1, arg2, arg3)
}

// UpdateWorkflow mocks base method.
func (m *MockInterface) UpdateWorkflow(arg0 context.Context, arg1, arg2 string, arg3 *v1.Workflow) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateWorkflow", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateWorkflow indicates an expected call of UpdateWorkflow.
func (mr *MockInterfaceMockRecorder) UpdateWorkflow(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateWorkflow", reflect.TypeOf((*MockInterface)(nil).UpdateWorkflow), arg0, arg1, arg2, arg3)
}
