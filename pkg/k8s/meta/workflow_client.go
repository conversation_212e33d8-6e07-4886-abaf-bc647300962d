// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2021/12/08 11:37:00, by <EMAIL>, create
*/
/*
DESCRIPTION
包含 k8sclient.Interface 中 Workflow CRD 相关接口的实现
*/

package meta

import (
	"context"
	"errors"
	"strings"
	"time"

	kerrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes/scheme"

	ccev1 "icode.baidu.com/baidu/cce-test/e2e-test/pkg/crd/api/v1"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	ccetypes "icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
)

const (
	workflowResource = "workflows"
)

// CreateWorkflow - 创建 Workflow
func (c *Client) CreateWorkflow(ctx context.Context, ns string, workflow *ccev1.Workflow) (*ccev1.Workflow, error) {
	// 添加公共 Label
	labels, err := workflowLabels(ctx, workflow)
	if err != nil {
		logger.Errorf(ctx, "workflowLabels failed: %s", err)
		return nil, err
	}
	workflow.Labels = labels

	result := ccev1.Workflow{}
	err = c.restclient.
		Post().
		Namespace(ns).
		Resource(workflowResource).
		Body(workflow).
		Do(ctx).
		Into(&result)

	return &result, err
}

// GetWorkflow - 查询 Workflow
func (c *Client) GetWorkflow(ctx context.Context, ns, name string, opts *metav1.GetOptions) (*ccev1.Workflow, error) {
	name = strings.ToLower(name)
	result := ccev1.Workflow{}
	err := c.restclient.
		Get().
		Namespace(ns).
		Resource(workflowResource).
		Name(name).
		VersionedParams(opts, scheme.ParameterCodec).
		Do(ctx).
		Into(&result)

	if kerrors.IsNotFound(err) {
		logger.Warnf(ctx, "Workflow %v/%v not found", ns, name)
		return nil, ErrWorkflowNotExist.New(ctx, "Workflow %v/%v not found", ns, name)
	}

	return &result, err
}

// ListWorkflows - Lists Workflow
func (c *Client) ListWorkflows(ctx context.Context, ns string, opts *metav1.ListOptions) (*ccev1.WorkflowList, error) {
	result := ccev1.WorkflowList{}
	err := c.restclient.
		Get().
		Namespace(ns).
		Resource(workflowResource).
		VersionedParams(opts, scheme.ParameterCodec).
		Do(ctx).
		Into(&result)

	return &result, err
}

// ListWorkflowsByClusterID - Lists Workflow from cluster
func (c *Client) ListWorkflowsByClusterID(ctx context.Context, ns, clusterID string) (*ccev1.WorkflowList, error) {
	labelSelector := metav1.LabelSelector{
		MatchLabels: map[string]string{
			ccev1.LabelWorkflowClusterID: clusterID,
		},
	}

	labelSelectorStr := metav1.FormatLabelSelector(&labelSelector)

	logger.Infof(ctx, "ListWorkflows selectorLabel: %v", labelSelectorStr)

	result := ccev1.WorkflowList{}

	err := c.restclient.
		Get().
		Namespace(ns).
		Resource(workflowResource).
		VersionedParams(&metav1.ListOptions{
			LabelSelector: labelSelectorStr,
		}, scheme.ParameterCodec).
		Do(ctx).
		Into(&result)

	return &result, err
}

// ListExclusiveWorkflowsByClusterID - Lists exclusive Workflow from workflow
func (c *Client) ListExclusiveWorkflowsByClusterID(ctx context.Context, ns, clusterID string) (*ccev1.WorkflowList, error) {
	labelSelector := metav1.LabelSelector{
		MatchExpressions: []metav1.LabelSelectorRequirement{
			{
				Key:      ccetypes.WorkflowAllowMultipleWithinClusterLabelKey,
				Operator: metav1.LabelSelectorOpDoesNotExist,
			},
			{
				Key:      ccev1.LabelWorkflowClusterID,
				Operator: metav1.LabelSelectorOpIn,
				Values:   []string{clusterID},
			},
		},
	}

	labelSelectorStr := metav1.FormatLabelSelector(&labelSelector)

	logger.Infof(ctx, "ListWorkflows selectorLabel: %v", labelSelectorStr)

	result := ccev1.WorkflowList{}

	err := c.restclient.
		Get().
		Namespace(ns).
		Resource(workflowResource).
		VersionedParams(&metav1.ListOptions{
			LabelSelector: labelSelectorStr,
		}, scheme.ParameterCodec).
		Do(ctx).
		Into(&result)

	return &result, err
}

// UpdateWorkflow - 更新 Workflow
func (c *Client) UpdateWorkflow(ctx context.Context, ns, name string, workflow *ccev1.Workflow) error {
	var err error
	name = strings.ToLower(name)

	for i := 0; i < 10; i++ {
		err = c.restclient.
			Put().
			Namespace(ns).
			Resource(workflowResource).
			Name(name).
			Body(workflow).
			Do(ctx).
			Error()

		if err == nil {
			logger.Infof(ctx, "Update workflow success")
			return nil
		} else if kerrors.IsConflict(err) {
			logger.Warnf(ctx, "Update workflow conflict, retry")

			new, err := c.GetWorkflow(ctx, ns, name, &metav1.GetOptions{})
			if err != nil {
				logger.Errorf(ctx, "GetWorkflow failed: %s", err)
				return err
			}

			if new == nil {
				return errors.New("GetWorkflow return nil")
			}

			// 不更新 Spec
			workflow.Spec = *(new.Spec.DeepCopy())

			// 更新 ResourceVersion
			workflow.ResourceVersion = new.ResourceVersion

			// 不更新 WatchDogStatus, 该部分由 watchdog-manager 调用 UpdateWatchDogStatus 更新
			workflow.Status.WatchDogStatus = new.Status.WatchDogStatus

			// 处理 RetryCount, 场景:
			// 1. workflow-controller 开始 reconcile;
			// 2. 期间 User 更新 RetryCount;
			// 3. workflow-controller 回写 Status, 不能覆盖用户设置 Status.
			// 4. workflow.Status.RetryCount != 1 避免第一次写入失败
			if new.Status.RetryCount == 0 && workflow.Status.RetryCount != 1 {
				workflow.Status.RetryCount = 0
			}
		} else if err != nil {
			logger.Errorf(ctx, "Upate workflow failed: %s", err)
			return err
		}
	}

	logger.Infof(ctx, "UpdateWorkflow %s/%s failed: %s", ns, name, err)
	return err
}

// UpdateWatchDogStatus - 更新 WatchDogStatus
func (c *Client) UpdateWatchDogStatus(ctx context.Context, ns, name string, workflow *ccev1.Workflow) error {
	var err error
	name = strings.ToLower(name)

	for i := 0; i < 10; i++ {
		err = c.restclient.
			Put().
			Namespace(ns).
			Resource(workflowResource).
			Name(name).
			Body(workflow).
			Do(ctx).
			Error()

		if err == nil {
			logger.Infof(ctx, "Update watchDogStatus success")
			return nil
		} else if kerrors.IsConflict(err) {
			logger.Warnf(ctx, "Update watchDogStatus conflict, retry")

			new, err := c.GetWorkflow(ctx, ns, name, &metav1.GetOptions{})
			if err != nil {
				logger.Errorf(ctx, "GetWorkflow failed: %s", err)
				return err
			}

			if new == nil {
				return errors.New("GetWorkflow return nil")
			}

			old := workflow.DeepCopy()

			// 不更新 Spec & Status
			workflow.Spec = *(new.Spec.DeepCopy())
			workflow.Status = *(new.Status.DeepCopy())

			// 更新 ResourceVersion
			workflow.ResourceVersion = new.ResourceVersion

			// 仅更新 Paused, WorkflowPhase, WatchDogStatus
			workflow.Spec.Paused = old.Spec.Paused
			workflow.Status.WorkflowPhase = old.Status.WorkflowPhase
			workflow.Status.WatchDogStatus = old.Status.WatchDogStatus
		} else if err != nil {
			logger.Errorf(ctx, "Upate watchDogStatus failed: %s", err)
			return err
		}
	}

	logger.Infof(ctx, "UpdateWatchDogStatus %s/%s failed: %s", ns, name, err)
	return err
}

// DeleteWorkflow - 删除 Workflow
func (c *Client) DeleteWorkflow(ctx context.Context, ns, name string, opts *metav1.DeleteOptions) error {
	name = strings.ToLower(name)
	err := c.restclient.
		Delete().
		Namespace(ns).
		Resource(workflowResource).
		Name(name).
		Body(opts).
		Do(ctx).
		Error()
	if err != nil {
		logger.Errorf(ctx, "DeleteWorkflow %s/%s failed: %s", ns, name, err)
		return err
	}

	// 更新 RetryCount, 防止 RetryCount 达到上限无法触发删除操作
	workflow, err := c.GetWorkflow(ctx, ns, name, &metav1.GetOptions{})
	if err != nil && !IsWorkflowNotExist(err) {
		logger.Errorf(ctx, "GetWorkflow failed: %s", err)
		return err
	}

	if IsWorkflowNotExist(err) {
		logger.Errorf(ctx, "GetWorkflow return nil, skip delete workflow")
		return nil
	}

	// 更新 RetryCount
	if workflow.Status.RetryCount != 0 {
		// TODO: 临时方案, 解决 controller 更新 status RetryCount 覆盖本次 update
		time.Sleep(3 * time.Second)
		logger.Infof(ctx, "Update RetryCount to 0")

		workflow, err := c.GetWorkflow(ctx, ns, name, &metav1.GetOptions{})
		if err != nil && !IsWorkflowNotExist(err) {
			logger.Errorf(ctx, "GetWorkflow failed: %s", err)
			return err
		}

		if IsWorkflowNotExist(err) {
			logger.Errorf(ctx, "GetWorkflow return nil, skip delete workflow")
			return nil
		}

		workflow.Status.RetryCount = 0

		if err := c.UpdateWorkflow(ctx, ns, name, workflow); err != nil {
			logger.Errorf(ctx, "UpdateWorkflow failed: %s", err)
			return err
		}
	}

	return nil
}

func workflowLabels(ctx context.Context, workflow *ccev1.Workflow) (map[string]string, error) {
	if workflow == nil {
		return nil, errors.New("workflow is nil")
	}

	// 初始化 labels
	result := workflow.Labels
	if result == nil {
		result = map[string]string{}
	}

	// 添加 ClusterID
	clusterID := workflow.Spec.ClusterID
	if clusterID != "" {
		result[ccev1.LabelWorkflowClusterID] = clusterID
	}

	// 添加 AccountID
	accountID := workflow.Spec.AccountID
	if accountID != "" {
		result[ccev1.LabelWorkflowAccountID] = accountID
	}

	return result, nil
}
