// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2019/11/19 22:02:00, by <EMAIL>, create
*/
/*
DESCRIPTION
封装 client-go 获取 Cluster & Instance CRD 对象
参考:
1. https://www.martin-helmich.de/en/blog/kubernetes-crd-client.html
2. https://github.com/kubernetes/client-go/blob/master/kubernetes/typed/core/v1/pod.go
*/

package meta

import (
	"context"

	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	ccev1 "icode.baidu.com/baidu/cce-test/e2e-test/pkg/crd/api/v1"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/gotag/validate"
	ccetypes "icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
)

//go:generate mockgen -destination=./mock/mock_client.go -package=mock icode.baidu.com/baidu/cce-test/e2e-test/pkg/k8s/meta Interface

// Interface 定义 k8s client-go 操作 CRD 相关方法
// TODO: 使用 Patch 方式来更新, 暂时使用乐观锁方式
// TODO: 所有集群ID作为name的方法，都要在里面转小写，兼容老集群ID
type Interface interface {
	CreateCluster(ctx context.Context, ns string, cluster *ccev1.Cluster) (*ccev1.Cluster, error)
	GetCluster(ctx context.Context, ns, name string, ops *metav1.GetOptions) (*ccev1.Cluster, error) // GetCluster 如果 Cluster 不存在, 返回 (nil, nil)
	ListClusters(ctx context.Context, ns string, opts *metav1.ListOptions) (*ccev1.ClusterList, error)
	UpdateCluster(ctx context.Context, ns, name string, cluster *ccev1.Cluster) error
	DeleteCluster(ctx context.Context, ns, name string, opts *metav1.DeleteOptions) error
	UpdateClusterSpec(ctx context.Context, ns, name string, spec *ccetypes.ClusterSpec, clusterMeta *metav1.ObjectMeta, conditions map[validate.ValidateCondition]any, skipValidate bool) error
	UpdateClusterLabels(ctx context.Context, ns, name string, Labels map[string]string) error

	CreateInstance(ctx context.Context, ns string, instance *ccev1.Instance) (*ccev1.Instance, error)
	GetInstance(ctx context.Context, ns, name string, opts *metav1.GetOptions) (*ccev1.Instance, error)
	ListInstances(ctx context.Context, ns string, opts *metav1.ListOptions) (*ccev1.InstanceList, error)
	UpdateInstance(ctx context.Context, ns, name string, instance *ccev1.Instance) error
	UpdateInstanceSpec(ctx context.Context, ns, name string, spec *ccetypes.InstanceSpec, instanceMeta *metav1.ObjectMeta, conditions map[validate.ValidateCondition]any, skipValidate bool) error
	UpdateInstanceStatus(ctx context.Context, ns, name string, status *ccetypes.InstanceStatus) error
	DeleteInstance(ctx context.Context, ns, name string, opts *metav1.DeleteOptions) error

	ListClusterMasters(ctx context.Context, ns string, clusterID string) (*ccev1.InstanceList, error)
	ListClusterNodes(ctx context.Context, ns string, clusterID string) (*ccev1.InstanceList, error)

	CreateClusterKubeConfig(ctx context.Context, ns, name, config string) error
	CheckClusterKubeConfig(ctx context.Context, ns, name string) (string, error)
	UpdateClusterKubeConfig(ctx context.Context, ns, name, config string) error
	DeleteClusterKubeConfig(ctx context.Context, ns, name string) error

	CreateBCIPod(ctx context.Context, pod *v1.Pod) error
	GetBCIPod(ctx context.Context, ns, name string, opts *metav1.GetOptions) (*v1.Pod, error)
	DeleteBCIPod(ctx context.Context, ns, name string, opts *metav1.DeleteOptions) error

	CreateBCIConfig(ctx context.Context, configMap *v1.ConfigMap) error
	GetBCIConfig(ctx context.Context, ns, name string) (*v1.ConfigMap, error)
	DeleteBCIConfig(ctx context.Context, ns, name string) error
	ListBCIConfigsByLabel(ctx context.Context, ns, labelSelector string) (*v1.ConfigMapList, error)

	CreateInstanceGroup(ctx context.Context, ns string, ig *ccev1.InstanceGroup) (*ccev1.InstanceGroup, error)
	GetInstanceGroup(ctx context.Context, ns, name string, opts *metav1.GetOptions) (*ccev1.InstanceGroup, error)
	ListInstanceGroup(ctx context.Context, ns string, opts *metav1.ListOptions) (*ccev1.InstanceGroupList, error)
	UpdateInstanceGroup(ctx context.Context, ns, name string, ig *ccev1.InstanceGroup) (*ccev1.InstanceGroup, error)
	DeleteInstanceGroup(ctx context.Context, ns, name string, opts *metav1.DeleteOptions) error

	CreateInstanceHealthCheck(ctx context.Context, ns string, ig *ccev1.InstanceHealthCheck) (*ccev1.InstanceHealthCheck, error)
	GetInstanceHealthCheck(ctx context.Context, ns, name string, opts *metav1.GetOptions) (*ccev1.InstanceHealthCheck, error)
	ListInstanceHealthCheck(ctx context.Context, ns string, opts *metav1.ListOptions) (*ccev1.InstanceHealthCheckList, error)
	UpdateInstanceHealthCheck(ctx context.Context, ns, name string, ig *ccev1.InstanceHealthCheck) (*ccev1.InstanceHealthCheck, error)
	DeleteInstanceHealthCheck(ctx context.Context, ns, name string, opts *metav1.DeleteOptions) error

	CreateClusterHealthCheck(ctx context.Context, ns string, ig *ccev1.ClusterHealthCheck) (*ccev1.ClusterHealthCheck, error)
	GetClusterHealthCheck(ctx context.Context, ns, name string, opts *metav1.GetOptions) (*ccev1.ClusterHealthCheck, error)
	ListClusterHealthCheck(ctx context.Context, ns string, opts *metav1.ListOptions) (*ccev1.ClusterHealthCheckList, error)
	UpdateClusterHealthCheck(ctx context.Context, ns, name string, ig *ccev1.ClusterHealthCheck) (*ccev1.ClusterHealthCheck, error)
	DeleteClusterHealthCheck(ctx context.Context, ns, name string, opts *metav1.DeleteOptions) error

	CreateTask(ctx context.Context, task *ccev1.Task) (*ccev1.Task, error)
	GetTask(ctx context.Context, ns, name string, opts *metav1.GetOptions) (*ccev1.Task, error)
	ListTask(ctx context.Context, ns string, opts *metav1.ListOptions) (*ccev1.TaskList, error)

	CreateWorkflow(ctx context.Context, ns string, workflow *ccev1.Workflow) (*ccev1.Workflow, error)
	GetWorkflow(ctx context.Context, ns, name string, ops *metav1.GetOptions) (*ccev1.Workflow, error)
	ListWorkflows(ctx context.Context, ns string, opts *metav1.ListOptions) (*ccev1.WorkflowList, error)
	ListWorkflowsByClusterID(ctx context.Context, ns, clusterID string) (*ccev1.WorkflowList, error)
	ListExclusiveWorkflowsByClusterID(ctx context.Context, ns, clusterID string) (*ccev1.WorkflowList, error)
	UpdateWorkflow(ctx context.Context, ns, name string, workflow *ccev1.Workflow) error
	UpdateWatchDogStatus(ctx context.Context, ns, name string, workflow *ccev1.Workflow) error
	DeleteWorkflow(ctx context.Context, ns, name string, opts *metav1.DeleteOptions) error

	DeleteClusterNamespace(ctx context.Context, namespace string) error
}
