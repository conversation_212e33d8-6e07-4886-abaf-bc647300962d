// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2019/11/20 15:14:00, by <EMAIL>, create
*/
/*
DESCRIPTION
包含 k8sclient.Interface 中 Instance CRD 相关接口实现
*/

package meta

import (
	"context"
	"errors"
	"fmt"
	"time"

	kerrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes/scheme"

	ccev1 "icode.baidu.com/baidu/cce-test/e2e-test/pkg/crd/api/v1"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/gotag/validate"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	ccetypes "icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
)

const (
	instanceResource = "instances"

	// InstanceClusterIDLabelKey InstanceCRD 关联 ClusterCRD
	// e.g. cluster-id: c-adfeadfc
	InstanceClusterIDLabelKey = "cluster-id"

	// InstanceClusterRoleLabelKey InstanceCRD 集群角色
	// e.g. cluster-role: master
	InstanceClusterRoleLabelKey = "cluster-role"

	// InstanceAccountIDLabelKey InstanceCRD 关联 AccountID
	InstanceAccountIDLabelKey = "account-id"

	// InstanceUserIDLabelKey InstanceCRD 关联 UserID
	InstanceUserIDLabelKey = "user-id"
)

// ListInstances 获取 Instance 列表
func (c *Client) ListInstances(ctx context.Context, ns string, opts *metav1.ListOptions) (*ccev1.InstanceList, error) {
	if opts != nil && len(opts.ResourceVersion) == 0 {
		opts.ResourceVersion = "0"
	}
	result := ccev1.InstanceList{}
	err := c.restclient.
		Get().
		Namespace(ns).
		Resource(instanceResource).
		VersionedParams(opts, scheme.ParameterCodec).
		Do(ctx).
		Into(&result)

	return &result, err
}

// GetInstance - 获取 Instance 详情
//
// PARAMS:
//   - ctx: The context to trace request
//   - ns:  namespace
//   - name: clusterID
//
// RETURNS:
//
//	instance: *ccev1.Instance
//	error: nil if succeed, error if fail
//	重要: 如果集群不存在, 返回 (nil, nil)
func (c *Client) GetInstance(ctx context.Context, ns, name string, opts *metav1.GetOptions) (*ccev1.Instance, error) {
	result := ccev1.Instance{}
	err := c.restclient.
		Get().
		Namespace(ns).
		Resource(instanceResource).
		Name(name).
		VersionedParams(opts, scheme.ParameterCodec).
		Do(ctx).
		Into(&result)

	// 如果对象不存在, 返回 (nil, nil)
	if kerrors.IsNotFound(err) {
		return nil, nil
	}

	return &result, err
}

// CreateInstance 创建 Instance
func (c *Client) CreateInstance(ctx context.Context, ns string, instance *ccev1.Instance) (*ccev1.Instance, error) {
	result := ccev1.Instance{}
	err := c.restclient.
		Post().
		Namespace(ns).
		Resource(instanceResource).
		Body(instance).
		Do(ctx).
		Into(&result)

	return &result, err
}

// UpdateInstance - UpdateInstance update instance
func (c *Client) UpdateInstance(ctx context.Context, ns, name string, instance *ccev1.Instance) error {
	// TODO: 只要这里有对conflict的重试还是可能导致数据丢失
	begin := time.Now()
	defer func() {
		logger.Infof(ctx, "update instance %s cost %s", name, time.Since(begin))
	}()

	var err error
	logger := logger.WithValues("instanceID", name)

	for i := 0; i < 10; i++ {
		err = c.restclient.
			Put().
			Namespace(ns).
			Resource(instanceResource).
			Name(name).
			Body(instance).
			Do(ctx).
			Error()

		if err == nil {
			logger.Infof(ctx, "Update instance success")
			return nil
		} else if kerrors.IsConflict(err) {
			logger.Warnf(ctx, "Update instance conflict, retry")

			new, err := c.GetInstance(ctx, ns, name, &metav1.GetOptions{})
			if err != nil {
				logger.Errorf(ctx, "GetInstance failed: %s", err)
				return err
			}

			if new == nil {
				return errors.New("GetInstance return nil")
			}

			// 更新 ResourceVersion
			instance.ResourceVersion = new.ResourceVersion
		} else if err != nil {
			logger.Errorf(ctx, "Update instance failed: %s", err)
			return err
		}
	}

	return err
}

// UpdateInstanceSpec - update instance spec
func (c *Client) UpdateInstanceSpec(ctx context.Context, ns, name string, spec *ccetypes.InstanceSpec,
	instanceMeta *metav1.ObjectMeta, conditions map[validate.ValidateCondition]any, skipValidate bool) error {
	// TODO: 只要这里有对conflict的重试还是可能导致数据丢失
	var err error
	var instance *ccev1.Instance
	logger := logger.WithValues("instanceID", name)

	for i := 0; i < 10; i++ {
		instance, err = c.GetInstance(ctx, ns, name, &metav1.GetOptions{})
		if err != nil {
			logger.Errorf(ctx, "failed to get instance, err: %v", err)
			return err
		}

		if !skipValidate {
			// validate spec
			valid, errMsg, err := validate.ValidateByConditions(*spec, instance.Spec, conditions)
			if err != nil {
				// 暂时放行，防止 validate 包有 bug
				logger.Errorf(ctx, "validate.ValidateByConditions failed: %v", err)
				break
			}
			if !valid {
				return fmt.Errorf(errMsg)
			}
		}

		instance.Spec = *spec
		if instanceMeta != nil {
			instance.ObjectMeta.Annotations = instanceMeta.Annotations
		}
		if instance.Status.InstancePhase != ccetypes.InstancePhaseRunning {
			instance.Status.RetryCount = 0
		}

		err = c.restclient.
			Put().
			Namespace(ns).
			Resource(instanceResource).
			Name(name).
			Body(instance).
			Do(ctx).
			Error()
		if err == nil {
			logger.Infof(ctx, "instance spec updated")
			return nil
		}

		if kerrors.IsConflict(err) {
			logger.Errorf(ctx, "update instance spec conflict")
			continue
		}

		logger.Errorf(ctx, "failed to update instance spec, err: %v", err)
		return err
	}
	return err
}

// UpdateInstanceStatus - update instance status
func (c *Client) UpdateInstanceStatus(ctx context.Context, ns, name string, status *ccetypes.InstanceStatus) error {
	// TODO: 只要这里有对conflict的重试还是可能导致数据丢失
	begin := time.Now()
	defer func() {
		logger.Infof(ctx, "update instance status %s cost %s", name, time.Since(begin))
	}()
	var err error
	var instance *ccev1.Instance
	logger := logger.WithValues("instanceID", name)

	for i := 0; i < 10; i++ {
		instance, err = c.GetInstance(ctx, ns, name, &metav1.GetOptions{})
		if err != nil {
			logger.Errorf(ctx, "failed to get instance, err: %v", err)
			return err
		}

		instance.Status = *status
		err = c.restclient.
			Put().
			Namespace(ns).
			Resource(instanceResource).
			Name(name).
			Body(instance).
			Do(ctx).
			Error()
		if err == nil {
			logger.Infof(ctx, "instance status updated")
			return nil
		}

		if kerrors.IsConflict(err) {
			logger.Errorf(ctx, "update instance status conflict")
			continue
		}

		logger.Errorf(ctx, "failed to update instance status, err: %v", err)
		return err
	}
	return err
}

// DeleteInstance 删除 Instance
func (c *Client) DeleteInstance(ctx context.Context, ns, name string, opts *metav1.DeleteOptions) error {
	err := c.restclient.
		Delete().
		Namespace(ns).
		Resource(instanceResource).
		Name(name).
		Body(opts).
		Do(ctx).
		Error()
	if err != nil {
		logger.Errorf(ctx, "DeleteInstance %s/%s failed: %s", ns, name, err)
		return err
	}

	// 更新 RetryCount = 0
	// 更新 RetryCount, 防止 RetryCount 达到上限无法触发删除操作
	instance, err := c.GetInstance(ctx, ns, name, &metav1.GetOptions{})
	if err != nil {
		logger.Errorf(ctx, "GetInstance failed: %s", err)
		return err
	}

	if instance == nil {
		logger.Errorf(ctx, "GetInstance return nil, skip delete instance")
		return nil
	}

	// 更新 RetryCount
	if instance.Status.RetryCount != 0 {
		// TODO: 临时方案, 解决 controller 更新 status RetryCount 覆盖本次 update
		time.Sleep(3 * time.Second)
		logger.Infof(ctx, "Update RetryCount to 0")

		instance, err := c.GetInstance(ctx, ns, name, &metav1.GetOptions{})
		if err != nil {
			logger.Errorf(ctx, "GetInstance failed: %s", err)
			return err
		}

		if instance == nil {
			logger.Errorf(ctx, "GetInstance return nil, skip delete instance")
			return nil
		}

		instance.Status.RetryCount = 0

		if err := c.UpdateInstance(ctx, ns, name, instance); err != nil {
			logger.Errorf(ctx, "UpdateInstance failed: %s", err)
			return err
		}
	}

	return nil
}

// ListClusterMasters 获取 Cluster 对应 Master InstanceList
func (c *Client) ListClusterMasters(ctx context.Context, ns, clusterID string) (*ccev1.InstanceList, error) {
	labelSelector := metav1.LabelSelector{
		MatchLabels: map[string]string{
			InstanceClusterIDLabelKey:   clusterID,
			InstanceClusterRoleLabelKey: string(ccetypes.ClusterRoleMaster),
		},
	}

	labelSelectorStr := metav1.FormatLabelSelector(&labelSelector)

	logger.Infof(ctx, "ListClusterMasters selectorLabel: %v", labelSelectorStr)

	result := ccev1.InstanceList{}

	err := c.restclient.
		Get().
		Namespace(ns).
		Resource(instanceResource).
		VersionedParams(&metav1.ListOptions{
			LabelSelector:   labelSelectorStr,
			ResourceVersion: "0",
		}, scheme.ParameterCodec).
		Do(ctx).
		Into(&result)

	return &result, err
}

// ListClusterNodes 获取 Cluster 对应 Master InstanceList
func (c *Client) ListClusterNodes(ctx context.Context, ns, clusterID string) (*ccev1.InstanceList, error) {
	labelSelector := metav1.LabelSelector{
		MatchLabels: map[string]string{
			InstanceClusterIDLabelKey:   clusterID,
			InstanceClusterRoleLabelKey: string(ccetypes.ClusterRoleNode),
		},
	}

	labelSelectorStr := metav1.FormatLabelSelector(&labelSelector)

	logger.Infof(ctx, "ListClusterNodes selectorLabel: %v", labelSelectorStr)

	result := ccev1.InstanceList{}
	err := c.restclient.
		Get().
		Namespace(ns).
		Resource(instanceResource).
		VersionedParams(&metav1.ListOptions{
			LabelSelector:   labelSelectorStr,
			ResourceVersion: "0",
		}, scheme.ParameterCodec).
		Do(ctx).
		Into(&result)

	return &result, err
}
