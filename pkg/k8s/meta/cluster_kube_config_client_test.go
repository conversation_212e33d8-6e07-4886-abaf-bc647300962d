package meta

import (
	"context"
	"testing"

	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/kubernetes/fake"
	"k8s.io/client-go/rest"

	"icode.baidu.com/baidu/cce-test/e2e-test/services/cluster/cluster-controller/consts"
)

func TestClient_UpdateClusterKubeConfig(t *testing.T) {
	type fields struct {
		restclient rest.Interface
		clientset  kubernetes.Interface
	}
	type args struct {
		ctx    context.Context
		ns     string
		name   string
		config string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "test1",
			fields: fields{
				clientset: fake.NewSimpleClientset(&v1.ConfigMap{
					ObjectMeta: metav1.ObjectMeta{
						Namespace: metav1.NamespaceDefault,
						Name:      "cce-test",
					},
					Data: map[string]string{
						consts.MetaClusterKubeConfigConfigMapKey: "default",
					},
				}),
			},
			args: args{
				ctx:    context.TODO(),
				ns:     metav1.NamespaceDefault,
				name:   "cce-test",
				config: "test kubeconfig",
			},
			wantErr: false,
		},

		{
			name: "test2",
			fields: fields{
				clientset: fake.NewSimpleClientset(&v1.ConfigMap{
					ObjectMeta: metav1.ObjectMeta{
						Namespace: metav1.NamespaceDefault,
						Name:      "cce-test1",
					},
					Data: map[string]string{
						consts.MetaClusterKubeConfigConfigMapKey: "default",
					},
				}),
			},
			args: args{
				ctx:    context.TODO(),
				ns:     metav1.NamespaceDefault,
				name:   "cce-test",
				config: "test kubeconfig",
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Client{
				restclient: tt.fields.restclient,
				clientset:  tt.fields.clientset,
			}
			if err := c.UpdateClusterKubeConfig(tt.args.ctx, tt.args.ns, tt.args.name, tt.args.config); (err != nil) != tt.wantErr {
				t.Errorf("UpdateClusterKubeConfig() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestClient_DeleteClusterNamespace(t *testing.T) {
	type fields struct {
		restclient rest.Interface
		clientset  kubernetes.Interface
	}
	type args struct {
		ctx       context.Context
		namespace string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "normal case",
			fields: fields{
				restclient: nil,
				clientset: func() kubernetes.Interface {
					c := fake.NewSimpleClientset()
					_, _ = c.CoreV1().Namespaces().Create(context.TODO(), &v1.Namespace{
						TypeMeta: metav1.TypeMeta{
							Kind:       "",
							APIVersion: "",
						},
						ObjectMeta: metav1.ObjectMeta{
							Name:                       "default",
							GenerateName:               "",
							Namespace:                  "",
							SelfLink:                   "",
							UID:                        "",
							ResourceVersion:            "",
							Generation:                 0,
							CreationTimestamp:          metav1.Time{},
							DeletionTimestamp:          &metav1.Time{},
							DeletionGracePeriodSeconds: nil,
							Labels:                     map[string]string{},
							Annotations:                map[string]string{},
							OwnerReferences:            []metav1.OwnerReference{},
							Finalizers:                 []string{},
							ClusterName:                "",
							ManagedFields:              []metav1.ManagedFieldsEntry{},
						},
						Spec: v1.NamespaceSpec{
							Finalizers: []v1.FinalizerName{},
						},
						Status: v1.NamespaceStatus{
							Phase:      "",
							Conditions: []v1.NamespaceCondition{},
						},
					}, metav1.CreateOptions{})
					return c
				}(),
			},
			args: args{
				ctx:       context.TODO(),
				namespace: "default",
			},
			wantErr: false,
		},
		{
			name: "error case",
			fields: fields{
				restclient: nil,
				clientset:  fake.NewSimpleClientset(),
			},
			args: args{
				ctx:       context.TODO(),
				namespace: "calico",
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Client{
				restclient: tt.fields.restclient,
				clientset:  tt.fields.clientset,
			}
			if err := c.DeleteClusterNamespace(tt.args.ctx, tt.args.namespace); (err != nil) != tt.wantErr {
				t.Errorf("Client.DeleteClusterNamespace() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
