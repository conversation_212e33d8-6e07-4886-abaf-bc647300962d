// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2018/12/25 19:30:00, by <EMAIL>, create
*/
/*
DESCRIPTION
定义 Client 对象及初始化方法, 实现 meta.Interface 中方法
设计参考:
1. https://www.martin-helmich.de/en/blog/kubernetes-crd-client.html
2. https://github.com/kubernetes/client-go/blob/master/kubernetes/typed/core/v1/pod.go
*/
package meta

import (
	"context"
	"fmt"

	"k8s.io/apimachinery/pkg/runtime/serializer"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/kubernetes/scheme"
	"k8s.io/client-go/rest"
	"k8s.io/client-go/tools/clientcmd"

	ccev1 "icode.baidu.com/baidu/cce-test/e2e-test/pkg/crd/api/v1"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
)

// 检查 Client 是否实现 resource.Interface
var _ Interface = &Client{}

// Client 实现 meta.Interface 中定义接口
type Client struct {
	restclient rest.Interface       // 操作 CRD
	clientset  kubernetes.Interface // 操作一般资源
}

// NewClientForConfig 初始化 Client
func NewClientForConfig(ctx context.Context, restCfg *rest.Config) (Interface, error) {
	restCfg.QPS = 400
	restCfg.Burst = 400

	clientSet, err := kubernetes.NewForConfig(restCfg)
	if err != nil {
		return nil, err
	}

	c := *restCfg
	c.ContentConfig.GroupVersion = &ccev1.GroupVersion
	c.APIPath = "/apis"
	c.NegotiatedSerializer = serializer.WithoutConversionCodecFactory{CodecFactory: scheme.Codecs}
	c.UserAgent = rest.DefaultKubernetesUserAgent()
	// 和 controller worker 数量一致

	// 创建 RESTClient
	restClient, err := rest.RESTClientFor(&c)
	if err != nil {
		return nil, err
	}

	return &Client{
		restclient: restClient,
		clientset:  clientSet,
	}, nil
}

// NewClient 初始化 Client
func NewClient(ctx context.Context, kubeConfigPath string) (Interface, error) {
	restclient, err := newRESTClient(ctx, kubeConfigPath)
	if err != nil {
		logger.Errorf(ctx, "newRESTClient failed: %v", err)
		return nil, err
	}

	clientset, err := newClientSets(ctx, kubeConfigPath)
	if err != nil {
		logger.Errorf(ctx, "newClientSets failed: %v", err)
		return nil, err
	}

	return &Client{
		restclient: restclient,
		clientset:  clientset,
	}, nil
}

// newClientSets - 初始化 kubernetes.Clientset, 用于请求 Meta Cluster 一般对象
//
// PARAMS:
//   - ctx: The context to trace request
//   - kubeConfigPath: meta clusters config 的配置路径
//
// RETURNS:
//
//	kubernetes.Clientset
//	error: nil if succeed, error if fail
func newClientSets(ctx context.Context, kubeConfigPath string) (kubernetes.Interface, error) {
	var config *rest.Config
	var err error

	// 初始化 kube-config
	if kubeConfigPath == "" {
		logger.Infof(ctx, "Using in-cluster configuration")
		config, err = rest.InClusterConfig()
		if err != nil {
			logger.Errorf(ctx, "InClusterConfig failed: %v", err)
			return nil, err
		}
	} else {
		logger.Infof(ctx, "Using configuration from '%s'", kubeConfigPath)
		config, err = clientcmd.BuildConfigFromFlags("", kubeConfigPath)
		if err != nil {
			logger.Errorf(ctx, "BuildConfigFromFlags failed: %v", err)
			return nil, err
		}
	}
	// 和 controller worker 数量一致
	config.QPS = 400
	config.Burst = 400

	clientset, err := kubernetes.NewForConfig(config)
	if err != nil {
		logger.Errorf(ctx, "NewForConfig failed: %v", err)
		return nil, err
	}

	return clientset, nil
}

// newRESTClient - 初始化 REST Client, 用于请求 Meta Cluster CRD 对象
//
// PARAMS:
//   - ctx: The context to trace request
//   - kubeConfigPath: meta clusters config 的配置路径
//
// RETURNS:
//
//	rest.Interface
//	error: nil if succeed, error if fail
func newRESTClient(ctx context.Context, kubeConfigPath string) (rest.Interface, error) {
	var config *rest.Config
	var err error

	// 初始化 kube-config
	if kubeConfigPath == "" {
		logger.Infof(ctx, "Using in-cluster configuration")
		config, err = rest.InClusterConfig()
		if err != nil {
			logger.Errorf(ctx, "InClusterConfig failed: %v", err)
			return nil, err
		}
	} else {
		logger.Infof(ctx, "Using configuration from '%s'", kubeConfigPath)
		config, err = clientcmd.BuildConfigFromFlags("", kubeConfigPath)
		if err != nil {
			logger.Errorf(ctx, "BuildConfigFromFlags failed: %v", err)
			return nil, err
		}
	}

	// 构建 RestClient
	if config == nil {
		msg := "NewClient failed: config is nil"
		logger.Errorf(ctx, msg)
		return nil, fmt.Errorf(msg)
	}

	// 构建 config
	c := *config
	c.ContentConfig.GroupVersion = &ccev1.GroupVersion
	c.APIPath = "/apis"
	c.NegotiatedSerializer = serializer.WithoutConversionCodecFactory{CodecFactory: scheme.Codecs}
	c.UserAgent = rest.DefaultKubernetesUserAgent()
	// 和 controller worker 数量一致
	c.QPS = 400
	c.Burst = 400

	// 创建 RESTClient
	client, err := rest.RESTClientFor(&c)
	if err != nil {
		logger.Errorf(ctx, "RESTClientFor failed: %v", err)
		return nil, err
	}

	return client, nil
}

func init() {
	// 注册 Scheme: CRD 序列化和反序列化方法
	ccev1.AddToScheme(scheme.Scheme)
}
