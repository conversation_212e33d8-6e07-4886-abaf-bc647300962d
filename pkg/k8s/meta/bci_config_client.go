// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2020/05/13 16:40:00, by ye<PERSON><PERSON><PERSON>@baidu.com, create
*/
/*
定义对meta集群中BCI config的增删改查
*/
package meta

import (
	"context"
	"errors"

	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	errs "icode.baidu.com/baidu/cce-test/e2e-test/services/cluster/cluster-controller/errors"
)

func (c *Client) CreateBCIConfig(ctx context.Context, cm *v1.ConfigMap) error {
	if cm == nil {
		return errors.New("configMap cannot be nil")
	}
	if _, err := c.clientset.CoreV1().ConfigMaps(cm.GetNamespace()).Create(ctx, cm, metav1.CreateOptions{}); err != nil {
		logger.Errorf(ctx, "Create configmap %v/%v failed: %v", cm.GetNamespace(), cm.GetName(), err)
		return wrapError(ctx, err)
	}

	return nil
}

func (c *Client) DeleteBCIConfig(ctx context.Context, ns, name string) error {
	return wrapError(ctx, c.clientset.CoreV1().ConfigMaps(ns).Delete(ctx, name, metav1.DeleteOptions{}))
}

func (c *Client) GetBCIConfig(ctx context.Context, ns, name string) (*v1.ConfigMap, error) {
	cm, err := c.clientset.CoreV1().ConfigMaps(ns).Get(ctx, name, metav1.GetOptions{})
	if err != nil {
		logger.Errorf(ctx, "Get configMap %s/%s failed: %v", ns, name, err)
		return nil, wrapError(ctx, err)
	}
	if cm == nil {
		return nil, errs.ErrObjectCorrupts.New(ctx, "GetBCIConfig failed: configMap is nil")
	}
	return cm, nil
}

func (c *Client) ListBCIConfigsByLabel(ctx context.Context, ns, labelSelector string) (*v1.ConfigMapList, error) {
	list, err := c.clientset.CoreV1().ConfigMaps(ns).List(ctx, metav1.ListOptions{
		LabelSelector: labelSelector,
	})
	if err != nil {
		logger.Errorf(ctx, "list configMap by label %s in %s failed: %v", labelSelector, ns, err)
		return nil, wrapError(ctx, err)
	}
	return list, nil
}
