// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2021/12/08 11:37:00, by <EMAIL>, create
*/
/*
DESCRIPTION
包含 k8sclient.Interface 中 Workflow CRD 相关接口的实现
*/

package meta

import (
	"context"
	"reflect"
	"testing"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	ccev1 "icode.baidu.com/baidu/cce-test/e2e-test/pkg/crd/api/v1"
	ccetypes "icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
)

func Test_workflowLabels(t *testing.T) {
	type args struct {
		ctx      context.Context
		workflow *ccev1.Workflow
	}
	tests := []struct {
		name    string
		args    args
		want    map[string]string
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "workflow.Labels == nil: 正常添加 cluster-id 和 account-id",
			args: args{
				ctx: context.TODO(),
				workflow: &ccev1.Workflow{
					ObjectMeta: metav1.ObjectMeta{},
					Spec: ccetypes.WorkflowSpec{
						AccountID: "account-id",
						ClusterID: "cluster-id",
					},
				},
			},
			want: map[string]string{
				ccev1.LabelWorkflowClusterID: "cluster-id",
				ccev1.LabelWorkflowAccountID: "account-id",
			},
			wantErr: false,
		},
		{
			name: "workflow.Labels != nil: 正常添加 cluster-id 和 account-id",
			args: args{
				ctx: context.TODO(),
				workflow: &ccev1.Workflow{
					ObjectMeta: metav1.ObjectMeta{
						Labels: map[string]string{
							"app":                        "cce",
							"name":                       "chen",
							ccev1.LabelWorkflowClusterID: "cluster-id",
						},
					},
					Spec: ccetypes.WorkflowSpec{
						AccountID: "account-id",
						ClusterID: "cluster-id",
					},
				},
			},
			want: map[string]string{
				"app":                        "cce",
				"name":                       "chen",
				ccev1.LabelWorkflowClusterID: "cluster-id",
				ccev1.LabelWorkflowAccountID: "account-id",
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := workflowLabels(tt.args.ctx, tt.args.workflow)
			if (err != nil) != tt.wantErr {
				t.Errorf("workflowLabels() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("workflowLabels() = %v, want %v", got, tt.want)
			}
		})
	}
}
