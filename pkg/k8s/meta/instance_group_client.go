// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2019/11/20 15:14:00, by <EMAIL>, create
*/
/*
DESCRIPTION
包含 k8sclient.Interface 中 InstanceGroup CRD 相关接口实现
*/

package meta

import (
	"context"
	"time"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes/scheme"

	ccev1 "icode.baidu.com/baidu/cce-test/e2e-test/pkg/crd/api/v1"
)

func (c *Client) CreateInstanceGroup(ctx context.Context, ns string, ig *ccev1.InstanceGroup) (*ccev1.InstanceGroup, error) {
	result := ccev1.InstanceGroup{}
	err := c.restclient.
		Post().
		Namespace(ns).
		Resource(ccev1.InstanceGroupResource).
		Body(ig).
		Do(ctx).
		Into(&result)

	return &result, err
}

func (c *Client) GetInstanceGroup(ctx context.Context, ns, name string, opts *metav1.GetOptions) (*ccev1.InstanceGroup, error) {
	result := ccev1.InstanceGroup{}
	err := c.restclient.
		Get().
		Namespace(ns).
		Resource(ccev1.InstanceGroupResource).
		Name(name).
		VersionedParams(opts, scheme.ParameterCodec).
		Do(ctx).
		Into(&result)

	return &result, err
}

func (c *Client) ListInstanceGroup(ctx context.Context, ns string, opts *metav1.ListOptions) (*ccev1.InstanceGroupList, error) {
	if opts == nil {
		opts = &metav1.ListOptions{}
	}

	var timeout time.Duration
	if opts.TimeoutSeconds != nil {
		timeout = time.Duration(*opts.TimeoutSeconds) * time.Second
	}
	// TODO: remove after update client-go version to release-1.18
	if deadline, ok := ctx.Deadline(); ok {
		timeout = time.Until(deadline)
	}

	result := ccev1.InstanceGroupList{}
	err := c.restclient.
		Get().
		Namespace(ns).
		Resource(ccev1.InstanceGroupResource).
		VersionedParams(opts, scheme.ParameterCodec).
		Timeout(timeout).
		Do(ctx).
		Into(&result)

	return &result, err
}

func (c *Client) UpdateInstanceGroup(ctx context.Context, ns, name string, ig *ccev1.InstanceGroup) (*ccev1.InstanceGroup, error) {
	result := ccev1.InstanceGroup{}
	err := c.restclient.
		Put().
		Namespace(ns).
		Resource(ccev1.InstanceGroupResource).
		Name(name).
		Body(ig).
		Do(ctx).
		Into(&result)

	return &result, err
}

func (c *Client) DeleteInstanceGroup(ctx context.Context, ns, name string, opts *metav1.DeleteOptions) error {
	return c.restclient.
		Delete().
		Namespace(ns).
		Resource(ccev1.InstanceGroupResource).
		Name(name).
		Body(opts).
		Do(ctx).
		Error()
}
