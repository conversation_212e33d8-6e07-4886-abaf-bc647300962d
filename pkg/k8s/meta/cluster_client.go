// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2018/12/25 19:30:00, by <EMAIL>, create
*/
/*
DESCRIPTION
包含 k8sclient.Interface 中 Cluster CRD 相关接口的实现
*/

package meta

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	kerrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes/scheme"

	ccev1 "icode.baidu.com/baidu/cce-test/e2e-test/pkg/crd/api/v1"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/gotag/validate"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	ccetypes "icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
)

const (
	clusterResource = "clusters"
)

// ListClusters 获取集群列表
func (c *Client) ListClusters(ctx context.Context, ns string, opts *metav1.ListOptions) (*ccev1.ClusterList, error) {
	result := ccev1.ClusterList{}
	err := c.restclient.
		Get().
		Namespace(ns).
		Resource(clusterResource).
		VersionedParams(opts, scheme.ParameterCodec).
		Do(ctx).
		Into(&result)

	return &result, err
}

// GetCluster - 获取集群详情
//
// PARAMS:
//   - ctx: The context to trace request
//   - ns:  namespace
//   - name: clusterID
//
// RETURNS:
//
//	cluster: *ccev1.Cluster
//	error: nil if succeed, error if fail
func (c *Client) GetCluster(ctx context.Context, ns, name string, opts *metav1.GetOptions) (*ccev1.Cluster, error) {
	name = strings.ToLower(name)
	result := ccev1.Cluster{}
	err := c.restclient.
		Get().
		Namespace(ns).
		Resource(clusterResource).
		Name(name).
		VersionedParams(opts, scheme.ParameterCodec).
		Do(ctx).
		Into(&result)

	if kerrors.IsNotFound(err) {
		logger.Warnf(ctx, "Cluster %v/%v not found", ns, name)
		return nil, ErrClusterNotExist.New(ctx, "Cluster %v/%v not found", ns, name)
	}

	return &result, err
}

// CreateCluster 创建集群
func (c *Client) CreateCluster(ctx context.Context, ns string, cluster *ccev1.Cluster) (*ccev1.Cluster, error) {
	result := ccev1.Cluster{}
	err := c.restclient.
		Post().
		Namespace(ns).
		Resource(clusterResource).
		Body(cluster).
		Do(ctx).
		Into(&result)

	return &result, err
}

// UpdateCluster 更新集群
func (c *Client) UpdateCluster(ctx context.Context, ns, name string, cluster *ccev1.Cluster) error {
	var err error
	name = strings.ToLower(name)
	for i := 0; i < 10; i++ {
		err = c.restclient.
			Put().
			Namespace(ns).
			Resource(clusterResource).
			Name(name).
			Body(cluster).
			Do(ctx).
			Error()

		if err == nil {
			logger.Infof(ctx, "Update cluster success")
			return nil
		} else if kerrors.IsConflict(err) {
			logger.Warnf(ctx, "Update cluster conflict, retry")

			new, err := c.GetCluster(ctx, ns, name, &metav1.GetOptions{})
			if err != nil {
				logger.Errorf(ctx, "GetCluster failed: %s", err)
				return err
			}

			if new == nil {
				return errors.New("GetCluster return nil")
			}

			// 不更新 Spec
			cluster.Spec = *(new.Spec.DeepCopy())

			// 更新 ResourceVersion
			cluster.ResourceVersion = new.ResourceVersion

			// 处理 RetryCount, 场景:
			// 1. cluster-controller 开始 reconcile;
			// 2. 期间 User 更新 RetryCount;
			// 3. cluster-controller 回写 Status, 不能覆盖用户设置 Status.
			// 4. cluster.Status.RetryCount != 1 避免第一次写入失败
			if new.Status.RetryCount == 0 && cluster.Status.RetryCount != 1 {
				cluster.Status.RetryCount = 0
			}
		} else if err != nil {
			logger.Errorf(ctx, "Upate cluster failed: %s", err)
			return err
		}
	}

	logger.Infof(ctx, "UpdateCluster %s/%s failed: %s", ns, name, err)
	return err
}

// DeleteCluster 删除集群
func (c *Client) DeleteCluster(ctx context.Context, ns, name string, opts *metav1.DeleteOptions) error {
	name = strings.ToLower(name)
	err := c.restclient.
		Delete().
		Namespace(ns).
		Resource(clusterResource).
		Name(name).
		Body(opts).
		Do(ctx).
		Error()
	if err != nil {
		logger.Errorf(ctx, "DeleteCluster %s/%s failed: %s", ns, name, err)
		return err
	}

	// 更新 RetryCount, 防止 RetryCount 达到上限无法触发删除操作
	cluster, err := c.GetCluster(ctx, ns, name, &metav1.GetOptions{})
	if err != nil {
		logger.Errorf(ctx, "GetCluster failed: %s", err)
		return err
	}

	if cluster == nil {
		logger.Errorf(ctx, "GetCluster return nil, skip delete cluster")
		return nil
	}

	// 更新 RetryCount
	if cluster.Status.RetryCount != 0 {
		// TODO: 临时方案, 解决 controller 更新 status RetryCount 覆盖本次 update
		time.Sleep(3 * time.Second)
		logger.Infof(ctx, "Update RetryCount to 0")

		cluster, err := c.GetCluster(ctx, ns, name, &metav1.GetOptions{})
		if err != nil {
			logger.Errorf(ctx, "GetCluster failed: %s", err)
			return err
		}

		if cluster == nil {
			logger.Errorf(ctx, "GetCluster return nil, skip delete cluster")
			return nil
		}
		cluster.Status.RetryCount = 0

		if err := c.UpdateCluster(ctx, ns, name, cluster); err != nil {
			logger.Errorf(ctx, "UpdateCluster failed: %s", err)
			return err
		}
	}

	return nil
}

// UpdateClusterSpec 更新cluster spec + annotation
func (c *Client) UpdateClusterSpec(ctx context.Context, ns, name string, spec *ccetypes.ClusterSpec, clusterMeta *metav1.ObjectMeta,
	conditions map[validate.ValidateCondition]any, skipValidate bool) error {
	var err error
	var cluster *ccev1.Cluster
	logger := logger.WithValues("clusterID", name)

	for i := 0; i < 10; i++ {
		cluster, err = c.GetCluster(ctx, ns, name, &metav1.GetOptions{})
		if err != nil {
			logger.Errorf(ctx, "failed to get cluster, err: %v", err)
			return err
		}

		if !skipValidate {
			// validate spec
			valid, errMsg, err := validate.ValidateByConditions(*spec, cluster.Spec, conditions)
			if err != nil {
				// TODO: 暂时放行，防止 validate 包有 bug
				logger.Errorf(ctx, "validate.ValidateByConditions failed: %v", err)
				break
			}
			if !valid {
				return fmt.Errorf(errMsg)
			}
		}

		cluster.Spec = *spec
		if clusterMeta != nil {
			cluster.ObjectMeta.Annotations = clusterMeta.Annotations
		}

		if cluster.Status.ClusterPhase != ccetypes.ClusterPhaseRunning && cluster.Status.ClusterPhase != ccetypes.ClusterPhaseUpgrading {
			cluster.Status.RetryCount = 0
		}

		err = c.restclient.
			Put().
			Namespace(ns).
			Resource(clusterResource).
			Name(name).
			Body(cluster).
			Do(ctx).
			Error()
		if err == nil {
			logger.Infof(ctx, "cluster spec updated")
			return nil
		}

		if kerrors.IsConflict(err) {
			logger.Errorf(ctx, "update cluster spec conflict")
			continue
		}

		logger.Errorf(ctx, "failed to update cluster spec, err: %v", err)
		return err
	}
	return err
}

// UpdateClusterLabels 更新且仅仅更新Cluster CRD的Label
func (c *Client) UpdateClusterLabels(ctx context.Context, ns, name string, labels map[string]string) error {
	var err error
	var cluster *ccev1.Cluster
	logger := logger.WithValues("clusterID", name)

	for i := 0; i < 10; i++ {
		cluster, err = c.GetCluster(ctx, ns, name, &metav1.GetOptions{})
		if err != nil {
			logger.Errorf(ctx, "failed to get cluster, err: %v", err)
			return err
		}

		if labels != nil {
			cluster.ObjectMeta.Labels = labels
		} else {
			logger.Warnf(ctx, "labels is nil, skip update labels", err)
			return nil
		}

		err = c.restclient.
			Put().
			Namespace(ns).
			Resource(clusterResource).
			Name(name).
			Body(cluster).
			Do(ctx).
			Error()
		if err == nil {
			logger.Infof(ctx, "cluster labels updated")
			return nil
		}

		if kerrors.IsConflict(err) {
			logger.Errorf(ctx, "update cluster labels conflict")
			continue
		}

		logger.Errorf(ctx, "failed to update cluster labels, err: %v", err)
		return err
	}
	return err
}
