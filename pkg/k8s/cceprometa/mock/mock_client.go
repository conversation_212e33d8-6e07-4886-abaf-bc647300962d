// Code generated by MockGen. DO NOT EDIT.
// Source: icode.baidu.com/baidu/cce-test/e2e-test/pkg/k8s/cceprometa (interfaces: Interface)

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	gomock "github.com/golang/mock/gomock"
	v1alpha1 "icode.baidu.com/baidu/cce-test/e2e-test/services/network/eni-resource-controller/api/v1alpha1"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	reflect "reflect"
)

// MockInterface is a mock of Interface interface
type MockInterface struct {
	ctrl     *gomock.Controller
	recorder *MockInterfaceMockRecorder
}

// MockInterfaceMockRecorder is the mock recorder for MockInterface
type MockInterfaceMockRecorder struct {
	mock *MockInterface
}

// NewMockInterface creates a new mock instance
func NewMockInterface(ctrl *gomock.Controller) *MockInterface {
	mock := &MockInterface{ctrl: ctrl}
	mock.recorder = &MockInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use
func (m *MockInterface) EXPECT() *MockInterfaceMockRecorder {
	return m.recorder
}

// GetCrossVPCENI mocks base method
func (m *MockInterface) GetCrossVPCENI(arg0 context.Context, arg1 string, arg2 *v1.GetOptions) (*v1alpha1.CrossVPCEni, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCrossVPCENI", arg0, arg1, arg2)
	ret0, _ := ret[0].(*v1alpha1.CrossVPCEni)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCrossVPCENI indicates an expected call of GetCrossVPCENI
func (mr *MockInterfaceMockRecorder) GetCrossVPCENI(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCrossVPCENI", reflect.TypeOf((*MockInterface)(nil).GetCrossVPCENI), arg0, arg1, arg2)
}

// ListCrossVPCENI mocks base method
func (m *MockInterface) ListCrossVPCENI(arg0 context.Context, arg1 *v1.ListOptions) (*v1alpha1.CrossVPCEniList, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListCrossVPCENI", arg0, arg1)
	ret0, _ := ret[0].(*v1alpha1.CrossVPCEniList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListCrossVPCENI indicates an expected call of ListCrossVPCENI
func (mr *MockInterfaceMockRecorder) ListCrossVPCENI(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListCrossVPCENI", reflect.TypeOf((*MockInterface)(nil).ListCrossVPCENI), arg0, arg1)
}
