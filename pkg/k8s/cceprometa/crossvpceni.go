package cceprometa

import (
	"context"
	"strings"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes/scheme"

	crossvpceni "icode.baidu.com/baidu/cce-test/e2e-test/services/network/eni-resource-controller/api/v1alpha1"
)

const (
	crossvpceniResource = "crossvpcenis"
)

// GetCrossVPCENI
//
// PARAMS:
//   - ctx: The context to trace request
//   - ns:  namespace
//   - name:
func (c *Client) GetCrossVPCENI(ctx context.Context, name string, opts *metav1.GetOptions) (*crossvpceni.CrossVPCEni, error) {
	name = strings.ToLower(name)
	result := crossvpceni.CrossVPCEni{}
	err := c.restclient.
		Get().
		// Namespace(ns).
		Resource(crossvpceniResource).
		Name(name).
		VersionedParams(opts, scheme.ParameterCodec).
		Do(ctx).
		Into(&result)

	return &result, err
}

// ListCrossVPCENI
func (c *Client) ListCrossVPCENI(ctx context.Context, opts *metav1.ListOptions) (*crossvpceni.CrossVPCEniList, error) {
	result := crossvpceni.CrossVPCEniList{}
	err := c.restclient.
		Get().
		// Namespace(ns).
		Resource(crossvpceniResource).
		VersionedParams(opts, scheme.ParameterCodec).
		Do(ctx).
		Into(&result)

	return &result, err
}
