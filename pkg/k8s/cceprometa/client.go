package cceprometa

import (
	"context"

	"k8s.io/apimachinery/pkg/runtime/serializer"
	"k8s.io/client-go/kubernetes/scheme"
	"k8s.io/client-go/rest"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	crossvpceni "icode.baidu.com/baidu/cce-test/e2e-test/services/network/eni-resource-controller/api/v1alpha1"
)

func init() {
	// 注册 Scheme: CRD 序列化和反序列化方法
	crossvpceni.AddToScheme(scheme.Scheme)
}

var _ Interface = &Client{}

type Client struct {
	restclient rest.Interface // 操作 CRD
}

func NewClient(ctx context.Context, config *rest.Config) (Interface, error) {
	config.ContentConfig.GroupVersion = &crossvpceni.GroupVersion
	config.APIPath = "/apis"
	config.NegotiatedSerializer = serializer.WithoutConversionCodecFactory{CodecFactory: scheme.Codecs}
	config.UserAgent = rest.DefaultKubernetesUserAgent()

	config.QPS = 400
	config.Burst = 400

	// 创建 RESTClient
	restclient, err := rest.RESTClientFor(config)
	if err != nil {
		logger.Errorf(ctx, "RESTClientFor failed: %v", err)
		return nil, err
	}

	return &Client{
		restclient: restclient,
	}, nil
}
