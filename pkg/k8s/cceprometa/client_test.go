package cceprometa

import (
	"context"
	"testing"

	"k8s.io/client-go/rest"
)

func TestNewClient(t *testing.T) {
	type args struct {
		ctx    context.Context
		config *rest.Config
	}
	tests := []struct {
		name    string
		args    args
		want    Interface
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "normal",
			args: args{
				ctx:    context.TODO(),
				config: &rest.Config{},
			},
			want: &Client{
				restclient: &rest.RESTClient{},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			_, err := NewClient(tt.args.ctx, tt.args.config)
			if (err != nil) != tt.wantErr {
				t.Errorf("NewClient() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			// gotclient, _ := got.(*Client)
			// wantclient, _ := tt.want.(*Client)
			// if !reflect.DeepEqual(got, tt.want) {
			// 	t.<PERSON>("NewClient() = %s, want %s", *gotclient, *wantclient)
			// }
		})
	}
}
