package cceprometa

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"reflect"
	"testing"

	kerrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/runtime/schema"
	"k8s.io/apimachinery/pkg/runtime/serializer"
	"k8s.io/client-go/kubernetes/scheme"
	"k8s.io/client-go/rest"
	"k8s.io/client-go/rest/fake"

	crossvpceni "icode.baidu.com/baidu/cce-test/e2e-test/services/network/eni-resource-controller/api/v1alpha1"
)

func init() {
	crossvpceni.AddToScheme(scheme.Scheme)
}

func newFakeRESTClient(resourcePaths map[string]runtime.Object) rest.Interface {
	return &fake.RESTClient{
		Client:       fake.CreateHTTPClient(fakeReqHandler(resourcePaths)),
		GroupVersion: crossvpceni.GroupVersion,
		NegotiatedSerializer: serializer.WithoutConversionCodecFactory{
			CodecFactory: scheme.Codecs,
		},
		VersionedAPIPath: "/apis/cce.io/v1alpha1",
	}
}

func fakeReqHandler(resourcePaths map[string]runtime.Object) func(req *http.Request) (*http.Response, error) {
	return func(req *http.Request) (*http.Response, error) {
		// URL 路由
		// TODO: Object NotFound 处理有问题
		cluster, exist := resourcePaths[req.URL.Path]
		if !exist && req.Method != "GET" {
			return nil, fmt.Errorf("unexpected request for URL %q with method %q", req.URL.String(), req.Method)
		}

		switch req.Method {
		case "GET":
			if cluster == nil {
				return &http.Response{StatusCode: 404, Header: defaultHeaders(), Body: bytesBody(nil)}, kerrors.NewNotFound(schema.GroupResource{}, "")
			}

			res, err := json.Marshal(cluster)
			if err != nil {
				return nil, err
			}

			return &http.Response{StatusCode: 200, Header: defaultHeaders(), Body: bytesBody(res)}, nil
		case "POST":
			// TODO: Map 记录 Object
			codecs := serializer.NewCodecFactory(scheme.Scheme)
			decoder := codecs.UniversalDeserializer()
			body, err := io.ReadAll(req.Body)
			if err != nil {
				return nil, err
			}

			newObj, newGVK, err := decoder.Decode(body, nil, nil)
			if err != nil {
				return nil, fmt.Errorf("unexpected request body: %v", err)
			}

			if *newGVK != newObj.GetObjectKind().GroupVersionKind() {
				return nil, fmt.Errorf("unexpected scale API version %s (expected %s)", newGVK.String(), newObj.GetObjectKind().GroupVersionKind().String())
			}

			res, err := json.Marshal(newObj)
			if err != nil {
				return nil, err
			}

			return &http.Response{StatusCode: 200, Header: defaultHeaders(), Body: bytesBody(res)}, nil
		case "PUT":
			return &http.Response{StatusCode: 200, Header: defaultHeaders(), Body: bytesBody([]byte{})}, nil
		case "DELETE":
			return &http.Response{StatusCode: 200, Header: defaultHeaders(), Body: bytesBody([]byte{})}, nil
		default:
			return nil, fmt.Errorf("unexpected request for URL %q with method %q", req.URL.String(), req.Method)
		}
	}
}

func bytesBody(bodyBytes []byte) io.ReadCloser {
	return io.NopCloser(bytes.NewReader(bodyBytes))
}

func defaultHeaders() http.Header {
	header := http.Header{}
	header.Set("Content-Type", runtime.ContentTypeJSON)
	return header
}

func defaultGetTest() *crossvpceni.CrossVPCEni {
	return &crossvpceni.CrossVPCEni{
		ObjectMeta: metav1.ObjectMeta{
			Name: "test",
		},
	}
}

func defaultListTest() *crossvpceni.CrossVPCEniList {
	return &crossvpceni.CrossVPCEniList{
		Items: []crossvpceni.CrossVPCEni{
			{
				ObjectMeta: metav1.ObjectMeta{
					Name: "test",
				},
			},
		},
	}
}

func TestClient_GetCrossVPCENI(t *testing.T) {
	type fields struct {
		restclient rest.Interface
	}
	type args struct {
		ctx context.Context

		// ns   string
		name string

		opts *metav1.GetOptions
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *crossvpceni.CrossVPCEni
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "Get 正常",
			fields: fields{
				restclient: newFakeRESTClient(map[string]runtime.Object{
					"/apis/cce.io/v1alpha1/crossvpcenis/test": defaultGetTest(),
				}),
			},
			args: args{
				ctx: context.TODO(),
				// ns:   "default",
				name: "test",
				opts: &metav1.GetOptions{},
			},
			want:    defaultGetTest(),
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Client{
				restclient: tt.fields.restclient,
			}
			got, err := c.GetCrossVPCENI(tt.args.ctx, tt.args.name, tt.args.opts)
			if (err != nil) != tt.wantErr {
				t.Errorf("Client.GetCrossVPCENI() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Client.GetCrossVPCENI() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestClient_ListCrossVPCENI(t *testing.T) {
	type fields struct {
		restclient rest.Interface
	}
	type args struct {
		ctx context.Context

		// ns   string
		opts *metav1.ListOptions
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *crossvpceni.CrossVPCEniList
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "List 正常",
			fields: fields{
				restclient: newFakeRESTClient(map[string]runtime.Object{
					"/apis/cce.io/v1alpha1/crossvpcenis": defaultListTest(),
				}),
			},
			args: args{
				ctx: context.TODO(),
				// ns:   "default",
				opts: &metav1.ListOptions{},
			},
			want:    defaultListTest(),
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Client{
				restclient: tt.fields.restclient,
			}
			got, err := c.ListCrossVPCENI(tt.args.ctx, tt.args.opts)
			if (err != nil) != tt.wantErr {
				t.Errorf("Client.ListCrossVPCENI() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Client.ListCrossVPCENI() = %v, want %v", got, tt.want)
			}
		})
	}
}
