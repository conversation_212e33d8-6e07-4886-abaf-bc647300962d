package cceprometa

import (
	"context"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	crossvpceni "icode.baidu.com/baidu/cce-test/e2e-test/services/network/eni-resource-controller/api/v1alpha1"
)

//go:generate mockgen -destination=./mock/mock_client.go -package=mock icode.baidu.com/baidu/cce-test/e2e-test/pkg/k8s/cceprometa Interface

// Interface 定义 k8s client-go 操作 CRD 相关方法
type Interface interface {
	// client for crossvpceni
	GetCrossVPCENI(ctx context.Context, name string, ops *metav1.GetOptions) (*crossvpceni.CrossVPCEni, error)

	ListCrossVPCENI(ctx context.Context, ops *metav1.ListOptions) (*crossvpceni.CrossVPCEniList, error)
}
