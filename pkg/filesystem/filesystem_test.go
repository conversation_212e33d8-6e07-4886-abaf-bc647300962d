package filesystem

import (
	"context"
	"os"
	"path/filepath"
	"testing"

	uuid "github.com/satori/go.uuid"
	"gotest.tools/assert"
)

func TestFilesystem(t *testing.T) {
	ctx := context.TODO()
	testDir := filepath.Join(os.Temp<PERSON>ir(), uuid.NewV4().String())

	d := NewDirectory()
	f := NewFile()

	_, err := f.Stat(ctx, testDir)
	assert.ErrorContains(t, err, "no such file or directory")

	err = d.Mkdir<PERSON>ll(ctx, testDir)
	assert.NilError(t, err)

	info, err := f.Stat(ctx, testDir)
	assert.NilError(t, err)
	assert.Equal(t, info.IsDir(), true)

	defer func() {
		err := d.RemoveAll(ctx, testDir)
		assert.NilError(t, err)
		_, err = f.Stat(ctx, testDir)
		assert.ErrorContains(t, err, "no such file or directory")
	}()

	filename := "test"
	path := filepath.Join(testDir, filename)
	content := `content`

	_, err = f.ReadFrom(ctx, path)
	assert.ErrorContains(t, err, "no such file or directory")

	err = f.WriteTo(ctx, path, content, 0644)
	assert.NilError(t, err)

	v, err := f.ReadFrom(ctx, path)
	assert.NilError(t, err)
	assert.Equal(t, v, content)

	err = f.WriteTo(ctx, testDir, content)
	assert.ErrorContains(t, err, "fail to open file")
}
