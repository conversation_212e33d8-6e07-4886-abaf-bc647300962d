// Code generated by MockGen. DO NOT EDIT.
// Source: icode.baidu.com/baidu/cce-test/e2e-test/pkg/filesystem (interfaces: Filesystem)

// Package filesystem is a generated GoMock package.
package filesystem

import (
	context "context"
	gomock "github.com/golang/mock/gomock"
	os "os"
	reflect "reflect"
)

// MockFilesystem is a mock of Filesystem interface
type MockFilesystem struct {
	ctrl     *gomock.Controller
	recorder *MockFilesystemMockRecorder
}

// MockFilesystemMockRecorder is the mock recorder for MockFilesystem
type MockFilesystemMockRecorder struct {
	mock *MockFilesystem
}

// NewMockFilesystem creates a new mock instance
func NewMockFilesystem(ctrl *gomock.Controller) *MockFilesystem {
	mock := &MockFilesystem{ctrl: ctrl}
	mock.recorder = &MockFilesystemMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use
func (m *MockFilesystem) EXPECT() *MockFilesystemMockRecorder {
	return m.recorder
}

// MkdirAll mocks base method
func (m *MockFilesystem) MkdirAll(arg0 context.Context, arg1 string, arg2 ...os.FileMode) error {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "MkdirAll", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// MkdirAll indicates an expected call of MkdirAll
func (mr *MockFilesystemMockRecorder) MkdirAll(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MkdirAll", reflect.TypeOf((*MockFilesystem)(nil).MkdirAll), varargs...)
}

// ReadFrom mocks base method
func (m *MockFilesystem) ReadFrom(arg0 context.Context, arg1 string) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ReadFrom", arg0, arg1)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ReadFrom indicates an expected call of ReadFrom
func (mr *MockFilesystemMockRecorder) ReadFrom(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReadFrom", reflect.TypeOf((*MockFilesystem)(nil).ReadFrom), arg0, arg1)
}

// Remove mocks base method
func (m *MockFilesystem) Remove(arg0 context.Context, arg1 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Remove", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// Remove indicates an expected call of Remove
func (mr *MockFilesystemMockRecorder) Remove(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Remove", reflect.TypeOf((*MockFilesystem)(nil).Remove), arg0, arg1)
}

// RemoveAll mocks base method
func (m *MockFilesystem) RemoveAll(arg0 context.Context, arg1 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RemoveAll", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// RemoveAll indicates an expected call of RemoveAll
func (mr *MockFilesystemMockRecorder) RemoveAll(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RemoveAll", reflect.TypeOf((*MockFilesystem)(nil).RemoveAll), arg0, arg1)
}

// Stat mocks base method
func (m *MockFilesystem) Stat(arg0 context.Context, arg1 string) (os.FileInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stat", arg0, arg1)
	ret0, _ := ret[0].(os.FileInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Stat indicates an expected call of Stat
func (mr *MockFilesystemMockRecorder) Stat(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stat", reflect.TypeOf((*MockFilesystem)(nil).Stat), arg0, arg1)
}

// WriteTo mocks base method
func (m *MockFilesystem) WriteTo(arg0 context.Context, arg1, arg2 string, arg3 ...os.FileMode) error {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1, arg2}
	for _, a := range arg3 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "WriteTo", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// WriteTo indicates an expected call of WriteTo
func (mr *MockFilesystemMockRecorder) WriteTo(arg0, arg1, arg2 interface{}, arg3 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1, arg2}, arg3...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WriteTo", reflect.TypeOf((*MockFilesystem)(nil).WriteTo), varargs...)
}
