package filesystem

import (
	"context"
	"os"
)

// Directory implements directory operations
type Directory interface {
	MkdirAll(ctx context.Context, path string, perm ...os.FileMode) error
	RemoveAll(ctx context.Context, path string) error
}

type directory struct{}

func NewDirectory() Directory {
	return &directory{}
}

// MkdirAll is like `mkdir -p`
// File permission is an optional argument whose default value is 0755.
func (*directory) MkdirAll(ctx context.Context, path string, perm ...os.FileMode) error {
	if len(perm) == 0 {
		// default filemode
		perm = []os.FileMode{0755}
	}
	return os.MkdirAll(path, perm[0])
}

// RemoveAll is like `rm -rf`
func (*directory) RemoveAll(ctx context.Context, path string) error {
	return os.RemoveAll(path)
}
