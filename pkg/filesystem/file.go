package filesystem

import (
	"context"
	"fmt"
	"os"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
)

// File implements regular file operations
type File interface {
	ReadFrom(ctx context.Context, path string) (string, error)
	WriteTo(ctx context.Context, path string, content string, perm ...os.FileMode) error
	Stat(ctx context.Context, path string) (os.FileInfo, error)
	Remove(ctx context.Context, path string) error
}

type file struct{}

func NewFile() File {
	return &file{}
}

// ReadFrom wraps ioutil.ReadFile
func (*file) ReadFrom(ctx context.Context, path string) (string, error) {
	out, err := os.ReadFile(path)
	return string(out), err
}

// WriteTo writes content to file specified by path.
// File permission is an optional argument whose default value is 0644.
func (*file) WriteTo(ctx context.Context, path string, content string, perm ...os.FileMode) error {
	if len(perm) == 0 {
		// default file mode
		perm = []os.FileMode{0644}
	}
	f, err := os.OpenFile(path, os.O_CREATE|os.O_WRONLY|os.O_TRUNC, perm[0])
	if err != nil {
		return fmt.Errorf("fail to open file '%s' to write: %w", path, err)
	}
	logger.Infof(ctx, "Content %s", content)
	defer func() {
		if err := f.Close(); err != nil {
			logger.Errorf(ctx, "fail to close file '%s' to write: %w", path, err)
		}
	}()
	if _, err := f.WriteString(content); err != nil {
		return fmt.Errorf("fail to write content to file '%s': %w", path, err)
	}
	logger.Infof(ctx, "successfully to write content to file '%s'", path)
	return nil
}

func (*file) Stat(ctx context.Context, path string) (os.FileInfo, error) {
	return os.Stat(path)
}

func (*file) Remove(ctx context.Context, path string) error {
	return os.Remove(path)
}
