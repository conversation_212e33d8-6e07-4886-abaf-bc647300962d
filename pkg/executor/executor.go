//go:generate mockgen -destination ./mock.go -package executor icode.baidu.com/baidu/cce-test/e2e-test/pkg/executor Executor
package executor

import (
	"bytes"
	"context"
	"os/exec"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
)

type Executor interface {
	Exec(ctx context.Context, name string, args []string, dir ...string) (stdout, stderr string, err error)
	ShellExec(ctx context.Context, cmd string, dir ...string) (stdout, stderr string, err error)
}

type executor struct {
	shell string
}

func NewExecutor(shell string) Executor {
	return &executor{
		shell: shell,
	}
}

// ShellExec calls the shell directly to exec the command WITHOUT any input validation.
// DO take care to escape any dangerous input before using this method.
func (e *executor) ShellExec(ctx context.Context, cmd string, dir ...string) (
	string, string, error) {
	return e.Exec(ctx, e.shell, []string{"-c", cmd}, dir...)
}

// Exec wraps os/exec
func (e *executor) Exec(ctx context.Context, name string, args []string, dir ...string) (
	string, string, error) {
	stdoutBuf, stderrBuf := new(bytes.Buffer), new(bytes.Buffer)
	cmd := exec.CommandContext(ctx, name, args...)
	cmd.Stdout, cmd.Stderr = stdoutBuf, stderrBuf
	if len(dir) > 0 {
		cmd.Dir = dir[0]
	}

	if err := cmd.Start(); err != nil {
		return "", "", err
	}

	err := cmd.Wait()
	stdout, stderr := stdoutBuf.String(), stderrBuf.String()
	if err != nil {
		return stdout, stderr, err
	}
	logger.Infof(ctx, "successfully exec")

	return stdout, stderr, nil
}
