package executor

import (
	"context"
	"testing"
	"time"

	"gotest.tools/assert"
)

func TestExecutor(t *testing.T) {
	e := &executor{
		shell: "/bin/sh",
	}

	start := time.Now()
	_, _, err := e.<PERSON>(context.TODO(), "sleep 2")
	interval := time.Since(start)
	assert.NilError(t, err)
	assert.Assert(t, interval > 2*time.Second)

	stdout, _, _ := e.ShellExec(context.TODO(), "echo aaa")
	assert.Equal(t, stdout, "aaa\n")

	_, _, err = e.ShellExec(context.TODO(), "exit 1")
	assert.ErrorContains(t, err, "exit status 1")

	_, _, err = e.Shell<PERSON>(context.TODO(), "ls | grep bin", "/")
	assert.NilError(t, err)

	_, _, err = e.Exec(context.TODO(), "lsssss", []string{"lslsls"}, "/")
	assert.ErrorContains(t, err, "not found")
}
