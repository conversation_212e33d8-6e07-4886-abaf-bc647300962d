package validate

import (
	"testing"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/gotag/test"
	ccetypes "icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
)

var (
	intVal   int   = -1
	int8Val  int8  = -2
	int16Val int16 = -3
	int32Val int32 = -4
	int64Val int64 = -5

	uintVal   uint   = 1
	uint8Val  uint8  = 2
	uint16Val uint16 = 3
	uint32Val uint32 = 4
	uint64Val uint64 = 5

	runeVal   rune   = '\t'
	byteVal   byte   = 'a'
	stringVal string = "macro"

	float32Val float32 = 0.001
	float64Val float64 = -0.00005

	boolVal bool = true

	intVal2   int   = -1
	int8Val2  int8  = -2
	int16Val2 int16 = -3
	int32Val2 int32 = -4
	int64Val2 int64 = -5

	uintVal2   uint   = 1
	uint8Val2  uint8  = 2
	uint16Val2 uint16 = 3
	uint32Val2 uint32 = 4
	uint64Val2 uint64 = 5

	runeVal2   rune   = '\t'
	byteVal2   byte   = 'a'
	stringVal2 string = "macro"

	float32Val2 float32 = 0.001
	float64Val2 float64 = -0.00005

	boolVal2 bool = true

	intVal3   test.Int   = -1
	int8Val3  test.Int8  = -2
	int16Val3 test.Int16 = -3
	int32Val3 test.Int32 = -4
	int64Val3 test.Int64 = -5

	uintVal3   test.Uint   = 1
	uint8Val3  test.Uint8  = 2
	uint16Val3 test.Uint16 = 3
	uint32Val3 test.Uint32 = 4
	uint64Val3 test.Uint64 = 5

	runeVal3   test.Rune   = '\t'
	byteVal3   test.Byte   = 'a'
	stringVal3 test.String = "macro"

	float32Val3 test.Float32 = 0.001
	float64Val3 test.Float64 = -0.00005

	boolVal3 test.Bool = true

	intVal4   test.Int   = -1
	int8Val4  test.Int8  = -2
	int16Val4 test.Int16 = -3
	int32Val4 test.Int32 = -4
	int64Val4 test.Int64 = -5

	uintVal4   test.Uint   = 1
	uint8Val4  test.Uint8  = 2
	uint16Val4 test.Uint16 = 3
	uint32Val4 test.Uint32 = 4
	uint64Val4 test.Uint64 = 5

	runeVal4   test.Rune   = '\t'
	byteVal4   test.Byte   = 'a'
	stringVal4 test.String = "macro"

	float32Val4 test.Float32 = 0.001
	float64Val4 test.Float64 = -0.00005

	boolVal4 test.Bool = true
)

func TestValidate(t *testing.T) {
	type args struct {
		new any
		old any
	}
	tests := []struct {
		name       string
		args       args
		wantValid  bool
		wantReason string
		wantErr    bool
	}{
		{
			name: "SonStruct equal",
			args: args{
				new: test.SonStruct{
					String:  stringVal,
					Int:     intVal,
					Int8:    int8Val,
					Int16:   int16Val,
					Int32:   int32Val,
					Int64:   int64Val,
					Uint:    uintVal,
					Uint8:   uint8Val,
					Uint16:  uint16Val,
					Uint32:  uint32Val,
					Uint64:  uint64Val,
					Rune:    runeVal,
					Byte:    byteVal,
					Float32: float32Val,
					Float64: float64Val,
					Bool:    boolVal,

					StringPtr:  &stringVal,
					IntPtr:     &intVal,
					Int8Ptr:    &int8Val,
					Int16Ptr:   &int16Val,
					Int32Ptr:   &int32Val,
					Int64Ptr:   &int64Val,
					UintPtr:    &uintVal,
					Uint8Ptr:   &uint8Val,
					Uint16Ptr:  &uint16Val,
					Uint32Ptr:  &uint32Val,
					Uint64Ptr:  &uint64Val,
					RunePtr:    &runeVal,
					BytePtr:    &byteVal,
					Float32Ptr: &float32Val,
					Float64Ptr: &float64Val,
					BoolPtr:    &boolVal,
				},
				old: test.SonStruct{
					String:  stringVal2,
					Int:     intVal2,
					Int8:    int8Val2,
					Int16:   int16Val2,
					Int32:   int32Val2,
					Int64:   int64Val2,
					Uint:    uintVal2,
					Uint8:   uint8Val2,
					Uint16:  uint16Val2,
					Uint32:  uint32Val2,
					Uint64:  uint64Val2,
					Rune:    runeVal2,
					Byte:    byteVal2,
					Float32: float32Val2,
					Float64: float64Val2,
					Bool:    boolVal2,

					StringPtr:  &stringVal2,
					IntPtr:     &intVal2,
					Int8Ptr:    &int8Val2,
					Int16Ptr:   &int16Val2,
					Int32Ptr:   &int32Val2,
					Int64Ptr:   &int64Val2,
					UintPtr:    &uintVal2,
					Uint8Ptr:   &uint8Val2,
					Uint16Ptr:  &uint16Val2,
					Uint32Ptr:  &uint32Val2,
					Uint64Ptr:  &uint64Val2,
					RunePtr:    &runeVal2,
					BytePtr:    &byteVal2,
					Float32Ptr: &float32Val2,
					Float64Ptr: &float64Val2,
					BoolPtr:    &boolVal2,
				},
			},
			wantValid:  true,
			wantReason: "",
			wantErr:    false,
		},

		{
			name: "SonStruct.String not equal",
			args: args{
				new: test.SonStruct{
					String: stringVal + "cell",
				},
				old: test.SonStruct{
					String: stringVal,
				},
			},
			wantValid:  false,
			wantReason: "readonly field String cannot be changed",
			wantErr:    false,
		},

		{
			name: "SonStruct.Int not equal",
			args: args{
				new: test.SonStruct{
					Int: intVal,
				},
				old: test.SonStruct{
					Int: intVal + 1,
				},
			},
			wantValid:  false,
			wantReason: "readonly field Int cannot be changed",
			wantErr:    false,
		},

		{
			name: "SonStruct.Float32 not equal",
			args: args{
				new: test.SonStruct{
					Float32: float32Val,
				},
				old: test.SonStruct{
					Float32: float32Val + 1,
				},
			},
			wantValid:  false,
			wantReason: "readonly field Float32 cannot be changed",
			wantErr:    false,
		},

		{
			name: "SonStruct.Bool not equal",
			args: args{
				new: test.SonStruct{
					Bool: false,
				},
				old: test.SonStruct{
					Bool: true,
				},
			},
			wantValid:  false,
			wantReason: "readonly field Bool cannot be changed",
			wantErr:    false,
		},

		{
			name: "SonStruct.StringPtr not equal",
			args: args{
				new: test.SonStruct{
					StringPtr: &stringVal,
				},
				old: func() test.SonStruct {
					tmp := stringVal + "cell"
					return test.SonStruct{
						StringPtr: &tmp,
					}
				}(),
			},
			wantValid:  false,
			wantReason: "readonly field StringPtr cannot be changed",
			wantErr:    false,
		},

		{
			name: "SonStruct.IntPtr not equal",
			args: args{
				new: test.SonStruct{
					IntPtr: &intVal,
				},
				old: func() test.SonStruct {
					tmp := intVal + 1
					return test.SonStruct{
						IntPtr: &tmp,
					}
				}(),
			},
			wantValid:  false,
			wantReason: "readonly field IntPtr cannot be changed",
			wantErr:    false,
		},

		{
			name: "SonStruct.Float32Ptr not equal",
			args: args{
				new: test.SonStruct{
					Float32Ptr: &float32Val,
				},
				old: func() test.SonStruct {
					tmp := float32Val + 1
					return test.SonStruct{
						Float32Ptr: &tmp,
					}
				}(),
			},
			wantValid:  false,
			wantReason: "readonly field Float32Ptr cannot be changed",
			wantErr:    false,
		},

		{
			name: "SonStruct.BoolPtr not equal",
			args: args{
				new: test.SonStruct{
					BoolPtr: &boolVal,
				},
				old: func() test.SonStruct {
					tmp := !boolVal
					return test.SonStruct{
						BoolPtr: &tmp,
					}
				}(),
			},
			wantValid:  false,
			wantReason: "readonly field BoolPtr cannot be changed",
			wantErr:    false,
		},

		{
			name: "ParentStruct equal",
			args: args{
				new: test.ParentStruct{
					String:     stringVal3,
					Int:        intVal3,
					Int8:       int8Val3,
					Int16:      int16Val3,
					Int32:      int32Val3,
					Int64:      int64Val3,
					Uint:       uintVal3,
					Uint8:      uint8Val3,
					Uint16:     uint16Val3,
					Uint32:     uint32Val3,
					Uint64:     uint64Val3,
					Rune:       runeVal3,
					Byte:       byteVal3,
					Float32:    float32Val3,
					Float64:    float64Val3,
					Bool:       boolVal3,
					StringPtr:  &stringVal3,
					IntPtr:     &intVal3,
					Int8Ptr:    &int8Val3,
					Int16Ptr:   &int16Val3,
					Int32Ptr:   &int32Val3,
					Int64Ptr:   &int64Val3,
					UintPtr:    &uintVal3,
					Uint8Ptr:   &uint8Val3,
					Uint16Ptr:  &uint16Val3,
					Uint32Ptr:  &uint32Val3,
					Uint64Ptr:  &uint64Val3,
					RunePtr:    &runeVal3,
					BytePtr:    &byteVal3,
					Float32Ptr: &float32Val3,
					Float64Ptr: &float64Val3,
					BoolPtr:    &boolVal3,
					SonStruct: test.SonStruct{
						String:  stringVal,
						Int:     intVal,
						Int8:    int8Val,
						Int16:   int16Val,
						Int32:   int32Val,
						Int64:   int64Val,
						Uint:    uintVal,
						Uint8:   uint8Val,
						Uint16:  uint16Val,
						Uint32:  uint32Val,
						Uint64:  uint64Val,
						Rune:    runeVal,
						Byte:    byteVal,
						Float32: float32Val,
						Float64: float64Val,
						Bool:    boolVal,

						StringPtr:  &stringVal,
						IntPtr:     &intVal,
						Int8Ptr:    &int8Val,
						Int16Ptr:   &int16Val,
						Int32Ptr:   &int32Val,
						Int64Ptr:   &int64Val,
						UintPtr:    &uintVal,
						Uint8Ptr:   &uint8Val,
						Uint16Ptr:  &uint16Val,
						Uint32Ptr:  &uint32Val,
						Uint64Ptr:  &uint64Val,
						RunePtr:    &runeVal,
						BytePtr:    &byteVal,
						Float32Ptr: &float32Val,
						Float64Ptr: &float64Val,
						BoolPtr:    &boolVal,
					},
					SonStructPtr: &test.SonStruct{
						String:  stringVal,
						Int:     intVal,
						Int8:    int8Val,
						Int16:   int16Val,
						Int32:   int32Val,
						Int64:   int64Val,
						Uint:    uintVal,
						Uint8:   uint8Val,
						Uint16:  uint16Val,
						Uint32:  uint32Val,
						Uint64:  uint64Val,
						Rune:    runeVal,
						Byte:    byteVal,
						Float32: float32Val,
						Float64: float64Val,
						Bool:    boolVal,

						StringPtr:  &stringVal,
						IntPtr:     &intVal,
						Int8Ptr:    &int8Val,
						Int16Ptr:   &int16Val,
						Int32Ptr:   &int32Val,
						Int64Ptr:   &int64Val,
						UintPtr:    &uintVal,
						Uint8Ptr:   &uint8Val,
						Uint16Ptr:  &uint16Val,
						Uint32Ptr:  &uint32Val,
						Uint64Ptr:  &uint64Val,
						RunePtr:    &runeVal,
						BytePtr:    &byteVal,
						Float32Ptr: &float32Val,
						Float64Ptr: &float64Val,
						BoolPtr:    &boolVal,
					},
					SonStructSlice: []test.SonStruct{
						{
							String:  stringVal,
							Int:     intVal,
							Int8:    int8Val,
							Int16:   int16Val,
							Int32:   int32Val,
							Int64:   int64Val,
							Uint:    uintVal,
							Uint8:   uint8Val,
							Uint16:  uint16Val,
							Uint32:  uint32Val,
							Uint64:  uint64Val,
							Rune:    runeVal,
							Byte:    byteVal,
							Float32: float32Val,
							Float64: float64Val,
							Bool:    boolVal,

							StringPtr:  &stringVal,
							IntPtr:     &intVal,
							Int8Ptr:    &int8Val,
							Int16Ptr:   &int16Val,
							Int32Ptr:   &int32Val,
							Int64Ptr:   &int64Val,
							UintPtr:    &uintVal,
							Uint8Ptr:   &uint8Val,
							Uint16Ptr:  &uint16Val,
							Uint32Ptr:  &uint32Val,
							Uint64Ptr:  &uint64Val,
							RunePtr:    &runeVal,
							BytePtr:    &byteVal,
							Float32Ptr: &float32Val,
							Float64Ptr: &float64Val,
							BoolPtr:    &boolVal,
						},
					},
					SonStructPtrSlice: []*test.SonStruct{
						{
							String:  stringVal,
							Int:     intVal,
							Int8:    int8Val,
							Int16:   int16Val,
							Int32:   int32Val,
							Int64:   int64Val,
							Uint:    uintVal,
							Uint8:   uint8Val,
							Uint16:  uint16Val,
							Uint32:  uint32Val,
							Uint64:  uint64Val,
							Rune:    runeVal,
							Byte:    byteVal,
							Float32: float32Val,
							Float64: float64Val,
							Bool:    boolVal,

							StringPtr:  &stringVal,
							IntPtr:     &intVal,
							Int8Ptr:    &int8Val,
							Int16Ptr:   &int16Val,
							Int32Ptr:   &int32Val,
							Int64Ptr:   &int64Val,
							UintPtr:    &uintVal,
							Uint8Ptr:   &uint8Val,
							Uint16Ptr:  &uint16Val,
							Uint32Ptr:  &uint32Val,
							Uint64Ptr:  &uint64Val,
							RunePtr:    &runeVal,
							BytePtr:    &byteVal,
							Float32Ptr: &float32Val,
							Float64Ptr: &float64Val,
							BoolPtr:    &boolVal,
						},
					},
					SonStructMap: map[string]test.SonStruct{
						"sonStruct": {
							String:  stringVal,
							Int:     intVal,
							Int8:    int8Val,
							Int16:   int16Val,
							Int32:   int32Val,
							Int64:   int64Val,
							Uint:    uintVal,
							Uint8:   uint8Val,
							Uint16:  uint16Val,
							Uint32:  uint32Val,
							Uint64:  uint64Val,
							Rune:    runeVal,
							Byte:    byteVal,
							Float32: float32Val,
							Float64: float64Val,
							Bool:    boolVal,

							StringPtr:  &stringVal,
							IntPtr:     &intVal,
							Int8Ptr:    &int8Val,
							Int16Ptr:   &int16Val,
							Int32Ptr:   &int32Val,
							Int64Ptr:   &int64Val,
							UintPtr:    &uintVal,
							Uint8Ptr:   &uint8Val,
							Uint16Ptr:  &uint16Val,
							Uint32Ptr:  &uint32Val,
							Uint64Ptr:  &uint64Val,
							RunePtr:    &runeVal,
							BytePtr:    &byteVal,
							Float32Ptr: &float32Val,
							Float64Ptr: &float64Val,
							BoolPtr:    &boolVal,
						},
					},
					SonStructPtrMap: map[string]*test.SonStruct{
						"1": {
							String:  stringVal,
							Int:     intVal,
							Int8:    int8Val,
							Int16:   int16Val,
							Int32:   int32Val,
							Int64:   int64Val,
							Uint:    uintVal,
							Uint8:   uint8Val,
							Uint16:  uint16Val,
							Uint32:  uint32Val,
							Uint64:  uint64Val,
							Rune:    runeVal,
							Byte:    byteVal,
							Float32: float32Val,
							Float64: float64Val,
							Bool:    boolVal,

							StringPtr:  &stringVal,
							IntPtr:     &intVal,
							Int8Ptr:    &int8Val,
							Int16Ptr:   &int16Val,
							Int32Ptr:   &int32Val,
							Int64Ptr:   &int64Val,
							UintPtr:    &uintVal,
							Uint8Ptr:   &uint8Val,
							Uint16Ptr:  &uint16Val,
							Uint32Ptr:  &uint32Val,
							Uint64Ptr:  &uint64Val,
							RunePtr:    &runeVal,
							BytePtr:    &byteVal,
							Float32Ptr: &float32Val,
							Float64Ptr: &float64Val,
							BoolPtr:    &boolVal,
						},
					},
				},
				old: test.ParentStruct{
					String:     stringVal4,
					Int:        intVal4,
					Int8:       int8Val4,
					Int16:      int16Val4,
					Int32:      int32Val4,
					Int64:      int64Val4,
					Uint:       uintVal4,
					Uint8:      uint8Val4,
					Uint16:     uint16Val4,
					Uint32:     uint32Val4,
					Uint64:     uint64Val4,
					Rune:       runeVal4,
					Byte:       byteVal4,
					Float32:    float32Val4,
					Float64:    float64Val4,
					Bool:       boolVal4,
					StringPtr:  &stringVal4,
					IntPtr:     &intVal4,
					Int8Ptr:    &int8Val4,
					Int16Ptr:   &int16Val4,
					Int32Ptr:   &int32Val4,
					Int64Ptr:   &int64Val4,
					UintPtr:    &uintVal4,
					Uint8Ptr:   &uint8Val4,
					Uint16Ptr:  &uint16Val4,
					Uint32Ptr:  &uint32Val4,
					Uint64Ptr:  &uint64Val4,
					RunePtr:    &runeVal4,
					BytePtr:    &byteVal4,
					Float32Ptr: &float32Val4,
					Float64Ptr: &float64Val4,
					BoolPtr:    &boolVal4,
					SonStruct: test.SonStruct{
						String:  stringVal2,
						Int:     intVal2,
						Int8:    int8Val2,
						Int16:   int16Val2,
						Int32:   int32Val2,
						Int64:   int64Val2,
						Uint:    uintVal2,
						Uint8:   uint8Val2,
						Uint16:  uint16Val2,
						Uint32:  uint32Val2,
						Uint64:  uint64Val2,
						Rune:    runeVal2,
						Byte:    byteVal2,
						Float32: float32Val2,
						Float64: float64Val2,
						Bool:    boolVal2,

						StringPtr:  &stringVal2,
						IntPtr:     &intVal2,
						Int8Ptr:    &int8Val2,
						Int16Ptr:   &int16Val2,
						Int32Ptr:   &int32Val2,
						Int64Ptr:   &int64Val2,
						UintPtr:    &uintVal2,
						Uint8Ptr:   &uint8Val2,
						Uint16Ptr:  &uint16Val2,
						Uint32Ptr:  &uint32Val2,
						Uint64Ptr:  &uint64Val2,
						RunePtr:    &runeVal2,
						BytePtr:    &byteVal2,
						Float32Ptr: &float32Val2,
						Float64Ptr: &float64Val2,
						BoolPtr:    &boolVal2,
					},
					SonStructPtr: &test.SonStruct{
						String:  stringVal2,
						Int:     intVal2,
						Int8:    int8Val2,
						Int16:   int16Val2,
						Int32:   int32Val2,
						Int64:   int64Val2,
						Uint:    uintVal2,
						Uint8:   uint8Val2,
						Uint16:  uint16Val2,
						Uint32:  uint32Val2,
						Uint64:  uint64Val2,
						Rune:    runeVal2,
						Byte:    byteVal2,
						Float32: float32Val2,
						Float64: float64Val2,
						Bool:    boolVal2,

						StringPtr:  &stringVal2,
						IntPtr:     &intVal2,
						Int8Ptr:    &int8Val2,
						Int16Ptr:   &int16Val2,
						Int32Ptr:   &int32Val2,
						Int64Ptr:   &int64Val2,
						UintPtr:    &uintVal2,
						Uint8Ptr:   &uint8Val2,
						Uint16Ptr:  &uint16Val2,
						Uint32Ptr:  &uint32Val2,
						Uint64Ptr:  &uint64Val2,
						RunePtr:    &runeVal2,
						BytePtr:    &byteVal2,
						Float32Ptr: &float32Val2,
						Float64Ptr: &float64Val2,
						BoolPtr:    &boolVal2,
					},
					SonStructSlice: []test.SonStruct{
						{
							String:  stringVal2,
							Int:     intVal2,
							Int8:    int8Val2,
							Int16:   int16Val2,
							Int32:   int32Val2,
							Int64:   int64Val2,
							Uint:    uintVal2,
							Uint8:   uint8Val2,
							Uint16:  uint16Val2,
							Uint32:  uint32Val2,
							Uint64:  uint64Val2,
							Rune:    runeVal2,
							Byte:    byteVal2,
							Float32: float32Val2,
							Float64: float64Val2,
							Bool:    boolVal2,

							StringPtr:  &stringVal2,
							IntPtr:     &intVal2,
							Int8Ptr:    &int8Val2,
							Int16Ptr:   &int16Val2,
							Int32Ptr:   &int32Val2,
							Int64Ptr:   &int64Val2,
							UintPtr:    &uintVal2,
							Uint8Ptr:   &uint8Val2,
							Uint16Ptr:  &uint16Val2,
							Uint32Ptr:  &uint32Val2,
							Uint64Ptr:  &uint64Val2,
							RunePtr:    &runeVal2,
							BytePtr:    &byteVal2,
							Float32Ptr: &float32Val2,
							Float64Ptr: &float64Val2,
							BoolPtr:    &boolVal2,
						},
					},
					SonStructPtrSlice: []*test.SonStruct{
						{
							String:  stringVal2,
							Int:     intVal2,
							Int8:    int8Val2,
							Int16:   int16Val2,
							Int32:   int32Val2,
							Int64:   int64Val2,
							Uint:    uintVal2,
							Uint8:   uint8Val2,
							Uint16:  uint16Val2,
							Uint32:  uint32Val2,
							Uint64:  uint64Val2,
							Rune:    runeVal2,
							Byte:    byteVal2,
							Float32: float32Val2,
							Float64: float64Val2,
							Bool:    boolVal2,

							StringPtr:  &stringVal2,
							IntPtr:     &intVal2,
							Int8Ptr:    &int8Val2,
							Int16Ptr:   &int16Val2,
							Int32Ptr:   &int32Val2,
							Int64Ptr:   &int64Val2,
							UintPtr:    &uintVal2,
							Uint8Ptr:   &uint8Val2,
							Uint16Ptr:  &uint16Val2,
							Uint32Ptr:  &uint32Val2,
							Uint64Ptr:  &uint64Val2,
							RunePtr:    &runeVal2,
							BytePtr:    &byteVal2,
							Float32Ptr: &float32Val2,
							Float64Ptr: &float64Val2,
							BoolPtr:    &boolVal2,
						},
					},
					SonStructMap: map[string]test.SonStruct{
						"sonStruct": {
							String:  stringVal2,
							Int:     intVal2,
							Int8:    int8Val2,
							Int16:   int16Val2,
							Int32:   int32Val2,
							Int64:   int64Val2,
							Uint:    uintVal2,
							Uint8:   uint8Val2,
							Uint16:  uint16Val2,
							Uint32:  uint32Val2,
							Uint64:  uint64Val2,
							Rune:    runeVal2,
							Byte:    byteVal2,
							Float32: float32Val2,
							Float64: float64Val2,
							Bool:    boolVal2,

							StringPtr:  &stringVal2,
							IntPtr:     &intVal2,
							Int8Ptr:    &int8Val2,
							Int16Ptr:   &int16Val2,
							Int32Ptr:   &int32Val2,
							Int64Ptr:   &int64Val2,
							UintPtr:    &uintVal2,
							Uint8Ptr:   &uint8Val2,
							Uint16Ptr:  &uint16Val2,
							Uint32Ptr:  &uint32Val2,
							Uint64Ptr:  &uint64Val2,
							RunePtr:    &runeVal2,
							BytePtr:    &byteVal2,
							Float32Ptr: &float32Val2,
							Float64Ptr: &float64Val2,
							BoolPtr:    &boolVal2,
						},
					},
					SonStructPtrMap: map[string]*test.SonStruct{
						"1": {
							String:  stringVal2,
							Int:     intVal2,
							Int8:    int8Val2,
							Int16:   int16Val2,
							Int32:   int32Val2,
							Int64:   int64Val2,
							Uint:    uintVal2,
							Uint8:   uint8Val2,
							Uint16:  uint16Val2,
							Uint32:  uint32Val2,
							Uint64:  uint64Val2,
							Rune:    runeVal2,
							Byte:    byteVal2,
							Float32: float32Val2,
							Float64: float64Val2,
							Bool:    boolVal2,

							StringPtr:  &stringVal2,
							IntPtr:     &intVal2,
							Int8Ptr:    &int8Val2,
							Int16Ptr:   &int16Val2,
							Int32Ptr:   &int32Val2,
							Int64Ptr:   &int64Val2,
							UintPtr:    &uintVal2,
							Uint8Ptr:   &uint8Val2,
							Uint16Ptr:  &uint16Val2,
							Uint32Ptr:  &uint32Val2,
							Uint64Ptr:  &uint64Val2,
							RunePtr:    &runeVal2,
							BytePtr:    &byteVal2,
							Float32Ptr: &float32Val2,
							Float64Ptr: &float64Val2,
							BoolPtr:    &boolVal2,
						},
					},
				},
			},
			wantValid:  true,
			wantReason: "",
			wantErr:    false,
		},
		{
			name: "ParentStruct.String not equal",
			args: args{
				new: test.ParentStruct{
					String: stringVal3,
				},
				old: test.ParentStruct{
					String: stringVal3 + "cell",
				},
			},
			wantValid:  false,
			wantReason: "readonly field String cannot be changed",
			wantErr:    false,
		},

		{
			name: "ParentStruct.Int not equal",
			args: args{
				new: test.ParentStruct{
					Int: intVal3,
				},
				old: test.ParentStruct{
					Int: intVal3 + 1,
				},
			},
			wantValid:  false,
			wantReason: "readonly field Int cannot be changed",
			wantErr:    false,
		},

		{
			name: "ParentStruct.Float32 not equal",
			args: args{
				new: test.ParentStruct{
					Float32: float32Val3,
				},
				old: test.ParentStruct{
					Float32: float32Val3 + 1,
				},
			},
			wantValid:  false,
			wantReason: "readonly field Float32 cannot be changed",
			wantErr:    false,
		},

		{
			name: "ParentStruct.Bool not equal",
			args: args{
				new: test.ParentStruct{
					Bool: boolVal3,
				},
				old: test.ParentStruct{
					Bool: !boolVal3,
				},
			},
			wantValid:  false,
			wantReason: "readonly field Bool cannot be changed",
			wantErr:    false,
		},

		{
			name: "ParentStruct.StringPtr not equal",
			args: args{
				new: test.ParentStruct{
					StringPtr: &stringVal3,
				},
				old: func() test.ParentStruct {
					tmp := stringVal3 + "cell"
					return test.ParentStruct{
						StringPtr: &tmp,
					}
				}(),
			},
			wantValid:  false,
			wantReason: "readonly field StringPtr cannot be changed",
			wantErr:    false,
		},

		{
			name: "ParentStruct.IntPtr not equal",
			args: args{
				new: test.ParentStruct{
					IntPtr: &intVal3,
				},
				old: func() test.ParentStruct {
					tmp := intVal3 + 1
					return test.ParentStruct{
						IntPtr: &tmp,
					}
				}(),
			},
			wantValid:  false,
			wantReason: "readonly field IntPtr cannot be changed",
			wantErr:    false,
		},

		{
			name: "ParentStruct.Float32Ptr not equal",
			args: args{
				new: test.ParentStruct{
					Float32Ptr: &float32Val3,
				},
				old: func() test.ParentStruct {
					tmp := float32Val3 + 1
					return test.ParentStruct{
						Float32Ptr: &tmp,
					}
				}(),
			},
			wantValid:  false,
			wantReason: "readonly field Float32Ptr cannot be changed",
			wantErr:    false,
		},

		{
			name: "ParentStruct.BoolPtr not equal",
			args: args{
				new: test.ParentStruct{
					BoolPtr: &boolVal3,
				},
				old: func() test.ParentStruct {
					tmp := !boolVal3
					return test.ParentStruct{
						BoolPtr: &tmp,
					}
				}(),
			},
			wantValid:  false,
			wantReason: "readonly field BoolPtr cannot be changed",
			wantErr:    false,
		},

		{
			name: "ParentStruct.SonStruct.String not equal",
			args: args{
				new: test.ParentStruct{
					SonStruct: test.SonStruct{
						String: stringVal,
					},
				},
				old: test.ParentStruct{
					SonStruct: test.SonStruct{
						String: stringVal + "cell",
					},
				},
			},
			wantValid:  false,
			wantReason: "readonly field SonStruct.String cannot be changed",
			wantErr:    false,
		},

		{
			name: "ParentStruct.SonStructPtr.String not equal",
			args: args{
				new: test.ParentStruct{
					SonStructPtr: &test.SonStruct{
						String: stringVal,
					},
				},
				old: test.ParentStruct{
					SonStructPtr: &test.SonStruct{
						String: stringVal + "cell",
					},
				},
			},
			wantValid:  false,
			wantReason: "readonly field SonStructPtr.String cannot be changed",
			wantErr:    false,
		},

		{
			name: "ParentStruct.SonStructSlice not equal",
			args: args{
				new: test.ParentStruct{
					SonStructSlice: []test.SonStruct{
						{
							String: stringVal,
						},
					},
				},
				old: test.ParentStruct{
					SonStructSlice: []test.SonStruct{
						{
							String: stringVal + "cell",
						},
					},
				},
			},
			wantValid:  false,
			wantReason: "readonly field SonStructSlice cannot be changed",
			wantErr:    false,
		},

		{
			name: "ParentStruct.SonStructPtrSlice not equal",
			args: args{
				new: test.ParentStruct{
					SonStructPtrSlice: []*test.SonStruct{
						{
							String: stringVal,
						},
					},
				},
				old: test.ParentStruct{
					SonStructPtrSlice: []*test.SonStruct{
						{
							String: stringVal + "cell",
						},
					},
				},
			},
			wantValid:  false,
			wantReason: "readonly field SonStructPtrSlice cannot be changed",
			wantErr:    false,
		},

		{
			name: "ParentStruct.SonStructMap not equal",
			args: args{
				new: test.ParentStruct{
					SonStructMap: map[string]test.SonStruct{
						"sonStruct": {
							String: stringVal,
						},
					},
				},
				old: test.ParentStruct{
					SonStructMap: map[string]test.SonStruct{
						"sonStruct": {
							String: stringVal + "cell",
						},
					},
				},
			},
			wantValid:  false,
			wantReason: "readonly field SonStructMap cannot be changed",
			wantErr:    false,
		},

		{
			name: "ParentStruct.SonStructPtrMap not equal",
			args: args{
				new: test.ParentStruct{
					SonStructPtrMap: map[string]*test.SonStruct{
						"sonStruct": {
							String: stringVal,
						},
					},
				},
				old: test.ParentStruct{
					SonStructPtrMap: map[string]*test.SonStruct{
						"sonStruct": {
							String: stringVal + "cell",
						},
					},
				},
			},
			wantValid:  false,
			wantReason: "readonly field SonStructPtrMap cannot be changed",
			wantErr:    false,
		},

		{
			name: "Cluster not equal",
			args: args{
				new: ccetypes.ClusterSpec{
					Handler:                "",
					ClusterID:              "ClusterID",
					UserID:                 "",
					AccountID:              "",
					ClusterName:            "",
					ClusterType:            "",
					Description:            "",
					K8SVersion:             "",
					RuntimeType:            "",
					RuntimeVersion:         "",
					VPCID:                  "",
					VPCUUID:                "",
					VPCCIDR:                "",
					VPCCIDRIPv6:            "",
					Plugins:                nil,
					MasterConfig:           ccetypes.MasterConfig{},
					ContainerNetworkConfig: ccetypes.ContainerNetworkConfig{},
					K8SCustomConfig:        ccetypes.K8SCustomConfig{},
					AuthenticateMode:       "AuthenticateMode",
				},
				old: ccetypes.ClusterSpec{
					Handler:                "",
					ClusterID:              "ClusterID",
					UserID:                 "",
					AccountID:              "",
					ClusterName:            "",
					ClusterType:            "",
					Description:            "",
					K8SVersion:             "",
					RuntimeType:            "",
					RuntimeVersion:         "",
					VPCID:                  "",
					VPCUUID:                "",
					VPCCIDR:                "",
					VPCCIDRIPv6:            "",
					Plugins:                nil,
					MasterConfig:           ccetypes.MasterConfig{},
					ContainerNetworkConfig: ccetypes.ContainerNetworkConfig{},
					K8SCustomConfig:        ccetypes.K8SCustomConfig{},
					AuthenticateMode:       "AuthenticateMode2",
				},
			},
			wantValid:  false,
			wantReason: "readonly field AuthenticateMode cannot be changed",
			wantErr:    false,
		},
		{
			name: "Instance not equal",
			args: args{
				new: ccetypes.InstanceSpec{
					Handler:           "",
					CCEInstanceID:     "CCEInstanceID",
					InstanceName:      "",
					RuntimeType:       "",
					RuntimeVersion:    "",
					ClusterID:         "",
					ClusterRole:       "",
					UserID:            "",
					AccountID:         "",
					InstanceGroupID:   "",
					InstanceGroupName: "",
					MasterType:        "",
					Existed:           false,
					ExistedOption:     ccetypes.ExistedOption{},
					MachineType:       "",
					InstanceOption:    nil,
					InstanceType:      "",
					BBCOption:         ccetypes.BBCOption{},
					VPCConfig: ccetypes.VPCConfig{
						VPCID: "VPCID",
					},
					InstanceResource:          ccetypes.InstanceResource{},
					ImageID:                   "",
					ImageUUID:                 "",
					InstanceOS:                ccetypes.InstanceOS{},
					NeedEIP:                   false,
					EIPOption:                 ccetypes.EIPOption{},
					AdminPassword:             "",
					SSHKeyID:                  "",
					InstanceChargingType:      "",
					InstancePreChargingOption: ccetypes.InstancePreChargingOption{},
					DeleteOption:              nil,
					DeployCustomConfig:        ccetypes.DeployCustomConfig{},
					Tags:                      nil,
					Labels:                    nil,
					Taints:                    nil,
					CCEInstancePriority:       0,
				},
				old: ccetypes.InstanceSpec{
					Handler:           "",
					CCEInstanceID:     "CCEInstanceID",
					InstanceName:      "",
					RuntimeType:       "",
					RuntimeVersion:    "",
					ClusterID:         "",
					ClusterRole:       "",
					UserID:            "",
					AccountID:         "",
					InstanceGroupID:   "",
					InstanceGroupName: "",
					MasterType:        "",
					Existed:           false,
					ExistedOption:     ccetypes.ExistedOption{},
					MachineType:       "",
					InstanceOption:    nil,
					InstanceType:      "",
					BBCOption:         ccetypes.BBCOption{},
					VPCConfig: ccetypes.VPCConfig{
						VPCID: "VPCID2",
					},
					InstanceResource:          ccetypes.InstanceResource{},
					ImageID:                   "",
					ImageUUID:                 "",
					InstanceOS:                ccetypes.InstanceOS{},
					NeedEIP:                   false,
					EIPOption:                 ccetypes.EIPOption{},
					AdminPassword:             "",
					SSHKeyID:                  "",
					InstanceChargingType:      "",
					InstancePreChargingOption: ccetypes.InstancePreChargingOption{},
					DeleteOption:              nil,
					DeployCustomConfig:        ccetypes.DeployCustomConfig{},
					Tags:                      nil,
					Labels:                    nil,
					Taints:                    nil,
					CCEInstancePriority:       0,
				},
			},
			wantValid:  false,
			wantReason: "readonly field VPCConfig.VPCID cannot be changed",
			wantErr:    false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, got1, err := ValidateReadonly(tt.args.new, tt.args.old)
			if (err != nil) != tt.wantErr {
				t.Errorf("ValidateReadonly() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.wantValid {
				t.Errorf("ValidateReadonly() gotValid = %v, want %v", got, tt.wantValid)
			}
			if got1 != tt.wantReason {
				t.Errorf("ValidateReadonly() gotReason = %v, want %v", got1, tt.wantReason)
			}
		})
	}
}
