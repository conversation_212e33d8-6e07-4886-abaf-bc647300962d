package validate

import (
	"errors"
	"fmt"
	"reflect"
)

// 如果条件匹配，则进行检测
type ValidateCondition string

// 默认情况（无tag，tag为空值）禁止修改，tag值为readonly时禁止修改，
// tag值为modifiable时可修改
// 其他情况按照condition确定禁止的tag值
const (
	modifiable = "modifiable"

	ValidateConditionClusterReady  ValidateCondition = "clusterReady" // 集群ready情况下禁止修改
	ValidateConditionInstanceReady ValidateCondition = "nodeReady"    // node ready情况下禁止修改
	ValidateConditionMachineReady  ValidateCondition = "machineReady"
	ValidateConditionBLBReady      ValidateCondition = "blbReady"
	ValidateConditionEIPReady      ValidateCondition = "eipReady"
)

// ValidateByConditions - 检查是否符合自定义 validate tag 的 []ValidateCondition 约束
// 默认情况（无tag，tag为空值）禁止修改，tag值为readonly时禁止修改，
// tag值为modifiable时可修改
// 其他情况按照condition确定禁止的tag值
//
// PARAMS:
//   - new: 更新后的值
//   - old: 旧值
//   - conditions: 检查条件
//
// RETURNS:
//
//	valid bool: new 和 old 是否满足 validate tag (conditions) 约束; 若满足 valid = true, 否则 valid = false
//	reason string: valid = true 时为空, valid = false 时展示原因
//	error: nil if succeed, error if fail
func ValidateByConditions(new, old any, conditions map[ValidateCondition]any) (bool, string, error) {
	// reflect.ValueOf(nil) 将返回 zero value
	newVal := reflect.ValueOf(new)
	oldVal := reflect.ValueOf(old)

	// reflect.Value 为 zero value 时, IsValid() 会返回 false
	if !newVal.IsValid() || !oldVal.IsValid() {
		return false, "", errors.New("new or old is nil")
	}

	// 比较 new old 类型是否相同
	if !compareType(newVal, oldVal) {
		return false, fmt.Sprintf("types are not equal"), nil
	}

	// 检查是否是 struct 类型
	if newVal.Kind() != reflect.Struct {
		return false, "", fmt.Errorf("kind %v is not struct", newVal.Kind())
	}

	valid, field, err := validateByConditionsRecursion(newVal, oldVal, conditions)
	if err != nil {
		return false, "", err
	}
	if !valid {
		return false, fmt.Sprintf("field %s cannot be changed", field), nil
	}
	return true, "", nil
}

// validateByConditionsRecursion - 递归判断 new old 是否满足 conditions []ValidateCondition 约束
// 1) 如果 new old 类型不同，直接返回 valid = false
//
// PARAMS:
//   - new: 更新后的值
//   - old: 旧值
//   - conditions: 检查条件
//
// RETURNS:
//
//	valid bool: new 和 old 是否满足 conditions []ValidateCondition 约束; 若满足 valid = true, 否则 valid = false
//	filedName string: 不满足 readonly 约束的字段名
//	error: nil if succeed, error if fail
func validateByConditionsRecursion(newVal, oldVal reflect.Value, conditions map[ValidateCondition]any) (bool, string, error) {
	rType := newVal.Type()
	kind := rType.Kind()

	if kind != reflect.Struct {
		return false, "", fmt.Errorf("kind %v is not struct", kind)
	}

	// 遍历结构体字段
	for i := 0; i < rType.NumField(); i++ {
		field := rType.Field(i)
		fieldName := field.Name

		newFieldVal := newVal.FieldByName(fieldName)
		oldFieldVal := oldVal.FieldByName(fieldName)

		tagValue := field.Tag.Get(tagName)

		// validate:"modifiable"
		if tagValue == modifiable {
			continue
		}

		// validate:"readonly"
		if tagValue == readonly || tagValue == "" {
			equal, err := compareAny(newFieldVal, oldFieldVal)
			if err != nil || !equal {
				return false, fieldName, err
			}
		}

		// validate:"{modify conditions}"
		if _, ok := conditions[ValidateCondition(tagValue)]; ok {
			equal, err := compareAny(newFieldVal, oldFieldVal)
			if err != nil || !equal {
				return false, fieldName, err
			}
		}

		// validate:"inline"
		if tagValue == inline {
			// inline 仅可应用于 struct 或者 *struct, **struct ...
			kind := field.Type.Kind()
			for kind == reflect.Ptr {
				newFieldVal = newFieldVal.Elem()
				oldFieldVal = oldFieldVal.Elem()
				kind = newFieldVal.Kind()
			}

			if kind == reflect.Invalid {
				equal, err := compareAny(newFieldVal, oldFieldVal)
				if err != nil || !equal {
					return false, fieldName, err
				}
			} else if kind == reflect.Struct {
				valid, subFiledName, err := validateByConditionsRecursion(newFieldVal, oldFieldVal, conditions)
				if err != nil || !valid {
					return false, fmt.Sprintf("%s.%s", fieldName, subFiledName), err
				}
			} else {
				return false, fieldName, errors.New("inline can only be applied to struct, *struct, **struct and so on")
			}
		}
	}

	return true, "", nil
}
