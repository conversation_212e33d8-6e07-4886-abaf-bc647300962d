package validate

import (
	"errors"
	"fmt"
	"reflect"
)

const (
	tagName  = "validate"
	readonly = "readonly"
	inline   = "inline"
)

// ValidateReadonly - 检查是否符合自定义 validate tag 的 readonly 约束
//
// PARAMS:
//   - new: 更新后的值
//   - old: 旧值
//
// RETURNS:
//
//	valid bool: new 和 old 是否满足 validate tag 约束; 若满足 valid = true, 否则 valid = false
//	reason string: valid = true 时为空, valid = false 时展示原因
//	error: nil if succeed, error if fail
func ValidateReadonly(new, old any) (bool, string, error) {
	// reflect.ValueOf(nil) 将返回 zero value
	newVal := reflect.ValueOf(new)
	oldVal := reflect.ValueOf(old)

	// reflect.Value 为 zero value 时, IsValid() 会返回 false
	if !newVal.IsValid() || !oldVal.IsValid() {
		return false, "", errors.New("new or old is nil")
	}

	// 比较 new old 类型是否相同
	if !compareType(newVal, oldVal) {
		return false, fmt.Sprintf("types are not equal"), nil
	}

	// 检查是否是 struct 类型
	if newVal.Kind() != reflect.Struct {
		return false, "", fmt.Errorf("kind %v is not struct", newVal.Kind())
	}

	valid, field, err := validateStructReadonly(newVal, oldVal)
	if err != nil {
		return false, "", err
	}
	if !valid {
		return false, fmt.Sprintf("readonly field %s cannot be changed", field), nil
	}
	return true, "", nil
}

// validateStructReadonly - 递归判断 new old 是否满足 validate:"readonly" 约束
// 1) 如果 new old 类型不同，直接返回 valid = false
//
// PARAMS:
//   - new: 更新后的值
//   - old: 旧值
//
// RETURNS:
//
//	valid bool: new 和 old 是否满足 readonly 约束; 若满足 valid = true, 否则 valid = false
//	filedName string: 不满足 readonly 约束的字段名
//	error: nil if succeed, error if fail
func validateStructReadonly(newVal, oldVal reflect.Value) (bool, string, error) {
	rType := newVal.Type()
	kind := rType.Kind()

	if kind != reflect.Struct {
		return false, "", fmt.Errorf("kind %v is not struct", kind)
	}

	// 遍历结构体字段
	for i := 0; i < rType.NumField(); i++ {
		field := rType.Field(i)
		fieldName := field.Name

		newFieldVal := newVal.FieldByName(fieldName)
		oldFieldVal := oldVal.FieldByName(fieldName)

		switch field.Tag.Get(tagName) {
		// validate:"readonly"
		case readonly:
			equal, err := compareAny(newFieldVal, oldFieldVal)
			if err != nil || !equal {
				return false, fieldName, err
			}

		// validate:"inline"
		case inline:
			// inline 仅可应用于 struct 或者 *struct, **struct ...
			kind := field.Type.Kind()
			for kind == reflect.Ptr {
				newFieldVal = newFieldVal.Elem()
				oldFieldVal = oldFieldVal.Elem()
				kind = newFieldVal.Kind()
			}

			if kind == reflect.Invalid {
				equal, err := compareAny(newFieldVal, oldFieldVal)
				if err != nil || !equal {
					return false, fieldName, err
				}
			} else if kind == reflect.Struct {
				valid, subFiledName, err := validateStructReadonly(newFieldVal, oldFieldVal)
				if err != nil || !valid {
					return false, fmt.Sprintf("%s.%s", fieldName, subFiledName), err
				}
			} else {
				return false, fieldName, errors.New("inline can only be applied to struct, *struct, **struct and so on")
			}
		}
	}

	return true, "", nil
}

// compareAny - 比较任意类型值是否相同
//
// PARAMS:
//   - new: 更新后的值
//   - old: 旧值
//
// RETURNS:
//
//	equal bool: new 和 old 是否相等; 若相等 equal = true, 否则 equal = false
//	error: nil if succeed, error if fail
func compareAny(newVal, oldVal reflect.Value) (bool, error) {
	// reflect.Value 为 zero value 时, IsValid() 会返回 false;
	// 当 reflect.Value 为 zero value 时, 其他方法除了 String() 都会 panic.
	// 如果 old new 只有一方为 zero value, 则认为 old != new, 而不会报错
	if !newVal.IsValid() || !oldVal.IsValid() {
		return !newVal.IsValid() && !oldVal.IsValid(), nil
	}

	kind := newVal.Type().Kind()

	switch kind {
	case reflect.Bool:
		return newVal.Bool() == oldVal.Bool(), nil
	case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
		return newVal.Int() == oldVal.Int(), nil
	case reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64, reflect.Uintptr:
		return newVal.Uint() == oldVal.Uint(), nil
	case reflect.Float32, reflect.Float64:
		return newVal.Float() == oldVal.Float(), nil
	case reflect.Complex64, reflect.Complex128:
		return newVal.Complex() == oldVal.Complex(), nil
	case reflect.String:
		return newVal.String() == oldVal.String(), nil
	case reflect.Chan, reflect.UnsafePointer:
		return newVal.Pointer() == oldVal.Pointer(), nil
	case reflect.Func:
		return newVal.IsNil() && oldVal.IsNil(), nil
	case reflect.Struct:
		return compareStruct(newVal, oldVal)
	case reflect.Slice, reflect.Array:
		return compareSlice(newVal, oldVal)
	case reflect.Map:
		return compareMap(newVal, oldVal)
	case reflect.Ptr:
		return comparePtr(newVal, oldVal)
	case reflect.Interface:
		return compareInterface(newVal, oldVal)
	default:
		return false, fmt.Errorf("kind %v cannot be handled", kind)
	}
}

// compareType - 比较 new old 类型是否相同, 若相等返回 true
func compareType(newVal, oldVal reflect.Value) bool {
	return newVal.Type() == oldVal.Type()
}

// compareStruct - 比较 struct 重所有字段是否相同, 若相等返回 true
func compareStruct(newVal, oldVal reflect.Value) (bool, error) {
	rType := newVal.Type()

	// 遍历 struct 字段
	for i := 0; i < rType.NumField(); i++ {
		field := rType.Field(i)
		name := field.Name
		equal, err := compareAny(newVal.FieldByName(name), oldVal.FieldByName(name))
		if err != nil {
			return false, err
		}
		if !equal {
			return false, nil
		}
	}

	return true, nil
}

// compareSlice - 比较 slice 是否相等, 若相等返回 true
// 1) new old 同为 nil 或空, 则判定 new = old
// 2) new old 不同为 nil 或空, 判定 new != old
// 3) 比较 new old 对应元素是否都相等
func compareSlice(newVal, oldVal reflect.Value) (bool, error) {
	if newVal.IsNil() || oldVal.IsNil() {
		return newVal.IsNil() && oldVal.IsNil(), nil
	}

	length := newVal.Len()
	if length != oldVal.Len() {
		return false, nil
	}

	// 遍历 slice 元素
	for i := 0; i < length; i++ {
		equal, err := compareAny(newVal.Index(i), oldVal.Index(i))
		if err != nil {
			return false, err
		}
		if !equal {
			return false, nil
		}
	}

	return true, nil
}

// compareMap - 比较 map 是否相等, 若相等返回 true
// 1) new old 同为 nil 或空, 则判定 new = old
// 2) new old 不同为 nil 或空, 则判定 new != old
// 3) 比较 new old 对应 key-value 对是否都相等
func compareMap(newVal, oldVal reflect.Value) (bool, error) {
	if newVal.IsNil() || oldVal.IsNil() {
		return newVal.IsNil() && oldVal.IsNil(), nil
	}

	if newVal.Len() != oldVal.Len() {
		return false, nil
	}

	for _, key := range newVal.MapKeys() {
		// 如果 map 中不含 key, 会返会 reflect.Value 类型的 zero value
		// compareAny 中, 如果 old new 只有一方为 zero value, 则认为 equal = false, 而不会报错
		equal, err := compareAny(newVal.MapIndex(key), oldVal.MapIndex(key))
		if err != nil {
			return false, err
		}
		if !equal {
			return false, nil
		}
	}

	return true, nil
}

// comparePtr - 比较指针是否相等, 若相等返回 true
// 1) new old 同为 nil 或空, 则判定 new = old
// 2) new old 不同为 nil 或空, 则判定 new != old
// 3) 比较 new old 的 element 是否都相等
func comparePtr(newVal, oldVal reflect.Value) (bool, error) {
	if newVal.IsNil() || oldVal.IsNil() {
		return newVal.IsNil() && oldVal.IsNil(), nil
	}

	return compareAny(newVal.Elem(), oldVal.Elem())
}

// compareInterface - 比较 interface 是否相等, 若相等返回 true
// 1) new old 同为 nil 或空, 则判定 new = old
// 2) new old 不同为 nil 或空, 则判定 new != old
// 3) 比较 new old 的 element 类型是否相等
// 4) 比较 new old 的 element 值是否相等
func compareInterface(newVal, oldVal reflect.Value) (bool, error) {
	if newVal.IsNil() || oldVal.IsNil() {
		return newVal.IsNil() && oldVal.IsNil(), nil
	}

	newVal, oldVal = newVal.Elem(), oldVal.Elem()
	if newVal.Type() != oldVal.Type() {
		return false, nil
	}

	return compareAny(newVal, oldVal)
}
