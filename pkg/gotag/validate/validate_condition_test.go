package validate

import (
	"testing"

	ccetypes "icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
)

func TestValidateByConditions(t *testing.T) {
	type args struct {
		new        any
		old        any
		conditions map[ValidateCondition]any
	}
	tests := []struct {
		name       string
		args       args
		wantValid  bool
		wantReason string
		wantErr    bool
	}{
		// 无tag/tag==""/tag=="readonly"/tag=="modifiable"/tag in conditions
		{
			name: "Cluster,no change",
			args: args{
				conditions: nil,
				new: ccetypes.ClusterSpec{
					Handler:                "",
					ClusterID:              "ClusterID",
					UserID:                 "",
					AccountID:              "",
					ClusterName:            "",
					ClusterType:            "",
					Description:            "",
					K8SVersion:             "",
					RuntimeType:            "",
					RuntimeVersion:         "",
					VPCID:                  "",
					VPCUUID:                "",
					VPCCIDR:                "",
					VPCCIDRIPv6:            "",
					Plugins:                nil,
					MasterConfig:           ccetypes.MasterConfig{},
					ContainerNetworkConfig: ccetypes.ContainerNetworkConfig{},
					K8SCustomConfig:        ccetypes.K8SCustomConfig{},
					AuthenticateMode:       "AuthenticateMode",
				},
				old: ccetypes.ClusterSpec{
					Handler:                "",
					ClusterID:              "ClusterID",
					UserID:                 "",
					AccountID:              "",
					ClusterName:            "",
					ClusterType:            "",
					Description:            "",
					K8SVersion:             "",
					RuntimeType:            "",
					RuntimeVersion:         "",
					VPCID:                  "",
					VPCUUID:                "",
					VPCCIDR:                "",
					VPCCIDRIPv6:            "",
					Plugins:                nil,
					MasterConfig:           ccetypes.MasterConfig{},
					ContainerNetworkConfig: ccetypes.ContainerNetworkConfig{},
					K8SCustomConfig:        ccetypes.K8SCustomConfig{},
					AuthenticateMode:       "AuthenticateMode",
				},
			},
			wantValid:  true,
			wantReason: "",
			wantErr:    false,
		},
		{
			name: "Cluster,无tag",
			args: args{
				conditions: nil,
				new: ccetypes.ClusterSpec{
					Handler:                "",
					ClusterID:              "ClusterID",
					UserID:                 "",
					AccountID:              "",
					ClusterName:            "",
					ClusterType:            "",
					Description:            "",
					K8SVersion:             "1.20",
					RuntimeType:            "",
					RuntimeVersion:         "",
					VPCID:                  "",
					VPCUUID:                "",
					VPCCIDR:                "",
					VPCCIDRIPv6:            "",
					Plugins:                nil,
					MasterConfig:           ccetypes.MasterConfig{},
					ContainerNetworkConfig: ccetypes.ContainerNetworkConfig{},
					K8SCustomConfig:        ccetypes.K8SCustomConfig{},
					AuthenticateMode:       "AuthenticateMode",
				},
				old: ccetypes.ClusterSpec{
					Handler:                "",
					ClusterID:              "ClusterID",
					UserID:                 "",
					AccountID:              "",
					ClusterName:            "",
					ClusterType:            "",
					Description:            "",
					K8SVersion:             "1.18",
					RuntimeType:            "",
					RuntimeVersion:         "",
					VPCID:                  "",
					VPCUUID:                "",
					VPCCIDR:                "",
					VPCCIDRIPv6:            "",
					Plugins:                nil,
					MasterConfig:           ccetypes.MasterConfig{},
					ContainerNetworkConfig: ccetypes.ContainerNetworkConfig{},
					K8SCustomConfig:        ccetypes.K8SCustomConfig{},
					AuthenticateMode:       "AuthenticateMode",
				},
			},
			wantValid:  false,
			wantReason: "field K8SVersion cannot be changed",
			wantErr:    false,
		},
		{
			name: "Cluster,struct无tag",
			args: args{
				conditions: nil,
				new: ccetypes.ClusterSpec{
					Handler:        "",
					ClusterID:      "ClusterID",
					UserID:         "",
					AccountID:      "",
					ClusterName:    "",
					ClusterType:    "",
					Description:    "",
					K8SVersion:     "",
					RuntimeType:    "",
					RuntimeVersion: "",
					VPCID:          "",
					VPCUUID:        "",
					VPCCIDR:        "",
					VPCCIDRIPv6:    "",
					Plugins: ccetypes.PluginListType{
						"a",
					},
					MasterConfig:           ccetypes.MasterConfig{},
					ContainerNetworkConfig: ccetypes.ContainerNetworkConfig{},
					K8SCustomConfig:        ccetypes.K8SCustomConfig{},
					AuthenticateMode:       "AuthenticateMode",
				},
				old: ccetypes.ClusterSpec{
					Handler:        "",
					ClusterID:      "ClusterID",
					UserID:         "",
					AccountID:      "",
					ClusterName:    "",
					ClusterType:    "",
					Description:    "",
					K8SVersion:     "",
					RuntimeType:    "",
					RuntimeVersion: "",
					VPCID:          "",
					VPCUUID:        "",
					VPCCIDR:        "",
					VPCCIDRIPv6:    "",
					Plugins: ccetypes.PluginListType{
						"b",
					},
					MasterConfig:           ccetypes.MasterConfig{},
					ContainerNetworkConfig: ccetypes.ContainerNetworkConfig{},
					K8SCustomConfig:        ccetypes.K8SCustomConfig{},
					AuthenticateMode:       "AuthenticateMode",
				},
			},
			wantValid:  false,
			wantReason: "field Plugins cannot be changed",
			wantErr:    false,
		},
		{
			name: "Cluster,tag==''",
			args: args{
				conditions: nil,
				new: ccetypes.ClusterSpec{
					Handler:                "",
					ClusterID:              "ClusterID",
					UserID:                 "",
					AccountID:              "",
					ClusterName:            "",
					ClusterType:            "",
					Description:            "",
					K8SVersion:             "",
					RuntimeType:            "",
					RuntimeVersion:         "",
					VPCID:                  "",
					VPCUUID:                "",
					VPCCIDR:                "",
					VPCCIDRIPv6:            "",
					Plugins:                nil,
					MasterConfig:           ccetypes.MasterConfig{},
					ContainerNetworkConfig: ccetypes.ContainerNetworkConfig{},
					K8SCustomConfig:        ccetypes.K8SCustomConfig{},
					AuthenticateMode:       "AuthenticateMode",
					ProjectTag: ccetypes.Tag{
						TagKey:   "1",
						TagValue: "1",
					},
				},
				old: ccetypes.ClusterSpec{
					Handler:                "",
					ClusterID:              "ClusterID",
					UserID:                 "",
					AccountID:              "",
					ClusterName:            "",
					ClusterType:            "",
					Description:            "",
					K8SVersion:             "",
					RuntimeType:            "",
					RuntimeVersion:         "",
					VPCID:                  "",
					VPCUUID:                "",
					VPCCIDR:                "",
					VPCCIDRIPv6:            "",
					Plugins:                nil,
					MasterConfig:           ccetypes.MasterConfig{},
					ContainerNetworkConfig: ccetypes.ContainerNetworkConfig{},
					K8SCustomConfig:        ccetypes.K8SCustomConfig{},
					AuthenticateMode:       "AuthenticateMode",
					ProjectTag: ccetypes.Tag{
						TagKey:   "1",
						TagValue: "2",
					},
				},
			},
			wantValid:  false,
			wantReason: "field ProjectTag cannot be changed",
			wantErr:    false,
		},
		{
			name: "Cluster,tag==readonly",
			args: args{
				conditions: nil,
				new: ccetypes.ClusterSpec{
					Handler:                "",
					ClusterID:              "ClusterID",
					UserID:                 "",
					AccountID:              "",
					ClusterName:            "",
					ClusterType:            "",
					Description:            "",
					K8SVersion:             "",
					RuntimeType:            "",
					RuntimeVersion:         "",
					VPCID:                  "",
					VPCUUID:                "",
					VPCCIDR:                "",
					VPCCIDRIPv6:            "",
					Plugins:                nil,
					MasterConfig:           ccetypes.MasterConfig{},
					ContainerNetworkConfig: ccetypes.ContainerNetworkConfig{},
					K8SCustomConfig:        ccetypes.K8SCustomConfig{},
					AuthenticateMode:       "AuthenticateMode",
				},
				old: ccetypes.ClusterSpec{
					Handler:                "",
					ClusterID:              "ClusterID",
					UserID:                 "",
					AccountID:              "",
					ClusterName:            "",
					ClusterType:            "",
					Description:            "",
					K8SVersion:             "",
					RuntimeType:            "",
					RuntimeVersion:         "",
					VPCID:                  "",
					VPCUUID:                "",
					VPCCIDR:                "",
					VPCCIDRIPv6:            "",
					Plugins:                nil,
					MasterConfig:           ccetypes.MasterConfig{},
					ContainerNetworkConfig: ccetypes.ContainerNetworkConfig{},
					K8SCustomConfig:        ccetypes.K8SCustomConfig{},
					AuthenticateMode:       "AuthenticateMode2",
				},
			},
			wantValid:  false,
			wantReason: "field AuthenticateMode cannot be changed",
			wantErr:    false,
		},
		{
			name: "Cluster,tag==modifiable",
			args: args{
				conditions: nil,
				new: ccetypes.ClusterSpec{
					Handler:                "",
					ClusterID:              "ClusterID",
					UserID:                 "",
					AccountID:              "",
					ClusterName:            "",
					ClusterType:            "",
					Description:            "",
					K8SVersion:             "",
					RuntimeType:            "",
					RuntimeVersion:         "",
					VPCID:                  "",
					VPCUUID:                "",
					VPCCIDR:                "",
					VPCCIDRIPv6:            "",
					Plugins:                nil,
					MasterConfig:           ccetypes.MasterConfig{},
					ContainerNetworkConfig: ccetypes.ContainerNetworkConfig{},
					K8SCustomConfig:        ccetypes.K8SCustomConfig{},
					AuthenticateMode:       "AuthenticateMode",
				},
				old: ccetypes.ClusterSpec{
					Handler:                "",
					ClusterID:              "ClusterID",
					UserID:                 "",
					AccountID:              "",
					ClusterName:            "lql",
					ClusterType:            "",
					Description:            "",
					K8SVersion:             "",
					RuntimeType:            "",
					RuntimeVersion:         "",
					VPCID:                  "",
					VPCUUID:                "",
					VPCCIDR:                "",
					VPCCIDRIPv6:            "",
					Plugins:                nil,
					MasterConfig:           ccetypes.MasterConfig{},
					ContainerNetworkConfig: ccetypes.ContainerNetworkConfig{},
					K8SCustomConfig:        ccetypes.K8SCustomConfig{},
					AuthenticateMode:       "AuthenticateMode",
				},
			},
			wantValid:  true,
			wantReason: "",
			wantErr:    false,
		},
		{
			name: "Instance equal",
			args: args{
				new: ccetypes.InstanceSpec{
					Handler:           "",
					CCEInstanceID:     "CCEInstanceID",
					InstanceName:      "",
					RuntimeType:       "",
					RuntimeVersion:    "",
					ClusterID:         "",
					ClusterRole:       "",
					UserID:            "",
					AccountID:         "",
					InstanceGroupID:   "",
					InstanceGroupName: "",
					MasterType:        "",
					Existed:           false,
					ExistedOption:     ccetypes.ExistedOption{},
					MachineType:       "",
					InstanceOption:    nil,
					InstanceType:      "",
					BBCOption:         ccetypes.BBCOption{},
					VPCConfig: ccetypes.VPCConfig{
						VPCID: "VPCID",
					},
					InstanceResource:          ccetypes.InstanceResource{},
					ImageID:                   "",
					ImageUUID:                 "",
					InstanceOS:                ccetypes.InstanceOS{},
					NeedEIP:                   false,
					EIPOption:                 ccetypes.EIPOption{},
					AdminPassword:             "",
					SSHKeyID:                  "",
					InstanceChargingType:      "",
					InstancePreChargingOption: ccetypes.InstancePreChargingOption{},
					DeleteOption:              nil,
					DeployCustomConfig:        ccetypes.DeployCustomConfig{},
					Tags:                      nil,
					Labels:                    nil,
					Taints:                    nil,
					CCEInstancePriority:       0,
				},
				old: ccetypes.InstanceSpec{
					Handler:           "",
					CCEInstanceID:     "CCEInstanceID",
					InstanceName:      "",
					RuntimeType:       "",
					RuntimeVersion:    "",
					ClusterID:         "",
					ClusterRole:       "",
					UserID:            "",
					AccountID:         "",
					InstanceGroupID:   "",
					InstanceGroupName: "",
					MasterType:        "",
					Existed:           false,
					ExistedOption:     ccetypes.ExistedOption{},
					MachineType:       "",
					InstanceOption:    nil,
					InstanceType:      "",
					BBCOption:         ccetypes.BBCOption{},
					VPCConfig: ccetypes.VPCConfig{
						VPCID: "VPCID",
					},
					InstanceResource:          ccetypes.InstanceResource{},
					ImageID:                   "",
					ImageUUID:                 "",
					InstanceOS:                ccetypes.InstanceOS{},
					NeedEIP:                   false,
					EIPOption:                 ccetypes.EIPOption{},
					AdminPassword:             "",
					SSHKeyID:                  "",
					InstanceChargingType:      "",
					InstancePreChargingOption: ccetypes.InstancePreChargingOption{},
					DeleteOption:              nil,
					DeployCustomConfig:        ccetypes.DeployCustomConfig{},
					Tags:                      nil,
					Labels:                    nil,
					Taints:                    nil,
					CCEInstancePriority:       0,
				},
			},
			wantValid:  true,
			wantReason: "",
			wantErr:    false,
		},
		{
			name: "Instance,tag==modifiable",
			args: args{
				new: ccetypes.InstanceSpec{
					Handler:           "",
					CCEInstanceID:     "CCEInstanceID",
					InstanceName:      "",
					RuntimeType:       "",
					RuntimeVersion:    "",
					ClusterID:         "",
					ClusterRole:       "",
					UserID:            "",
					AccountID:         "",
					InstanceGroupID:   "",
					InstanceGroupName: "",
					MasterType:        "",
					Existed:           false,
					ExistedOption:     ccetypes.ExistedOption{},
					MachineType:       "",
					InstanceOption:    nil,
					InstanceType:      "",
					BBCOption:         ccetypes.BBCOption{},
					VPCConfig: ccetypes.VPCConfig{
						VPCID: "VPCID",
					},
					InstanceResource:          ccetypes.InstanceResource{},
					ImageID:                   "",
					ImageUUID:                 "",
					InstanceOS:                ccetypes.InstanceOS{},
					NeedEIP:                   false,
					EIPOption:                 ccetypes.EIPOption{},
					AdminPassword:             "oldpswd",
					SSHKeyID:                  "",
					InstanceChargingType:      "",
					InstancePreChargingOption: ccetypes.InstancePreChargingOption{},
					DeleteOption:              nil,
					DeployCustomConfig:        ccetypes.DeployCustomConfig{},
					Tags:                      nil,
					Labels:                    nil,
					Taints:                    nil,
					CCEInstancePriority:       0,
				},
				old: ccetypes.InstanceSpec{
					Handler:           "",
					CCEInstanceID:     "CCEInstanceID",
					InstanceName:      "",
					RuntimeType:       "",
					RuntimeVersion:    "",
					ClusterID:         "",
					ClusterRole:       "",
					UserID:            "",
					AccountID:         "",
					InstanceGroupID:   "",
					InstanceGroupName: "",
					MasterType:        "",
					Existed:           false,
					ExistedOption:     ccetypes.ExistedOption{},
					MachineType:       "",
					InstanceOption:    nil,
					InstanceType:      "",
					BBCOption:         ccetypes.BBCOption{},
					VPCConfig: ccetypes.VPCConfig{
						VPCID: "VPCID",
					},
					InstanceResource:          ccetypes.InstanceResource{},
					ImageID:                   "",
					ImageUUID:                 "",
					InstanceOS:                ccetypes.InstanceOS{},
					NeedEIP:                   false,
					EIPOption:                 ccetypes.EIPOption{},
					AdminPassword:             "newpswd",
					SSHKeyID:                  "",
					InstanceChargingType:      "",
					InstancePreChargingOption: ccetypes.InstancePreChargingOption{},
					DeleteOption:              nil,
					DeployCustomConfig:        ccetypes.DeployCustomConfig{},
					Tags:                      nil,
					Labels:                    nil,
					Taints:                    nil,
					CCEInstancePriority:       0,
				},
			},
			wantValid:  true,
			wantReason: "",
			wantErr:    false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, got1, err := ValidateByConditions(tt.args.new, tt.args.old, tt.args.conditions)
			if (err != nil) != tt.wantErr {
				t.Errorf("ValidateByConditions() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.wantValid {
				t.Errorf("ValidateByConditions() got = %v, want %v", got, tt.wantValid)
			}
			if got1 != tt.wantReason {
				t.Errorf("ValidateByConditions() got1 = %v, want %v", got1, tt.wantReason)
			}
		})
	}
}
