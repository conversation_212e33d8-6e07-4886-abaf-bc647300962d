package gorm

import (
	"reflect"
	"testing"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/gotag/test"
	ccetypes "icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/utils"
)

var (
	intVal     int     = -1
	int8Val    int8    = -2
	int16Val   int16   = -3
	int32Val   int32   = -4
	int64Val   int64   = -5
	uintVal    uint    = 1
	uint8Val   uint8   = 2
	uint16Val  uint16  = 3
	uint32Val  uint32  = 4
	uint64Val  uint64  = 5
	float32Val float32 = 0.001
	float64Val float64 = -0.00005
	runeVal    rune    = '\t'
	byteVal    byte    = 'a'
	stringVal  string  = "macro"
	boolVal    bool    = true

	intVal3     test.Int     = -1
	int8Val3    test.Int8    = -2
	int16Val3   test.Int16   = -3
	int32Val3   test.Int32   = -4
	int64Val3   test.Int64   = -5
	uintVal3    test.Uint    = 1
	uint8Val3   test.Uint8   = 2
	uint16Val3  test.Uint16  = 3
	uint32Val3  test.Uint32  = 4
	uint64Val3  test.Uint64  = 5
	float32Val3 test.Float32 = 0.001
	float64Val3 test.Float64 = -0.00005
	runeVal3    test.Rune    = '\t'
	byteVal3    test.Byte    = 'a'
	stringVal3  test.String  = "macro"
	boolVal3    test.Bool    = true
)

func TestConvertStructToMap(t *testing.T) {
	type args struct {
		obj any
	}
	tests := []struct {
		name    string
		args    args
		want    map[string]any
		wantErr bool
	}{
		{
			name: "正常情况: 遍历各种类型",
			args: args{
				obj: test.ParentStruct{
					String:     stringVal3,
					Int:        intVal3,
					Int8:       int8Val3,
					Int16:      int16Val3,
					Int32:      int32Val3,
					Int64:      int64Val3,
					Uint:       uintVal3,
					Uint8:      uint8Val3,
					Uint16:     uint16Val3,
					Uint32:     uint32Val3,
					Uint64:     uint64Val3,
					Rune:       runeVal3,
					Byte:       byteVal3,
					Float32:    float32Val3,
					Float64:    float64Val3,
					Bool:       boolVal3,
					StringPtr:  &stringVal3,
					IntPtr:     &intVal3,
					Int8Ptr:    &int8Val3,
					Int16Ptr:   &int16Val3,
					Int32Ptr:   &int32Val3,
					Int64Ptr:   &int64Val3,
					UintPtr:    &uintVal3,
					Uint8Ptr:   &uint8Val3,
					Uint16Ptr:  &uint16Val3,
					Uint32Ptr:  &uint32Val3,
					Uint64Ptr:  &uint64Val3,
					RunePtr:    &runeVal3,
					BytePtr:    &byteVal3,
					Float32Ptr: &float32Val3,
					Float64Ptr: &float64Val3,
					BoolPtr:    &boolVal3,
					SonStruct: test.SonStruct{
						String:  stringVal,
						Int:     intVal,
						Int8:    int8Val,
						Int16:   int16Val,
						Int32:   int32Val,
						Int64:   int64Val,
						Uint:    uintVal,
						Uint8:   uint8Val,
						Uint16:  uint16Val,
						Uint32:  uint32Val,
						Uint64:  uint64Val,
						Rune:    runeVal,
						Byte:    byteVal,
						Float32: float32Val,
						Float64: float64Val,
						Bool:    boolVal,

						StringPtr:  &stringVal,
						IntPtr:     &intVal,
						Int8Ptr:    &int8Val,
						Int16Ptr:   &int16Val,
						Int32Ptr:   &int32Val,
						Int64Ptr:   &int64Val,
						UintPtr:    &uintVal,
						Uint8Ptr:   &uint8Val,
						Uint16Ptr:  &uint16Val,
						Uint32Ptr:  &uint32Val,
						Uint64Ptr:  &uint64Val,
						RunePtr:    &runeVal,
						BytePtr:    &byteVal,
						Float32Ptr: &float32Val,
						Float64Ptr: &float64Val,
						BoolPtr:    &boolVal,
					},
					SonStructPtr: &test.SonStruct{
						String:  stringVal,
						Int:     intVal,
						Int8:    int8Val,
						Int16:   int16Val,
						Int32:   int32Val,
						Int64:   int64Val,
						Uint:    uintVal,
						Uint8:   uint8Val,
						Uint16:  uint16Val,
						Uint32:  uint32Val,
						Uint64:  uint64Val,
						Rune:    runeVal,
						Byte:    byteVal,
						Float32: float32Val,
						Float64: float64Val,
						Bool:    boolVal,

						StringPtr:  &stringVal,
						IntPtr:     &intVal,
						Int8Ptr:    &int8Val,
						Int16Ptr:   &int16Val,
						Int32Ptr:   &int32Val,
						Int64Ptr:   &int64Val,
						UintPtr:    &uintVal,
						Uint8Ptr:   &uint8Val,
						Uint16Ptr:  &uint16Val,
						Uint32Ptr:  &uint32Val,
						Uint64Ptr:  &uint64Val,
						RunePtr:    &runeVal,
						BytePtr:    &byteVal,
						Float32Ptr: &float32Val,
						Float64Ptr: &float64Val,
						BoolPtr:    &boolVal,
					},
					SonStructSlice: []test.SonStruct{
						{
							String:  stringVal,
							Int:     intVal,
							Int8:    int8Val,
							Int16:   int16Val,
							Int32:   int32Val,
							Int64:   int64Val,
							Uint:    uintVal,
							Uint8:   uint8Val,
							Uint16:  uint16Val,
							Uint32:  uint32Val,
							Uint64:  uint64Val,
							Rune:    runeVal,
							Byte:    byteVal,
							Float32: float32Val,
							Float64: float64Val,
							Bool:    boolVal,

							StringPtr:  &stringVal,
							IntPtr:     &intVal,
							Int8Ptr:    &int8Val,
							Int16Ptr:   &int16Val,
							Int32Ptr:   &int32Val,
							Int64Ptr:   &int64Val,
							UintPtr:    &uintVal,
							Uint8Ptr:   &uint8Val,
							Uint16Ptr:  &uint16Val,
							Uint32Ptr:  &uint32Val,
							Uint64Ptr:  &uint64Val,
							RunePtr:    &runeVal,
							BytePtr:    &byteVal,
							Float32Ptr: &float32Val,
							Float64Ptr: &float64Val,
							BoolPtr:    &boolVal,
						},
					},
					SonStructPtrSlice: []*test.SonStruct{
						{
							String:  stringVal,
							Int:     intVal,
							Int8:    int8Val,
							Int16:   int16Val,
							Int32:   int32Val,
							Int64:   int64Val,
							Uint:    uintVal,
							Uint8:   uint8Val,
							Uint16:  uint16Val,
							Uint32:  uint32Val,
							Uint64:  uint64Val,
							Rune:    runeVal,
							Byte:    byteVal,
							Float32: float32Val,
							Float64: float64Val,
							Bool:    boolVal,

							StringPtr:  &stringVal,
							IntPtr:     &intVal,
							Int8Ptr:    &int8Val,
							Int16Ptr:   &int16Val,
							Int32Ptr:   &int32Val,
							Int64Ptr:   &int64Val,
							UintPtr:    &uintVal,
							Uint8Ptr:   &uint8Val,
							Uint16Ptr:  &uint16Val,
							Uint32Ptr:  &uint32Val,
							Uint64Ptr:  &uint64Val,
							RunePtr:    &runeVal,
							BytePtr:    &byteVal,
							Float32Ptr: &float32Val,
							Float64Ptr: &float64Val,
							BoolPtr:    &boolVal,
						},
					},
					SonStructMap: map[string]test.SonStruct{
						"sonStruct": {
							String:  stringVal,
							Int:     intVal,
							Int8:    int8Val,
							Int16:   int16Val,
							Int32:   int32Val,
							Int64:   int64Val,
							Uint:    uintVal,
							Uint8:   uint8Val,
							Uint16:  uint16Val,
							Uint32:  uint32Val,
							Uint64:  uint64Val,
							Rune:    runeVal,
							Byte:    byteVal,
							Float32: float32Val,
							Float64: float64Val,
							Bool:    boolVal,

							StringPtr:  &stringVal,
							IntPtr:     &intVal,
							Int8Ptr:    &int8Val,
							Int16Ptr:   &int16Val,
							Int32Ptr:   &int32Val,
							Int64Ptr:   &int64Val,
							UintPtr:    &uintVal,
							Uint8Ptr:   &uint8Val,
							Uint16Ptr:  &uint16Val,
							Uint32Ptr:  &uint32Val,
							Uint64Ptr:  &uint64Val,
							RunePtr:    &runeVal,
							BytePtr:    &byteVal,
							Float32Ptr: &float32Val,
							Float64Ptr: &float64Val,
							BoolPtr:    &boolVal,
						},
					},
					SonStructPtrMap: map[string]*test.SonStruct{
						"1": {
							String:  stringVal,
							Int:     intVal,
							Int8:    int8Val,
							Int16:   int16Val,
							Int32:   int32Val,
							Int64:   int64Val,
							Uint:    uintVal,
							Uint8:   uint8Val,
							Uint16:  uint16Val,
							Uint32:  uint32Val,
							Uint64:  uint64Val,
							Rune:    runeVal,
							Byte:    byteVal,
							Float32: float32Val,
							Float64: float64Val,
							Bool:    boolVal,

							StringPtr:  &stringVal,
							IntPtr:     &intVal,
							Int8Ptr:    &int8Val,
							Int16Ptr:   &int16Val,
							Int32Ptr:   &int32Val,
							Int64Ptr:   &int64Val,
							UintPtr:    &uintVal,
							Uint8Ptr:   &uint8Val,
							Uint16Ptr:  &uint16Val,
							Uint32Ptr:  &uint32Val,
							Uint64Ptr:  &uint64Val,
							RunePtr:    &runeVal,
							BytePtr:    &byteVal,
							Float32Ptr: &float32Val,
							Float64Ptr: &float64Val,
							BoolPtr:    &boolVal,
						},
					},
				},
			},
			want: map[string]any{
				"string":  "macro",
				"int":     -1,
				"int8":    -2,
				"int16":   -3,
				"int32":   -4,
				"int64":   -5,
				"uint":    1,
				"uint8":   2,
				"uint16":  3,
				"uint32":  4,
				"uint64":  5,
				"rune":    9,
				"byte":    97,
				"float32": 0.001,
				"float64": -0.00005,
				"bool":    true,

				"string_ptr":  "macro",
				"int_ptr":     -1,
				"int8_ptr":    -2,
				"int16_ptr":   -3,
				"int32_ptr":   -4,
				"int64_ptr":   -5,
				"uint_ptr":    1,
				"uint8_ptr":   2,
				"uint16_ptr":  3,
				"uint32_ptr":  4,
				"uint64_ptr":  5,
				"rune_ptr":    9,
				"byte_ptr":    97,
				"float32_ptr": 0.001,
				"float64_ptr": -0.00005,
				"bool_ptr":    true,

				"son_string":  "macro",
				"son_int":     -1,
				"son_int8":    -2,
				"son_int16":   -3,
				"son_int32":   -4,
				"son_int64":   -5,
				"son_uint":    1,
				"son_uint8":   2,
				"son_uint16":  3,
				"son_uint32":  4,
				"son_uint64":  5,
				"son_rune":    9,
				"son_byte":    97,
				"son_float32": 0.001,
				"son_float64": -0.00005,
				"son_bool":    true,

				"son_string_ptr":  "macro",
				"son_int_ptr":     -1,
				"son_int8_ptr":    -2,
				"son_int16_ptr":   -3,
				"son_int32_ptr":   -4,
				"son_int64_ptr":   -5,
				"son_uint_ptr":    1,
				"son_uint8_ptr":   2,
				"son_uint16_ptr":  3,
				"son_uint32_ptr":  4,
				"son_uint64_ptr":  5,
				"son_rune_ptr":    9,
				"son_byte_ptr":    97,
				"son_float32_ptr": 0.001,
				"son_float64_ptr": -0.00005,
				"son_bool_ptr":    true,
				"son_struct": test.SonStruct{
					String:  stringVal,
					Int:     intVal,
					Int8:    int8Val,
					Int16:   int16Val,
					Int32:   int32Val,
					Int64:   int64Val,
					Uint:    uintVal,
					Uint8:   uint8Val,
					Uint16:  uint16Val,
					Uint32:  uint32Val,
					Uint64:  uint64Val,
					Rune:    runeVal,
					Byte:    byteVal,
					Float32: float32Val,
					Float64: float64Val,
					Bool:    boolVal,

					StringPtr:  &stringVal,
					IntPtr:     &intVal,
					Int8Ptr:    &int8Val,
					Int16Ptr:   &int16Val,
					Int32Ptr:   &int32Val,
					Int64Ptr:   &int64Val,
					UintPtr:    &uintVal,
					Uint8Ptr:   &uint8Val,
					Uint16Ptr:  &uint16Val,
					Uint32Ptr:  &uint32Val,
					Uint64Ptr:  &uint64Val,
					RunePtr:    &runeVal,
					BytePtr:    &byteVal,
					Float32Ptr: &float32Val,
					Float64Ptr: &float64Val,
					BoolPtr:    &boolVal,
				},
				"son_struct_slice": []test.SonStruct{
					{
						String:  stringVal,
						Int:     intVal,
						Int8:    int8Val,
						Int16:   int16Val,
						Int32:   int32Val,
						Int64:   int64Val,
						Uint:    uintVal,
						Uint8:   uint8Val,
						Uint16:  uint16Val,
						Uint32:  uint32Val,
						Uint64:  uint64Val,
						Rune:    runeVal,
						Byte:    byteVal,
						Float32: float32Val,
						Float64: float64Val,
						Bool:    boolVal,

						StringPtr:  &stringVal,
						IntPtr:     &intVal,
						Int8Ptr:    &int8Val,
						Int16Ptr:   &int16Val,
						Int32Ptr:   &int32Val,
						Int64Ptr:   &int64Val,
						UintPtr:    &uintVal,
						Uint8Ptr:   &uint8Val,
						Uint16Ptr:  &uint16Val,
						Uint32Ptr:  &uint32Val,
						Uint64Ptr:  &uint64Val,
						RunePtr:    &runeVal,
						BytePtr:    &byteVal,
						Float32Ptr: &float32Val,
						Float64Ptr: &float64Val,
						BoolPtr:    &boolVal,
					},
				},
				"son_struct_ptr_slice": []*test.SonStruct{
					{
						String:  stringVal,
						Int:     intVal,
						Int8:    int8Val,
						Int16:   int16Val,
						Int32:   int32Val,
						Int64:   int64Val,
						Uint:    uintVal,
						Uint8:   uint8Val,
						Uint16:  uint16Val,
						Uint32:  uint32Val,
						Uint64:  uint64Val,
						Rune:    runeVal,
						Byte:    byteVal,
						Float32: float32Val,
						Float64: float64Val,
						Bool:    boolVal,

						StringPtr:  &stringVal,
						IntPtr:     &intVal,
						Int8Ptr:    &int8Val,
						Int16Ptr:   &int16Val,
						Int32Ptr:   &int32Val,
						Int64Ptr:   &int64Val,
						UintPtr:    &uintVal,
						Uint8Ptr:   &uint8Val,
						Uint16Ptr:  &uint16Val,
						Uint32Ptr:  &uint32Val,
						Uint64Ptr:  &uint64Val,
						RunePtr:    &runeVal,
						BytePtr:    &byteVal,
						Float32Ptr: &float32Val,
						Float64Ptr: &float64Val,
						BoolPtr:    &boolVal,
					},
				},
				"son_struct_map": map[string]test.SonStruct{
					"sonStruct": {
						String:  stringVal,
						Int:     intVal,
						Int8:    int8Val,
						Int16:   int16Val,
						Int32:   int32Val,
						Int64:   int64Val,
						Uint:    uintVal,
						Uint8:   uint8Val,
						Uint16:  uint16Val,
						Uint32:  uint32Val,
						Uint64:  uint64Val,
						Rune:    runeVal,
						Byte:    byteVal,
						Float32: float32Val,
						Float64: float64Val,
						Bool:    boolVal,

						StringPtr:  &stringVal,
						IntPtr:     &intVal,
						Int8Ptr:    &int8Val,
						Int16Ptr:   &int16Val,
						Int32Ptr:   &int32Val,
						Int64Ptr:   &int64Val,
						UintPtr:    &uintVal,
						Uint8Ptr:   &uint8Val,
						Uint16Ptr:  &uint16Val,
						Uint32Ptr:  &uint32Val,
						Uint64Ptr:  &uint64Val,
						RunePtr:    &runeVal,
						BytePtr:    &byteVal,
						Float32Ptr: &float32Val,
						Float64Ptr: &float64Val,
						BoolPtr:    &boolVal,
					},
				},
				"son_struct_ptr_map": map[string]*test.SonStruct{
					"1": {
						String:  stringVal,
						Int:     intVal,
						Int8:    int8Val,
						Int16:   int16Val,
						Int32:   int32Val,
						Int64:   int64Val,
						Uint:    uintVal,
						Uint8:   uint8Val,
						Uint16:  uint16Val,
						Uint32:  uint32Val,
						Uint64:  uint64Val,
						Rune:    runeVal,
						Byte:    byteVal,
						Float32: float32Val,
						Float64: float64Val,
						Bool:    boolVal,

						StringPtr:  &stringVal,
						IntPtr:     &intVal,
						Int8Ptr:    &int8Val,
						Int16Ptr:   &int16Val,
						Int32Ptr:   &int32Val,
						Int64Ptr:   &int64Val,
						UintPtr:    &uintVal,
						Uint8Ptr:   &uint8Val,
						Uint16Ptr:  &uint16Val,
						Uint32Ptr:  &uint32Val,
						Uint64Ptr:  &uint64Val,
						RunePtr:    &runeVal,
						BytePtr:    &byteVal,
						Float32Ptr: &float32Val,
						Float64Ptr: &float64Val,
						BoolPtr:    &boolVal,
					},
				},
			},
			wantErr: false,
		},
		{
			name: "reflect zero Value",
			args: args{
				obj: ccetypes.InstanceSpec{},
			},
			want: map[string]any{
				"account_id":            "",
				"admin_password":        "",
				"auto_renew":            false,
				"auto_renew_time":       0,
				"auto_renew_timeunit":   "",
				"available_zone":        "",
				"bbc_raid_id":           "",
				"bbc_reserve_data":      false,
				"bbc_sys_disk_size":     0,
				"bec_city":              "",
				"bec_region":            "",
				"bec_service_provider":  "",
				"bid":                   false,
				"cce_instance_id":       "",
				"cce_instance_priority": 0,
				"cds_list":              nil,
				"cluster_id":            "",
				"cluster_role":          "",
				"cpu":                   0,
				"deploy_custom_config": ccetypes.DeployCustomConfig{
					DockerConfig:     ccetypes.DockerConfig{},
					ContainerdConfig: ccetypes.ContainerdConfig{},
				},
				"eip_bandwidth":          0,
				"eip_charging_type":      "",
				"eip_name":               "",
				"eip_purchase_type":      "",
				"purchase_timeunit":      "",
				"existed":                false,
				"existed_instance_id":    "",
				"gpu_count":              0,
				"gpu_type":               "",
				"handler":                "",
				"image_id":               "",
				"image_name":             "",
				"image_type":             "",
				"image_uuid":             "",
				"instance_charging_type": "",
				"instance_group_id":      "",
				"instance_group_name":    "",
				"instance_name":          "",
				"instance_type":          "",
				"labels":                 nil,
				"annotations":            nil,
				"local_disk_size":        0,
				"machine_spec":           "",
				"machine_type":           "",
				"master_type":            "",
				"mem":                    0,
				"need_eip":               false,
				"os_arch":                "",
				"os_build":               "",
				"os_name":                "",
				"os_type":                "",
				"os_version":             "",
				"purchase_time":          0,
				"root_disk_size":         0,
				"root_disk_type":         "",
				"runtime_type":           "",
				"runtime_version":        "",
				"security_group_id":      "",
				"security_group_uuid":    "",
				"ssh_key_id":             "",
				"tag_list":               nil,
				"taints":                 nil,
				"user_id":                "",
				"vpc_id":                 "",
				"vpc_subnet_cidr":        "",
				"vpc_subnet_cidr_ipv6":   "",
				"vpc_subnet_id":          "",
				"vpc_subnet_type":        "",
				"vpc_subnet_uuid":        "",
				"vpc_uuid":               "",
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := ConvertStructToMap(tt.args.obj)
			if (err != nil) != tt.wantErr {
				t.Errorf("ConvertStructToMap() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(utils.ToJSON(got), utils.ToJSON(tt.want)) {
				t.Errorf("ConvertStructToMap() got = %v, want %v", utils.ToJSON(got), utils.ToJSON(tt.want))
			}
		})
	}
}
