package gorm

import (
	"errors"
	"fmt"
	"reflect"
	"strings"
)

const (
	tagName = "gorm"

	gormEmbedded = "EMBEDDED"
	gormOmit     = "-"

	gormSeparatorSemicolon = ";"
	gormSeparatorColon     = ":"

	gormKeyColumn = "column"
	gormKeyType   = "type"

	gormTypeJson = "json"
)

// ConvertStructToMap - 根据 gorm tag 将 struct 转成 map
// 背景：gorm 无法通过 struct 将各个 Go 类型的零值更新到 DB 中，只能通过 map 方式更新零值
// 1) 输出的 map 的 key 等于 struct 字段的 gorm column; 例如 gorm:"column:instance_type" 中的 instance_type
// 2) 输出的 map 的 value 等于 struct 字段的真实值
func ConvertStructToMap(obj any) (map[string]any, error) {
	objVal := reflect.ValueOf(obj)

	// reflect.Value 为 zero value 时, IsValid() 会返回 false
	if !objVal.IsValid() {
		return nil, errors.New("input obj is nil")
	}

	result := make(map[string]any, 0)
	err := convertStructToMap(objVal, result)
	if err != nil {
		return nil, err
	}

	return result, nil
}

func convertStructToMap(val reflect.Value, result map[string]any) error {
	rType := val.Type()
	kind := rType.Kind()

	// 检查是否是 struct 类型
	if kind != reflect.Struct {
		return fmt.Errorf("kind %v is not struct", kind)
	}

	for i := 0; i < rType.NumField(); i++ {
		field := rType.Field(i)
		fieldName := field.Name
		fieldVal := val.FieldByName(fieldName)

		// gorm:"-"
		tag := field.Tag.Get(tagName)
		if tag == gormOmit {
			continue
		}

		// gorm:"EMBEDDED"
		if tag == gormEmbedded {
			// EMBEDDED 仅可应用于 struct 或者 *struct, **struct ...
			kind := fieldVal.Kind()
			for kind == reflect.Ptr {
				fieldVal = fieldVal.Elem()
				kind = val.Kind()
			}

			// nil 跳过
			if kind == reflect.Invalid {
				continue
			}

			if kind != reflect.Struct {
				return errors.New("EMBEDDED can only be applied to struct, *struct, **struct and so on")
			}

			if !fieldVal.IsValid() {
				continue
			}

			// EMBEDDED struct 递归
			err := convertStructToMap(fieldVal, result)
			if err != nil {
				return err
			}
			continue
		}

		// gorm:"type:json;column:xxx"
		kvMap := parseTag(tag)
		column := kvMap[gormKeyColumn]
		if column != "" {
			value := readAnyValue(fieldVal)

			// nil 跳过
			if value == nil {
				continue
			}

			result[column] = value
		}
	}

	return nil
}

// parseTag - 解析 gorm tag 中的 key 和 value, 存在 map 中
func parseTag(tag string) map[string]string {
	kvs := strings.Split(tag, gormSeparatorSemicolon)
	kvMap := make(map[string]string, len(kvs))

	for _, kv := range kvs {
		tmp := strings.Split(kv, gormSeparatorColon)
		if len(tmp) == 2 {
			kvMap[strings.TrimSpace(tmp[0])] = strings.TrimSpace(tmp[1])
		}
	}

	return kvMap
}

func readAnyValue(val reflect.Value) any {
	// reflect.Value 为 zero value 时, IsValid() 会返回 false;
	// 当 reflect.Value 为 zero value 时, 其他方法除了 String() 都会 panic.
	if !val.IsValid() {
		return nil
	}

	if val.Type().Kind() == reflect.Ptr {
		return readAnyValue(val.Elem())
	}

	return val.Interface()
}
