package test

// 以下类型定义仅用于测试
type ParentStruct struct {
	String  String  `validate:"readonly" gorm:"column:string"`
	Int     Int     `validate:"readonly" gorm:"column:int"`
	Int8    Int8    `validate:"readonly" gorm:"column:int8"`
	Int16   Int16   `validate:"readonly" gorm:"column:int16"`
	Int32   Int32   `validate:"readonly" gorm:"column:int32"`
	Int64   Int64   `validate:"readonly" gorm:"column:int64"`
	Uint    Uint    `validate:"readonly" gorm:"column:uint"`
	Uint8   Uint8   `validate:"readonly" gorm:"column:uint8"`
	Uint16  Uint16  `validate:"readonly" gorm:"column:uint16"`
	Uint32  Uint32  `validate:"readonly" gorm:"column:uint32"`
	Uint64  Uint64  `validate:"readonly" gorm:"column:uint64"`
	Rune    Rune    `validate:"readonly" gorm:"column:rune"`
	Byte    Byte    `validate:"readonly" gorm:"column:byte"`
	Float32 Float32 `validate:"readonly" gorm:"column:float32"`
	Float64 Float64 `validate:"readonly" gorm:"column:float64"`
	Bool    Bool    `validate:"readonly" gorm:"column:bool"`

	StringPtr  *String  `validate:"readonly" gorm:"column:string_ptr"`
	IntPtr     *Int     `validate:"readonly" gorm:"column:int_ptr"`
	Int8Ptr    *Int8    `validate:"readonly" gorm:"column:int8_ptr"`
	Int16Ptr   *Int16   `validate:"readonly" gorm:"column:int16_ptr"`
	Int32Ptr   *Int32   `validate:"readonly" gorm:"column:int32_ptr"`
	Int64Ptr   *Int64   `validate:"readonly" gorm:"column:int64_ptr"`
	UintPtr    *Uint    `validate:"readonly" gorm:"column:uint_ptr"`
	Uint8Ptr   *Uint8   `validate:"readonly" gorm:"column:uint8_ptr"`
	Uint16Ptr  *Uint16  `validate:"readonly" gorm:"column:uint16_ptr"`
	Uint32Ptr  *Uint32  `validate:"readonly" gorm:"column:uint32_ptr"`
	Uint64Ptr  *Uint64  `validate:"readonly" gorm:"column:uint64_ptr"`
	RunePtr    *Rune    `validate:"readonly" gorm:"column:rune_ptr"`
	BytePtr    *Byte    `validate:"readonly" gorm:"column:byte_ptr"`
	Float32Ptr *Float32 `validate:"readonly" gorm:"column:float32_ptr"`
	Float64Ptr *Float64 `validate:"readonly" gorm:"column:float64_ptr"`
	BoolPtr    *Bool    `validate:"readonly" gorm:"column:bool_ptr"`

	// 结构体
	SonStruct SonStruct `validate:"inline" gorm:"type:json;column:son_struct"`

	SonStructPtr *SonStruct `validate:"inline" gorm:"EMBEDDED"`

	// 数组、切片
	SonStructSlice []SonStruct `validate:"readonly" gorm:"type:json;column:son_struct_slice"`

	SonStructPtrSlice []*SonStruct `validate:"readonly" gorm:"type:json;column:son_struct_ptr_slice"`

	// map
	SonStructMap map[string]SonStruct `validate:"readonly" gorm:"type:json;column:son_struct_map"`

	SonStructPtrMap map[string]*SonStruct `validate:"readonly" gorm:"type:json;column:son_struct_ptr_map"`

	// 匿名结构体，匿名结构体指针，函数
	// 小写的类型

	NoTagFiled string `json:"noTagFiled" gorm:"-"`
}

type SonStruct struct {
	String  string  `validate:"readonly" gorm:"column:son_string" json:"string"`
	Int     int     `validate:"readonly" gorm:"column:son_int" json:"int"`
	Int8    int8    `validate:"readonly" gorm:"column:son_int8" json:"int8"`
	Int16   int16   `validate:"readonly" gorm:"column:son_int16" json:"int16"`
	Int32   int32   `validate:"readonly" gorm:"column:son_int32" json:"int32"`
	Int64   int64   `validate:"readonly" gorm:"column:son_int64" json:"int64"`
	Uint    uint    `validate:"readonly" gorm:"column:son_uint" json:"uint"`
	Uint8   uint8   `validate:"readonly" gorm:"column:son_uint8" json:"uint8"`
	Uint16  uint16  `validate:"readonly" gorm:"column:son_uint16" json:"uint16"`
	Uint32  uint32  `validate:"readonly" gorm:"column:son_uint32" json:"uint32"`
	Uint64  uint64  `validate:"readonly" gorm:"column:son_uint64" json:"uint64"`
	Rune    rune    `validate:"readonly" gorm:"column:son_rune" json:"rune"`
	Byte    byte    `validate:"readonly" gorm:"column:son_byte" json:"byte"`
	Float32 float32 `validate:"readonly" gorm:"column:son_float32" json:"float32"`
	Float64 float64 `validate:"readonly" gorm:"column:son_float64" json:"float64"`
	Bool    bool    `validate:"readonly" gorm:"column:son_bool" json:"bool"`

	StringPtr  *string  `validate:"readonly" gorm:"column:son_string_ptr" json:"stringPtr"`
	IntPtr     *int     `validate:"readonly" gorm:"column:son_int_ptr" json:"intPtr"`
	Int8Ptr    *int8    `validate:"readonly" gorm:"column:son_int8_ptr" json:"int8Ptr"`
	Int16Ptr   *int16   `validate:"readonly" gorm:"column:son_int16_ptr" json:"int16Ptr"`
	Int32Ptr   *int32   `validate:"readonly" gorm:"column:son_int32_ptr" json:"int32Ptr"`
	Int64Ptr   *int64   `validate:"readonly" gorm:"column:son_int64_ptr" json:"int64Ptr"`
	UintPtr    *uint    `validate:"readonly" gorm:"column:son_uint_ptr" json:"uintPtr"`
	Uint8Ptr   *uint8   `validate:"readonly" gorm:"column:son_uint8_ptr" json:"uint8Ptr"`
	Uint16Ptr  *uint16  `validate:"readonly" gorm:"column:son_uint16_ptr" json:"uint16Ptr"`
	Uint32Ptr  *uint32  `validate:"readonly" gorm:"column:son_uint32_ptr" json:"uint32Ptr"`
	Uint64Ptr  *uint64  `validate:"readonly" gorm:"column:son_uint64_ptr" json:"uint64Ptr"`
	RunePtr    *rune    `validate:"readonly" gorm:"column:son_rune_ptr" json:"runePtr"`
	BytePtr    *byte    `validate:"readonly" gorm:"column:son_byte_ptr" json:"bytePtr"`
	Float32Ptr *float32 `validate:"readonly" gorm:"column:son_float32_ptr" json:"float32Ptr"`
	Float64Ptr *float64 `validate:"readonly" gorm:"column:son_float64_ptr" json:"float64Ptr"`
	BoolPtr    *bool    `validate:"readonly" gorm:"column:son_bool_ptr" json:"boolPtr"`
}

type String string

type Int int

type Int8 int8

type Int16 int16

type Int32 int32

type Int64 int64

type Uint uint

type Uint8 uint

type Uint16 uint16

type Uint32 uint32

type Uint64 uint64

type Rune rune

type Byte byte

type Float32 float32

type Float64 float64

type Bool bool
